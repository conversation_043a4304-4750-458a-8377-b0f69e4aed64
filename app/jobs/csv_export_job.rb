class CsvExportJob < ApplicationJob
  queue_as :default

  def perform(admin_user_id, controller_name, filters = {}, export_id = nil)
    admin_user = User.find(admin_user_id)

    # Generate a unique export ID if not provided
    export_id ||= SecureRandom.uuid

    # Create or find the export tracking record
    csv_export = CsvExport.find_or_create_by(export_id: export_id) do |export|
      export.admin_user = admin_user
      export.controller_name = controller_name
      export.filters = filters.to_json
      export.status = :queued
    end

    # Update status to processing
    csv_export.update!(status: :processing, started_at: Time.current)

    # Log the start of the export
    Rails.logger.info "Starting CSV export for #{controller_name} by admin user #{admin_user.email}"
    
    begin
      # Generate the CSV data
      csv_data = generate_csv_for_controller(controller_name, filters)
      
      # Create filename
      filename = "#{controller_name}_#{Date.current.strftime('%Y%m%d')}_#{export_id}.csv"
      
      # Store the CSV file temporarily
      temp_file = Tempfile.new([filename, '.csv'])
      temp_file.write(csv_data)
      temp_file.rewind
      
      # Count records for tracking
      record_count = count_records_for_controller(controller_name, filters)

      # Send email with the CSV attachment
      AdminMailer.csv_export_ready(admin_user, filename, temp_file.path).deliver_now

      # Update export status to completed
      csv_export.update!(
        status: :completed,
        completed_at: Time.current,
        record_count: record_count
      )

      # Log successful completion
      AdminAuditLog.log_action(
        action: 'export_completed',
        controller: "super_admin/#{controller_name}",
        resource: nil,
        changes: {
          'export_id' => [nil, export_id],
          'filename' => [nil, filename],
          'filters' => [nil, filters],
          'status' => [nil, 'completed'],
          'record_count' => [nil, record_count]
        },
        admin_user: admin_user
      )

      Rails.logger.info "CSV export completed for #{controller_name} by admin user #{admin_user.email}"
      
    rescue => e
      # Update export status to failed
      csv_export&.update!(
        status: :failed,
        completed_at: Time.current,
        error_message: e.message
      )

      # Log the error
      Rails.logger.error "CSV export failed for #{controller_name} by admin user #{admin_user.email}: #{e.message}"

      # Log failed export
      AdminAuditLog.log_action(
        action: 'export_failed',
        controller: "super_admin/#{controller_name}",
        resource: nil,
        changes: {
          'export_id' => [nil, export_id],
          'error' => [nil, e.message],
          'filters' => [nil, filters],
          'status' => [nil, 'failed']
        },
        admin_user: admin_user
      )

      # Send error notification email
      AdminMailer.csv_export_failed(admin_user, controller_name, e.message).deliver_now

      raise e
    ensure
      # Clean up temp file
      temp_file&.close
      temp_file&.unlink
    end
  end

  private

  def count_records_for_controller(controller_name, filters)
    case controller_name
    when 'admin_users'
      collection = User.includes(:roles, :organizations)
      apply_filters_to_collection(collection, filters).count
    when 'admin_jobs'
      collection = Job.includes(:organization, :job_applications)
      apply_filters_to_collection(collection, filters).count
    when 'admin_job_applications'
      collection = JobApplication.includes(:user, :job, :job_invitation)
      apply_filters_to_collection(collection, filters).count
    when 'admin_messages'
      collection = Message.includes(:user, :conversation)
      apply_filters_to_collection(collection, filters).count
    when 'admin_talent_profiles'
      collection = TalentProfile.includes(:user)
      apply_filters_to_collection(collection, filters).count
    when 'admin_organizations'
      collection = Organization.includes(:users, :jobs)
      apply_filters_to_collection(collection, filters).count
    when 'admin_conversations'
      collection = Conversation.includes(:users, :messages, :job)
      apply_filters_to_collection(collection, filters).count
    when 'admin_chat_requests'
      collection = ChatRequest.includes(:scout, :talent)
      apply_filters_to_collection(collection, filters).count
    when 'admin_roles'
      collection = User.joins(:roles).includes(:roles, :organizations).distinct
      apply_filters_to_collection(collection, filters).count
    when 'admin_audit_logs'
      collection = AdminAuditLog.includes(:admin_user, :resource)
      apply_filters_to_collection(collection, filters).count
    else
      0
    end
  rescue
    0
  end

  def generate_csv_for_controller(controller_name, filters)
    case controller_name
    when 'admin_users'
      generate_users_csv(filters)
    when 'admin_jobs'
      generate_jobs_csv(filters)
    when 'admin_job_applications'
      generate_job_applications_csv(filters)
    when 'admin_messages'
      generate_messages_csv(filters)
    when 'admin_talent_profiles'
      generate_talent_profiles_csv(filters)
    when 'admin_organizations'
      generate_organizations_csv(filters)
    when 'admin_conversations'
      generate_conversations_csv(filters)
    when 'admin_chat_requests'
      generate_chat_requests_csv(filters)
    when 'admin_roles'
      generate_admin_roles_csv(filters)
    when 'admin_audit_logs'
      generate_audit_logs_csv(filters)
    else
      raise "CSV export not implemented for #{controller_name}"
    end
  end

  def apply_filters_to_collection(collection, filters)
    # Apply common filters
    if filters[:search].present?
      # This would need to be customized per model
      collection = collection.where("name ILIKE ? OR email ILIKE ?", "%#{filters[:search]}%", "%#{filters[:search]}%")
    end
    
    if filters[:created_after].present?
      collection = collection.where('created_at >= ?', filters[:created_after])
    end
    
    if filters[:created_before].present?
      collection = collection.where('created_at <= ?', filters[:created_before])
    end
    
    collection
  end

  def generate_users_csv(filters)
    users = User.includes(:roles, :organizations)
    users = apply_filters_to_collection(users, filters)
    
    CSV.generate(headers: true) do |csv|
      csv << ['ID', 'Name', 'Email', 'Verified', 'Signup Intent', 'Organizations', 'Created At']
      
      users.find_each(batch_size: 1000) do |user|
        csv << [
          user.id,
          user.full_name,
          user.email,
          user.verified? ? 'Yes' : 'No',
          user.signup_intent&.humanize,
          user.organizations.pluck(:name).join(', '),
          user.created_at.strftime('%Y-%m-%d %H:%M:%S')
        ]
      end
    end
  end

  def generate_jobs_csv(filters)
    jobs = Job.includes(:organization, :job_applications)
    jobs = apply_filters_to_collection(jobs, filters)
    
    CSV.generate(headers: true) do |csv|
      csv << ['ID', 'Title', 'Category', 'Status', 'Organization', 'Budget Range', 'Platform', 'Applications Count', 'Created At', 'Published At']

      jobs.find_each(batch_size: 1000) do |job|
        csv << [
          job.id,
          job.title,
          job.job_category&.humanize,
          job.status&.humanize,
          job.organization&.name,
          job.budget_range&.humanize&.gsub('_', ' '),
          job.platform&.humanize&.gsub('_', ' '),
          job.job_applications.count,
          job.created_at.strftime('%Y-%m-%d %H:%M:%S'),
          job.published_at&.strftime('%Y-%m-%d %H:%M:%S')
        ]
      end
    end
  end

  def generate_job_applications_csv(filters)
    applications = JobApplication.includes(:user, :job, :job_invitation)
    applications = apply_filters_to_collection(applications, filters)
    
    CSV.generate(headers: true) do |csv|
      csv << ['ID', 'Applicant Name', 'Applicant Email', 'Job Title', 'Status', 'Invitation Status', 'Salary Consideration', 'Applied At']
      
      applications.find_each(batch_size: 1000) do |application|
        csv << [
          application.id,
          application.user.full_name,
          application.user.email,
          application.job.title,
          application.status&.humanize,
          application.job_invitation&.status&.humanize || 'Not Invited',
          application.salary_consideration ? 'Yes' : 'No',
          application.created_at.strftime('%Y-%m-%d %H:%M:%S')
        ]
      end
    end
  end

  def generate_messages_csv(filters)
    messages = Message.includes(:user, :conversation)
    messages = apply_filters_to_collection(messages, filters)
    
    CSV.generate(headers: true) do |csv|
      csv << ['ID', 'Sender Name', 'Sender Email', 'Conversation ID', 'Content Preview', 'Sent At']
      
      messages.find_each(batch_size: 1000) do |message|
        csv << [
          message.id,
          message.user.full_name,
          message.user.email,
          message.conversation_id,
          message.content&.truncate(100),
          message.created_at.strftime('%Y-%m-%d %H:%M:%S')
        ]
      end
    end
  end

  def generate_talent_profiles_csv(filters)
    profiles = TalentProfile.includes(:user)
    profiles = apply_filters_to_collection(profiles, filters)
    
    CSV.generate(headers: true) do |csv|
      csv << ['ID', 'User Name', 'User Email', 'Bio Preview', 'Experience Level', 'Availability', 'Created At']
      
      profiles.find_each(batch_size: 1000) do |profile|
        csv << [
          profile.id,
          profile.user.full_name,
          profile.user.email,
          profile.bio&.truncate(100),
          profile.experience_level&.humanize,
          profile.availability&.humanize,
          profile.created_at.strftime('%Y-%m-%d %H:%M:%S')
        ]
      end
    end
  end

  def generate_organizations_csv(filters)
    organizations = Organization.includes(:users, :jobs)
    organizations = apply_filters_to_collection(organizations, filters)
    
    CSV.generate(headers: true) do |csv|
      csv << ['ID', 'Name', 'Company Size', 'Members Count', 'Jobs Count', 'Created At']
      
      organizations.find_each(batch_size: 1000) do |org|
        csv << [
          org.id,
          org.name,
          org.size&.humanize,
          org.users.count,
          org.jobs.count,
          org.created_at.strftime('%Y-%m-%d %H:%M:%S')
        ]
      end
    end
  end

  def generate_conversations_csv(filters)
    conversations = Conversation.includes(:users, :messages, :job)
    conversations = apply_filters_to_collection(conversations, filters)
    
    CSV.generate(headers: true) do |csv|
      csv << ['ID', 'Participants', 'Messages Count', 'Related Job', 'Last Activity', 'Created At']
      
      conversations.find_each(batch_size: 1000) do |conversation|
        csv << [
          conversation.id,
          conversation.users.pluck(:email).join(', '),
          conversation.messages.count,
          conversation.job&.title || 'No Job',
          conversation.updated_at.strftime('%Y-%m-%d %H:%M:%S'),
          conversation.created_at.strftime('%Y-%m-%d %H:%M:%S')
        ]
      end
    end
  end

  def generate_chat_requests_csv(filters)
    requests = ChatRequest.includes(:scout, :talent)
    requests = apply_filters_to_collection(requests, filters)
    
    CSV.generate(headers: true) do |csv|
      csv << ['ID', 'Scout Name', 'Scout Email', 'Talent Name', 'Talent Email', 'Status', 'Note', 'Requested At']
      
      requests.find_each(batch_size: 1000) do |request|
        csv << [
          request.id,
          request.scout.full_name,
          request.scout.email,
          request.talent.full_name,
          request.talent.email,
          request.status&.humanize,
          request.note,
          request.requested_at&.strftime('%Y-%m-%d %H:%M:%S')
        ]
      end
    end
  end

  def generate_admin_roles_csv(filters)
    users = User.joins(:roles).includes(:roles, :organizations).distinct
    users = apply_filters_to_collection(users, filters)
    
    CSV.generate(headers: true) do |csv|
      csv << ['ID', 'Name', 'Email', 'Admin Roles', 'Organizations', 'Verified', 'Created At']
      
      users.find_each(batch_size: 1000) do |user|
        csv << [
          user.id,
          user.full_name,
          user.email,
          user.roles.pluck(:name).join(', '),
          user.organizations.pluck(:name).join(', '),
          user.verified? ? 'Yes' : 'No',
          user.created_at.strftime('%Y-%m-%d %H:%M:%S')
        ]
      end
    end
  end

  def generate_audit_logs_csv(filters)
    logs = AdminAuditLog.includes(:admin_user, :resource)
    logs = apply_filters_to_collection(logs, filters)
    
    CSV.generate(headers: true) do |csv|
      csv << ['ID', 'Action', 'Admin User', 'Resource Type', 'Resource ID', 'Changes', 'IP Address', 'Created At']
      
      logs.find_each(batch_size: 1000) do |log|
        csv << [
          log.id,
          log.action&.humanize,
          log.admin_user&.email || 'Unknown',
          log.resource_type,
          log.resource_id,
          log.changes&.to_json,
          log.ip_address,
          log.created_at.strftime('%Y-%m-%d %H:%M:%S')
        ]
      end
    end
  end
end
