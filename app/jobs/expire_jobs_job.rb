class ExpireJobsJob < ApplicationJob
  queue_as :default

  def perform(*args)
    # Find published jobs where expires_at is in the past
    expired_jobs =
      Job.where(status: 'published').where('expires_at <= ?', Time.current)

    # Update their status to 'expired'
    # Use update_all for efficiency if no callbacks are needed, otherwise loop and update
    if expired_jobs.any?
      Rails.logger.info "Expiring #{expired_jobs.count} jobs."
      expired_jobs.update_all(status: 'expired')
      # If you need callbacks:
      # expired_jobs.find_each { |job| job.update(status: 'expired') }
    else
      Rails.logger.info 'No jobs to expire.'
    end
  end
end
