class Current < ActiveSupport::CurrentAttributes
  attribute :session
  attribute :user
  attribute :user_agent, :ip_address
  attribute :organization
  attribute :impersonator_id, :impersonation_log_id

  delegate :user, to: :session, allow_nil: true

  def impersonating?
    impersonator_id.present?
  end

  def impersonator
    return nil unless impersonator_id
    User.find_by(id: impersonator_id)
  end

  def impersonation_log
    return nil unless impersonation_log_id
    ImpersonationLog.find_by(id: impersonation_log_id)
  end

  def impersonation_expired?
    return false unless impersonating?
    log = impersonation_log
    return false unless log
    log.started_at < 24.hours.ago
  end
end
