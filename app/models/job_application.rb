# == Schema Information
#
# Table name: job_applications
#
#  id                 :bigint           not null, primary key
#  accepted_at        :datetime
#  additional_info    :text
#  application_letter :text
#  applied_at         :datetime
#  invited            :boolean          default(FALSE)
#  rejected_at        :datetime
#  salary_considered  :boolean          default(FALSE)
#  status             :integer          default("applied")
#  created_at         :datetime         not null
#  updated_at         :datetime         not null
#  job_id             :bigint           not null
#  job_invitation_id  :bigint
#  user_id            :bigint           not null
#
# Indexes
#
#  index_job_applications_on_created_at          (created_at)
#  index_job_applications_on_job_id              (job_id)
#  index_job_applications_on_job_id_and_status   (job_id,status)
#  index_job_applications_on_job_id_and_user_id  (job_id,user_id) UNIQUE
#  index_job_applications_on_job_invitation_id   (job_invitation_id)
#  index_job_applications_on_status              (status)
#  index_job_applications_on_user_id             (user_id)
#  index_job_applications_on_user_id_and_status  (user_id,status)
#
# Foreign Keys
#
#  fk_rails_...  (job_id => jobs.id)
#  fk_rails_...  (job_invitation_id => job_invitations.id)
#  fk_rails_...  (user_id => users.id)
#
class JobApplication < ApplicationRecord
  searchkick word_start: %i[
               applicant_name
               applicant_email
               application_letter
               job_title
             ],
             filterable: %i[job_id job_title status],
             mappings: {
               properties: {
                 updated_at: {
                   type: 'date',
                 },
                 status: {
                   type: 'keyword',
                 }, # Add mapping for status
               },
             }

  def search_data
    {
      id: id,
      status: status,
      application_letter: application_letter,
      applied_at: applied_at,
      job_title: job.title,
      job_id: job_id,
      applicant_name: user.name.full,
      applicant_email: user.email,
      created_at: created_at,
      updated_at: updated_at,
    }
  end

  belongs_to :job, counter_cache: true
  belongs_to :user

  has_one_attached :resume
  has_many_attached :documents

  enum :status,
       {
         applied: 0,
         reviewed: 1, # Changed from review to reviewed
         qualified: 2,
         offered: 3,
         accepted: 4,
         withdrawn: 5,
       }

  validates :job_id,
            uniqueness: {
              scope: :user_id,
              message: 'You have already applied to this job',
            }

  belongs_to :job_invitation, optional: true

  before_create :set_applied_at

  private

  def set_applied_at
    self.applied_at ||= Time.current
  end

  # Add this method as a public method (not inside a private block)
  def talent_notes
    user.talent_profile&.talent_notes || TalentNote.none
  end
end
