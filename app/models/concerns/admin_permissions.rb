module AdminPermissions
  extend ActiveSupport::Concern

  # Admin permission levels based on PRD requirements
  ADMIN_ROLES = {
    superadmin: {
      name: 'Super Admin',
      description: 'Full administrative control over all system functions',
      permissions: :all
    },
    support: {
      name: 'Support Agent',
      description: 'Limited administrative access for customer support tasks',
      permissions: %w[
        users_read users_edit users_search
        jobs_read jobs_edit jobs_search
        organizations_read organizations_search
        conversations_read messages_read
        chat_requests_read
        talent_profiles_read talent_profiles_search
        export_data
      ]
    },
    readonly: {
      name: 'Read-Only Admin',
      description: 'View-only access for reporting and oversight',
      permissions: %w[
        users_read users_search
        jobs_read jobs_search
        organizations_read organizations_search
        conversations_read messages_read
        chat_requests_read
        talent_profiles_read talent_profiles_search
        job_applications_read
        export_data
      ]
    }
  }.freeze

  included do
    # Add admin role methods to User model
    def admin_role
      return :superadmin if superadmin?
      return :support if has_role?(:support)
      return :readonly if has_role?(:readonly)
      nil
    end

    def admin?
      admin_role.present?
    end

    def admin_role_name
      role = admin_role
      return nil unless role
      ADMIN_ROLES[role][:name]
    end

    def admin_permissions
      role = admin_role
      return [] unless role
      
      permissions = ADMIN_ROLES[role][:permissions]
      return :all if permissions == :all
      permissions || []
    end

    def can_access_admin?
      admin?
    end

    def can?(permission)
      return false unless admin?
      return true if admin_permissions == :all
      admin_permissions.include?(permission.to_s)
    end

    # Specific permission checks for common admin actions
    def can_read?(resource)
      can?("#{resource}_read")
    end

    def can_edit?(resource)
      can?("#{resource}_edit")
    end

    def can_create?(resource)
      can?("#{resource}_create")
    end

    def can_delete?(resource)
      can?("#{resource}_delete")
    end

    def can_search?(resource)
      can?("#{resource}_search")
    end

    def can_export_data?
      can?(:export_data)
    end

    def can_manage_users?
      can?(:users_edit) || can?(:users_create) || can?(:users_delete)
    end

    def can_manage_roles?
      admin_role == :superadmin
    end

    def can_view_audit_logs?
      admin_role == :superadmin
    end

    def can_impersonate?
      admin_role == :superadmin
    end
  end

  class_methods do
    def admin_roles_for_select
      ADMIN_ROLES.map do |key, config|
        [config[:name], key.to_s]
      end
    end

    def admin_role_description(role)
      ADMIN_ROLES[role.to_sym]&.dig(:description)
    end
  end
end
