# Job Form Configuration Concern
# Provides server-side configuration for the modular job form wizard

module JobFormConfiguration
  extend ActiveSupport::Concern

  # Form categories and their step sequences
  FORM_CATEGORIES = {
    'social_media' => {
      name: 'Social Media',
      steps: %w[platform goal],
    },
    'newsletter' => {
      name: 'Newsletter',
      steps: %w[goal frequency length budget],
    },
    'lead_magnet' => {
      name: 'Lead Magnet',
      steps: %w[goal],
    },
  }.freeze

  # Step definitions with validation requirements
  STEP_DEFINITIONS = {
    'category_selection' => {
      name: 'Category Selection',
      title: 'Tell us about the ghostwriter you need',
      required: true,
      fields: %w[job_category],
    },
    'social_media.platform' => {
      name: 'Social Media Platform',
      title: 'Social Media Platform',
      category: 'social_media',
      required: true,
      fields: %w[platform],
    },
    'social_media.goal' => {
      name: 'Social Media Goal',
      title: 'Social Media Goal',
      category: 'social_media',
      required: true,
      fields: %w[outcome],
      conditional_fields: {
        'outcome' => {
          values: %w[leads booked_calls],
          required_fields: %w[
            social_media_goal_type
            social_media_understands_risk_acknowledged
          ],
        },
      },
    },
    'newsletter.goal' => {
      name: 'Newsletter Goal',
      title: 'Newsletter Goal',
      category: 'newsletter',
      required: true,
      fields: %w[outcome],
    },
    'newsletter.frequency' => {
      name: 'Newsletter Frequency',
      title: 'Newsletter Frequency',
      category: 'newsletter',
      required: true,
      fields: %w[newsletter_frequency],
    },
    'newsletter.length' => {
      name: 'Newsletter Length',
      title: 'Newsletter Length',
      category: 'newsletter',
      required: true,
      fields: %w[newsletter_length],
    },
    'newsletter.budget' => {
      name: 'Newsletter Budget',
      title: 'Newsletter Budget',
      category: 'newsletter',
      required: true,
      fields: %w[budget_range],
    },
    'lead_magnet.goal' => {
      name: 'Lead Magnet Goal',
      title: 'Lead Magnet Goal',
      category: 'lead_magnet',
      required: true,
      fields: %w[outcome],
    },
    'job_details' => {
      name: 'Job Details',
      title: 'Job Listing Details',
      required: true,
      fields: %w[title description],
    },
    'topics' => {
      name: 'Topics',
      title: 'Topics & Expertise',
      required: false,
      fields: %w[topics],
    },
    'budget' => {
      name: 'Budget',
      title: 'Budget Range',
      required: true,
      fields: %w[budget_range],
    },
    'work_duration' => {
      name: 'Work Duration',
      title: 'Work Duration',
      required: true,
      fields: %w[work_duration],
    },
    'client_info' => {
      name: 'Client Info',
      title: 'Additional Information',
      required: false,
      fields: %w[],
    },
  }.freeze

  class_methods do
    # Get the step sequence for a given category
    def step_sequence_for_category(category = nil)
      sequence = ['category_selection']

      if category && FORM_CATEGORIES[category]
        category_steps = FORM_CATEGORIES[category][:steps]
        category_steps.each { |step| sequence << "#{category}.#{step}" }
      end

      # Add common steps
      sequence.concat(%w[job_details topics budget work_duration client_info])
      sequence
    end

    # Get step definition by key
    def step_definition(step_key)
      STEP_DEFINITIONS[step_key]
    end

    # Get all available categories
    def available_categories
      FORM_CATEGORIES.keys
    end

    # Get category configuration
    def category_config(category)
      FORM_CATEGORIES[category]
    end

    # Validate step requirements
    def validate_step_requirements(step_key, job_params)
      step_def = step_definition(step_key)
      return true unless step_def

      errors = []

      # Check required fields
      if step_def[:fields]
        step_def[:fields].each do |field|
          errors << "#{field.humanize} is required" if job_params[field].blank?
        end
      end

      # Check conditional fields
      if step_def[:conditional_fields]
        step_def[:conditional_fields].each do |trigger_field, config|
          trigger_value = job_params[trigger_field]

          if config[:values].include?(trigger_value)
            config[:required_fields].each do |required_field|
              if job_params[required_field].blank?
                errors <<
                  "#{required_field.humanize} is required when #{trigger_field.humanize} is #{trigger_value}"
              end
            end
          end
        end
      end

      errors.empty? ? true : errors
    end

    # Get total steps for a category
    def total_steps_for_category(category = nil)
      step_sequence_for_category(category).length
    end

    # Get progress percentage for a step index
    def progress_percentage(step_index, category = nil)
      total = total_steps_for_category(category)
      return 0 if total.zero?

      ((step_index + 1).to_f / total * 100).round
    end

    # Check if step is last in sequence
    def last_step?(step_index, category = nil)
      step_index >= total_steps_for_category(category) - 1
    end

    # Check if step is first in sequence
    def first_step?(step_index)
      step_index <= 0
    end

    # Get step key by index
    def step_key_by_index(step_index, category = nil)
      sequence = step_sequence_for_category(category)
      sequence[step_index]
    end

    # Get step index by key
    def step_index_by_key(step_key, category = nil)
      sequence = step_sequence_for_category(category)
      sequence.index(step_key) || 0
    end

    # Export configuration as JSON for JavaScript
    def form_config_json(category = nil)
      {
        categories: FORM_CATEGORIES,
        steps: STEP_DEFINITIONS,
        sequence: step_sequence_for_category(category),
        selectedCategory: category,
        totalSteps: total_steps_for_category(category),
      }.to_json
    end
  end

  included do
    # Instance methods for Job model

    # Get the current step sequence based on job category
    def step_sequence
      self.class.step_sequence_for_category(job_category)
    end

    # Get current step definition
    def current_step_definition(step_index)
      step_key = self.class.step_key_by_index(step_index, job_category)
      self.class.step_definition(step_key)
    end

    # Validate specific step
    def validate_step(step_index, job_params = nil)
      step_key = self.class.step_key_by_index(step_index, job_category)
      params_to_validate = job_params || attributes
      self.class.validate_step_requirements(step_key, params_to_validate)
    end

    # Get form configuration as JSON
    def form_config_json
      self.class.form_config_json(job_category)
    end

    # Check if all required steps are complete
    def all_required_steps_complete?
      sequence = step_sequence
      sequence.each_with_index do |step_key, index|
        step_def = self.class.step_definition(step_key)
        next unless step_def && step_def[:required]

        validation_result = validate_step(index)
        return false unless validation_result == true
      end
      true
    end
  end
end
