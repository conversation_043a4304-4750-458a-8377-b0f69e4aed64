# == Schema Information
#
# Table name: badge_views
#
#  id                    :bigint           not null, primary key
#  badge_types_displayed :json             not null
#  ip_address            :string
#  metadata              :json
#  referrer              :string
#  request_path          :string
#  user_agent            :string
#  created_at            :datetime         not null
#  updated_at            :datetime         not null
#  session_id            :bigint
#  viewed_user_id        :bigint           not null
#  viewer_user_id        :bigint
#
# Indexes
#
#  index_badge_views_on_created_at                     (created_at)
#  index_badge_views_on_created_at_and_viewed_user_id  (created_at,viewed_user_id)
#  index_badge_views_on_ip_address                     (ip_address)
#  index_badge_views_on_session_id                     (session_id)
#  index_badge_views_on_viewed_user_id                 (viewed_user_id)
#  index_badge_views_on_viewed_user_id_and_created_at  (viewed_user_id,created_at)
#  index_badge_views_on_viewer_user_id                 (viewer_user_id)
#  index_badge_views_on_viewer_user_id_and_created_at  (viewer_user_id,created_at)
#
# Foreign Keys
#
#  fk_rails_...  (session_id => sessions.id)
#  fk_rails_...  (viewed_user_id => users.id)
#  fk_rails_...  (viewer_user_id => users.id)
#
class BadgeView < ApplicationRecord
  # Associations
  belongs_to :viewed_user, class_name: 'User'
  belongs_to :viewer_user, class_name: 'User', optional: true
  belongs_to :session, optional: true

  # Validations
  validates :viewed_user_id, presence: true
  validates :badge_types_displayed, presence: true
  validate :has_viewer_identification
  validate :badge_types_displayed_format

  # Scopes
  scope :recent, -> { where('created_at > ?', 30.days.ago) }
  scope :today, -> { where('created_at > ?', 1.day.ago) }
  scope :this_week, -> { where('created_at > ?', 1.week.ago) }
  scope :this_month, -> { where('created_at > ?', 1.month.ago) }
  
  scope :by_viewed_user, ->(user) { where(viewed_user: user) }
  scope :by_viewer_user, ->(user) { where(viewer_user: user) }
  scope :anonymous_views, -> { where(viewer_user: nil) }
  scope :authenticated_views, -> { where.not(viewer_user: nil) }
  
  scope :with_badge_type, ->(badge_type_id) {
    where("badge_types_displayed::jsonb @> ?", "[#{badge_type_id}]")
  }
  
  scope :by_ip, ->(ip) { where(ip_address: ip) }
  scope :unique_viewers, -> { distinct.pluck(:viewer_user_id, :ip_address) }

  # Class methods for logging badge views
  def self.log_view(viewed_user:, badge_types_displayed:, viewer_user: nil, session: nil, request: nil, metadata: {})
    # Don't log if no badges are displayed
    return if badge_types_displayed.blank?
    
    # Extract request information
    ip_address = request&.remote_ip || Current.ip_address
    user_agent = request&.user_agent || Current.user_agent
    referrer = request&.referer
    request_path = request&.fullpath
    
    # Use current session if not provided
    session ||= Current.session
    
    # Use current user if not provided
    viewer_user ||= Current.user
    
    create!(
      viewed_user: viewed_user,
      viewer_user: viewer_user,
      session: session,
      badge_types_displayed: badge_types_displayed.map(&:to_i),
      ip_address: ip_address,
      user_agent: user_agent,
      referrer: referrer,
      request_path: request_path,
      metadata: metadata
    )
  rescue => e
    Rails.logger.error "Failed to log badge view: #{e.message}"
    # Don't raise the error to avoid breaking the main action
    nil
  end

  # Class methods for analytics
  def self.views_for_user(user, period: 30.days)
    by_viewed_user(user).where('created_at > ?', period.ago)
  end

  def self.views_with_badge_type(badge_type, period: 30.days)
    with_badge_type(badge_type.id).where('created_at > ?', period.ago)
  end

  def self.unique_viewers_for_user(user, period: 30.days)
    views_in_period = by_viewed_user(user).where('created_at > ?', period.ago)

    # Calculate unique viewers by combining authenticated and anonymous counts
    authenticated_viewers = views_in_period.where.not(viewer_user_id: nil).distinct.count(:viewer_user_id)
    anonymous_viewers = views_in_period.where(viewer_user_id: nil).distinct.count(:ip_address)

    authenticated_viewers + anonymous_viewers
  end

  def self.anonymous_view_percentage(period: 30.days)
    total_views = where('created_at > ?', period.ago).count
    return 0 if total_views.zero?
    
    anonymous_count = anonymous_views.where('created_at > ?', period.ago).count
    (anonymous_count.to_f / total_views * 100).round(2)
  end

  # Instance methods
  def anonymous?
    viewer_user.nil?
  end

  def authenticated?
    !anonymous?
  end

  def badge_types
    @badge_types ||= BadgeType.where(id: badge_types_displayed)
  end

  def viewer_identifier
    viewer_user&.email || ip_address || 'Unknown'
  end

  private

  def has_viewer_identification
    if viewer_user.nil? && session.nil? && ip_address.blank?
      errors.add(:base, 'Must have either viewer_user, session, or ip_address for tracking')
    end
  end

  def badge_types_displayed_format
    unless badge_types_displayed.is_a?(Array) && badge_types_displayed.all? { |id| id.is_a?(Integer) }
      errors.add(:badge_types_displayed, 'must be an array of badge type IDs')
    end
  end
end
