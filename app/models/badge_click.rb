# == Schema Information
#
# Table name: badge_clicks
#
#  id              :bigint           not null, primary key
#  click_context   :string           not null
#  ip_address      :string
#  metadata        :json
#  referrer        :string
#  request_path    :string
#  user_agent      :string
#  created_at      :datetime         not null
#  updated_at      :datetime         not null
#  badge_owner_id  :bigint           not null
#  badge_type_id   :bigint           not null
#  clicker_user_id :bigint
#  session_id      :bigint
#
# Indexes
#
#  index_badge_clicks_on_badge_owner_id                  (badge_owner_id)
#  index_badge_clicks_on_badge_owner_id_and_created_at   (badge_owner_id,created_at)
#  index_badge_clicks_on_badge_type_id                   (badge_type_id)
#  index_badge_clicks_on_badge_type_id_and_created_at    (badge_type_id,created_at)
#  index_badge_clicks_on_click_context                   (click_context)
#  index_badge_clicks_on_click_context_and_created_at    (click_context,created_at)
#  index_badge_clicks_on_clicker_user_id                 (clicker_user_id)
#  index_badge_clicks_on_clicker_user_id_and_created_at  (clicker_user_id,created_at)
#  index_badge_clicks_on_created_at                      (created_at)
#  index_badge_clicks_on_created_at_and_badge_type_id    (created_at,badge_type_id)
#  index_badge_clicks_on_ip_address                      (ip_address)
#  index_badge_clicks_on_session_id                      (session_id)
#
# Foreign Keys
#
#  fk_rails_...  (badge_owner_id => users.id)
#  fk_rails_...  (badge_type_id => badge_types.id)
#  fk_rails_...  (clicker_user_id => users.id)
#  fk_rails_...  (session_id => sessions.id)
#
class BadgeClick < ApplicationRecord
  # Associations
  belongs_to :badge_type
  belongs_to :badge_owner, class_name: 'User'
  belongs_to :clicker_user, class_name: 'User', optional: true
  belongs_to :session, optional: true

  # Constants for click contexts
  CLICK_CONTEXTS = %w[
    profile
    search_results
    modal
    directory
    recommendation
    other
  ].freeze

  # Validations
  validates :badge_type_id, presence: true
  validates :badge_owner_id, presence: true
  validates :click_context, presence: true, inclusion: { in: CLICK_CONTEXTS }
  validate :has_clicker_identification

  # Scopes
  scope :recent, -> { where('created_at > ?', 30.days.ago) }
  scope :today, -> { where('created_at > ?', 1.day.ago) }
  scope :this_week, -> { where('created_at > ?', 1.week.ago) }
  scope :this_month, -> { where('created_at > ?', 1.month.ago) }
  
  scope :by_badge_type, ->(badge_type) { where(badge_type: badge_type) }
  scope :by_badge_owner, ->(user) { where(badge_owner: user) }
  scope :by_clicker, ->(user) { where(clicker_user: user) }
  scope :by_context, ->(context) { where(click_context: context) }
  scope :by_ip, ->(ip) { where(ip_address: ip) }
  
  scope :anonymous_clicks, -> { where(clicker_user: nil) }
  scope :authenticated_clicks, -> { where.not(clicker_user: nil) }
  
  scope :profile_clicks, -> { by_context('profile') }
  scope :search_clicks, -> { by_context('search_results') }
  scope :modal_clicks, -> { by_context('modal') }

  # Class methods for logging badge clicks
  def self.log_click(badge_type:, badge_owner:, click_context:, clicker_user: nil, session: nil, request: nil, metadata: {})
    # Validate click context
    unless CLICK_CONTEXTS.include?(click_context.to_s)
      Rails.logger.warn "Invalid badge click context: #{click_context}"
      click_context = 'other'
    end
    
    # Extract request information
    ip_address = request&.remote_ip || Current.ip_address
    user_agent = request&.user_agent || Current.user_agent
    referrer = request&.referer
    request_path = request&.fullpath
    
    # Use current session if not provided
    session ||= Current.session
    
    # Use current user if not provided
    clicker_user ||= Current.user
    
    create!(
      badge_type: badge_type,
      badge_owner: badge_owner,
      clicker_user: clicker_user,
      session: session,
      click_context: click_context,
      ip_address: ip_address,
      user_agent: user_agent,
      referrer: referrer,
      request_path: request_path,
      metadata: metadata
    )
  rescue => e
    Rails.logger.error "Failed to log badge click: #{e.message}"
    # Don't raise the error to avoid breaking the main action
    nil
  end

  # Class methods for analytics
  def self.clicks_for_badge_type(badge_type, period: 30.days)
    by_badge_type(badge_type).where('created_at > ?', period.ago)
  end

  def self.clicks_for_user_badges(user, period: 30.days)
    by_badge_owner(user).where('created_at > ?', period.ago)
  end

  def self.click_rate_by_context(period: 30.days)
    where('created_at > ?', period.ago)
      .group(:click_context)
      .count
  end

  def self.unique_clickers_for_badge_type(badge_type, period: 30.days)
    clicks_in_period = by_badge_type(badge_type).where('created_at > ?', period.ago)

    # Calculate unique clickers by combining authenticated and anonymous counts
    authenticated_clickers = clicks_in_period.where.not(clicker_user_id: nil).distinct.count(:clicker_user_id)
    anonymous_clickers = clicks_in_period.where(clicker_user_id: nil).distinct.count(:ip_address)

    authenticated_clickers + anonymous_clickers
  end

  def self.top_clicked_badges(limit: 10, period: 30.days)
    joins(:badge_type)
      .where('badge_clicks.created_at > ?', period.ago)
      .group('badge_types.id', 'badge_types.name')
      .order('COUNT(*) DESC')
      .limit(limit)
      .count
  end

  def self.anonymous_click_percentage(period: 30.days)
    total_clicks = where('created_at > ?', period.ago).count
    return 0 if total_clicks.zero?
    
    anonymous_count = anonymous_clicks.where('created_at > ?', period.ago).count
    (anonymous_count.to_f / total_clicks * 100).round(2)
  end

  # Instance methods
  def anonymous?
    clicker_user.nil?
  end

  def authenticated?
    !anonymous?
  end

  def clicker_identifier
    clicker_user&.email || ip_address || 'Unknown'
  end

  def context_description
    case click_context
    when 'profile'
      'Clicked on user profile page'
    when 'search_results'
      'Clicked in search results'
    when 'modal'
      'Clicked to open badge details modal'
    when 'directory'
      'Clicked in talent directory'
    when 'recommendation'
      'Clicked in recommendation section'
    else
      'Clicked in other context'
    end
  end

  private

  def has_clicker_identification
    if clicker_user.nil? && session.nil? && ip_address.blank?
      errors.add(:base, 'Must have either clicker_user, session, or ip_address for tracking')
    end
  end
end
