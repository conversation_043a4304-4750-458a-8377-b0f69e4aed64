# == Schema Information
#
# Table name: talent_notes
#
#  id                  :bigint           not null, primary key
#  category            :integer          default("general")
#  content             :text
#  pinned              :boolean          default(FALSE)
#  created_at          :datetime         not null
#  updated_at          :datetime         not null
#  last_modified_by_id :bigint
#  organization_id     :bigint           not null
#  talent_profile_id   :bigint           not null
#  user_id             :bigint           not null
#
# Indexes
#
#  index_talent_notes_on_last_modified_by_id  (last_modified_by_id)
#  index_talent_notes_on_organization_id      (organization_id)
#  index_talent_notes_on_talent_profile_id    (talent_profile_id)
#  index_talent_notes_on_user_id              (user_id)
#
# Foreign Keys
#
#  fk_rails_...  (last_modified_by_id => users.id)
#  fk_rails_...  (organization_id => organizations.id)
#  fk_rails_...  (talent_profile_id => talent_profiles.id)
#  fk_rails_...  (user_id => users.id)
#
class TalentNote < ApplicationRecord
  belongs_to :talent_profile
  belongs_to :user
  belongs_to :organization
  belongs_to :last_modified_by, class_name: 'User', optional: true

  validates :content, presence: true

  # Categories for notes
  enum :category,
       {
         general: 0,
         interview_feedback: 1,
         follow_up: 2,
         skill_assessment: 3,
         reference_check: 4,
         hiring_decision: 5,
       },
       prefix: true

  # Scopes
  scope :visible_to_organization,
        ->(organization_id) { where(organization_id: organization_id) }
  scope :pinned, -> { where(pinned: true) }
  scope :ordered, -> { order(pinned: :desc, created_at: :desc) }

  # Search functionality
  searchkick word_middle: %i[content_text category], callbacks: :async

  def search_data
    {
      content_text:
        content.respond_to?(:to_plain_text) ? content.to_plain_text : content,
      category: category,
      talent_profile_id: talent_profile_id,
      organization_id: organization_id,
      created_at: created_at,
      pinned: pinned,
    }
  end
end
