# == Schema Information
#
# Table name: csv_exports
#
#  id              :bigint           not null, primary key
#  completed_at    :datetime
#  controller_name :string           not null
#  error_message   :text
#  filters         :text
#  record_count    :integer
#  started_at      :datetime
#  status          :integer          default("queued"), not null
#  created_at      :datetime         not null
#  updated_at      :datetime         not null
#  admin_user_id   :bigint           not null
#  export_id       :string           not null
#
# Indexes
#
#  index_csv_exports_on_admin_user_id  (admin_user_id)
#  index_csv_exports_on_export_id      (export_id) UNIQUE
#
# Foreign Keys
#
#  fk_rails_...  (admin_user_id => users.id)
#
class CsvExport < ApplicationRecord
  belongs_to :admin_user, class_name: 'User'
  
  enum :status, {
    queued: 0,
    processing: 1,
    completed: 2,
    failed: 3
  }
  
  validates :export_id, presence: true, uniqueness: true
  validates :controller_name, presence: true
  validates :status, presence: true
  
  scope :recent, -> { order(created_at: :desc) }
  scope :for_user, ->(user) { where(admin_user: user) }
  
  def filename
    "#{controller_name}_#{created_at.strftime('%Y%m%d')}_#{export_id[0..7]}.csv"
  end
  
  def display_name
    controller_name.humanize.gsub('Admin ', '')
  end
  
  def progress_percentage
    case status
    when 'queued'
      0
    when 'processing'
      50
    when 'completed', 'failed'
      100
    else
      0
    end
  end
  
  def status_color
    case status
    when 'queued'
      'yellow'
    when 'processing'
      'blue'
    when 'completed'
      'green'
    when 'failed'
      'red'
    else
      'gray'
    end
  end
  
  def status_icon
    case status
    when 'queued'
      '⏳'
    when 'processing'
      '⚙️'
    when 'completed'
      '✅'
    when 'failed'
      '❌'
    else
      '❓'
    end
  end
end
