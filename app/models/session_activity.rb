# == Schema Information
#
# Table name: session_activities
#
#  id            :bigint           not null, primary key
#  action        :string
#  activity_type :string           not null
#  controller    :string
#  ip_address    :string
#  metadata      :json
#  request_path  :string
#  user_agent    :string
#  created_at    :datetime         not null
#  updated_at    :datetime         not null
#  session_id    :bigint           not null
#
# Indexes
#
#  index_session_activities_on_activity_type              (activity_type)
#  index_session_activities_on_created_at                 (created_at)
#  index_session_activities_on_ip_address                 (ip_address)
#  index_session_activities_on_session_id                 (session_id)
#  index_session_activities_on_session_id_and_created_at  (session_id,created_at)
#
# Foreign Keys
#
#  fk_rails_...  (session_id => sessions.id)
#
class SessionActivity < ApplicationRecord
  belongs_to :session

  validates :activity_type, presence: true
  validates :session_id, presence: true

  # Activity types for tracking different kinds of session activities
  ACTIVITY_TYPES = %w[
    login
    logout
    page_view
    admin_action
    api_request
    security_event
    timeout
    concurrent_login
  ].freeze

  validates :activity_type, inclusion: { in: ACTIVITY_TYPES }

  scope :recent, -> { where('created_at > ?', 30.days.ago) }
  scope :by_type, ->(type) { where(activity_type: type) }
  scope :by_session, ->(session_id) { where(session_id: session_id) }
  scope :by_ip, ->(ip) { where(ip_address: ip) }
  scope :admin_activities, -> { where(activity_type: ['admin_action', 'security_event']) }
  scope :security_events, -> { where(activity_type: 'security_event') }

  delegate :user, to: :session, allow_nil: true

  # Class method to log session activity
  def self.log_activity(activity_type:, session: nil, controller: nil, action: nil, metadata: {})
    session ||= Current.session
    return unless session

    create!(
      session: session,
      activity_type: activity_type,
      controller: controller,
      action: action,
      ip_address: Current.ip_address,
      user_agent: Current.user_agent,
      request_path: metadata[:request_path],
      metadata: metadata
    )
  rescue => e
    Rails.logger.error "Failed to log session activity: #{e.message}"
    # Don't raise the error to avoid breaking the main action
  end

  # Instance methods
  def user_name
    user&.full_name || 'Unknown User'
  end

  def user_email
    user&.email || '<EMAIL>'
  end

  def description
    case activity_type
    when 'login'
      "User logged in from #{ip_address}"
    when 'logout'
      "User logged out"
    when 'page_view'
      "Viewed #{controller}##{action}"
    when 'admin_action'
      "Performed admin action: #{controller}##{action}"
    when 'api_request'
      "Made API request to #{request_path}"
    when 'security_event'
      metadata['description'] || 'Security event occurred'
    when 'timeout'
      "Session timed out"
    when 'concurrent_login'
      "Concurrent login detected from #{ip_address}"
    else
      "#{activity_type.humanize} activity"
    end
  end

  def suspicious?
    activity_type == 'security_event' ||
    (activity_type == 'concurrent_login') ||
    (metadata.present? && metadata['suspicious'] == true)
  end
end
