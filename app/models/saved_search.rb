# == Schema Information
#
# Table name: saved_searches
#
#  id            :bigint           not null, primary key
#  name          :string(100)      not null
#  resource_type :string(50)       not null
#  search_params :text             not null
#  created_at    :datetime         not null
#  updated_at    :datetime         not null
#  user_id       :bigint           not null
#
# Indexes
#
#  index_saved_searches_on_resource_type                       (resource_type)
#  index_saved_searches_on_user_id                             (user_id)
#  index_saved_searches_on_user_id_and_name_and_resource_type  (user_id,name,resource_type) UNIQUE
#  index_saved_searches_on_user_id_and_resource_type           (user_id,resource_type)
#
# Foreign Keys
#
#  fk_rails_...  (user_id => users.id)
#
class SavedSearch < ApplicationRecord
  belongs_to :user

  validates :name, presence: true, length: { maximum: 100 }
  validates :resource_type, presence: true
  validates :search_params, presence: true
  validates :name, uniqueness: { scope: [:user_id, :resource_type] }

  VALID_RESOURCE_TYPES = %w[
    AdminUser
    AdminJob
    AdminChatRequest
    AdminConversation
    AdminMessage
    AdminJobApplication
    AdminTalentProfile
    AdminOrganization
    AdminAuditLog
  ].freeze

  validates :resource_type, inclusion: { in: VALID_RESOURCE_TYPES }

  scope :for_resource, ->(resource_type) { where(resource_type: resource_type) }
  scope :recent, -> { order(updated_at: :desc) }

  def parsed_params
    JSON.parse(search_params)
  rescue JSON::ParserError
    {}
  end

  def description
    params = parsed_params
    description_parts = []

    # Add basic search term
    if params['search'].present?
      description_parts << "Search: '#{params['search']}'"
    end

    if params['text_search'].present?
      description_parts << "Text: '#{params['text_search']}'"
    end

    # Add filters
    filter_descriptions = []
    
    params.each do |key, value|
      next if value.blank? || key.in?(%w[search text_search sort_by sort_direction advanced_search])
      
      case key
      when /^(.+)_from$/
        field = $1.humanize
        filter_descriptions << "#{field} from #{value}"
      when /^(.+)_to$/
        field = $1.humanize
        filter_descriptions << "#{field} to #{value}"
      when /^(.+)_range$/
        field = $1.humanize
        filter_descriptions << "#{field} range: #{value.humanize}"
      when /^(.+)_min$/
        field = $1.humanize
        filter_descriptions << "#{field} min: #{value}"
      when /^(.+)_max$/
        field = $1.humanize
        filter_descriptions << "#{field} max: #{value}"
      else
        filter_descriptions << "#{key.humanize}: #{value.humanize}"
      end
    end

    description_parts.concat(filter_descriptions)

    # Add sorting
    if params['sort_by'].present?
      sort_desc = "Sort by #{params['sort_by'].humanize}"
      sort_desc += " (#{params['sort_direction']})" if params['sort_direction'].present?
      description_parts << sort_desc
    end

    description_parts.any? ? description_parts.join(', ') : 'No search criteria'
  end

  def apply_to_params(current_params = {})
    parsed_params.merge(current_params.stringify_keys)
  end

  def resource_display_name
    resource_type.gsub('Admin', '').pluralize
  end
end
