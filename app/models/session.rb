# == Schema Information
#
# Table name: sessions
#
#  id                      :bigint           not null, primary key
#  ip_address              :string
#  last_activity_at        :datetime
#  locked_until            :datetime
#  login_attempts          :integer          default(0), not null
#  security_warnings_count :integer          default(0), not null
#  user_agent              :string
#  created_at              :datetime         not null
#  updated_at              :datetime         not null
#  user_id                 :bigint           not null
#
# Indexes
#
#  index_sessions_on_last_activity_at  (last_activity_at)
#  index_sessions_on_locked_until      (locked_until)
#  index_sessions_on_user_id           (user_id)
#
# Foreign Keys
#
#  fk_rails_...  (user_id => users.id)
#
class Session < ApplicationRecord
  belongs_to :user
  has_many :session_activities, dependent: :destroy

  before_create do
    self.user_agent = Current.user_agent
    self.ip_address = Current.ip_address
    self.last_activity_at = Time.current
  end

  scope :active, -> { where('last_activity_at > ?', 30.minutes.ago) }
  scope :inactive, -> { where('last_activity_at <= ?', 30.minutes.ago) }
  scope :locked, -> { where('locked_until > ?', Time.current) }
  scope :unlocked, -> { where('locked_until IS NULL OR locked_until <= ?', Time.current) }
  scope :admin_sessions, -> { joins(:user).where(users: { id: User.joins(:roles).distinct }) }

  # Update activity timestamp
  def touch_activity!
    update_column(:last_activity_at, Time.current)
  end

  # Check if session is active (within timeout period)
  def active?
    last_activity_at && last_activity_at > 30.minutes.ago
  end

  # Check if session is locked
  def locked?
    locked_until && locked_until > Time.current
  end

  # Lock the session for a specified duration
  def lock!(duration = 1.hour)
    update!(locked_until: Time.current + duration)
  end

  # Unlock the session
  def unlock!
    update!(locked_until: nil, login_attempts: 0)
  end

  # Increment security warnings
  def increment_security_warnings!
    increment!(:security_warnings_count)
  end

  # Increment login attempts
  def increment_login_attempts!
    increment!(:login_attempts)

    # Lock session after too many failed attempts
    if login_attempts >= 5
      lock!(1.hour)
      SecurityAlert.create_alert(
        user: user,
        alert_type: 'brute_force_attempt',
        severity: 'high',
        description: "Session locked due to #{login_attempts} failed login attempts",
        metadata: {
          ip_address: ip_address,
          user_agent: user_agent,
          session_id: id
        }
      )
    end
  end

  # Reset login attempts on successful login
  def reset_login_attempts!
    update!(login_attempts: 0)
  end

  # Check for suspicious activity
  def check_for_suspicious_activity!
    # Check for new IP address
    if ip_address_changed? && persisted?
      SecurityAlert.create_alert(
        user: user,
        alert_type: 'new_ip_address',
        severity: 'medium',
        description: "Login from new IP address: #{ip_address}",
        metadata: {
          old_ip: ip_address_was,
          new_ip: ip_address,
          user_agent: user_agent
        }
      )
    end

    # Check for concurrent sessions
    concurrent_sessions = user.sessions.active.where.not(id: id).count
    if concurrent_sessions > 0
      SecurityAlert.create_alert(
        user: user,
        alert_type: 'concurrent_sessions',
        severity: 'medium',
        description: "#{concurrent_sessions + 1} concurrent active sessions detected",
        metadata: {
          session_count: concurrent_sessions + 1,
          ip_address: ip_address,
          user_agent: user_agent
        }
      )
    end
  end

  # Get session location (simplified - in production you'd use a GeoIP service)
  def location
    return 'Unknown' unless ip_address

    case ip_address
    when /^127\./, /^192\.168\./, /^10\./, /^172\.(1[6-9]|2[0-9]|3[01])\./
      'Local Network'
    else
      'External'
    end
  end

  # Get device type from user agent
  def device_type
    return 'Unknown' unless user_agent

    case user_agent.downcase
    when /mobile|android|iphone|ipad/
      'Mobile'
    when /tablet/
      'Tablet'
    else
      'Desktop'
    end
  end

  # Get browser from user agent
  def browser
    return 'Unknown' unless user_agent

    case user_agent.downcase
    when /chrome/
      'Chrome'
    when /firefox/
      'Firefox'
    when /safari/
      'Safari'
    when /edge/
      'Edge'
    else
      'Other'
    end
  end
end
