# == Schema Information
#
# Table name: messages
#
#  id              :bigint           not null, primary key
#  body            :text
#  read_at         :datetime
#  created_at      :datetime         not null
#  updated_at      :datetime         not null
#  conversation_id :bigint           not null
#  user_id         :bigint           not null
#
# Indexes
#
#  index_messages_on_conversation_id                 (conversation_id)
#  index_messages_on_conversation_id_and_created_at  (conversation_id,created_at)
#  index_messages_on_created_at                      (created_at)
#  index_messages_on_read_at                         (read_at)
#  index_messages_on_user_id                         (user_id)
#
# Foreign Keys
#
#  fk_rails_...  (conversation_id => conversations.id)
#  fk_rails_...  (user_id => users.id)
#
class Message < ApplicationRecord
  belongs_to :conversation, counter_cache: true, touch: true # Add touch: true here
  belongs_to :user

  validates :body, presence: true

  scope :unread, -> { where(read_at: nil) }
end
