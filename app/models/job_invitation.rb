# == Schema Information
#
# Table name: job_invitations
#
#  id                :bigint           not null, primary key
#  accepted_at       :datetime
#  ignored_at        :datetime
#  invitation_letter :text
#  invited_at        :datetime
#  status            :integer          default("pending")
#  created_at        :datetime         not null
#  updated_at        :datetime         not null
#  job_id            :bigint           not null
#  talent_profile_id :bigint           not null
#  user_id           :bigint           not null
#
# Indexes
#
#  index_job_invitations_on_job_id             (job_id)
#  index_job_invitations_on_talent_profile_id  (talent_profile_id)
#  index_job_invitations_on_user_id            (user_id)
#
# Foreign Keys
#
#  fk_rails_...  (job_id => jobs.id)
#  fk_rails_...  (talent_profile_id => talent_profiles.id)
#  fk_rails_...  (user_id => users.id)
#
class JobInvitation < ApplicationRecord
  belongs_to :job
  belongs_to :user
  belongs_to :talent_profile
  has_one :job_application

  enum :status, { pending: 0, accepted: 1, ignored: 2 }

  scope :pending, -> { where(status: :pending) }
  scope :accepted, -> { where(status: :accepted) }
  scope :ignored, -> { where(status: :ignored) }

  validates :job_id,
            uniqueness: {
              scope: :talent_profile_id,
              message: 'Invitation already exists for this job and talent',
            }

  before_create :set_invited_at

  def accept!
    transaction do
      update!(status: :accepted, accepted_at: Time.current)
      unless job_application.present?
        create_job_application!(
          job: job,
          user: user,
          job_invitation: self,
          invited: true,
          status: :submitted,
        )
      end
    end
  end

  def ignore!
    update!(status: :ignored, ignored_at: Time.current)
  end

  private

  def set_invited_at
    self.invited_at ||= Time.current
  end
end
