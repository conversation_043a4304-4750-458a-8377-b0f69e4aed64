# == Schema Information
#
# Table name: saved_jobs
#
#  id         :bigint           not null, primary key
#  created_at :datetime         not null
#  updated_at :datetime         not null
#  job_id     :bigint           not null
#  user_id    :bigint           not null
#
# Indexes
#
#  index_saved_jobs_on_job_id   (job_id)
#  index_saved_jobs_on_user_id  (user_id)
#
# Foreign Keys
#
#  fk_rails_...  (job_id => jobs.id)
#  fk_rails_...  (user_id => users.id)
#
class SavedJob < ApplicationRecord
  belongs_to :user
  belongs_to :job
end
