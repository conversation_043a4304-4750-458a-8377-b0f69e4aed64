# == Schema Information
#
# Table name: security_alerts
#
#  id             :bigint           not null, primary key
#  alert_type     :string           not null
#  description    :text             not null
#  metadata       :json
#  resolved_at    :datetime
#  severity       :string           not null
#  created_at     :datetime         not null
#  updated_at     :datetime         not null
#  resolved_by_id :bigint
#  user_id        :bigint           not null
#
# Indexes
#
#  index_security_alerts_on_alert_type              (alert_type)
#  index_security_alerts_on_created_at              (created_at)
#  index_security_alerts_on_resolved_at             (resolved_at)
#  index_security_alerts_on_resolved_by_id          (resolved_by_id)
#  index_security_alerts_on_severity                (severity)
#  index_security_alerts_on_user_id                 (user_id)
#  index_security_alerts_on_user_id_and_created_at  (user_id,created_at)
#
# Foreign Keys
#
#  fk_rails_...  (resolved_by_id => users.id)
#  fk_rails_...  (user_id => users.id)
#
class SecurityAlert < ApplicationRecord
  belongs_to :user
  belongs_to :resolved_by, class_name: "User", optional: true

  validates :alert_type, presence: true
  validates :severity, presence: true
  validates :description, presence: true

  # Alert types for different security events
  ALERT_TYPES = %w[
    suspicious_login
    new_ip_address
    new_device
    concurrent_sessions
    failed_login_attempts
    admin_access_attempt
    unusual_activity
    session_hijacking
    brute_force_attempt
    account_lockout
  ].freeze

  # Severity levels
  SEVERITIES = %w[
    low
    medium
    high
    critical
  ].freeze

  validates :alert_type, inclusion: { in: ALERT_TYPES }
  validates :severity, inclusion: { in: SEVERITIES }

  scope :unresolved, -> { where(resolved_at: nil) }
  scope :resolved, -> { where.not(resolved_at: nil) }
  scope :recent, -> { where('created_at > ?', 30.days.ago) }
  scope :by_severity, ->(severity) { where(severity: severity) }
  scope :by_type, ->(type) { where(alert_type: type) }
  scope :critical, -> { where(severity: 'critical') }
  scope :high_priority, -> { where(severity: ['high', 'critical']) }

  # Class method to create security alerts
  def self.create_alert(user:, alert_type:, severity:, description:, metadata: {})
    create!(
      user: user,
      alert_type: alert_type,
      severity: severity,
      description: description,
      metadata: metadata
    )
  rescue => e
    Rails.logger.error "Failed to create security alert: #{e.message}"
    # Don't raise the error to avoid breaking the main action
  end

  # Instance methods
  def resolved?
    resolved_at.present?
  end

  def resolve!(resolved_by_user)
    update!(
      resolved_at: Time.current,
      resolved_by: resolved_by_user
    )
  end

  def severity_color
    case severity
    when 'low'
      'text-green-600'
    when 'medium'
      'text-yellow-600'
    when 'high'
      'text-orange-600'
    when 'critical'
      'text-red-600'
    else
      'text-gray-600'
    end
  end

  def severity_badge_color
    case severity
    when 'low'
      'bg-green-100 text-green-800'
    when 'medium'
      'bg-yellow-100 text-yellow-800'
    when 'high'
      'bg-orange-100 text-orange-800'
    when 'critical'
      'bg-red-100 text-red-800'
    else
      'bg-gray-100 text-gray-800'
    end
  end

  def user_name
    user&.full_name || 'Unknown User'
  end

  def user_email
    user&.email || '<EMAIL>'
  end

  def resolver_name
    resolved_by&.full_name || 'System'
  end

  def time_to_resolution
    return nil unless resolved?
    resolved_at - created_at
  end

  def formatted_metadata
    return {} unless metadata.present?
    metadata.transform_keys(&:humanize)
  end
end
