# == Schema Information
#
# Table name: impersonation_logs
#
#  id         :bigint           not null, primary key
#  ended_at   :datetime
#  ip_address :string
#  started_at :datetime         not null
#  user_agent :string
#  created_at :datetime         not null
#  updated_at :datetime         not null
#  admin_id   :bigint           not null
#  user_id    :bigint           not null
#
# Indexes
#
#  index_impersonation_logs_on_admin_id  (admin_id)
#  index_impersonation_logs_on_user_id   (user_id)
#
# Foreign Keys
#
#  fk_rails_...  (admin_id => users.id)
#  fk_rails_...  (user_id => users.id)
#
class ImpersonationLog < ApplicationRecord
  belongs_to :admin, class_name: 'User'
  belongs_to :user

  validates :started_at, presence: true
  validates :admin_id, presence: true
  validates :user_id, presence: true
  validates :ip_address, presence: true
  validates :user_agent, presence: true

  # Prevent admin from impersonating themselves
  validate :admin_cannot_impersonate_self
  validate :cannot_impersonate_super_admin

  scope :active, -> { where(ended_at: nil) }
  scope :ended, -> { where.not(ended_at: nil) }
  scope :completed, -> { where.not(ended_at: nil) }
  scope :recent, -> { where('started_at > ?', 7.days.ago) }
  scope :long_sessions, -> { where('ended_at - started_at > ?', 1.hour) }

  def active?
    ended_at.nil?
  end

  def duration
    return nil unless ended_at
    ended_at - started_at
  end

  def end_impersonation!
    update!(ended_at: Time.current)
  end

  private

  def admin_cannot_impersonate_self
    errors.add(:user, 'cannot impersonate yourself') if admin_id == user_id
  end

  def cannot_impersonate_super_admin
    errors.add(:user, 'cannot impersonate super admins') if user&.superadmin?
  end
end
