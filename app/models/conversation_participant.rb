# == Schema Information
#
# Table name: conversation_participants
#
#  id              :bigint           not null, primary key
#  archived        :boolean
#  bookmarked      :boolean
#  created_at      :datetime         not null
#  updated_at      :datetime         not null
#  conversation_id :bigint           not null
#  user_id         :bigint           not null
#
# Indexes
#
#  index_conversation_participants_on_conversation_id  (conversation_id)
#  index_conversation_participants_on_user_id          (user_id)
#
# Foreign Keys
#
#  fk_rails_...  (conversation_id => conversations.id)
#  fk_rails_...  (user_id => users.id)
#
class ConversationParticipant < ApplicationRecord
  belongs_to :conversation
  belongs_to :user, counter_cache: :conversations_count

  scope :active, -> { where(archived: [false, nil]) }
  scope :archived, -> { where(archived: true) }

  def archive!
    update(archived: true)
  end

  def unarchive!
    update(archived: false)
  end

  def bookmark!
    update(bookmarked: true)
  end

  def unbookmark!
    update(bookmarked: false)
  end
end
