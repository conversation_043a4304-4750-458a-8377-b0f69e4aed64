# frozen_string_literal: true

module Pay
  module Webhooks
    module Stripe
      # Handles the invoice.payment_succeeded event from Stripe.
      class InvoicePaymentSucceeded
        def call(event)
          # The Pay gem might have already processed the charge/subscription update.
          # This handler is for custom application logic *after* that.

          pay_charge = Pay::Charge.find_by(processor: :stripe, processor_id: event.data.object.charge)
          pay_subscription = pay_charge&.subscription
          user = pay_charge&.customer&.owner # Assuming owner is the User model

          Rails.logger.info "Pay::Webhooks::Stripe::InvoicePaymentSucceeded: Handling event for Charge #{pay_charge&.id}, Subscription #{pay_subscription&.id}, User #{user&.id}"

          # Grant premium access if the subscription paid for is the premium one
          if user && pay_subscription && pay_subscription.processor_plan == 'price_1R9Q66DYYVPVcCCrnqiXNafF' # Talent Premium Subscription Price ID
            Rails.logger.info "Pay::Webhooks::Stripe::InvoicePaymentSucceeded: Granting premium access to User ##{user.id} for plan #{pay_subscription.processor_plan}"
            user.talent_profile&.update(is_premium: true)
          elsif user && pay_subscription
            Rails.logger.info "Pay::Webhooks::Stripe::InvoicePaymentSucceeded: Invoice paid for non-premium plan #{pay_subscription.processor_plan} by User ##{user.id}"
            # Optional: Ensure premium is false if they somehow paid for a non-premium plan while premium
            # user.talent_profile&.update(is_premium: false) if pay_subscription.processor_plan == 'price_1R9Q55DYYVPVcCCrWQOwsKmT' # Basic Plan ID
          else
            Rails.logger.warn "Pay::Webhooks::Stripe::InvoicePaymentSucceeded: Could not find User or Pay::Subscription for event."
          end

        rescue StandardError => e
          Rails.logger.error "Pay::Webhooks::Stripe::InvoicePaymentSucceeded Error: #{e.message}\n#{e.backtrace.join("\n")}"
          # Optionally notify an error service
        end
      end
    end
  end
end
