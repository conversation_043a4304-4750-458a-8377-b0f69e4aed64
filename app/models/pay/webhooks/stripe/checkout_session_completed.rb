# frozen_string_literal: true

module Pay
  module Webhooks
    module Stripe
      # Handles the checkout.session.completed event from <PERSON><PERSON>, specifically for job payments.
      class CheckoutSessionCompleted
        def call(event)
          checkout_session = event.data.object
          Rails.logger.info "Processing checkout.session.completed: #{checkout_session.id}, Payment Status: #{checkout_session.payment_status}"

          # Ensure payment was successful. Handle async payments via checkout.session.async_payment_succeeded if needed,
          # although Pay might handle syncing the charge status automatically.
          # We only proceed here if the immediate status is 'paid'.
          return unless checkout_session.payment_status == 'paid'

          # Retrieve metadata
          metadata = checkout_session.metadata
          job_id_from_meta = metadata&.job_id
          payment_type = metadata&.type # 'standard_listing' or 'premium_listing'

          unless job_id_from_meta
            Rails.logger.warn "Webhook checkout.session.completed #{checkout_session.id}: Missing job_id in metadata."
            return
          end

          # Convert job_id to integer
          job_id = job_id_from_meta.to_i
          if job_id == 0 # Handle cases where to_i might return 0 for non-numeric strings
            Rails.logger.error "Webhook checkout.session.completed #{checkout_session.id}: Invalid job_id format in metadata: '#{job_id_from_meta}'."
            Rails.logger.warn "Webhook checkout.session.completed #{checkout_session.id}: Missing job_id in metadata."
            return
          end

          Rails.logger.info "Webhook checkout.session.completed #{checkout_session.id}: Attempting to find Job with ID: #{job_id} (Integer)" # Log the ID before finding

          # Find the job using the integer ID
          job = ::Job.find_by(id: job_id)
          unless job
            Rails.logger.error "Webhook checkout.session.completed #{checkout_session.id}: Job not found with ID #{job_id} (Integer)."
            return
          end

          # Update the job
          now = Time.current
          is_premium_upgrade = (payment_type&.strip == 'premium_listing') # Check premium status early
          Rails.logger.info "Webhook checkout.session.completed #{checkout_session.id}: Raw payment_type from metadata: '#{payment_type}' (Class: #{payment_type.class})"
          Rails.logger.info "Webhook checkout.session.completed #{checkout_session.id}: Comparison result for premium ('#{payment_type&.strip}' == 'premium_listing'): #{is_premium_upgrade}."

          if job.published?
            Rails.logger.info "Webhook checkout.session.completed #{checkout_session.id}: Job #{job_id} is already published. Processing potential premium upgrade."
            if is_premium_upgrade && !job.is_premium # Only update if it's an upgrade and not already premium
              # Try using update_column to bypass callbacks/validations for debugging
              update_result = job.update_column(:is_premium, true)
              # Reload to confirm the change in the database immediately after update_column
              reloaded_job = ::Job.find_by(id: job_id)
              Rails.logger.info "Webhook checkout.session.completed #{checkout_session.id}: Attempted update_column(:is_premium, true). Result: #{update_result}. Current DB value for is_premium: #{reloaded_job&.is_premium}"
            else
              Rails.logger.info "Webhook checkout.session.completed #{checkout_session.id}: No premium status change needed. is_premium_upgrade=#{is_premium_upgrade}, job.is_premium=#{job.is_premium}."
            end
          else
            # This is the initial publish
            job.status = :published
            job.published_at = now
            job.expires_at = now + 30.days # Or use a configurable duration
            job.is_premium = is_premium_upgrade # Set premium status for initial publish too

            Rails.logger.info "Webhook checkout.session.completed #{checkout_session.id}: Job #{job_id} attributes before initial save: status=#{job.status}, published_at=#{job.published_at}, expires_at=#{job.expires_at}, is_premium=#{job.is_premium}"
            save_result = job.save
            if save_result
              Rails.logger.info "Webhook checkout.session.completed #{checkout_session.id}: Successfully saved initial publish Job #{job_id}. Premium: #{job.is_premium}. Save returned: #{save_result}"
            else
              Rails.logger.error "Webhook checkout.session.completed #{checkout_session.id}: Failed to save initial publish Job #{job_id}. Errors: #{job.errors.full_messages.join(', ')}"
            end
          end

        rescue ActiveRecord::RecordNotFound => e
          Rails.logger.error "Webhook checkout.session.completed Error: Job not found. Event ID: #{event.id}, Job ID from metadata: #{job_id}. Error: #{e.message}"
        rescue StandardError => e
          Rails.logger.error "Webhook checkout.session.completed Error processing event #{event.id}: #{e.message}\n#{e.backtrace.join("\n")}"
          # Optional: Re-raise or notify monitoring service
        end
      end
    end
  end
end
