# frozen_string_literal: true

module Pay
  module Webhooks
    module Stripe
      # Handles the customer.subscription.deleted event from Stripe.
      class CustomerSubscriptionDeleted
        def call(event)
          # Pay gem likely updates the Pay::Subscription record status already.
          # This handler adds custom logic based on the deletion.

          # Find the subscription record using the ID from the event
          pay_subscription = Pay::Subscription.find_by(processor: :stripe, processor_id: event.data.object.id)
          user = pay_subscription&.customer&.owner # Assuming owner is the User model

          Rails.logger.info "Pay::Webhooks::Stripe::CustomerSubscriptionDeleted: Handling event for Subscription #{pay_subscription&.id}, User #{user&.id}"

          # Revoke premium access if the deleted subscription was the premium one
          # Check the plan ID stored in the event data object as the pay_subscription record might be deleted or status updated already
          deleted_plan_id = event.data.object.items.data.first&.price&.id

          if user && deleted_plan_id == 'price_1R9Q66DYYVPVcCCrnqiXNafF' # Talent Premium Subscription Price ID
            Rails.logger.info "Pay::Webhooks::Stripe::CustomerSubscriptionDeleted: Revoking premium access for User ##{user.id} due to subscription deletion."
            user.talent_profile&.update(is_premium: false)
          elsif user
            Rails.logger.info "Pay::Webhooks::Stripe::CustomerSubscriptionDeleted: Non-premium subscription deleted for User ##{user.id}."
          else
            Rails.logger.warn "Pay::Webhooks::Stripe::CustomerSubscriptionDeleted: Could not find User for event."
          end

        rescue StandardError => e
          Rails.logger.error "Pay::Webhooks::Stripe::CustomerSubscriptionDeleted Error: #{e.message}\n#{e.backtrace.join("\n")}"
          # Optionally notify an error service
        end
      end
    end
  end
end
