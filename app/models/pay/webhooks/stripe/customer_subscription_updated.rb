# frozen_string_literal: true

module Pay
  module Webhooks
    module Stripe
      # Handles the customer.subscription.updated event from Stripe.
      class CustomerSubscriptionUpdated
        def call(event)
          # Pay gem likely updates the Pay::Subscription record status/plan already.
          # This handler adds custom logic based on those changes.

          pay_subscription = Pay::Subscription.find_by(processor: :stripe, processor_id: event.data.object.id)
          user = pay_subscription&.customer&.owner # Assuming owner is the User model

          Rails.logger.info "Pay::Webhooks::Stripe::CustomerSubscriptionUpdated: Handling event for Subscription #{pay_subscription&.id}, User #{user&.id}"

          if user && pay_subscription
            # Check the current plan and status from the event data or the updated pay_subscription record
            # Note: event.data.object might reflect the state *before* Pay gem updates its record.
            # Relying on pay_subscription might be safer if Pay updates synchronously. Let's use event data for now.
            current_plan_id = event.data.object.items.data.first&.price&.id
            current_status = event.data.object.status

            Rails.logger.info "Pay::Webhooks::Stripe::CustomerSubscriptionUpdated: User ##{user.id}. Current Plan: #{current_plan_id}, Current Status: #{current_status}"

            # Update premium status based on the current plan and status
            is_premium_plan = (current_plan_id == 'price_1R9Q66DYYVPVcCCrnqiXNafF') # Talent Premium Subscription Price ID
            is_active_status = %w[active trialing].include?(current_status) # Consider 'trialing' as active for premium access

            user.talent_profile&.update(is_premium: (is_premium_plan && is_active_status))
            Rails.logger.info "Pay::Webhooks::Stripe::CustomerSubscriptionUpdated: Updated User ##{user.id} premium status to #{(is_premium_plan && is_active_status)}"

          else
            Rails.logger.warn "Pay::Webhooks::Stripe::CustomerSubscriptionUpdated: Could not find User or Pay::Subscription for event."
          end

        rescue StandardError => e
          Rails.logger.error "Pay::Webhooks::Stripe::CustomerSubscriptionUpdated Error: #{e.message}\n#{e.backtrace.join("\n")}"
          # Optionally notify an error service
        end
      end
    end
  end
end
