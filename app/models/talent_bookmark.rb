# == Schema Information
#
# Table name: talent_bookmarks
#
#  id                :bigint           not null, primary key
#  created_at        :datetime         not null
#  updated_at        :datetime         not null
#  talent_profile_id :bigint           not null
#  user_id           :bigint           not null
#
# Indexes
#
#  index_talent_bookmarks_on_talent_profile_id              (talent_profile_id)
#  index_talent_bookmarks_on_user_id                        (user_id)
#  index_talent_bookmarks_on_user_id_and_talent_profile_id  (user_id,talent_profile_id) UNIQUE
#
# Foreign Keys
#
#  fk_rails_...  (talent_profile_id => talent_profiles.id)
#  fk_rails_...  (user_id => users.id)
#
class TalentBookmark < ApplicationRecord
  belongs_to :user
  belongs_to :talent_profile
  
  validates :user_id, uniqueness: { scope: :talent_profile_id }
end
