// Add these lines if they're not already included
@import "trix/dist/trix";
@import "actiontext";

// Add some custom styling for Trix editor in your application
.trix-content {
  .attachment-gallery {
    > action-text-attachment,
    > .attachment {
      flex: 1 0 33%;
      padding: 0 0.5em;
      max-width: 33%;
    }

    &.attachment-gallery--2,
    &.attachment-gallery--4 {
      > action-text-attachment,
      > .attachment {
        flex-basis: 50%;
        max-width: 50%;
      }
    }
  }

  action-text-attachment {
    .attachment {
      padding: 0 !important;
      max-width: 100% !important;
    }
  }
}

// Make Trix editor smaller for notes
trix-editor {
  min-height: 5rem;
  font-size: 0.75rem;
  
  &.trix-content {
    font-size: 0.75rem;
  }
}