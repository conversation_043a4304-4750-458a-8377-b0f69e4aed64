/* Admin Interface Custom Styles */
@layer components {
  /* Admin Color Palette - Consistent with main app stone theme */
  :root {
    --admin-primary: #0f172a;      /* stone-900 */
    --admin-primary-light: #1e293b; /* stone-800 */
    --admin-secondary: #475569;     /* stone-600 */
    --admin-accent: #3b82f6;        /* blue-500 */
    --admin-accent-light: #60a5fa;  /* blue-400 */
    --admin-success: #10b981;       /* emerald-500 */
    --admin-warning: #f59e0b;       /* amber-500 */
    --admin-error: #ef4444;         /* red-500 */
    --admin-surface: #ffffff;       /* white */
    --admin-surface-secondary: #f8fafc; /* stone-50 */
    --admin-border: #e2e8f0;        /* stone-200 */
    --admin-text: #1e293b;          /* stone-800 */
    --admin-text-secondary: #64748b; /* stone-500 */
  }

  /* Admin Button Styles */
  .admin-btn {
    @apply inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-offset-2;
  }

  .admin-btn-primary {
    @apply admin-btn bg-stone-900 text-white hover:bg-stone-800 focus:ring-stone-500 shadow-sm;
  }

  .admin-btn-secondary {
    @apply admin-btn bg-white text-stone-700 border border-stone-300 hover:bg-stone-50 focus:ring-stone-500 shadow-sm;
  }

  .admin-btn-accent {
    @apply admin-btn bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500 shadow-sm;
  }

  .admin-btn-success {
    @apply admin-btn bg-emerald-600 text-white hover:bg-emerald-700 focus:ring-emerald-500 shadow-sm;
  }

  .admin-btn-warning {
    @apply admin-btn bg-amber-600 text-white hover:bg-amber-700 focus:ring-amber-500 shadow-sm;
  }

  .admin-btn-danger {
    @apply admin-btn bg-red-600 text-white hover:bg-red-700 focus:ring-red-500 shadow-sm;
  }

  .admin-btn-sm {
    @apply px-3 py-1.5 text-xs;
  }

  .admin-btn-lg {
    @apply px-6 py-3 text-base;
  }

  /* Admin Card Styles */
  .admin-card {
    @apply bg-white rounded-lg shadow-sm border border-stone-200 overflow-hidden;
  }

  .admin-card-header {
    @apply px-6 py-4 border-b border-stone-200 bg-stone-50;
  }

  .admin-card-body {
    @apply p-6;
  }

  .admin-card-footer {
    @apply px-6 py-4 border-t border-stone-200 bg-stone-50;
  }

  /* Admin Form Styles */
  .admin-form-group {
    @apply mb-6;
  }

  .admin-form-label {
    @apply block text-sm font-medium text-stone-700 mb-2;
  }

  .admin-form-input {
    @apply block w-full px-3 py-2 border border-stone-300 rounded-lg shadow-sm placeholder-stone-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200;
  }

  .admin-form-select {
    @apply admin-form-input pr-10 bg-white;
  }

  .admin-form-textarea {
    @apply admin-form-input resize-vertical min-h-[100px];
  }

  .admin-form-error {
    @apply mt-1 text-sm text-red-600;
  }

  .admin-form-help {
    @apply mt-1 text-sm text-stone-500;
  }

  /* Admin Table Styles */
  .admin-table {
    @apply min-w-full divide-y divide-stone-200 table-fixed;
  }

  .admin-table-header {
    @apply bg-stone-50;
  }

  .admin-table-header-cell {
    @apply px-4 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider;
  }

  .admin-table-body {
    @apply bg-white divide-y divide-stone-200;
  }

  .admin-table-row {
    @apply hover:bg-stone-50 transition-colors duration-150;
  }

  .admin-table-cell {
    @apply px-4 py-3 text-sm text-stone-900;
  }

  .admin-table-cell-secondary {
    @apply admin-table-cell text-stone-500;
  }

  /* Specific column width classes for better table layout */
  .admin-table-cell-id {
    @apply admin-table-cell w-16;
  }

  .admin-table-cell-title {
    @apply admin-table-cell w-64;
  }

  .admin-table-cell-category {
    @apply admin-table-cell w-32;
  }

  .admin-table-cell-status {
    @apply admin-table-cell w-24;
  }

  .admin-table-cell-budget {
    @apply admin-table-cell w-32;
  }

  .admin-table-cell-applications {
    @apply admin-table-cell w-28;
  }

  .admin-table-cell-premium {
    @apply admin-table-cell w-24;
  }

  .admin-table-cell-date {
    @apply admin-table-cell w-32;
  }

  .admin-table-cell-actions {
    @apply admin-table-cell w-32 text-right;
  }

  /* Text overflow handling for table cells */
  .admin-table-cell-truncate {
    @apply truncate;
  }

  .admin-table-cell-wrap {
    @apply break-words;
  }

  /* Admin Badge Styles - Matching Scout Area Design Patterns */
  .admin-badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }

  .admin-badge-primary {
    @apply admin-badge bg-stone-100 text-stone-800;
  }

  .admin-badge-success {
    @apply admin-badge bg-green-100 text-green-800;
  }

  .admin-badge-warning {
    @apply admin-badge bg-yellow-100 text-yellow-800;
  }

  .admin-badge-danger {
    @apply admin-badge bg-red-100 text-red-800;
  }

  .admin-badge-info {
    @apply admin-badge bg-blue-100 text-blue-800;
  }

  .admin-badge-premium {
    @apply admin-badge bg-purple-100 text-purple-800;
  }

  /* Additional Scout-style badge variants for availability status */
  .admin-badge-available {
    @apply admin-badge bg-green-100 text-green-700;
  }

  .admin-badge-limited {
    @apply admin-badge bg-yellow-100 text-yellow-700;
  }

  .admin-badge-unavailable {
    @apply admin-badge bg-red-100 text-red-700;
  }

  /* Admin Sidebar Enhancements */
  .admin-sidebar {
    @apply w-64 bg-white shadow-sm border-r border-stone-200 min-h-screen transition-all duration-300 ease-in-out;
  }

  .admin-sidebar-collapsed {
    @apply w-16;
  }

  .admin-sidebar-link {
    @apply flex items-center space-x-3 px-3 py-2 text-sm font-medium rounded-lg transition-all duration-200 ease-in-out group;
  }

  .admin-sidebar-link-active {
    @apply bg-stone-100 text-stone-900 shadow-sm;
  }

  .admin-sidebar-link-inactive {
    @apply text-stone-600 hover:bg-stone-50 hover:text-stone-900;
  }

  .admin-sidebar-icon {
    @apply w-5 h-5 transition-colors duration-200;
  }

  .admin-sidebar-text {
    @apply transition-opacity duration-200;
  }

  .admin-sidebar-collapsed .admin-sidebar-text {
    @apply opacity-0 pointer-events-none;
  }

  /* Admin Stats Cards */
  .admin-stat-card {
    @apply admin-card;
  }

  .admin-stat-icon {
    @apply w-8 h-8 rounded-lg flex items-center justify-center text-white;
  }

  .admin-stat-icon-blue {
    @apply admin-stat-icon bg-blue-600;
  }

  .admin-stat-icon-emerald {
    @apply admin-stat-icon bg-emerald-600;
  }

  .admin-stat-icon-purple {
    @apply admin-stat-icon bg-purple-600;
  }

  .admin-stat-icon-amber {
    @apply admin-stat-icon bg-amber-600;
  }

  .admin-stat-label {
    @apply text-sm font-medium text-stone-500;
  }

  .admin-stat-value {
    @apply text-lg font-medium text-stone-900;
  }

  .admin-stat-icon-emerald {
    @apply admin-stat-icon bg-emerald-600;
  }

  .admin-stat-icon-purple {
    @apply admin-stat-icon bg-purple-600;
  }

  .admin-stat-icon-amber {
    @apply admin-stat-icon bg-amber-600;
  }

  .admin-stat-value {
    @apply text-2xl font-bold text-stone-900;
  }

  .admin-stat-label {
    @apply text-sm font-medium text-stone-500;
  }

  .admin-stat-change {
    @apply text-xs font-medium;
  }

  .admin-stat-change-positive {
    @apply admin-stat-change text-emerald-600;
  }

  .admin-stat-change-negative {
    @apply admin-stat-change text-red-600;
  }

  /* Admin Loading States */
  .admin-skeleton {
    @apply animate-pulse bg-stone-200 rounded;
  }

  .admin-skeleton-text {
    @apply admin-skeleton h-4;
  }

  .admin-skeleton-title {
    @apply admin-skeleton h-6;
  }

  .admin-skeleton-avatar {
    @apply admin-skeleton w-10 h-10 rounded-full;
  }

  /* Admin Focus States */
  .admin-focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2;
  }

  /* Admin Responsive Utilities */
  @media (max-width: 768px) {
    .admin-sidebar {
      @apply fixed inset-y-0 left-0 z-50 transform -translate-x-full;
    }

    .admin-sidebar-open {
      @apply translate-x-0;
    }

    .admin-main-content {
      @apply ml-0;
    }
  }

  /* Admin Animation Utilities */
  .admin-fade-in {
    @apply opacity-0 animate-[fadeIn_0.3s_ease-in-out_forwards];
  }

  @keyframes fadeIn {
    to {
      opacity: 1;
    }
  }

  .admin-slide-up {
    @apply transform translate-y-4 opacity-0 animate-[slideUp_0.3s_ease-out_forwards];
  }

  @keyframes slideUp {
    to {
      transform: translateY(0);
      opacity: 1;
    }
  }

  /* Admin Notification Styles */
  .admin-notification {
    @apply fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg border max-w-sm;
  }

  .admin-notification-success {
    @apply admin-notification bg-emerald-50 text-emerald-800 border-emerald-200;
  }

  .admin-notification-error {
    @apply admin-notification bg-red-50 text-red-800 border-red-200;
  }

  .admin-notification-warning {
    @apply admin-notification bg-amber-50 text-amber-800 border-amber-200;
  }

  .admin-notification-info {
    @apply admin-notification bg-blue-50 text-blue-800 border-blue-200;
  }
}
