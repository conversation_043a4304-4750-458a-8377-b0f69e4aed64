@tailwind base;
@tailwind components;

@tailwind utilities;

@layer components {
  /* ARTIFACT UI HOLOGRAPHIC BADGE EFFECTS */
  .badge,
  .badge-compact {
    position: relative !important;
    overflow: hidden !important;
    border-radius: 0.75rem; /* rounded-xl */
    transition: all 0.2s ease;
    transform-style: preserve-3d;
    will-change: transform;
    isolation: isolate;
  }

  /* Holographic gradient overlay - Primary layer */
  .badge:before,
  .badge-compact:before {
    content: "";
    position: absolute;
    inset: 0;
    z-index: 10;
    background: var(--holographic-gradient, linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent));
    pointer-events: none;
    opacity: 0.15;
    mix-blend-mode: overlay;
    border-radius: inherit;
  }

  /* Mouse spotlight effect - Secondary layer */
  .badge:after,
  .badge-compact:after {
    content: "";
    position: absolute;
    inset: 0;
    z-index: 20;
    background: radial-gradient(
      circle at var(--mouse-x, 50%) var(--mouse-y, 50%),
      rgba(255, 255, 255, 0.15) 0%,
      transparent 80%
    );
    pointer-events: none;
    border-radius: inherit;
  }

  /* Scanline effect for retro look */
  .badge.scanlines:before,
  .badge-compact.scanlines:before {
    background-image:
      var(--holographic-gradient, linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent)),
      repeating-linear-gradient(
        0deg,
        transparent 0%,
        rgba(255, 255, 255, 0.05) 0.5px,
        transparent 1px
      );
    mix-blend-mode: overlay;
  }

  /* Refraction pattern overlay */
  .badge.refraction:after,
  .badge-compact.refraction:after {
    background-image:
      radial-gradient(
        circle at var(--mouse-x, 50%) var(--mouse-y, 50%),
        rgba(255, 255, 255, 0.15) 0%,
        transparent 80%
      ),
      var(--refraction-pattern, none);
    opacity: 0.3;
  }

  /* Enhanced glow effect */
  .badge {
    box-shadow:
      0 10px 30px -10px var(--shadow-color, rgba(0, 0, 0, 0.5)),
      0 0 calc(var(--glow-intensity, 0.8) * 20px) calc(var(--glow-intensity, 0.8) * 5px) rgba(255, 255, 255, 0.1);
  }

  .badge-compact {
    box-shadow:
      0 5px 15px -5px var(--shadow-color, rgba(0, 0, 0, 0.5)),
      0 0 calc(var(--glow-intensity, 0.8) * 10px) calc(var(--glow-intensity, 0.8) * 2px) rgba(255, 255, 255, 0.1);
  }

  /* Hover state enhancements */
  .badge:hover:before,
  .badge-compact:hover:before {
    opacity: 0.25;
  }

  .badge:hover:after,
  .badge-compact:hover:after {
    background: radial-gradient(
      circle at var(--mouse-x, 50%) var(--mouse-y, 50%),
      rgba(255, 255, 255, 0.25) 0%,
      transparent 70%
    );
  }

  /* Depth effect for 3D appearance */
  .badge.depth-effect,
  .badge-compact.depth-effect {
    transform-style: preserve-3d;
  }

  .badge.depth-effect:hover,
  .badge-compact.depth-effect:hover {
    transform: translateZ(50px);
  }

  /* Glitch effect styles */
  .badge.glitch-effect,
  .badge-compact.glitch-effect {
    position: relative;
  }

  .badge.glitch-effect:before,
  .badge-compact.glitch-effect:before {
    animation: glitch-1 0.3s infinite linear alternate-reverse;
  }

  .badge.glitch-effect:after,
  .badge-compact.glitch-effect:after {
    animation: glitch-2 0.3s infinite linear alternate-reverse;
  }

  /* Glitch animations */
  @keyframes glitch-1 {
    0% { clip-path: inset(40% 0 61% 0); }
    20% { clip-path: inset(92% 0 1% 0); }
    40% { clip-path: inset(43% 0 1% 0); }
    60% { clip-path: inset(25% 0 58% 0); }
    80% { clip-path: inset(54% 0 7% 0); }
    100% { clip-path: inset(58% 0 43% 0); }
  }

  @keyframes glitch-2 {
    0% { clip-path: inset(25% 0 58% 0); }
    20% { clip-path: inset(54% 0 7% 0); }
    40% { clip-path: inset(58% 0 43% 0); }
    60% { clip-path: inset(40% 0 61% 0); }
    80% { clip-path: inset(92% 0 1% 0); }
    100% { clip-path: inset(43% 0 1% 0); }
  }

  /* Prismatic rainbow effect */
  .badge.prismatic,
  .badge-compact.prismatic {
    background: linear-gradient(45deg,
      rgba(255, 0, 0, 0.1) 0%,
      rgba(255, 165, 0, 0.1) 16.66%,
      rgba(255, 255, 0, 0.1) 33.33%,
      rgba(0, 255, 0, 0.1) 50%,
      rgba(0, 0, 255, 0.1) 66.66%,
      rgba(75, 0, 130, 0.1) 83.33%,
      rgba(238, 130, 238, 0.1) 100%
    );
    background-size: 200% 200%;
    animation: prismatic-shift 3s ease-in-out infinite;
  }

  @keyframes prismatic-shift {
    0%, 100% { background-position: 0% 0%; }
    25% { background-position: 100% 0%; }
    50% { background-position: 100% 100%; }
    75% { background-position: 0% 100%; }
  }

  .scrollbar-gutter-stable {
    scrollbar-gutter: stable;
  }

  .message-sent {
    @apply text-right;
  }

  .message-received {
    @apply text-left;
  }

  .message-sent .message-bubble {
    @apply text-stone-100 bg-stone-900;
  }

  .message-received .message-bubble {
    @apply text-stone-900 bg-stone-100;
  }

  .messages-container {
    @apply p-4 overflow-y-auto h-96;
    scroll-behavior: smooth;
  }

  /* Sidebar styles */
  .sidebar-collapsed {
    @apply w-16 !important;
  }

  .sidebar-expanded {
    @apply items-center;
    @apply w-64 !important;
  }

  /* Hide text when sidebar is collapsed */
  .sidebar-collapsed .sidebar-text {
    @apply hidden;
  }
  
  /* Ensure the user avatar is visible in collapsed view */
  .sidebar-collapsed .border-t {
    @apply border-stone-200;
  }
  
  /* Keep avatar visible and centered in collapsed view */
  .sidebar-collapsed img.rounded-full {
    @apply mx-auto;
  }
  
  /* Adjust footer padding in collapsed view */
  .sidebar-collapsed .p-4.mt-auto {
    @apply px-2 py-4;
  }
  
  /* Style the user avatar button in collapsed mode */
  .sidebar-collapsed .user-avatar-button > div {
    @apply mx-auto;
  }
  
  .sidebar-collapsed .user-avatar-button svg {
    @apply hidden;
  }
  
  /* Dropdown menu positioning */
  .dropdown-menu {
    min-width: 220px;
    position: fixed !important;
    bottom: 70px;
    left: 70px;
    transform-origin: bottom left;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  }
  
  /* Adjust dropdown position in collapsed view */
  .sidebar-collapsed .dropdown-menu {
    left: 60px;
  }

  /* Reset padding for collapsed nav */
  .sidebar-collapsed nav {
    @apply px-2; /* Adjust padding slightly for centered icons */
  }

  /* Style the dedicated sidebar toggle button */
  .sidebar-toggle-btn {
    @apply border border-stone-200;
  }
  
  .sidebar-collapsed .sidebar-toggle-btn {
    @apply p-2;
  }

  /* Style nav links when collapsed */
  .sidebar-collapsed nav a {
    @apply flex p-2 mb-2 border rounded-md border-stone-200; /* Add padding and visible border */
  }

  .sidebar-collapsed nav a:hover {
    @apply bg-stone-200 border-stone-100; /* Add background and border on hover */
  }

  /* Adjust main content margin when sidebar is collapsed/expanded */
  .sidebar-collapsed-main {
    @apply ml-16 !important;
  }

  .sidebar-expanded-main {
    @apply ml-64 !important;
  }

  /* Responsive adjustments */
  @media (max-width: 1023px) {
    .sidebar-expanded-main {
      @apply ml-0 !important;
    }
  }
}
