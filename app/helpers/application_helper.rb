module ApplicationHelper
  include Pagy::Frontend
  def status_color_class(status)
    case status.to_s
    when "applied"
      "bg-yellow-100 text-yellow-800 border-yellow-800"
    when "reviewed"  # Changed from review to reviewed
      "bg-blue-100 text-blue-800 border-blue-800"
    when "qualified"
      "bg-purple-100 text-purple-800 border-purple-800"
    when "offered"
      "bg-orange-100 text-orange-800 border-orange-800"
    when "accepted"
      "bg-emerald-100 text-emerald-800 border-emerald-800"
    else
      "bg-stone-100 text-stone-800"
    end
  end
  
  def time_difference_text(timezone)
    return "" unless timezone.present?
    
    # Get the current user's timezone or use UTC as default
    current_timezone = Current.user&.time_zone || "UTC"
    
    # Calculate offset in hours between the two timezones
    user_offset = ActiveSupport::TimeZone[current_timezone].utc_offset / 3600
    other_offset = ActiveSupport::TimeZone[timezone].utc_offset / 3600
    difference = other_offset - user_offset
    
    case difference
    when 0
      "Same timezone"
    when 1
      "1 hour ahead"
    when -1
      "1 hour behind"
    when difference > 0
      "#{difference} hours ahead"
    else
      "#{difference.abs} hours behind"
    end
  end
end
