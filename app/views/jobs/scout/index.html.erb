<div class="flex min-h-screen bg-stone-50">
  <!-- Sidebar -->
  <aside class="flex flex-col p-8 w-80">
    <div class="mb-12 text-2xl font-semibold letter-spacing-tight">
      Settings <span class="text-purple-400">.</span>
    </div>
  </aside>
  <!-- Main Content -->
  <main class="flex-1 p-2">
    <div class="flex-1 px-8 mx-auto bg-white border border-stone-200 rounded shadow-sm">
      <header class="w-full px-8 py-8 bg-white">
        <div class="relative pb-5 border-b border-stone-200 sm:pb-0">
          <div class="md:flex md:items-center md:justify-between">
            <h3 class="text-2xl font-semibold leading-6 text-stone-900">New Job Listing</h3>
            <div class="flex mt-3 md:absolute md:right-0 md:top-3 md:mt-0">
              <button type="button" class="inline-flex items-center px-3 py-2 text-sm font-semibold text-stone-900 bg-white shadow-sm rounded-m ring-1 ring-inset ring-stone-500 hover:bg-stone-50">Back</button>
              <button type="button" class="inline-flex items-center px-3 py-2 ml-3 text-sm font-semibold text-white bg-stone-900 rounded-sm shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600">Save</button>
            </div>
          </div>
          <div class="mt-4">
            <!-- Dropdown menu on small screens -->
            <div class="sm:hidden">
              <label for="current-tab" class="sr-only">Select a tab</label>
              <select id="current-tab" name="current-tab" class="block w-full rounded-md border-0 py-1.5 pl-3 pr-10 ring-1 ring-inset ring-stone-300 focus:ring-2 focus:ring-inset focus:ring-indigo-600">
                <option>Overview</option>
                <option selected>Job Details</option>
                <option >Application Question</option>
              </select>
            </div>
            <!-- Tabs at small breakpoint and up -->
            <div class="hidden sm:block">
              <nav class="flex -mb-px space-x-8">
                <!-- Current: "border-indigo-500 text-indigo-600", Default: "border-transparent text-stone-500 hover:border-stone-300 hover:text-stone-700" -->
                <a href="#" class="px-1 pb-4 text-sm font-medium text-stone-500 border-b-2 border-transparent whitespace-nowrap hover:border-stone-300 hover:text-stone-700">Edit listing</a>
                <a href="#" class="px-1 pb-4 text-sm font-medium text-stone-500 border-b-2 border-transparent whitespace-nowrap hover:border-stone-300 hover:text-stone-700">Review</a>
              </nav>
            </div>
          </div>
        </div>
        <div class="max-w-[1103px] mx-auto">
        </div>
      </header>
      <div class="px-6 py-6">
        <div class="space-y-8 ">
          <form class="max-w-xl">
            <div class="space-y-12">
              <fieldset>
                <legend class="text-sm font-semibold leading-6 text-stone-900">What do you need ghostwriting for?</legend>
                <div class="grid grid-cols-1 mt-6 gap-y-6 sm:grid-cols-3 sm:gap-x-4">
                  <!-- Active: "border-indigo-600 ring-2 ring-indigo-600", Not Active: "border-stone-300" -->
                  <label aria-label="Newsletter" aria-description="Last message sent an hour ago to 621 users" class="relative flex p-4 bg-white border rounded-lg shadow-sm cursor-pointer focus:outline-none">
                    <input type="radio" name="project-type" value="Newsletter" class="sr-only">
                    <span class="flex flex-1">
                      <span class="flex flex-col">
                        <span class="block text-sm font-medium text-stone-900">Social Media</span>
                      </span>
                    </span>
                    <!-- Not Checked: "invisible" -->
                    <svg class="hidden w-5 h-5 text-indigo-600" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                      <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z" clip-rule="evenodd" />
                    </svg>
                    <!--
        Active: "border", Not Active: "border-2"
        Checked: "border-indigo-600", Not Checked: "border-transparent"
      -->
                    <span class="absolute border-2 rounded-lg pointer-events-none -inset-px" aria-hidden="true"></span>
                  </label>
                  <!-- Active: "border-indigo-600 ring-2 ring-indigo-600", Not Active: "border-stone-300" -->
                  <label aria-label="Existing Customers" aria-description="Last message sent 2 weeks ago to 1200 users" class="relative flex p-4 bg-white border rounded-lg shadow-sm cursor-pointer focus:outline-none">
                    <input type="radio" name="project-type" value="Existing Customers" class="sr-only">
                    <span class="flex flex-1">
                      <span class="flex flex-col">
                        <span class="block text-sm font-medium text-stone-900">Email Course </span>
                      </span>
                    </span>
                    <!-- Not Checked: "invisible" -->
                    <svg class="w-5 h-5 text-purple-700" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                      <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z" clip-rule="evenodd" />
                    </svg>
                    <!--
        Active: "border", Not Active: "border-2"
        Checked: "border-indigo-600", Not Checked: "border-transparent"
      -->
                    <span class="absolute border-2 rounded-lg pointer-events-none -inset-px" aria-hidden="true"></span>
                  </label>
                  <!-- Active: "border-indigo-600 ring-2 ring-indigo-600", Not Active: "border-stone-300" -->
                  <label aria-label="Trial Users" aria-description="Last message sent 4 days ago to 2740 users" class="relative flex p-4 bg-white border rounded-lg shadow-sm cursor-pointer focus:outline-none">
                    <input type="radio" name="project-type" value="Trial Users" class="sr-only">
                    <span class="flex flex-1">
                      <span class="flex flex-col">
                        <span class="block text-sm font-medium text-stone-900">Newsletter</span>
                      </span>
                    </span>
                    <!-- Not Checked: "invisible" -->
                    <svg class="hidden w-5 h-5 text-indigo-600" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                      <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z" clip-rule="evenodd" />
                    </svg>
                    <!--
        Active: "border", Not Active: "border-2"
        Checked: "border-indigo-600", Not Checked: "border-transparent"
      -->
                    <span class="absolute border-2 rounded-lg pointer-events-none -inset-px" aria-hidden="true"></span>
                  </label>
                </div>
              </fieldset>
              <div class="pb-12 border-b border-stone-900/10">
                <h2 class="font-semibold text-stone-900 text-base/7">Job Details</h2>
                <p class="mt-1 text-stone-600 text-sm/6">This information will be displayed publicly so be careful what you share.</p>
                <div class="grid grid-cols-1 mt-10 gap-x-6 gap-y-8 sm:grid-cols-6">
                  <div class="sm:col-span-4">
                    <label for="email" class="block font-medium text-stone-900 text-sm/6">Title*</label>
                    <div class="mt-2">
                      <input id="email" name="email" type="email" autocomplete="email" class="block w-full rounded-md bg-white px-3 py-1.5 text-base text-stone-900 outline outline-1 -outline-offset-1 outline-stone-300 placeholder:text-stone-400 focus:outline focus:outline-2 focus:-outline-offset-2 focus:outline-indigo-600 sm:text-sm/6">
                    </div>
                  </div>
                  <div class="col-span-full">
                    <label for="about" class="block font-medium text-stone-900 text-sm/6">Description</label>
                    <div class="mt-2">
                      <textarea name="about" id="about" rows="3" class="block w-full rounded-md bg-white px-3 py-1.5 text-base text-stone-900 outline outline-1 -outline-offset-1 outline-stone-300 placeholder:text-stone-400 focus:outline focus:outline-2 focus:-outline-offset-2 focus:outline-indigo-600 sm:text-sm/6"></textarea>
                    </div>
                    <p class="mt-3 text-stone-600 text-sm/6">Write a few sentences about yourself.</p>
                  </div>
                  <div class="col-span-full">
                    <fieldset>
                      <p class="mt-1 text-sm font-medium leading-6 text-stone-900">How do you prefer to receive notifications?</p>
                      <div class="mt-6 space-y-6 sm:flex sm:items-center sm:space-x-10 sm:space-y-0">
                        <div class="flex items-center">
                          <input id="email" name="notification-method" type="radio" checked class="w-4 h-4 text-indigo-600 border-stone-300 focus:ring-indigo-600">
                          <label for="email" class="block ml-3 text-sm font-medium leading-6 text-stone-700">Remote</label>
                        </div>
                        <div class="flex items-center">
                          <input id="sms" name="notification-method" type="radio" class="w-4 h-4 text-indigo-600 border-stone-300 focus:ring-indigo-600">
                          <label for="sms" class="block ml-3 text-sm font-medium leading-6 text-stone-700">In-person</label>
                        </div>
                      </div>
                    </fieldset>
                  </div>
                </div>
              </div>
              <div class="pb-12 border-b border-stone-900/10">
                <h2 class="font-semibold text-stone-900 text-base/7">Content Information</h2>
                <p class="mt-1 text-stone-600 text-sm/6">This information will helps us know what you want.</p>
                <div class="grid grid-cols-1 mt-10 gap-x-6 gap-y-8 sm:grid-cols-6">
                  <div class="sm:col-span-4">
                    <label for="country" class="block font-medium text-stone-900 text-sm/6">
                      What is the #1 outcome you're looking for?
                    </label>
                    <div class="grid grid-cols-1 mt-2">
                      <select id="country" name="country" autocomplete="country-name" class="col-start-1 row-start-1 w-full appearance-none rounded-md bg-white py-1.5 pl-3 pr-8 text-base text-stone-900 outline outline-1 -outline-offset-1 outline-stone-300 focus:outline focus:outline-2 focus:-outline-offset-2 focus:outline-indigo-600 sm:text-sm/6">
                        <option>Followers</option>
                        <option>Leads</option>
                        <option>High-value connections and opportunies</option>
                        <option>Product sales</option>
                        <option>Newsletter subscribers</option>
                      </select>
                    </div>
                  </div>
                  <div class="sm:col-span-5">
                    <label for="country" class="block font-medium text-stone-900 text-sm/6">
                      What is the main social media platform you want to grow on?
                    </label>
                    <div class="grid grid-cols-1 mt-2 ">
                      <select id="country" name="country" autocomplete="country-name" class="col-start-1 row-start-1 w-full appearance-none rounded-md bg-white py-1.5 pl-3 pr-8 text-base text-stone-900 outline outline-1 -outline-offset-1 outline-stone-300 focus:outline focus:outline-2 focus:-outline-offset-2 focus:outline-indigo-600 sm:text-sm/6">
                        <option>Twitter (X)</option>
                        <option>LinkedIn</option>
                        <option>Instagram</option>
                        <option>Threads</option>
                        <option>Other</option>
                      </select>
                    </div>
                  </div>
                  <div class="sm:col-span-5">
                    <label for="country" class="block font-medium text-stone-900 text-sm/6">
                      What are the top 1-5 content topics you want ghostwriting for?
                    </label>
                    <div class="grid grid-cols-1 mt-2 ">
                      <%= turbo_mount("MultiSelect") %>
                    </div>
                  </div>
                </div>
              </div>
              <div class="pb-12 border-b border-stone-900/10">
                <h2 class="font-semibold text-stone-900 text-base/7">Budget Preferences</h2>
                <p class="mt-1 text-stone-600 text-sm/6">Use a permanent address where you can receive mail.</p>
                <div class="grid grid-cols-1 mt-10 gap-x-6 gap-y-8 sm:grid-cols-6">
                  <div class="sm:col-span-3">
                    <label for="price" class="block text-sm font-medium leading-6 text-stone-900">Min (USD)</label>
                    <div class="relative mt-2 rounded-sm shadow-sm">
                      <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                        <span class="text-stone-500 sm:text-sm">$</span>
                      </div>
                      <input type="text" name="price" id="price" class="block w-full rounded-md border-0 py-1.5 pl-7 pr-20 text-stone-900 ring-1 ring-inset ring-stone-300 placeholder:text-stone-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6" placeholder="0.00">
                    </div>
                  </div>
                  <div class="sm:col-span-3">
                    <label for="price" class="block text-sm font-medium leading-6 text-stone-900">Max (USD)</label>
                    <div class="relative mt-2 rounded-sm shadow-sm">
                      <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                        <span class="text-stone-500 sm:text-sm">$</span>
                      </div>
                      <input type="text" name="price" id="price" class="block w-full rounded-md border-0 py-1.5 pl-7 pr-20 text-stone-900 ring-1 ring-inset ring-stone-300 placeholder:text-stone-400 focus:ring-1 focus:ring-inset focus:ring-purple-700 sm:text-sm sm:leading-6" placeholder="0.00">
                    </div>
                  </div>
                  <div class="sm:col-span-6">
                    <fieldset>
                      <p class="mt-1 text-sm font-medium leading-6 text-stone-900">How do you prefer to receive notifications?</p>
                      <div class="mt-6 space-y-6 sm:flex sm:items-center sm:space-x-10 sm:space-y-0">
                        <div class="flex items-center">
                          <input id="email" name="notification-method" type="radio" checked class="w-4 h-4 text-indigo-600 border-stone-300 focus:ring-indigo-600">
                          <label for="email" class="block ml-3 text-sm font-medium leading-6 text-stone-700">Monthly Retainer</label>
                        </div>
                        <div class="flex items-center">
                          <input id="sms" name="notification-method" type="radio" class="w-4 h-4 text-indigo-600 border-stone-300 focus:ring-indigo-600">
                          <label for="sms" class="block ml-3 text-sm font-medium leading-6 text-stone-700">Quarterly Retainer</label>
                        </div>
                        <div class="flex items-center">
                          <input id="sms" name="notification-method" type="radio" class="w-4 h-4 text-indigo-600 border-stone-300 focus:ring-indigo-600">
                          <label for="sms" class="block ml-3 text-sm font-medium leading-6 text-stone-700">Project-Based Pricing</label>
                        </div>
                      </div>
                    </fieldset>
                  </div>
                </div>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  </main>
</div>
