<% content_for :title, "Edit Profile" %>

<div class="container px-4 py-8 mx-auto">
  <%# Back Link %>
  <div class="mb-4"> <%# Add margin below the link %>
    <%= link_to :back, class: "text-sm text-stone-600 hover:text-stone-900 inline-flex items-center" do %>
      <%# Left arrow icon (simple unicode) %>
      <span class="mr-1">←</span>
      Back
    <% end %>
  </div>

  <h1 class="mb-6 text-2xl font-semibold text-stone-800">Edit Profile</h1>

  <%= render "shared/flash_messages" %>

  <%= form_with(model: @user, url: account_profile_path, method: :patch, class: "max-w-lg space-y-6") do |form| %>
    <%# Display validation errors %>
    <% if @user.errors.any? %>
      <div class="p-4 mb-4 text-red-800 bg-red-100 border border-red-200 rounded">
        <h2 class="text-lg font-semibold">
          <%= pluralize(@user.errors.count, "error") %> prohibited this profile from being saved:
        </h2>
        <ul class="mt-2 list-disc list-inside">
          <% @user.errors.full_messages.each do |message| %>
            <li><%= message %></li>
          <% end %>
        </ul>
      </div>
    <% end %>

    <%# Avatar %>
    <div>
      <%= form.label :avatar, class: "block text-sm font-medium text-stone-700 mb-1" %>
      <% if @user.avatar.attached? %>
         <div class="flex items-center space-x-4">
           <%= image_tag @user.avatar, class: "w-16 h-16 rounded-full object-cover" %> <%# Use direct attachment + object-cover %>
           <%= form.file_field :avatar, class: "block w-full text-sm text-stone-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-indigo-50 file:text-indigo-700 hover:file:bg-indigo-100" %>
         </div>
        <p class="mt-1 text-xs text-stone-500">Upload a new image to change your avatar.</p>
      <% else %>
        <%= form.file_field :avatar, class: "block w-full text-sm text-stone-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-indigo-50 file:text-indigo-700 hover:file:bg-indigo-100" %>
        <p class="mt-1 text-xs text-stone-500">Upload an image for your avatar.</p>
      <% end %>
    </div>

    <%# First Name %>
    <div>
      <%= form.label :first_name, class: "block text-sm font-medium text-stone-700 mb-1" %>
      <%= form.text_field :first_name, required: true, class: "w-full border border-stone-300 rounded px-3 py-2 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" %>
    </div>

    <%# Last Name %>
    <div>
      <%= form.label :last_name, class: "block text-sm font-medium text-stone-700 mb-1" %>
      <%= form.text_field :last_name, required: true, class: "w-full border border-stone-300 rounded px-3 py-2 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" %>
    </div>

    <%# Submit Button %>
    <div class="pt-4">
      <%= form.submit "Update Profile", class: "px-4 py-2 font-medium text-white bg-black rounded cursor-pointer hover:bg-stone-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black" %>
      <%= link_to "Cancel", launchpad_path, class: "ml-4 text-sm text-stone-600 hover:text-stone-900" %>
    </div>
  <% end %>
</div>
