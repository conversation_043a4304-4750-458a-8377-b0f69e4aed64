<% content_for :title, "Job: #{@job.title}" %>

<div class="max-w-7xl mx-auto">
  <!-- Header -->
  <div class="mb-8">
    <div class="flex items-center justify-between">
      <div>
        <nav class="flex" aria-label="Breadcrumb">
          <ol class="flex items-center space-x-4">
            <li>
              <%= link_to "Jobs", super_admin_admin_jobs_path, class: "text-stone-500 hover:text-stone-700" %>
            </li>
            <li>
              <div class="flex items-center">
                <svg class="flex-shrink-0 h-5 w-5 text-stone-400" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                </svg>
                <span class="ml-4 text-sm font-medium text-stone-900"><%= truncate(@job.title, length: 50) %></span>
              </div>
            </li>
          </ol>
        </nav>
        <h1 class="mt-2 text-3xl font-bold text-stone-900"><%= @job.title %></h1>
        <p class="mt-1 text-stone-600">
          <%= @job.organization.name %> • 
          <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <%= @job.status == 'published' ? 'bg-green-100 text-green-800' : @job.status == 'draft' ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800' %>">
            <%= @job.status.humanize %>
          </span>
        </p>
      </div>
      <div class="flex space-x-3">
        <%= link_to "Edit Job", edit_super_admin_admin_job_path(@job), 
            class: "inline-flex items-center px-4 py-2 border border-stone-300 rounded-md shadow-sm text-sm font-medium text-stone-700 bg-white hover:bg-stone-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500" %>
      </div>
    </div>
  </div>

  <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
    <!-- Main Content -->
    <div class="lg:col-span-2 space-y-8">
      <!-- Job Details -->
      <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-stone-200">
          <h2 class="text-lg font-medium text-stone-900">Job Details</h2>
        </div>
        <div class="px-6 py-4">
          <dl class="grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2">
            <div>
              <dt class="text-sm font-medium text-stone-500">Job ID</dt>
              <dd class="mt-1 text-sm text-stone-900"><%= @job.id %></dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-stone-500">Category</dt>
              <dd class="mt-1">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                  <%= @job.job_category&.humanize || "Not set" %>
                </span>
              </dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-stone-500">Budget Range</dt>
              <dd class="mt-1 text-sm text-stone-900"><%= @job.budget_range&.humanize&.gsub('_', ' ') || "Not set" %></dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-stone-500">Work Duration</dt>
              <dd class="mt-1 text-sm text-stone-900"><%= @job.work_duration&.humanize&.gsub('_', ' ') || "Not set" %></dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-stone-500">Platform</dt>
              <dd class="mt-1 text-sm text-stone-900"><%= @job.platform&.humanize&.gsub('_', ' ') || "Not set" %></dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-stone-500">Outcome</dt>
              <dd class="mt-1 text-sm text-stone-900"><%= @job.outcome&.humanize&.gsub('_', ' ') || "Not set" %></dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-stone-500">Payment Frequency</dt>
              <dd class="mt-1 text-sm text-stone-900"><%= @job.payment_frequency&.humanize&.gsub('_', ' ') || "Not set" %></dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-stone-500">Involvement Level</dt>
              <dd class="mt-1 text-sm text-stone-900"><%= @job.involvement_level&.humanize&.gsub('_', ' ') || "Not set" %></dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-stone-500">Application Deadline</dt>
              <dd class="mt-1 text-sm text-stone-900">
                <% if @job.application_deadline %>
                  <%= @job.application_deadline.strftime('%B %d, %Y at %I:%M %p') %>
                  <% if @job.application_deadline < Time.current %>
                    <span class="text-red-600">(Expired)</span>
                  <% end %>
                <% else %>
                  No deadline set
                <% end %>
              </dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-stone-500">Premium Job</dt>
              <dd class="mt-1">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <%= @job.is_premium? ? 'bg-purple-100 text-purple-800' : 'bg-stone-100 text-stone-800' %>">
                  <%= @job.is_premium? ? 'Premium' : 'Regular' %>
                </span>
              </dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-stone-500">Created</dt>
              <dd class="mt-1 text-sm text-stone-900"><%= @job.created_at.strftime('%B %d, %Y at %I:%M %p') %></dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-stone-500">Published</dt>
              <dd class="mt-1 text-sm text-stone-900">
                <%= @job.published_at&.strftime('%B %d, %Y at %I:%M %p') || "Not published" %>
              </dd>
            </div>
          </dl>
        </div>
      </div>

      <!-- Description -->
      <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-stone-200">
          <h2 class="text-lg font-medium text-stone-900">Description</h2>
        </div>
        <div class="px-6 py-4">
          <% if @job.description.present? %>
            <div class="prose max-w-none text-stone-900">
              <%= simple_format(@job.description) %>
            </div>
          <% else %>
            <p class="text-stone-500">No description provided</p>
          <% end %>
        </div>
      </div>

      <!-- Category-Specific Attributes -->
      <% if @job.social_media? %>
        <div class="bg-white shadow rounded-lg">
          <div class="px-6 py-4 border-b border-stone-200">
            <h2 class="text-lg font-medium text-stone-900">Social Media Specific</h2>
          </div>
          <div class="px-6 py-4">
            <dl class="grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2">
              <div>
                <dt class="text-sm font-medium text-stone-500">Goal Type</dt>
                <dd class="mt-1 text-sm text-stone-900"><%= @job.social_media_goal_type&.humanize&.gsub('_', ' ') || "Not set" %></dd>
              </div>
              <div>
                <dt class="text-sm font-medium text-stone-500">Risk Acknowledged</dt>
                <dd class="mt-1">
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <%= @job.social_media_understands_risk_acknowledged? ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' %>">
                    <%= @job.social_media_understands_risk_acknowledged? ? 'Yes' : 'No' %>
                  </span>
                </dd>
              </div>
            </dl>
          </div>
        </div>
      <% end %>

      <% if @job.newsletter? %>
        <div class="bg-white shadow rounded-lg">
          <div class="px-6 py-4 border-b border-stone-200">
            <h2 class="text-lg font-medium text-stone-900">Newsletter Specific</h2>
          </div>
          <div class="px-6 py-4">
            <dl class="grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2">
              <div>
                <dt class="text-sm font-medium text-stone-500">Frequency</dt>
                <dd class="mt-1 text-sm text-stone-900"><%= @job.newsletter_frequency&.humanize&.gsub('_', ' ') || "Not set" %></dd>
              </div>
              <div>
                <dt class="text-sm font-medium text-stone-500">Length</dt>
                <dd class="mt-1 text-sm text-stone-900"><%= @job.newsletter_length&.humanize&.gsub('_', ' ') || "Not set" %></dd>
              </div>
            </dl>
          </div>
        </div>
      <% end %>

      <% if @job.lead_magnet? %>
        <div class="bg-white shadow rounded-lg">
          <div class="px-6 py-4 border-b border-stone-200">
            <h2 class="text-lg font-medium text-stone-900">Lead Magnet Specific</h2>
          </div>
          <div class="px-6 py-4">
            <dl class="grid grid-cols-1 gap-x-4 gap-y-6">
              <div>
                <dt class="text-sm font-medium text-stone-500">Type</dt>
                <dd class="mt-1 text-sm text-stone-900"><%= @job.lead_magnet_type&.humanize || "Not set" %></dd>
              </div>
            </dl>
          </div>
        </div>
      <% end %>

      <!-- Recent Applications -->
      <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-stone-200">
          <h2 class="text-lg font-medium text-stone-900">Recent Applications</h2>
        </div>
        <div class="overflow-hidden">
          <% if @applications.any? %>
            <table class="min-w-full divide-y divide-stone-200">
              <tbody class="bg-white divide-y divide-stone-200">
                <% @applications.each do |application| %>
                  <tr>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="flex items-center">
                        <div class="flex-shrink-0 h-8 w-8">
                          <% if application.user.avatar.attached? %>
                            <%= image_tag application.user.avatar, class: "h-8 w-8 rounded-full object-cover" %>
                          <% else %>
                            <div class="h-8 w-8 rounded-full bg-stone-300 flex items-center justify-center">
                              <span class="text-xs font-medium text-stone-700"><%= application.user.initials %></span>
                            </div>
                          <% end %>
                        </div>
                        <div class="ml-4">
                          <div class="text-sm font-medium text-stone-900"><%= application.user.name %></div>
                          <div class="text-sm text-stone-500"><%= application.user.email %></div>
                        </div>
                      </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <%= application.status == 'accepted' ? 'bg-green-100 text-green-800' : application.status == 'applied' ? 'bg-blue-100 text-blue-800' : 'bg-yellow-100 text-yellow-800' %>">
                        <%= application.status.humanize %>
                      </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-right text-sm text-stone-500">
                      <%= time_ago_in_words(application.created_at) %> ago
                    </td>
                  </tr>
                <% end %>
              </tbody>
            </table>
          <% else %>
            <div class="px-6 py-12 text-center text-stone-500">
              No applications yet.
            </div>
          <% end %>
        </div>
      </div>
    </div>

    <!-- Sidebar -->
    <div class="space-y-8">
      <!-- Quick Stats -->
      <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-stone-200">
          <h2 class="text-lg font-medium text-stone-900">Quick Stats</h2>
        </div>
        <div class="px-6 py-4">
          <dl class="space-y-4">
            <div>
              <dt class="text-sm font-medium text-stone-500">Total Applications</dt>
              <dd class="text-2xl font-semibold text-stone-900"><%= @stats[:applications_count] %></dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-stone-500">Total Invitations</dt>
              <dd class="text-2xl font-semibold text-stone-900"><%= @stats[:invitations_count] %></dd>
            </div>
          </dl>
        </div>
      </div>

      <!-- Application Status Breakdown -->
      <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-stone-200">
          <h2 class="text-lg font-medium text-stone-900">Applications by Status</h2>
        </div>
        <div class="px-6 py-4">
          <% if @stats[:applications_by_status].any? %>
            <dl class="space-y-3">
              <% @stats[:applications_by_status].each do |status, count| %>
                <div class="flex justify-between">
                  <dt class="text-sm text-stone-600"><%= status.humanize %></dt>
                  <dd class="text-sm font-medium text-stone-900"><%= count %></dd>
                </div>
              <% end %>
            </dl>
          <% else %>
            <p class="text-stone-500">No applications yet</p>
          <% end %>
        </div>
      </div>

      <!-- Organization Info -->
      <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-stone-200">
          <h2 class="text-lg font-medium text-stone-900">Organization</h2>
        </div>
        <div class="px-6 py-4">
          <div class="text-center">
            <h3 class="text-lg font-medium text-stone-900"><%= @job.organization.name %></h3>
            <p class="text-sm text-stone-500 mt-1">
              <%= link_to "View Organization", super_admin_admin_organization_path(@job.organization), 
                  class: "text-blue-600 hover:text-blue-900" %>
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
