<% content_for :title, "Jobs" %>

<div class="bg-white rounded-lg shadow">
  <div class="px-4 py-5 sm:p-6">
    <!-- Header Section -->
    <div class="sm:flex sm:items-center">
      <div class="sm:flex-auto">
        <h1 class="text-xl font-semibold text-stone-900">Jobs</h1>
        <p class="mt-2 text-sm text-stone-700">Manage job postings and applications</p>
      </div>
      <div class="mt-4 sm:mt-0 sm:ml-16 sm:flex-none">
        <%= link_to request.params.merge(format: :csv),
            class: "inline-flex items-center justify-center rounded-md border border-stone-300 px-4 py-2 text-sm font-medium text-stone-800 shadow-sm hover:bg-stone-100 focus:outline-none focus:ring-2 focus:ring-stone-500 focus:ring-offset-2" do %>
          <svg class="w-4 h-4 mr-2 -ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
          </svg>
          Export CSV
        <% end %>
      </div>
    </div>

    <!-- Search and Filters Section -->
    <div class="mt-8 pt-6 border-t border-stone-200">
      <%= form_with url: request.path, method: :get, local: true, class: "space-y-4" do |form| %>
        <!-- Search and Action Buttons Row -->
        <div class="flex items-end gap-4">
          <!-- Search Field (takes most space) -->
          <div class="flex-1">
            <%= form.label :search, "Search", class: "block text-sm font-medium text-stone-700" %>
            <%= form.text_field :search,
                value: params[:search],
                placeholder: "Search jobs by title or description...",
                class: "mt-1 block w-full rounded-md border-stone-300 shadow-sm focus:border-stone-500 focus:ring-stone-500 sm:text-sm" %>
          </div>

          <!-- Filter and Clear Buttons -->
          <div class="flex items-center space-x-4">
            <%= form.submit "Filter", class: "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-stone-800 hover:bg-stone-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-stone-500" %>
            <% if params[:search].present? || params[:status].present? || params[:job_category].present? || params[:platform].present? %>
              <%= link_to "Clear", super_admin_admin_jobs_path, class: "text-stone-600 hover:text-stone-900" %>
            <% end %>
          </div>
        </div>

        <!-- Filter Dropdowns Row -->
        <div class="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
          <!-- Status Filter -->
          <div>
            <%= form.label :status, "Status", class: "block text-sm font-medium text-stone-700" %>
            <%= form.select :status,
                options_for_select([['All', '']] + @filter_options[:status], params[:status]),
                {},
                { class: "mt-1 block w-full rounded-md border-stone-300 shadow-sm focus:border-stone-500 focus:ring-stone-500 sm:text-sm" } %>
          </div>

          <!-- Category Filter -->
          <div>
            <%= form.label :job_category, "Category", class: "block text-sm font-medium text-stone-700" %>
            <%= form.select :job_category,
                options_for_select([['All', '']] + @filter_options[:job_category], params[:job_category]),
                {},
                { class: "mt-1 block w-full rounded-md border-stone-300 shadow-sm focus:border-stone-500 focus:ring-stone-500 sm:text-sm" } %>
          </div>

          <!-- Platform Filter -->
          <div>
            <%= form.label :platform, "Platform", class: "block text-sm font-medium text-stone-700" %>
            <%= form.select :platform,
                options_for_select([['All', '']] + @filter_options[:platform], params[:platform]),
                {},
                { class: "mt-1 block w-full rounded-md border-stone-300 shadow-sm focus:border-stone-500 focus:ring-stone-500 sm:text-sm" } %>
          </div>

          <!-- Budget Range Filter -->
          <div>
            <%= form.label :budget_range, "Budget Range", class: "block text-sm font-medium text-stone-700" %>
            <%= form.select :budget_range,
                options_for_select([['All', '']] + @filter_options[:budget_range], params[:budget_range]),
                {},
                { class: "mt-1 block w-full rounded-md border-stone-300 shadow-sm focus:border-stone-500 focus:ring-stone-500 sm:text-sm" } %>
          </div>
        </div>
      <% end %>
    </div>

    <!-- Table Section -->
    <div class="mt-8 pt-6 border-t border-stone-200">
      <div class="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
        <table class="min-w-full divide-y divide-stone-300">
          <thead class="bg-stone-50">
            <tr>
              <th scope="col" class="px-6 py-3 text-xs font-medium tracking-wider text-left uppercase text-stone-500">
                ID
              </th>
              <th scope="col" class="px-6 py-3 text-xs font-medium tracking-wider text-left uppercase text-stone-500">
                Job
              </th>
              <th scope="col" class="px-6 py-3 text-xs font-medium tracking-wider text-left uppercase text-stone-500">
                Category
              </th>
              <th scope="col" class="px-6 py-3 text-xs font-medium tracking-wider text-left uppercase text-stone-500">
                Status
              </th>
              <th scope="col" class="px-6 py-3 text-xs font-medium tracking-wider text-left uppercase text-stone-500">
                Budget
              </th>
              <th scope="col" class="px-6 py-3 text-xs font-medium tracking-wider text-left uppercase text-stone-500">
                Applications
              </th>
              <th scope="col" class="px-6 py-3 text-xs font-medium tracking-wider text-left uppercase text-stone-500">
                Created
              </th>
              <th scope="col" class="relative px-6 py-3">
                <span class="sr-only">Actions</span>
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-stone-200">
            <% if @jobs.any? %>
              <% @jobs.each do |job| %>
                <tr class="hover:bg-stone-50">
                  <td class="px-6 py-4 text-sm text-stone-900 whitespace-nowrap">
                    <%= job.id %>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm font-medium text-stone-900">
                      <%= truncate(job.title, length: 40) %>
                    </div>
                    <div class="text-sm text-stone-500">
                      <%= truncate(job.organization.name, length: 30) %>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-stone-100 text-stone-800">
                      <%= job.job_category&.humanize || "Not set" %>
                    </span>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <% status_class = case job.status
                                     when 'published' then 'bg-green-100 text-green-800'
                                     when 'draft' then 'bg-yellow-100 text-yellow-800'
                                     when 'expired' then 'bg-red-100 text-red-800'
                                     else 'bg-stone-100 text-stone-800'
                                     end %>
                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full <%= status_class %>">
                      <%= job.status.humanize %>
                    </span>
                  </td>
                  <td class="px-6 py-4 text-sm text-stone-900 whitespace-nowrap">
                    <%= job.budget_range&.humanize&.gsub('_', ' ') || "Not set" %>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <% applications_count = job.job_applications.count %>
                    <div class="text-sm font-medium text-stone-900"><%= applications_count %></div>
                    <div class="text-xs text-stone-500">applications</div>
                  </td>
                  <td class="px-6 py-4 text-sm text-stone-900 whitespace-nowrap">
                    <div class="text-sm text-stone-900"><%= job.created_at.strftime('%b %d, %Y') %></div>
                    <div class="text-xs text-stone-500"><%= job.created_at.strftime('%I:%M %p') %></div>
                  </td>
                  <td class="relative px-6 py-4 text-sm font-medium text-right whitespace-nowrap">
                    <div class="flex items-center justify-end space-x-2">
                      <%= link_to "View", super_admin_admin_job_path(job),
                          class: "inline-flex items-center px-3 py-1.5 text-xs font-medium rounded-md text-stone-700 bg-white border border-stone-300 hover:bg-stone-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-stone-500 transition-colors" %>
                      <%= link_to "Edit", edit_super_admin_admin_job_path(job),
                          class: "inline-flex items-center px-3 py-1.5 text-xs font-medium rounded-md text-stone-700 bg-white border border-stone-300 hover:bg-stone-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-stone-500 transition-colors" %>
                    </div>
                  </td>
                </tr>
              <% end %>
            <% else %>
              <tr>
                <td colspan="8" class="px-6 py-12 text-sm text-center text-stone-900">
                  <div class="text-stone-500">
                    <svg class="w-12 h-12 mx-auto mb-4 text-stone-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0V6a2 2 0 012 2v6.5"></path>
                    </svg>
                    <p class="text-sm font-medium">No jobs found</p>
                    <p class="text-xs">Try adjusting your search or filter criteria</p>
                  </div>
                </td>
              </tr>
            <% end %>
          </tbody>
        </table>

        <!-- Table Footer -->
        <div class="px-6 py-2 border-t bg-stone-50 border-stone-200">
          <div class="flex items-center justify-between">
            <div class="text-sm text-stone-700">
              Showing <%= @pagy.from %> to <%= @pagy.to %> of <%= @pagy.count %> jobs
            </div>
            <div class="flex items-center space-x-2">
              <% if @pagy.respond_to?(:pages) && @pagy.pages > 1 %>
                <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                  <% if @pagy.prev %>
                    <%= link_to request.params.merge(page: @pagy.prev),
                        class: "relative inline-flex items-center px-2 py-2 rounded-l-md border border-stone-300 bg-white text-sm font-medium text-stone-500 hover:bg-stone-50" do %>
                      <span class="sr-only">Previous</span>
                      <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
                      </svg>
                    <% end %>
                  <% else %>
                    <span class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-stone-300 bg-stone-50 text-sm font-medium text-stone-400 cursor-not-allowed">
                      <span class="sr-only">Previous</span>
                      <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
                      </svg>
                    </span>
                  <% end %>

                  <% @pagy.series.each do |item| %>
                    <% if item == :gap %>
                      <span class="relative inline-flex items-center px-4 py-2 border border-stone-300 bg-white text-sm font-medium text-stone-700">
                        ...
                      </span>
                    <% elsif item == @pagy.page %>
                      <span class="relative inline-flex items-center px-4 py-2 border border-blue-500 bg-blue-50 text-sm font-medium text-blue-600">
                        <%= item %>
                      </span>
                    <% else %>
                      <%= link_to item, request.params.merge(page: item),
                          class: "relative inline-flex items-center px-4 py-2 border border-stone-300 bg-white text-sm font-medium text-stone-700 hover:bg-stone-50" %>
                    <% end %>
                  <% end %>

                  <% if @pagy.next %>
                    <%= link_to request.params.merge(page: @pagy.next),
                        class: "relative inline-flex items-center px-2 py-2 rounded-r-md border border-stone-300 bg-white text-sm font-medium text-stone-500 hover:bg-stone-50" do %>
                      <span class="sr-only">Next</span>
                      <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                      </svg>
                    <% end %>
                  <% else %>
                    <span class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-stone-300 bg-stone-50 text-sm font-medium text-stone-400 cursor-not-allowed">
                      <span class="sr-only">Next</span>
                      <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                      </svg>
                    </span>
                  <% end %>
                </nav>
              <% end %>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
