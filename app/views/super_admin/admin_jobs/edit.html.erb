<% content_for :title, "Edit Job: #{@job.title}" %>

<div class="max-w-4xl mx-auto">
  <!-- Header -->
  <div class="mb-8">
    <nav class="flex" aria-label="Breadcrumb">
      <ol class="flex items-center space-x-4">
        <li>
          <%= link_to "Jobs", super_admin_admin_jobs_path, class: "text-stone-500 hover:text-stone-700" %>
        </li>
        <li>
          <div class="flex items-center">
            <svg class="flex-shrink-0 h-5 w-5 text-stone-400" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
            </svg>
            <%= link_to truncate(@job.title, length: 30), super_admin_admin_job_path(@job), class: "ml-4 text-stone-500 hover:text-stone-700" %>
          </div>
        </li>
        <li>
          <div class="flex items-center">
            <svg class="flex-shrink-0 h-5 w-5 text-stone-400" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
            </svg>
            <span class="ml-4 text-sm font-medium text-stone-900">Edit</span>
          </div>
        </li>
      </ol>
    </nav>
    <h1 class="mt-2 text-3xl font-bold text-stone-900">Edit Job</h1>
    <p class="mt-1 text-stone-600">Update job information and settings</p>
  </div>

  <!-- Form -->
  <div class="bg-white shadow rounded-lg">
    <%= form_with model: @job, url: super_admin_admin_job_path(@job), method: :patch, local: true, class: "space-y-6" do |form| %>
      <div class="px-6 py-4 border-b border-stone-200">
        <h2 class="text-lg font-medium text-stone-900">Job Information</h2>
      </div>

      <div class="px-6 py-4 space-y-6">
        <!-- Display any errors -->
        <% if @job.errors.any? %>
          <div class="rounded-md bg-red-50 p-4">
            <div class="flex">
              <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                </svg>
              </div>
              <div class="ml-3">
                <h3 class="text-sm font-medium text-red-800">
                  There were <%= pluralize(@job.errors.count, "error") %> with your submission:
                </h3>
                <div class="mt-2 text-sm text-red-700">
                  <ul class="list-disc pl-5 space-y-1">
                    <% @job.errors.full_messages.each do |message| %>
                      <li><%= message %></li>
                    <% end %>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        <% end %>

        <!-- Basic Information -->
        <div class="space-y-6">
          <div>
            <%= form.label :title, class: "block text-sm font-medium text-stone-700" %>
            <%= form.text_field :title, 
                class: "mt-1 block w-full border-stone-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm" %>
          </div>

          <div>
            <%= form.label :description, class: "block text-sm font-medium text-stone-700" %>
            <%= form.text_area :description, 
                rows: 6,
                class: "mt-1 block w-full border-stone-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm" %>
          </div>

          <div class="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-2">
            <div>
              <%= form.label :status, class: "block text-sm font-medium text-stone-700" %>
              <%= form.select :status, 
                  options_for_select(Job.statuses.map { |key, value| [key.humanize, key] }, @job.status),
                  {},
                  { class: "mt-1 block w-full border-stone-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm" } %>
            </div>

            <div>
              <%= form.label :job_category, class: "block text-sm font-medium text-stone-700" %>
              <%= form.select :job_category, 
                  options_for_select(Job.job_categories.map { |key, value| [key.humanize, key] }, @job.job_category),
                  { include_blank: "Select category" },
                  { class: "mt-1 block w-full border-stone-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm" } %>
            </div>

            <div>
              <%= form.label :budget_range, class: "block text-sm font-medium text-stone-700" %>
              <%= form.select :budget_range, 
                  options_for_select(Job.budget_ranges.map { |key, value| [key.humanize.gsub('_', ' '), key] }, @job.budget_range),
                  { include_blank: "Select budget range" },
                  { class: "mt-1 block w-full border-stone-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm" } %>
            </div>

            <div>
              <%= form.label :work_duration, class: "block text-sm font-medium text-stone-700" %>
              <%= form.select :work_duration, 
                  options_for_select(Job.work_durations.map { |key, value| [key.humanize.gsub('_', ' '), key] }, @job.work_duration),
                  { include_blank: "Select duration" },
                  { class: "mt-1 block w-full border-stone-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm" } %>
            </div>

            <div>
              <%= form.label :platform, class: "block text-sm font-medium text-stone-700" %>
              <%= form.select :platform, 
                  options_for_select(Job.platforms.map { |key, value| [key.humanize.gsub('_', ' '), key] }, @job.platform),
                  { include_blank: "Select platform" },
                  { class: "mt-1 block w-full border-stone-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm" } %>
            </div>

            <div>
              <%= form.label :outcome, class: "block text-sm font-medium text-stone-700" %>
              <%= form.select :outcome, 
                  options_for_select(Job.outcomes.map { |key, value| [key.humanize.gsub('_', ' '), key] }, @job.outcome),
                  { include_blank: "Select outcome" },
                  { class: "mt-1 block w-full border-stone-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm" } %>
            </div>

            <div>
              <%= form.label :payment_frequency, class: "block text-sm font-medium text-stone-700" %>
              <%= form.select :payment_frequency, 
                  options_for_select(Job.payment_frequencies.map { |key, value| [key.humanize.gsub('_', ' '), key] }, @job.payment_frequency),
                  { include_blank: "Select frequency" },
                  { class: "mt-1 block w-full border-stone-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm" } %>
            </div>

            <div>
              <%= form.label :involvement_level, class: "block text-sm font-medium text-stone-700" %>
              <%= form.select :involvement_level, 
                  options_for_select(Job.involvement_levels.map { |key, value| [key.humanize.gsub('_', ' '), key] }, @job.involvement_level),
                  { include_blank: "Select level" },
                  { class: "mt-1 block w-full border-stone-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm" } %>
            </div>
          </div>

          <!-- Dates -->
          <div class="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-2">
            <div>
              <%= form.label :application_deadline, class: "block text-sm font-medium text-stone-700" %>
              <%= form.datetime_local_field :application_deadline, 
                  value: @job.application_deadline&.strftime('%Y-%m-%dT%H:%M'),
                  class: "mt-1 block w-full border-stone-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm" %>
            </div>

            <div>
              <%= form.label :expires_at, class: "block text-sm font-medium text-stone-700" %>
              <%= form.datetime_local_field :expires_at, 
                  value: @job.expires_at&.strftime('%Y-%m-%dT%H:%M'),
                  class: "mt-1 block w-full border-stone-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm" %>
            </div>
          </div>

          <!-- Premium Status -->
          <div class="flex items-center">
            <%= form.check_box :is_premium,
                class: "h-4 w-4 text-blue-600 focus:ring-blue-500 border-stone-300 rounded" %>
            <%= form.label :is_premium, "Premium job", class: "ml-2 block text-sm text-stone-900" %>
          </div>

          <!-- Topics -->
          <div>
            <%= form.label :topics, class: "block text-sm font-medium text-stone-700" %>
            <p class="text-sm text-stone-500 mb-2">Enter topics separated by commas</p>
            <%= form.text_field :topics,
                value: @job.topics&.join(', '),
                placeholder: "e.g., marketing, social media, content creation",
                class: "mt-1 block w-full border-stone-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm" %>
          </div>
        </div>

        <!-- Category-Specific Fields -->
        <% if @job.social_media? %>
          <div class="border-t border-stone-200 pt-6">
            <h3 class="text-lg font-medium text-stone-900 mb-4">Social Media Specific</h3>
            <div class="space-y-6">
              <div>
                <%= form.label :social_media_goal_type, class: "block text-sm font-medium text-stone-700" %>
                <%= form.select :social_media_goal_type,
                    options_for_select(Job.social_media_goal_types.map { |key, value| [key.humanize.gsub('_', ' '), key] }, @job.social_media_goal_type),
                    { include_blank: "Select goal type" },
                    { class: "mt-1 block w-full border-stone-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm" } %>
              </div>

              <div class="flex items-center">
                <%= form.check_box :social_media_understands_risk_acknowledged,
                    class: "h-4 w-4 text-blue-600 focus:ring-blue-500 border-stone-300 rounded" %>
                <%= form.label :social_media_understands_risk_acknowledged, "Risk acknowledged", class: "ml-2 block text-sm text-stone-900" %>
              </div>
            </div>
          </div>
        <% end %>

        <% if @job.newsletter? %>
          <div class="border-t border-stone-200 pt-6">
            <h3 class="text-lg font-medium text-stone-900 mb-4">Newsletter Specific</h3>
            <div class="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-2">
              <div>
                <%= form.label :newsletter_frequency, class: "block text-sm font-medium text-stone-700" %>
                <%= form.select :newsletter_frequency,
                    options_for_select(Job.newsletter_frequencies.map { |key, value| [key.humanize.gsub('_', ' '), key] }, @job.newsletter_frequency),
                    { include_blank: "Select frequency" },
                    { class: "mt-1 block w-full border-stone-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm" } %>
              </div>

              <div>
                <%= form.label :newsletter_length, class: "block text-sm font-medium text-stone-700" %>
                <%= form.select :newsletter_length,
                    options_for_select(Job.newsletter_lengths.map { |key, value| [key.humanize.gsub('_', ' '), key] }, @job.newsletter_length),
                    { include_blank: "Select length" },
                    { class: "mt-1 block w-full border-stone-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm" } %>
              </div>
            </div>
          </div>
        <% end %>

        <% if @job.lead_magnet? %>
          <div class="border-t border-stone-200 pt-6">
            <h3 class="text-lg font-medium text-stone-900 mb-4">Lead Magnet Specific</h3>
            <div>
              <%= form.label :lead_magnet_type, class: "block text-sm font-medium text-stone-700" %>
              <%= form.select :lead_magnet_type,
                  options_for_select(Job.lead_magnet_types.map { |key, value| [key.humanize, key] }, @job.lead_magnet_type),
                  { include_blank: "Select type" },
                  { class: "mt-1 block w-full border-stone-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm" } %>
            </div>
          </div>
        <% end %>

        <!-- Additional Information -->
        <div class="border-t border-stone-200 pt-6">
          <h3 class="text-lg font-medium text-stone-900 mb-4">Additional Information</h3>
          <div class="space-y-6">
            <div>
              <%= form.label :business_challenge, class: "block text-sm font-medium text-stone-700" %>
              <%= form.text_area :business_challenge,
                  rows: 4,
                  class: "mt-1 block w-full border-stone-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm" %>
            </div>

            <div>
              <%= form.label :useful_info, class: "block text-sm font-medium text-stone-700" %>
              <%= form.text_area :useful_info,
                  rows: 4,
                  class: "mt-1 block w-full border-stone-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm" %>
            </div>

            <div>
              <%= form.label :offer_summary, class: "block text-sm font-medium text-stone-700" %>
              <%= form.text_area :offer_summary,
                  rows: 4,
                  class: "mt-1 block w-full border-stone-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm" %>
            </div>

            <div>
              <%= form.label :target_audience_description, class: "block text-sm font-medium text-stone-700" %>
              <%= form.text_area :target_audience_description,
                  rows: 4,
                  class: "mt-1 block w-full border-stone-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm" %>
            </div>

            <div>
              <%= form.label :emulated_brands_description, class: "block text-sm font-medium text-stone-700" %>
              <%= form.text_area :emulated_brands_description,
                  rows: 4,
                  class: "mt-1 block w-full border-stone-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm" %>
            </div>

            <div class="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-2">
              <div>
                <%= form.label :client_count, class: "block text-sm font-medium text-stone-700" %>
                <%= form.text_field :client_count,
                    class: "mt-1 block w-full border-stone-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm" %>
              </div>

              <div>
                <%= form.label :charge_per_client, class: "block text-sm font-medium text-stone-700" %>
                <%= form.text_field :charge_per_client,
                    class: "mt-1 block w-full border-stone-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm" %>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Form Actions -->
      <div class="px-6 py-4 bg-stone-50 border-t border-stone-200 flex justify-between">
        <div>
          <%= link_to "Cancel", super_admin_admin_job_path(@job), 
              class: "inline-flex items-center px-4 py-2 border border-stone-300 rounded-md shadow-sm text-sm font-medium text-stone-700 bg-white hover:bg-stone-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500" %>
        </div>
        <div class="flex space-x-3">
          <%= link_to "View Job", super_admin_admin_job_path(@job), 
              class: "inline-flex items-center px-4 py-2 border border-stone-300 rounded-md shadow-sm text-sm font-medium text-stone-700 bg-white hover:bg-stone-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500" %>
          <%= form.submit "Update Job", 
              class: "inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500" %>
        </div>
      </div>
    <% end %>
  </div>
</div>
