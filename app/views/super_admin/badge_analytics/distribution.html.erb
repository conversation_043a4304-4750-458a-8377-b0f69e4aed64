<% content_for :page_title, "Badge Distribution Reports" %>
<% content_for :page_description, "Comprehensive reports on badge distribution patterns and assignment trends" %>

<div class="mx-auto max-w-7xl">
  <!-- Page Header -->
  <div class="mb-8 bg-white rounded-lg shadow">
    <div class="px-6 py-4 border-b border-stone-200">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-2xl font-bold text-stone-900">Badge Distribution Reports</h1>
          <p class="mt-1 text-sm text-stone-500">Analyze badge distribution patterns, assignment trends, and usage statistics</p>
        </div>
        <div class="flex items-center space-x-3">
          <%= link_to distribution_super_admin_badge_analytics_path(format: :csv, date_range: params[:date_range], badge_type_id: params[:badge_type_id]),
                      class: "inline-flex items-center px-4 py-2 border border-stone-300 rounded-md shadow-sm text-sm font-medium text-stone-700 bg-white hover:bg-stone-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500" do %>
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
            </svg>
            Export Distribution CSV
          <% end %>
          <%= link_to super_admin_badge_analytics_path, 
                      class: "inline-flex items-center px-4 py-2 border border-stone-300 rounded-md shadow-sm text-sm font-medium text-stone-700 bg-white hover:bg-stone-50" do %>
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
            </svg>
            Back to Analytics
          <% end %>
        </div>
      </div>
    </div>

    <!-- Filters -->
    <div class="px-6 py-4 bg-stone-50">
      <%= form_with url: distribution_super_admin_badge_analytics_path, method: :get, local: true, class: "flex items-center space-x-4" do |form| %>
        <div class="flex items-center space-x-2">
          <label for="date_range" class="text-sm font-medium text-stone-700">Date Range:</label>
          <%= form.select :date_range, 
                          options_for_select([
                            ['Last 7 days', '7'],
                            ['Last 14 days', '14'],
                            ['Last 30 days', '30'],
                            ['Last 90 days', '90'],
                            ['Last year', '365']
                          ], params[:date_range] || '30'),
                          {},
                          { class: "rounded-md border-stone-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 text-sm" } %>
        </div>

        <div class="flex items-center space-x-2">
          <label for="badge_type_id" class="text-sm font-medium text-stone-700">Badge Type:</label>
          <%= form.select :badge_type_id, 
                          options_from_collection_for_select(@badge_types, :id, :name, params[:badge_type_id]),
                          { prompt: 'All Badge Types' },
                          { class: "rounded-md border-stone-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 text-sm" } %>
        </div>

        <%= form.submit "Apply Filters", class: "inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500" %>
        
        <% if params[:date_range].present? || params[:badge_type_id].present? %>
          <%= link_to "Clear Filters", distribution_super_admin_badge_analytics_path, class: "text-sm text-stone-500 hover:text-stone-700" %>
        <% end %>
      <% end %>
    </div>
  </div>

  <!-- Summary Metrics -->
  <div class="mb-8 bg-white rounded-lg shadow">
    <div class="px-6 py-4 border-b border-stone-200">
      <h2 class="text-lg font-medium text-stone-900">Distribution Summary</h2>
      <p class="text-sm text-stone-500">
        Overview of badge distribution for the last <%= @date_range.to_i %> days
        <% if @selected_badge_type %>
          for "<%= @selected_badge_type.name %>" badges
        <% end %>
      </p>
    </div>
    <div class="p-6">
      <div class="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
        <div class="p-4 border border-stone-200 rounded-lg">
          <div class="text-2xl font-bold text-blue-600"><%= @distribution_report[:summary][:total_assignments] %></div>
          <div class="text-sm text-stone-600">Total Assignments</div>
        </div>
        <div class="p-4 border border-stone-200 rounded-lg">
          <div class="text-2xl font-bold text-green-600"><%= @distribution_report[:summary][:unique_users_with_badges] %></div>
          <div class="text-sm text-stone-600">Users with Badges</div>
        </div>
        <div class="p-4 border border-stone-200 rounded-lg">
          <div class="text-2xl font-bold text-purple-600"><%= @distribution_report[:summary][:unique_badge_types_assigned] %></div>
          <div class="text-sm text-stone-600">Badge Types Assigned</div>
        </div>
        <div class="p-4 border border-stone-200 rounded-lg">
          <div class="text-2xl font-bold text-orange-600"><%= @distribution_report[:summary][:unique_admins_assigning] %></div>
          <div class="text-sm text-stone-600">Active Admins</div>
        </div>
        <div class="p-4 border border-stone-200 rounded-lg">
          <div class="text-2xl font-bold text-teal-600"><%= @distribution_report[:summary][:active_assignments] %></div>
          <div class="text-sm text-stone-600">Active Assignments</div>
        </div>
        <div class="p-4 border border-stone-200 rounded-lg">
          <div class="text-2xl font-bold text-indigo-600"><%= @distribution_report[:summary][:expired_assignments] %></div>
          <div class="text-sm text-stone-600">Expired Assignments</div>
        </div>
        <div class="p-4 border border-stone-200 rounded-lg">
          <div class="text-2xl font-bold text-amber-600"><%= @distribution_report[:summary][:avg_assignments_per_day] %></div>
          <div class="text-sm text-stone-600">Avg Assignments/Day</div>
        </div>
        <div class="p-4 border border-stone-200 rounded-lg">
          <div class="text-2xl font-bold text-rose-600">
            <%= @distribution_report[:summary][:most_active_assignment_day] || 'N/A' %>
          </div>
          <div class="text-sm text-stone-600">Most Active Day</div>
        </div>
      </div>
    </div>
  </div>

  <!-- Distribution Charts Grid -->
  <div class="grid grid-cols-1 gap-8 mb-8 lg:grid-cols-2">
    <!-- Badge Type Distribution -->
    <div class="bg-white rounded-lg shadow">
      <div class="px-6 py-4 border-b border-stone-200">
        <h2 class="text-lg font-medium text-stone-900">Distribution by Badge Type</h2>
        <p class="text-sm text-stone-500">Assignment counts and user reach by badge type</p>
      </div>
      <div class="p-6">
        <div class="overflow-x-auto">
          <table class="min-w-full">
            <thead>
              <tr class="text-left text-xs font-medium text-stone-500 uppercase tracking-wider">
                <th class="pb-3">Badge</th>
                <th class="pb-3">Assignments</th>
                <th class="pb-3">Users</th>
                <th class="pb-3">Share</th>
              </tr>
            </thead>
            <tbody class="space-y-2">
              <% @distribution_report[:distribution_by_badge_type].each do |badge_data| %>
                <tr class="border-t border-stone-100">
                  <td class="py-3">
                    <div class="flex items-center">
                      <div class="flex-shrink-0 w-6 h-6 rounded-full flex items-center justify-center text-xs font-medium mr-3" 
                           style="background-color: <%= badge_data[:badge_type][:background_color] %>; color: white;">
                        <%= badge_data[:badge_type][:icon] %>
                      </div>
                      <div class="text-sm font-medium text-stone-900"><%= badge_data[:badge_type][:name] %></div>
                    </div>
                  </td>
                  <td class="py-3 text-sm text-stone-900"><%= badge_data[:assignment_count] %></td>
                  <td class="py-3 text-sm text-stone-900"><%= badge_data[:unique_users] %></td>
                  <td class="py-3">
                    <div class="flex items-center">
                      <div class="text-sm text-stone-900"><%= badge_data[:percentage_of_total] %>%</div>
                      <div class="ml-2 flex-1 bg-stone-200 rounded-full h-2 max-w-20">
                        <div class="bg-blue-600 h-2 rounded-full" style="width: <%= badge_data[:percentage_of_total] %>%"></div>
                      </div>
                    </div>
                  </td>
                </tr>
              <% end %>
              <% if @distribution_report[:distribution_by_badge_type].empty? %>
                <tr>
                  <td colspan="4" class="py-4 text-center text-sm text-stone-500">
                    No badge distribution data available for the selected period.
                  </td>
                </tr>
              <% end %>
            </tbody>
          </table>
        </div>
      </div>
    </div>

    <!-- User Role Distribution -->
    <div class="bg-white rounded-lg shadow">
      <div class="px-6 py-4 border-b border-stone-200">
        <h2 class="text-lg font-medium text-stone-900">Distribution by User Role</h2>
        <p class="text-sm text-stone-500">Badge assignments across different user roles</p>
      </div>
      <div class="p-6">
        <div class="space-y-4">
          <% @distribution_report[:distribution_by_user_role].each do |role_data| %>
            <div class="flex items-center justify-between p-3 border border-stone-200 rounded-lg">
              <div class="flex-1">
                <div class="text-sm font-medium text-stone-900"><%= role_data[:role_name] %></div>
                <div class="text-xs text-stone-500">
                  <%= role_data[:assignment_count] %> assignments to <%= role_data[:unique_users] %> users
                </div>
              </div>
              <div class="text-right">
                <div class="text-sm font-semibold text-stone-900"><%= role_data[:percentage_of_total] %>%</div>
                <div class="text-xs text-stone-500"><%= role_data[:avg_badges_per_user] %> avg/user</div>
              </div>
            </div>
          <% end %>
          <% if @distribution_report[:distribution_by_user_role].empty? %>
            <p class="text-sm text-stone-500 text-center py-4">No role distribution data available.</p>
          <% end %>
        </div>
      </div>
    </div>
  </div>

  <!-- Assignment Trends -->
  <div class="mb-8 bg-white rounded-lg shadow">
    <div class="px-6 py-4 border-b border-stone-200">
      <h2 class="text-lg font-medium text-stone-900">Assignment Trends</h2>
      <p class="text-sm text-stone-500">Badge assignment patterns over time</p>
    </div>
    <div class="p-6">
      <div class="grid grid-cols-1 gap-6 lg:grid-cols-3">
        <div class="lg:col-span-2">
          <h4 class="text-sm font-medium text-stone-700 mb-3">Daily Assignment Activity</h4>
          <div class="space-y-2 max-h-64 overflow-y-auto">
            <% @distribution_report[:assignment_trends][:daily_assignments].reverse.each do |day_data| %>
              <div class="flex items-center justify-between py-1">
                <span class="text-sm text-stone-600"><%= Date.parse(day_data[:date]).strftime('%b %d, %Y') %></span>
                <div class="flex items-center">
                  <span class="text-sm font-medium text-stone-900 mr-2"><%= day_data[:count] %></span>
                  <div class="w-16 bg-stone-200 rounded-full h-2">
                    <% max_count = @distribution_report[:assignment_trends][:daily_assignments].map { |d| d[:count] }.max %>
                    <% width = max_count > 0 ? (day_data[:count].to_f / max_count * 100) : 0 %>
                    <div class="bg-blue-600 h-2 rounded-full" style="width: <%= width %>%"></div>
                  </div>
                </div>
              </div>
            <% end %>
          </div>
        </div>
        
        <div>
          <h4 class="text-sm font-medium text-stone-700 mb-3">Trend Analysis</h4>
          <div class="space-y-3">
            <div class="p-3 border border-stone-200 rounded-lg">
              <div class="text-xs text-stone-500">Trend Direction</div>
              <div class="text-sm font-medium text-stone-900 capitalize">
                <%= @distribution_report[:assignment_trends][:trend_direction].humanize %>
              </div>
            </div>
            <div class="p-3 border border-stone-200 rounded-lg">
              <div class="text-xs text-stone-500">Peak Day</div>
              <div class="text-sm font-medium text-stone-900">
                <%= @distribution_report[:assignment_trends][:peak_assignment_day] ? Date.parse(@distribution_report[:assignment_trends][:peak_assignment_day]).strftime('%b %d') : 'N/A' %>
              </div>
            </div>
            <div class="p-3 border border-stone-200 rounded-lg">
              <div class="text-xs text-stone-500">Lowest Day</div>
              <div class="text-sm font-medium text-stone-900">
                <%= @distribution_report[:assignment_trends][:lowest_assignment_day] ? Date.parse(@distribution_report[:assignment_trends][:lowest_assignment_day]).strftime('%b %d') : 'N/A' %>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- User Badge Count Distribution -->
  <div class="mb-8 bg-white rounded-lg shadow">
    <div class="px-6 py-4 border-b border-stone-200">
      <h2 class="text-lg font-medium text-stone-900">User Badge Count Distribution</h2>
      <p class="text-sm text-stone-500">How many badges users typically have</p>
    </div>
    <div class="p-6">
      <div class="grid grid-cols-1 gap-6 lg:grid-cols-2">
        <div>
          <h4 class="text-sm font-medium text-stone-700 mb-3">Distribution Breakdown</h4>
          <div class="space-y-2">
            <% @distribution_report[:user_badge_counts][:distribution].each do |count_data| %>
              <div class="flex items-center justify-between py-2 border-b border-stone-100">
                <span class="text-sm text-stone-600">
                  <%= pluralize(count_data[:badge_count], 'badge') %>
                </span>
                <div class="flex items-center">
                  <span class="text-sm font-medium text-stone-900 mr-2">
                    <%= pluralize(count_data[:user_count], 'user') %>
                  </span>
                  <span class="text-xs text-stone-500">(<%= count_data[:percentage] %>%)</span>
                </div>
              </div>
            <% end %>
          </div>
        </div>
        
        <div>
          <h4 class="text-sm font-medium text-stone-700 mb-3">Summary Statistics</h4>
          <div class="space-y-3">
            <div class="p-3 border border-stone-200 rounded-lg">
              <div class="text-xs text-stone-500">Average Badges per User</div>
              <div class="text-lg font-semibold text-stone-900">
                <%= number_with_precision(@distribution_report[:user_badge_counts][:avg_badges_per_user], precision: 1) %>
              </div>
            </div>
            <div class="p-3 border border-stone-200 rounded-lg">
              <div class="text-xs text-stone-500">Maximum Badges per User</div>
              <div class="text-lg font-semibold text-stone-900">
                <%= @distribution_report[:user_badge_counts][:max_badges_per_user] %>
              </div>
            </div>
            <div class="p-3 border border-stone-200 rounded-lg">
              <div class="text-xs text-stone-500">Users with Multiple Badges</div>
              <div class="text-lg font-semibold text-stone-900">
                <%= @distribution_report[:user_badge_counts][:users_with_multiple_badges] %>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Admin Activity and Expiration Analysis Grid -->
  <div class="grid grid-cols-1 gap-8 mb-8 lg:grid-cols-2">
    <!-- Admin Assignment Activity -->
    <div class="bg-white rounded-lg shadow">
      <div class="px-6 py-4 border-b border-stone-200">
        <h2 class="text-lg font-medium text-stone-900">Admin Assignment Activity</h2>
        <p class="text-sm text-stone-500">Badge assignment activity by admin users</p>
      </div>
      <div class="p-6">
        <div class="space-y-3">
          <% @distribution_report[:admin_activity].first(5).each do |admin_data| %>
            <div class="p-3 border border-stone-200 rounded-lg">
              <div class="flex items-center justify-between">
                <div class="flex-1 min-w-0">
                  <div class="text-sm font-medium text-stone-900 truncate">
                    <%= admin_data[:admin][:name] %>
                  </div>
                  <div class="text-xs text-stone-500 truncate">
                    <%= admin_data[:admin][:email] %>
                  </div>
                </div>
                <div class="text-right ml-3">
                  <div class="text-sm font-semibold text-stone-900">
                    <%= admin_data[:assignment_count] %> assignments
                  </div>
                  <div class="text-xs text-stone-500">
                    <%= admin_data[:percentage_of_total] %>% of total
                  </div>
                </div>
              </div>
              <div class="mt-2 text-xs text-stone-500">
                <%= admin_data[:unique_badge_types] %> badge types • 
                <%= admin_data[:unique_users_assigned] %> users • 
                <%= admin_data[:avg_assignments_per_day] %> avg/day
              </div>
            </div>
          <% end %>
          <% if @distribution_report[:admin_activity].empty? %>
            <p class="text-sm text-stone-500 text-center py-4">No admin activity data available.</p>
          <% end %>
        </div>
      </div>
    </div>

    <!-- Expiration Analysis -->
    <div class="bg-white rounded-lg shadow">
      <div class="px-6 py-4 border-b border-stone-200">
        <h2 class="text-lg font-medium text-stone-900">Badge Expiration Analysis</h2>
        <p class="text-sm text-stone-500">Badge expiration patterns and lifecycle</p>
      </div>
      <div class="p-6">
        <div class="space-y-4">
          <div class="grid grid-cols-2 gap-4">
            <div class="p-3 border border-stone-200 rounded-lg text-center">
              <div class="text-lg font-semibold text-stone-900">
                <%= @distribution_report[:expiration_analysis][:total_with_expiration] %>
              </div>
              <div class="text-xs text-stone-500">With Expiration</div>
            </div>
            <div class="p-3 border border-stone-200 rounded-lg text-center">
              <div class="text-lg font-semibold text-red-600">
                <%= @distribution_report[:expiration_analysis][:expired_count] %>
              </div>
              <div class="text-xs text-stone-500">Expired</div>
            </div>
            <div class="p-3 border border-stone-200 rounded-lg text-center">
              <div class="text-lg font-semibold text-amber-600">
                <%= @distribution_report[:expiration_analysis][:expiring_soon_count] %>
              </div>
              <div class="text-xs text-stone-500">Expiring Soon</div>
            </div>
            <div class="p-3 border border-stone-200 rounded-lg text-center">
              <div class="text-lg font-semibold text-green-600">
                <%= @distribution_report[:expiration_analysis][:never_expire_count] %>
              </div>
              <div class="text-xs text-stone-500">Never Expire</div>
            </div>
          </div>
          
          <div class="border-t border-stone-200 pt-4">
            <div class="flex items-center justify-between mb-2">
              <span class="text-sm font-medium text-stone-600">Expiration Rate</span>
              <span class="text-sm text-stone-900">
                <%= @distribution_report[:expiration_analysis][:expiration_rate] %>%
              </span>
            </div>
            <div class="flex items-center justify-between">
              <span class="text-sm font-medium text-stone-600">Avg Days to Expiration</span>
              <span class="text-sm text-stone-900">
                <%= @distribution_report[:expiration_analysis][:avg_days_to_expiration] %> days
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
