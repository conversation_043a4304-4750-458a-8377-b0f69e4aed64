<% content_for :page_title, "Badge Performance Monitoring" %>
<% content_for :page_description, "Real-time performance monitoring and alerts for the badge system" %>

<div class="mx-auto max-w-7xl">
  <!-- Page Header -->
  <div class="mb-8 bg-white rounded-lg shadow">
    <div class="px-6 py-4 border-b border-stone-200">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-2xl font-bold text-stone-900">Badge Performance Monitoring</h1>
          <p class="mt-1 text-sm text-stone-500">Real-time performance tracking and system health monitoring</p>
        </div>
        <div class="flex items-center space-x-3">
          <%= link_to performance_super_admin_badge_analytics_path(format: :csv, timeframe: params[:timeframe]),
                      class: "inline-flex items-center px-4 py-2 border border-stone-300 rounded-md shadow-sm text-sm font-medium text-stone-700 bg-white hover:bg-stone-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500" do %>
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
            </svg>
            Export Performance CSV
          <% end %>
          <%= link_to super_admin_badge_analytics_path, 
                      class: "inline-flex items-center px-4 py-2 border border-stone-300 rounded-md shadow-sm text-sm font-medium text-stone-700 bg-white hover:bg-stone-50" do %>
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
            </svg>
            Back to Analytics
          <% end %>
        </div>
      </div>
    </div>

    <!-- Timeframe Filter -->
    <div class="px-6 py-4 bg-stone-50">
      <%= form_with url: performance_super_admin_badge_analytics_path, method: :get, local: true, class: "flex items-center space-x-4" do |form| %>
        <div class="flex items-center space-x-2">
          <label for="timeframe" class="text-sm font-medium text-stone-700">Timeframe:</label>
          <%= form.select :timeframe, 
                          options_for_select([
                            ['Last hour', '1'],
                            ['Last 6 hours', '6'],
                            ['Last 24 hours', '24'],
                            ['Last 3 days', '72'],
                            ['Last week', '168']
                          ], params[:timeframe] || '1'),
                          {},
                          { class: "rounded-md border-stone-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 text-sm" } %>
        </div>

        <%= form.submit "Update", class: "inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500" %>
        
        <div class="flex items-center space-x-2 ml-auto">
          <span class="text-sm text-stone-500">Auto-refresh:</span>
          <button type="button" id="auto-refresh-toggle" class="inline-flex items-center px-3 py-1 border border-stone-300 rounded-md text-sm text-stone-700 bg-white hover:bg-stone-50">
            <span id="refresh-status">Off</span>
          </button>
        </div>
      <% end %>
    </div>
  </div>

  <!-- System Health Status -->
  <div class="mb-8 bg-white rounded-lg shadow">
    <div class="px-6 py-4 border-b border-stone-200">
      <h2 class="text-lg font-medium text-stone-900">System Health Status</h2>
      <p class="text-sm text-stone-500">Current badge system performance and health indicators</p>
    </div>
    <div class="p-6">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
          <div class="flex items-center">
            <div class="w-4 h-4 rounded-full mr-2 <%= @health_status == 'healthy' ? 'bg-green-500' : @health_status == 'warning' ? 'bg-yellow-500' : 'bg-red-500' %>"></div>
            <span class="text-lg font-semibold text-stone-900 capitalize"><%= @health_status || 'Unknown' %></span>
          </div>
          <div class="text-sm text-stone-500">
            Last updated: <%= Time.current.strftime('%H:%M:%S') %>
          </div>
        </div>
        <div class="text-right">
          <div class="text-sm text-stone-500">Monitoring Period</div>
          <div class="text-lg font-medium text-stone-900"><%= @timeframe.to_i / 1.hour %> hour<%= 's' if @timeframe.to_i / 1.hour != 1 %></div>
        </div>
      </div>
    </div>
  </div>

  <!-- Performance Overview -->
  <div class="mb-8 bg-white rounded-lg shadow">
    <div class="px-6 py-4 border-b border-stone-200">
      <h2 class="text-lg font-medium text-stone-900">Performance Overview</h2>
      <p class="text-sm text-stone-500">Key performance metrics for badge system operations</p>
    </div>
    <div class="p-6">
      <div class="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4">
        <!-- Page Renders -->
        <div class="p-4 border border-stone-200 rounded-lg">
          <div class="text-2xl font-bold text-blue-600"><%= @performance_summary[:page_renders][:total_renders] || 0 %></div>
          <div class="text-sm text-stone-600">Page Renders</div>
          <div class="text-xs text-stone-500 mt-1">
            Avg: <%= @performance_summary[:page_renders][:avg_duration] || 0 %>ms
          </div>
        </div>

        <!-- Database Queries -->
        <div class="p-4 border border-stone-200 rounded-lg">
          <div class="text-2xl font-bold text-green-600"><%= @performance_summary[:queries][:total_queries] || 0 %></div>
          <div class="text-sm text-stone-600">Database Queries</div>
          <div class="text-xs text-stone-500 mt-1">
            Avg: <%= @performance_summary[:queries][:avg_duration] || 0 %>ms
          </div>
        </div>

        <!-- Component Renders -->
        <div class="p-4 border border-stone-200 rounded-lg">
          <div class="text-2xl font-bold text-purple-600"><%= @performance_summary[:components][:total_renders] || 0 %></div>
          <div class="text-sm text-stone-600">Component Renders</div>
          <div class="text-xs text-stone-500 mt-1">
            Avg: <%= @performance_summary[:components][:avg_duration] || 0 %>ms
          </div>
        </div>

        <!-- Operations -->
        <div class="p-4 border border-stone-200 rounded-lg">
          <div class="text-2xl font-bold text-orange-600"><%= @performance_summary[:operations][:total_operations] || 0 %></div>
          <div class="text-sm text-stone-600">Operations</div>
          <div class="text-xs text-stone-500 mt-1">
            Avg: <%= @performance_summary[:operations][:avg_duration] || 0 %>ms
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Performance Details Grid -->
  <div class="grid grid-cols-1 gap-8 mb-8 lg:grid-cols-2">
    <!-- Slowest Operations -->
    <div class="bg-white rounded-lg shadow">
      <div class="px-6 py-4 border-b border-stone-200">
        <h2 class="text-lg font-medium text-stone-900">Slowest Operations</h2>
        <p class="text-sm text-stone-500">Operations with the highest response times</p>
      </div>
      <div class="p-6">
        <div class="space-y-3">
          <% if @performance_summary[:page_renders][:slowest_render] %>
            <div class="p-3 border border-stone-200 rounded-lg">
              <div class="flex items-center justify-between">
                <div class="flex-1 min-w-0">
                  <div class="text-sm font-medium text-stone-900">Page Render</div>
                  <div class="text-xs text-stone-500 truncate">
                    <%= @performance_summary[:page_renders][:slowest_render][:controller_action] %>
                  </div>
                </div>
                <div class="text-right ml-3">
                  <div class="text-sm font-semibold text-red-600">
                    <%= @performance_summary[:page_renders][:slowest_render][:duration] %>ms
                  </div>
                  <div class="text-xs text-stone-500">
                    <%= @performance_summary[:page_renders][:slowest_render][:timestamp]&.strftime('%H:%M:%S') %>
                  </div>
                </div>
              </div>
            </div>
          <% end %>

          <% if @performance_summary[:queries][:slowest_query] %>
            <div class="p-3 border border-stone-200 rounded-lg">
              <div class="flex items-center justify-between">
                <div class="flex-1 min-w-0">
                  <div class="text-sm font-medium text-stone-900">Database Query</div>
                  <div class="text-xs text-stone-500 truncate">
                    <%= @performance_summary[:queries][:slowest_query][:query_type] %> on <%= @performance_summary[:queries][:slowest_query][:table_name] %>
                  </div>
                </div>
                <div class="text-right ml-3">
                  <div class="text-sm font-semibold text-red-600">
                    <%= @performance_summary[:queries][:slowest_query][:duration] %>ms
                  </div>
                  <div class="text-xs text-stone-500">
                    <%= @performance_summary[:queries][:slowest_query][:timestamp]&.strftime('%H:%M:%S') %>
                  </div>
                </div>
              </div>
            </div>
          <% end %>

          <% if @performance_summary[:operations][:slowest_operation] %>
            <div class="p-3 border border-stone-200 rounded-lg">
              <div class="flex items-center justify-between">
                <div class="flex-1 min-w-0">
                  <div class="text-sm font-medium text-stone-900">Operation</div>
                  <div class="text-xs text-stone-500 truncate">
                    <%= @performance_summary[:operations][:slowest_operation][:operation] %>
                  </div>
                </div>
                <div class="text-right ml-3">
                  <div class="text-sm font-semibold text-red-600">
                    <%= @performance_summary[:operations][:slowest_operation][:duration] %>ms
                  </div>
                  <div class="text-xs text-stone-500">
                    <%= @performance_summary[:operations][:slowest_operation][:timestamp]&.strftime('%H:%M:%S') %>
                  </div>
                </div>
              </div>
            </div>
          <% end %>

          <% if @performance_summary[:page_renders][:slowest_render].nil? && @performance_summary[:queries][:slowest_query].nil? && @performance_summary[:operations][:slowest_operation].nil? %>
            <p class="text-sm text-stone-500 text-center py-4">No performance data available for the selected timeframe.</p>
          <% end %>
        </div>
      </div>
    </div>

    <!-- Recent Alerts -->
    <div class="bg-white rounded-lg shadow">
      <div class="px-6 py-4 border-b border-stone-200">
        <h2 class="text-lg font-medium text-stone-900">Recent Performance Alerts</h2>
        <p class="text-sm text-stone-500">Critical performance issues and warnings</p>
      </div>
      <div class="p-6">
        <div class="space-y-3">
          <% @recent_alerts.each do |alert| %>
            <div class="p-3 border border-stone-200 rounded-lg">
              <div class="flex items-start justify-between">
                <div class="flex-1 min-w-0">
                  <div class="flex items-center">
                    <div class="w-2 h-2 rounded-full mr-2 <%= alert[:severity] == 'critical' ? 'bg-red-500' : 'bg-yellow-500' %>"></div>
                    <div class="text-sm font-medium text-stone-900 capitalize">
                      <%= alert[:type].humanize %>
                    </div>
                  </div>
                  <div class="text-xs text-stone-500 mt-1">
                    <%= alert[:details].to_s.truncate(100) %>
                  </div>
                </div>
                <div class="text-right ml-3">
                  <div class="text-xs text-stone-500">
                    <%= alert[:timestamp]&.strftime('%H:%M:%S') %>
                  </div>
                </div>
              </div>
            </div>
          <% end %>
          
          <% if @recent_alerts.empty? %>
            <div class="text-center py-4">
              <svg class="mx-auto h-12 w-12 text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <p class="mt-2 text-sm text-green-600 font-medium">No recent alerts</p>
              <p class="text-xs text-stone-500">System is performing well</p>
            </div>
          <% end %>
        </div>
      </div>
    </div>
  </div>

  <!-- Performance Breakdown -->
  <div class="mb-8 bg-white rounded-lg shadow">
    <div class="px-6 py-4 border-b border-stone-200">
      <h2 class="text-lg font-medium text-stone-900">Performance Breakdown</h2>
      <p class="text-sm text-stone-500">Detailed performance metrics by operation type</p>
    </div>
    <div class="p-6">
      <div class="grid grid-cols-1 gap-6 lg:grid-cols-3">
        <!-- Page Renders by Controller -->
        <div>
          <h4 class="text-sm font-medium text-stone-700 mb-3">Page Renders by Controller</h4>
          <div class="space-y-2">
            <% (@performance_summary[:page_renders][:by_controller] || {}).each do |controller, stats| %>
              <div class="flex items-center justify-between py-1 text-sm">
                <span class="text-stone-600 truncate"><%= controller %></span>
                <div class="flex items-center space-x-2">
                  <span class="text-stone-900"><%= stats[:count] %></span>
                  <span class="text-xs text-stone-500">(<%= stats[:avg_duration] %>ms)</span>
                </div>
              </div>
            <% end %>
            <% if (@performance_summary[:page_renders][:by_controller] || {}).empty? %>
              <p class="text-xs text-stone-500 text-center py-2">No data available</p>
            <% end %>
          </div>
        </div>

        <!-- Queries by Type -->
        <div>
          <h4 class="text-sm font-medium text-stone-700 mb-3">Queries by Type</h4>
          <div class="space-y-2">
            <% (@performance_summary[:queries][:by_type] || {}).each do |type, stats| %>
              <div class="flex items-center justify-between py-1 text-sm">
                <span class="text-stone-600"><%= type %></span>
                <div class="flex items-center space-x-2">
                  <span class="text-stone-900"><%= stats[:count] %></span>
                  <span class="text-xs text-stone-500">(<%= stats[:avg_duration] %>ms)</span>
                </div>
              </div>
            <% end %>
            <% if (@performance_summary[:queries][:by_type] || {}).empty? %>
              <p class="text-xs text-stone-500 text-center py-2">No data available</p>
            <% end %>
          </div>
        </div>

        <!-- Operations by Name -->
        <div>
          <h4 class="text-sm font-medium text-stone-700 mb-3">Operations by Name</h4>
          <div class="space-y-2">
            <% (@performance_summary[:operations][:by_operation] || {}).each do |operation, stats| %>
              <div class="flex items-center justify-between py-1 text-sm">
                <span class="text-stone-600 truncate"><%= operation %></span>
                <div class="flex items-center space-x-2">
                  <span class="text-stone-900"><%= stats[:count] %></span>
                  <span class="text-xs text-stone-500">(<%= stats[:avg_duration] %>ms)</span>
                </div>
              </div>
            <% end %>
            <% if (@performance_summary[:operations][:by_operation] || {}).empty? %>
              <p class="text-xs text-stone-500 text-center py-2">No data available</p>
            <% end %>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Auto-refresh JavaScript -->
<script>
document.addEventListener('DOMContentLoaded', function() {
  let autoRefreshInterval;
  let isAutoRefreshEnabled = false;
  
  const toggleButton = document.getElementById('auto-refresh-toggle');
  const statusSpan = document.getElementById('refresh-status');
  
  function toggleAutoRefresh() {
    if (isAutoRefreshEnabled) {
      clearInterval(autoRefreshInterval);
      isAutoRefreshEnabled = false;
      statusSpan.textContent = 'Off';
      toggleButton.classList.remove('bg-green-100', 'text-green-700');
      toggleButton.classList.add('bg-white', 'text-stone-700');
    } else {
      autoRefreshInterval = setInterval(() => {
        window.location.reload();
      }, 30000); // Refresh every 30 seconds
      isAutoRefreshEnabled = true;
      statusSpan.textContent = 'On (30s)';
      toggleButton.classList.remove('bg-white', 'text-stone-700');
      toggleButton.classList.add('bg-green-100', 'text-green-700');
    }
  }
  
  toggleButton.addEventListener('click', toggleAutoRefresh);
});
</script>
