<% content_for :title, "Users" %>

<div class="bg-white rounded-lg shadow">
  <div class="px-4 py-5 sm:p-6">
    <!-- Header Section -->
    <div class="sm:flex sm:items-center">
      <div class="sm:flex-auto">
        <h1 class="text-xl font-semibold text-stone-900">Users</h1>
        <p class="mt-2 text-sm text-stone-700">Manage user accounts and permissions</p>
      </div>
      <div class="mt-4 sm:mt-0 sm:ml-16 sm:flex-none">
        <%= link_to request.params.merge(format: :csv),
            class: "inline-flex items-center justify-center rounded-md border border-stone-300 px-4 py-2 text-sm font-medium text-stone-800 shadow-sm hover:bg-stone-100 focus:outline-none focus:ring-2 focus:ring-stone-500 focus:ring-offset-2" do %>
          <svg class="w-4 h-4 mr-2 -ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
          </svg>
          Export CSV
        <% end %>
      </div>
    </div>

    <!-- Search and Filters Section -->
    <div class="mt-8 pt-6 border-t border-stone-200">
      <%= form_with url: request.path, method: :get, local: true, class: "space-y-4" do |form| %>
        <!-- Search and Action Buttons Row -->
        <div class="flex items-end gap-4">
          <!-- Search Field (takes most space) -->
          <div class="flex-1">
            <%= form.label :search, "Search", class: "block text-sm font-medium text-stone-700" %>
            <%= form.text_field :search,
                value: params[:search],
                placeholder: "Search by name, email...",
                class: "mt-1 block w-full rounded-md border-stone-300 shadow-sm focus:border-stone-500 focus:ring-stone-500 sm:text-sm" %>
          </div>

          <!-- Filter and Clear Buttons -->
          <div class="flex items-center space-x-4">
            <%= form.submit "Filter", class: "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-stone-800 hover:bg-stone-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-stone-500" %>
            <% if params[:search].present? || params[:verified].present? || params[:role].present? %>
              <%= link_to "Clear", super_admin_admin_users_path, class: "text-stone-600 hover:text-stone-900" %>
            <% end %>
          </div>
        </div>

        <!-- Filter Dropdowns Row -->
        <div class="grid grid-cols-1 gap-4 sm:grid-cols-3">
          <!-- Verification Status Filter -->
          <div>
            <%= form.label :verified, "Verification Status", class: "block text-sm font-medium text-stone-700" %>
            <%= form.select :verified,
                options_for_select([['All', ''], ['Verified', 'true'], ['Unverified', 'false']], params[:verified]),
                {},
                { class: "mt-1 block w-full rounded-md border-stone-300 shadow-sm focus:border-stone-500 focus:ring-stone-500 sm:text-sm" } %>
          </div>

          <!-- Role Filter -->
          <div>
            <%= form.label :role, "Role", class: "block text-sm font-medium text-stone-700" %>
            <%= form.select :role,
                options_for_select([['All', '']] + @filter_options[:role], params[:role]),
                {},
                { class: "mt-1 block w-full rounded-md border-stone-300 shadow-sm focus:border-stone-500 focus:ring-stone-500 sm:text-sm" } %>
          </div>
        </div>
      <% end %>
    </div>


    <!-- Table Section -->
    <div class="mt-8 pt-6 border-t border-stone-200">
      <div class="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
        <table class="min-w-full divide-y divide-stone-300">
          <thead class="bg-stone-50">
            <tr>
              <th scope="col" class="px-6 py-3 text-xs font-medium tracking-wider text-left uppercase text-stone-500">
                ID
              </th>
              <th scope="col" class="px-6 py-3 text-xs font-medium tracking-wider text-left uppercase text-stone-500">
                User
              </th>
              <th scope="col" class="px-6 py-3 text-xs font-medium tracking-wider text-left uppercase text-stone-500">
                Email
              </th>
              <th scope="col" class="px-6 py-3 text-xs font-medium tracking-wider text-left uppercase text-stone-500">
                Roles
              </th>
              <th scope="col" class="px-6 py-3 text-xs font-medium tracking-wider text-left uppercase text-stone-500">
                Status
              </th>
              <th scope="col" class="px-6 py-3 text-xs font-medium tracking-wider text-left uppercase text-stone-500">
                Created
              </th>
              <th scope="col" class="relative px-6 py-3">
                <span class="sr-only">Actions</span>
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-stone-200">
            <% if @users.any? %>
              <% @users.each do |user| %>
                <tr class="hover:bg-stone-50">
                  <td class="px-6 py-4 text-sm text-stone-900 whitespace-nowrap">
                    <%= user.id %>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center">
                      <div class="flex-shrink-0 w-10 h-10">
                        <% if user.avatar.attached? && user.avatar.representable? %>
                          <%= image_tag url_for(user.avatar), class: "h-10 w-10 rounded-full object-cover", alt: user.name %>
                        <% else %>
                          <div class="flex items-center justify-center w-10 h-10 rounded-full bg-stone-300">
                            <span class="text-sm font-medium text-stone-700"><%= user.initials %></span>
                          </div>
                        <% end %>
                      </div>
                      <div class="ml-4">
                        <div class="text-sm font-medium text-stone-900">
                          <%= user.name.presence || "No name" %>
                        </div>
                        <div class="text-sm text-stone-500">ID: <%= user.id %></div>
                      </div>
                    </div>
                  </td>
                  <td class="px-6 py-4 text-sm whitespace-nowrap text-stone-900">
                    <%= user.email %>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <% if user.roles.any? %>
                      <% user.roles.each do |role| %>
                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-stone-100 text-stone-800 mr-1">
                          <%= role.name %>
                        </span>
                      <% end %>
                    <% else %>
                      <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-stone-100 text-stone-800">
                        user
                      </span>
                    <% end %>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <% if user.verified? %>
                      <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                        Verified
                      </span>
                    <% else %>
                      <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">
                        Unverified
                      </span>
                    <% end %>
                  </td>
                  <td class="px-6 py-4 text-sm text-stone-900 whitespace-nowrap">
                    <div class="text-sm text-stone-900"><%= user.created_at.strftime('%b %d, %Y') %></div>
                    <div class="text-xs text-stone-500"><%= user.created_at.strftime('%I:%M %p') %></div>
                  </td>
                  <td class="relative px-6 py-4 text-sm font-medium text-right whitespace-nowrap">
                    <div class="flex items-center justify-end space-x-2">
                      <%= link_to "View", super_admin_admin_user_path(user),
                          class: "inline-flex items-center px-3 py-1.5 text-xs font-medium rounded-md text-stone-700 bg-white border border-stone-300 hover:bg-stone-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-stone-500 transition-colors" %>
                      <% if can_perform?(:edit, :users) %>
                        <%= link_to "Edit", edit_super_admin_admin_user_path(user),
                            class: "inline-flex items-center px-3 py-1.5 text-xs font-medium rounded-md text-stone-700 bg-white border border-stone-300 hover:bg-stone-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-stone-500 transition-colors" %>
                      <% end %>
                    </div>
                  </td>
                </tr>
              <% end %>
            <% else %>
              <tr>
                <td colspan="7" class="px-6 py-12 text-sm text-center text-stone-900">
                  <div class="text-stone-500">
                    <svg class="w-12 h-12 mx-auto mb-4 text-stone-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                    </svg>
                    <p class="text-sm font-medium">No users found</p>
                    <p class="text-xs">Try adjusting your search or filter criteria</p>
                  </div>
                </td>
              </tr>
            <% end %>
          </tbody>
        </table>

        <!-- Table Footer -->
        <div class="px-6 py-2 border-t bg-stone-50 border-stone-200">
          <div class="flex items-center justify-between">
            <div class="text-sm text-stone-700">
              Showing <%= @pagy.from %> to <%= @pagy.to %> of <%= @pagy.count %> users
            </div>
            <div class="flex items-center space-x-2">
              <% if @pagy.respond_to?(:pages) && @pagy.pages > 1 %>
                <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                  <% if @pagy.prev %>
                    <%= link_to request.params.merge(page: @pagy.prev),
                        class: "relative inline-flex items-center px-2 py-2 rounded-l-md border border-stone-300 bg-white text-sm font-medium text-stone-500 hover:bg-stone-50" do %>
                      <span class="sr-only">Previous</span>
                      <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
                      </svg>
                    <% end %>
                  <% else %>
                    <span class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-stone-300 bg-stone-50 text-sm font-medium text-stone-400 cursor-not-allowed">
                      <span class="sr-only">Previous</span>
                      <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
                      </svg>
                    </span>
                  <% end %>

                  <% @pagy.series.each do |item| %>
                    <% if item == :gap %>
                      <span class="relative inline-flex items-center px-4 py-2 border border-stone-300 bg-white text-sm font-medium text-stone-700">
                        ...
                      </span>
                    <% elsif item == @pagy.page %>
                      <span class="relative inline-flex items-center px-4 py-2 border border-blue-500 bg-blue-50 text-sm font-medium text-blue-600">
                        <%= item %>
                      </span>
                    <% else %>
                      <%= link_to item, request.params.merge(page: item),
                          class: "relative inline-flex items-center px-4 py-2 border border-stone-300 bg-white text-sm font-medium text-stone-700 hover:bg-stone-50" %>
                    <% end %>
                  <% end %>

                  <% if @pagy.next %>
                    <%= link_to request.params.merge(page: @pagy.next),
                        class: "relative inline-flex items-center px-2 py-2 rounded-r-md border border-stone-300 bg-white text-sm font-medium text-stone-500 hover:bg-stone-50" do %>
                      <span class="sr-only">Next</span>
                      <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                      </svg>
                    <% end %>
                  <% else %>
                    <span class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-stone-300 bg-stone-50 text-sm font-medium text-stone-400 cursor-not-allowed">
                      <span class="sr-only">Next</span>
                      <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                      </svg>
                    </span>
                  <% end %>
                </nav>
              <% end %>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
