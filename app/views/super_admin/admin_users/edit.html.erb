<% content_for :title, "Edit User: #{@user.name}" %>

<div class="max-w-4xl mx-auto">
  <!-- Header -->
  <div class="mb-8">
    <nav class="flex" aria-label="Breadcrumb">
      <ol class="flex items-center space-x-4">
        <li>
          <%= link_to "Users", super_admin_admin_users_path, class: "text-stone-500 hover:text-stone-700" %>
        </li>
        <li>
          <div class="flex items-center">
            <svg class="flex-shrink-0 h-5 w-5 text-stone-400" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
            </svg>
            <%= link_to @user.name, super_admin_admin_user_path(@user), class: "ml-4 text-stone-500 hover:text-stone-700" %>
          </div>
        </li>
        <li>
          <div class="flex items-center">
            <svg class="flex-shrink-0 h-5 w-5 text-stone-400" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
            </svg>
            <span class="ml-4 text-sm font-medium text-stone-900">Edit</span>
          </div>
        </li>
      </ol>
    </nav>
    <h1 class="mt-2 text-3xl font-bold text-stone-900">Edit User</h1>
    <p class="mt-1 text-stone-600">Update user information and settings</p>
  </div>

  <!-- Form -->
  <div class="bg-white shadow rounded-lg">
    <%= form_with model: @user, url: super_admin_admin_user_path(@user), method: :patch, local: true, class: "space-y-6" do |form| %>
      <div class="px-6 py-4 border-b border-stone-200">
        <h2 class="text-lg font-medium text-stone-900">User Information</h2>
      </div>

      <div class="px-6 py-4 space-y-6">
        <!-- Display any errors -->
        <% if @user.errors.any? %>
          <div class="rounded-md bg-red-50 p-4">
            <div class="flex">
              <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                </svg>
              </div>
              <div class="ml-3">
                <h3 class="text-sm font-medium text-red-800">
                  There were <%= pluralize(@user.errors.count, "error") %> with your submission:
                </h3>
                <div class="mt-2 text-sm text-red-700">
                  <ul class="list-disc pl-5 space-y-1">
                    <% @user.errors.full_messages.each do |message| %>
                      <li><%= message %></li>
                    <% end %>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        <% end %>

        <!-- Basic Information -->
        <div class="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-2">
          <div>
            <%= form.label :email, class: "block text-sm font-medium text-stone-700" %>
            <%= form.email_field :email, 
                class: "mt-1 block w-full border-stone-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm" %>
          </div>

          <div>
            <%= form.label :first_name, class: "block text-sm font-medium text-stone-700" %>
            <%= form.text_field :first_name, 
                class: "mt-1 block w-full border-stone-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm" %>
          </div>

          <div>
            <%= form.label :last_name, class: "block text-sm font-medium text-stone-700" %>
            <%= form.text_field :last_name, 
                class: "mt-1 block w-full border-stone-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm" %>
          </div>

          <div>
            <%= form.label :time_zone, class: "block text-sm font-medium text-stone-700" %>
            <%= form.select :time_zone, 
                options_for_select(ActiveSupport::TimeZone.all.map { |tz| [tz.to_s, tz.name] }, @user.time_zone),
                { include_blank: "Select time zone" },
                { class: "mt-1 block w-full border-stone-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm" } %>
          </div>
        </div>

        <!-- Status Fields -->
        <div class="space-y-4">
          <h3 class="text-lg font-medium text-stone-900">Status & Settings</h3>
          
          <div class="space-y-4">
            <div class="flex items-center">
              <%= form.check_box :verified, 
                  class: "h-4 w-4 text-blue-600 focus:ring-blue-500 border-stone-300 rounded" %>
              <%= form.label :verified, "Email verified", class: "ml-2 block text-sm text-stone-900" %>
            </div>

            <div class="flex items-center">
              <%= form.check_box :onboarding_completed, 
                  class: "h-4 w-4 text-blue-600 focus:ring-blue-500 border-stone-300 rounded" %>
              <%= form.label :onboarding_completed, "Onboarding completed", class: "ml-2 block text-sm text-stone-900" %>
            </div>

            <div class="flex items-center">
              <%= form.check_box :scout_signup_completed, 
                  class: "h-4 w-4 text-blue-600 focus:ring-blue-500 border-stone-300 rounded" %>
              <%= form.label :scout_signup_completed, "Scout signup completed", class: "ml-2 block text-sm text-stone-900" %>
            </div>

            <div class="flex items-center">
              <%= form.check_box :talent_signup_completed, 
                  class: "h-4 w-4 text-blue-600 focus:ring-blue-500 border-stone-300 rounded" %>
              <%= form.label :talent_signup_completed, "Talent signup completed", class: "ml-2 block text-sm text-stone-900" %>
            </div>
          </div>
        </div>

        <!-- Onboarding Step -->
        <div>
          <%= form.label :onboarding_step, class: "block text-sm font-medium text-stone-700" %>
          <%= form.select :onboarding_step, 
              options_for_select([
                ['Personal', 'personal'],
                ['Organization', 'organization'],
                ['Complete', 'complete']
              ], @user.onboarding_step),
              { include_blank: "Select step" },
              { class: "mt-1 block w-full border-stone-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm" } %>
        </div>

        <!-- Signup Intent -->
        <div>
          <%= form.label :signup_intent, class: "block text-sm font-medium text-stone-700" %>
          <%= form.select :signup_intent, 
              options_for_select([
                ['Scout', 'scout'],
                ['Talent', 'talent'],
                ['Both', 'both']
              ], @user.signup_intent),
              { include_blank: "Select intent" },
              { class: "mt-1 block w-full border-stone-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm" } %>
        </div>
      </div>

      <!-- Form Actions -->
      <div class="px-6 py-4 bg-stone-50 border-t border-stone-200 flex justify-between">
        <div>
          <%= link_to "Cancel", super_admin_admin_user_path(@user), 
              class: "inline-flex items-center px-4 py-2 border border-stone-300 rounded-md shadow-sm text-sm font-medium text-stone-700 bg-white hover:bg-stone-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500" %>
        </div>
        <div class="flex space-x-3">
          <%= link_to "View User", super_admin_admin_user_path(@user), 
              class: "inline-flex items-center px-4 py-2 border border-stone-300 rounded-md shadow-sm text-sm font-medium text-stone-700 bg-white hover:bg-stone-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500" %>
          <%= form.submit "Update User", 
              class: "inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500" %>
        </div>
      </div>
    <% end %>
  </div>
</div>
