<% content_for :title, "User: #{@user.name}" %>

<div class="max-w-7xl mx-auto">
  <!-- Header -->
  <div class="mb-8">
    <div class="flex items-center justify-between">
      <div>
        <nav class="flex" aria-label="Breadcrumb">
          <ol class="flex items-center space-x-4">
            <li>
              <%= link_to "Users", super_admin_admin_users_path, class: "text-stone-500 hover:text-stone-700" %>
            </li>
            <li>
              <div class="flex items-center">
                <svg class="flex-shrink-0 h-5 w-5 text-stone-400" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                </svg>
                <span class="ml-4 text-sm font-medium text-stone-900"><%= @user.name %></span>
              </div>
            </li>
          </ol>
        </nav>
        <h1 class="mt-2 text-3xl font-bold text-stone-900"><%= @user.name %></h1>
        <p class="mt-1 text-stone-600"><%= @user.email %></p>
      </div>
      <div class="flex space-x-3">
        <%= link_to "Edit User", edit_super_admin_admin_user_path(@user), 
            class: "inline-flex items-center px-4 py-2 border border-stone-300 rounded-md shadow-sm text-sm font-medium text-stone-700 bg-white hover:bg-stone-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500" %>
      </div>
    </div>
  </div>

  <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
    <!-- Main Content -->
    <div class="lg:col-span-2 space-y-8">
      <!-- User Details -->
      <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-stone-200">
          <h2 class="text-lg font-medium text-stone-900">User Details</h2>
        </div>
        <div class="px-6 py-4">
          <dl class="grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2">
            <div>
              <dt class="text-sm font-medium text-stone-500">User ID</dt>
              <dd class="mt-1 text-sm text-stone-900"><%= @user.id %></dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-stone-500">Email</dt>
              <dd class="mt-1 text-sm text-stone-900"><%= @user.email %></dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-stone-500">First Name</dt>
              <dd class="mt-1 text-sm text-stone-900"><%= @user.first_name || "Not provided" %></dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-stone-500">Last Name</dt>
              <dd class="mt-1 text-sm text-stone-900"><%= @user.last_name || "Not provided" %></dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-stone-500">Verified</dt>
              <dd class="mt-1">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <%= @user.verified? ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700' %>">
                  <%= @user.verified? ? 'Verified' : 'Unverified' %>
                </span>
              </dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-stone-500">Onboarding Status</dt>
              <dd class="mt-1">
                <% if @user.onboarding_completed? %>
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-700">
                    Complete
                  </span>
                <% else %>
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-700">
                    <%= @user.onboarding_step&.humanize || "Not started" %>
                  </span>
                <% end %>
              </dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-stone-500">Signup Intent</dt>
              <dd class="mt-1 text-sm text-stone-900"><%= @user.signup_intent&.humanize || "Not specified" %></dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-stone-500">Time Zone</dt>
              <dd class="mt-1 text-sm text-stone-900"><%= @user.time_zone || "Not set" %></dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-stone-500">Created</dt>
              <dd class="mt-1 text-sm text-stone-900"><%= @user.created_at.strftime('%B %d, %Y at %I:%M %p') %></dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-stone-500">Last Updated</dt>
              <dd class="mt-1 text-sm text-stone-900"><%= @user.updated_at.strftime('%B %d, %Y at %I:%M %p') %></dd>
            </div>
          </dl>
        </div>
      </div>

      <!-- Roles -->
      <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-stone-200">
          <h2 class="text-lg font-medium text-stone-900">Roles</h2>
        </div>
        <div class="px-6 py-4">
          <% if @user.roles.any? %>
            <div class="flex flex-wrap gap-2">
              <% @user.roles.each do |role| %>
                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                  <%= role.name.humanize %>
                </span>
              <% end %>
            </div>
          <% else %>
            <p class="text-stone-500">No roles assigned</p>
          <% end %>
        </div>
      </div>

      <!-- Organizations -->
      <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-stone-200">
          <h2 class="text-lg font-medium text-stone-900">Organizations</h2>
        </div>
        <div class="px-6 py-4">
          <% if @organizations.any? %>
            <div class="space-y-4">
              <% @organizations.each do |org| %>
                <div class="border border-stone-200 rounded-lg p-4">
                  <div class="flex items-center justify-between">
                    <div>
                      <h3 class="text-sm font-medium text-stone-900"><%= org.name %></h3>
                      <p class="text-sm text-stone-500"><%= pluralize(org.jobs.count, 'job') %></p>
                    </div>
                    <%= link_to "View", super_admin_admin_organization_path(org), 
                        class: "text-blue-600 hover:text-blue-900 text-sm" %>
                  </div>
                </div>
              <% end %>
            </div>
          <% else %>
            <p class="text-stone-500">No organizations</p>
          <% end %>
        </div>
      </div>
    </div>

    <!-- Sidebar -->
    <div class="space-y-8">
      <!-- Avatar -->
      <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-stone-200">
          <h2 class="text-lg font-medium text-stone-900">Avatar</h2>
        </div>
        <div class="px-6 py-4 text-center">
          <% if @user.avatar.attached? %>
            <%= image_tag @user.avatar, class: "mx-auto h-24 w-24 rounded-full object-cover" %>
          <% else %>
            <div class="mx-auto h-24 w-24 rounded-full bg-stone-300 flex items-center justify-center">
              <span class="text-xl font-medium text-stone-700"><%= @user.initials %></span>
            </div>
          <% end %>
        </div>
      </div>

      <!-- Quick Stats -->
      <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-stone-200">
          <h2 class="text-lg font-medium text-stone-900">Quick Stats</h2>
        </div>
        <div class="px-6 py-4">
          <dl class="space-y-4">
            <div>
              <dt class="text-sm font-medium text-stone-500">Organizations</dt>
              <dd class="text-2xl font-semibold text-stone-900"><%= @organizations.count %></dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-stone-500">Chat Requests Sent</dt>
              <dd class="text-2xl font-semibold text-stone-900"><%= @chat_requests_sent.count %></dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-stone-500">Chat Requests Received</dt>
              <dd class="text-2xl font-semibold text-stone-900"><%= @chat_requests_received.count %></dd>
            </div>
          </dl>
        </div>
      </div>

      <!-- Recent Activity -->
      <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-stone-200">
          <h2 class="text-lg font-medium text-stone-900">Recent Chat Requests</h2>
        </div>
        <div class="px-6 py-4">
          <% recent_requests = (@chat_requests_sent + @chat_requests_received).sort_by(&:created_at).reverse.first(5) %>
          <% if recent_requests.any? %>
            <div class="space-y-3">
              <% recent_requests.each do |request| %>
                <div class="text-sm">
                  <div class="flex items-center justify-between">
                    <span class="text-stone-900">
                      <%= request.scout == @user ? "Sent to #{request.talent.name}" : "Received from #{request.scout.name}" %>
                    </span>
                    <span class="text-stone-500"><%= time_ago_in_words(request.created_at) %> ago</span>
                  </div>
                  <div class="text-stone-500">
                    <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium <%= request.status == 'accepted' ? 'bg-green-100 text-green-800' : request.status == 'declined' ? 'bg-red-100 text-red-800' : 'bg-yellow-100 text-yellow-800' %>">
                      <%= request.status.humanize %>
                    </span>
                  </div>
                </div>
              <% end %>
            </div>
          <% else %>
            <p class="text-stone-500">No recent chat requests</p>
          <% end %>
        </div>
      </div>
    </div>
  </div>
</div>
