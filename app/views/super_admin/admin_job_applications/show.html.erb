<% content_for :title, "Job Application ##{@job_application.id}" %>

<div class="max-w-7xl mx-auto">
  <!-- Header -->
  <div class="mb-8">
    <div class="flex items-center justify-between">
      <div>
        <nav class="flex" aria-label="Breadcrumb">
          <ol class="flex items-center space-x-4">
            <li>
              <%= link_to "Job Applications", super_admin_admin_job_applications_path, class: "text-stone-500 hover:text-stone-700" %>
            </li>
            <li>
              <div class="flex items-center">
                <svg class="flex-shrink-0 h-5 w-5 text-stone-400" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                </svg>
                <span class="ml-4 text-sm font-medium text-stone-900">Application #<%= @job_application.id %></span>
              </div>
            </li>
          </ol>
        </nav>
        <h1 class="mt-2 text-3xl font-bold text-stone-900">Job Application #<%= @job_application.id %></h1>
        <p class="mt-1 text-stone-600">Application details and management</p>
      </div>
      <div class="flex space-x-3">
        <%= link_to "View Job", super_admin_admin_job_path(@job_application.job), 
            class: "inline-flex items-center px-4 py-2 border border-stone-300 text-sm font-medium rounded-md text-stone-700 bg-white hover:bg-stone-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500" %>
        <%= link_to "View Applicant", super_admin_admin_user_path(@job_application.user), 
            class: "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500" %>
      </div>
    </div>
  </div>

  <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
    <!-- Main Content -->
    <div class="lg:col-span-2 space-y-8">
      <!-- Application Overview -->
      <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-stone-200">
          <h2 class="text-lg font-medium text-stone-900">Application Overview</h2>
        </div>
        <div class="px-6 py-4">
          <dl class="grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2">
            <div>
              <dt class="text-sm font-medium text-stone-500">Application ID</dt>
              <dd class="mt-1 text-sm text-stone-900"><%= @job_application.id %></dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-stone-500">Status</dt>
              <dd class="mt-1">
                <% status_class = case @job_application.status
                                  when 'applied' then 'bg-blue-100 text-blue-800'
                                  when 'reviewed' then 'bg-yellow-100 text-yellow-800'
                                  when 'qualified' then 'bg-green-100 text-green-800'
                                  when 'offered' then 'bg-purple-100 text-purple-800'
                                  when 'accepted' then 'bg-emerald-100 text-emerald-800'
                                  when 'withdrawn' then 'bg-red-100 text-red-800'
                                  else 'bg-stone-100 text-stone-800'
                                  end %>
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <%= status_class %>">
                  <%= @job_application.status.humanize %>
                </span>
              </dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-stone-500">Applied At</dt>
              <dd class="mt-1 text-sm text-stone-900">
                <%= @job_application.applied_at&.strftime('%B %d, %Y at %I:%M %p') || "Not set" %>
              </dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-stone-500">Invitation Status</dt>
              <dd class="mt-1">
                <% if @job_application.invited? %>
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                    Invited Application
                  </span>
                <% else %>
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-stone-100 text-stone-800">
                    Direct Application
                  </span>
                <% end %>
              </dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-stone-500">Salary Considered</dt>
              <dd class="mt-1">
                <% if @job_application.salary_considered? %>
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    Yes
                  </span>
                <% else %>
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-stone-100 text-stone-800">
                    No
                  </span>
                <% end %>
              </dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-stone-500">Created At</dt>
              <dd class="mt-1 text-sm text-stone-900"><%= @job_application.created_at.strftime('%B %d, %Y at %I:%M %p') %></dd>
            </div>
            <% if @job_application.accepted_at.present? %>
              <div>
                <dt class="text-sm font-medium text-stone-500">Accepted At</dt>
                <dd class="mt-1 text-sm text-stone-900"><%= @job_application.accepted_at.strftime('%B %d, %Y at %I:%M %p') %></dd>
              </div>
            <% end %>
            <% if @job_application.rejected_at.present? %>
              <div>
                <dt class="text-sm font-medium text-stone-500">Rejected At</dt>
                <dd class="mt-1 text-sm text-stone-900"><%= @job_application.rejected_at.strftime('%B %d, %Y at %I:%M %p') %></dd>
              </div>
            <% end %>
          </dl>
        </div>
      </div>

      <!-- Job Information -->
      <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-stone-200">
          <h2 class="text-lg font-medium text-stone-900">Job Information</h2>
        </div>
        <div class="px-6 py-4">
          <div class="flex items-start space-x-4">
            <div class="flex-1">
              <h3 class="text-lg font-medium text-stone-900">
                <%= link_to @job_application.job.title, super_admin_admin_job_path(@job_application.job), 
                    class: "text-blue-600 hover:text-blue-900" %>
              </h3>
              <p class="mt-1 text-sm text-stone-500">
                <%= @job_application.job.job_category.humanize %> • 
                <%= @job_application.job.status.humanize %>
              </p>
              <% if @job_application.job.description.present? %>
                <div class="mt-3 prose prose-sm max-w-none">
                  <%= truncate(strip_tags(@job_application.job.description), length: 200) %>
                </div>
              <% end %>
            </div>
          </div>
        </div>
      </div>

      <!-- Applicant Information -->
      <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-stone-200">
          <h2 class="text-lg font-medium text-stone-900">Applicant Information</h2>
        </div>
        <div class="px-6 py-4">
          <div class="flex items-start space-x-4">
            <div class="flex-shrink-0">
              <% if @job_application.user.avatar.attached? %>
                <%= image_tag @job_application.user.avatar, class: "h-12 w-12 rounded-full object-cover" %>
              <% else %>
                <div class="h-12 w-12 rounded-full bg-stone-300 flex items-center justify-center">
                  <span class="text-sm font-medium text-stone-700"><%= @job_application.user.initials %></span>
                </div>
              <% end %>
            </div>
            <div class="flex-1">
              <h3 class="text-lg font-medium text-stone-900">
                <%= link_to @job_application.user.name.full, super_admin_admin_user_path(@job_application.user),
                    class: "text-blue-600 hover:text-blue-900" %>
              </h3>
              <p class="mt-1 text-sm text-stone-500"><%= @job_application.user.email %></p>
              <% if @job_application.user.talent_profile.present? %>
                <p class="mt-1 text-sm text-stone-500">
                  Talent Profile: <%= @job_application.user.talent_profile.headline || "No headline" %>
                </p>
              <% end %>
            </div>
          </div>
        </div>
      </div>

      <!-- Application Letter -->
      <% if @job_application.application_letter.present? %>
        <div class="bg-white shadow rounded-lg">
          <div class="px-6 py-4 border-b border-stone-200">
            <h2 class="text-lg font-medium text-stone-900">Application Letter</h2>
          </div>
          <div class="px-6 py-4">
            <div class="prose max-w-none">
              <%= simple_format(@job_application.application_letter, class: "text-stone-700") %>
            </div>
          </div>
        </div>
      <% end %>

      <!-- Additional Information -->
      <% if @job_application.additional_info.present? %>
        <div class="bg-white shadow rounded-lg">
          <div class="px-6 py-4 border-b border-stone-200">
            <h2 class="text-lg font-medium text-stone-900">Additional Information</h2>
          </div>
          <div class="px-6 py-4">
            <div class="prose max-w-none">
              <%= simple_format(@job_application.additional_info, class: "text-stone-700") %>
            </div>
          </div>
        </div>
      <% end %>

      <!-- Attachments -->
      <% if @job_application.resume.attached? || @job_application.documents.any? %>
        <div class="bg-white shadow rounded-lg">
          <div class="px-6 py-4 border-b border-stone-200">
            <h2 class="text-lg font-medium text-stone-900">Attachments</h2>
          </div>
          <div class="px-6 py-4">
            <div class="space-y-4">
              <% if @job_application.resume.attached? %>
                <div class="flex items-center justify-between p-3 border border-stone-200 rounded-lg">
                  <div class="flex items-center">
                    <svg class="h-8 w-8 text-red-500" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clip-rule="evenodd" />
                    </svg>
                    <div class="ml-3">
                      <p class="text-sm font-medium text-stone-900">Resume</p>
                      <p class="text-sm text-stone-500"><%= @job_application.resume.filename %></p>
                    </div>
                  </div>
                  <%= link_to "Download", rails_blob_path(@job_application.resume, disposition: "attachment"),
                      class: "text-blue-600 hover:text-blue-900 text-sm font-medium" %>
                </div>
              <% end %>

              <% @job_application.documents.each do |document| %>
                <div class="flex items-center justify-between p-3 border border-stone-200 rounded-lg">
                  <div class="flex items-center">
                    <svg class="h-8 w-8 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clip-rule="evenodd" />
                    </svg>
                    <div class="ml-3">
                      <p class="text-sm font-medium text-stone-900">Document</p>
                      <p class="text-sm text-stone-500"><%= document.filename %></p>
                    </div>
                  </div>
                  <%= link_to "Download", rails_blob_path(document, disposition: "attachment"),
                      class: "text-blue-600 hover:text-blue-900 text-sm font-medium" %>
                </div>
              <% end %>
            </div>
          </div>
        </div>
      <% end %>
    </div>

    <!-- Sidebar -->
    <div class="space-y-8">
      <!-- Quick Actions -->
      <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-stone-200">
          <h2 class="text-lg font-medium text-stone-900">Quick Actions</h2>
        </div>
        <div class="px-6 py-4 space-y-3">
          <%= link_to "View Full Job", super_admin_admin_job_path(@job_application.job), 
              class: "block w-full text-center px-4 py-2 border border-stone-300 text-sm font-medium rounded-md text-stone-700 bg-white hover:bg-stone-50" %>
          <%= link_to "View Applicant Profile", super_admin_admin_user_path(@job_application.user), 
              class: "block w-full text-center px-4 py-2 border border-stone-300 text-sm font-medium rounded-md text-stone-700 bg-white hover:bg-stone-50" %>
          <% if @job_application.job_invitation.present? %>
            <%= link_to "View Job Invitation", super_admin_admin_job_invitation_path(@job_application.job_invitation), 
                class: "block w-full text-center px-4 py-2 border border-stone-300 text-sm font-medium rounded-md text-stone-700 bg-white hover:bg-stone-50" %>
          <% end %>
        </div>
      </div>

      <!-- Application Timeline -->
      <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-stone-200">
          <h2 class="text-lg font-medium text-stone-900">Timeline</h2>
        </div>
        <div class="px-6 py-4">
          <div class="flow-root">
            <ul class="-mb-8">
              <li>
                <div class="relative pb-8">
                  <div class="relative flex space-x-3">
                    <div>
                      <span class="h-8 w-8 rounded-full bg-blue-500 flex items-center justify-center ring-8 ring-white">
                        <svg class="h-5 w-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                          <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                        </svg>
                      </span>
                    </div>
                    <div class="min-w-0 flex-1 pt-1.5">
                      <div>
                        <p class="text-sm text-stone-500">Application created</p>
                        <p class="text-xs text-stone-400"><%= @job_application.created_at.strftime('%B %d, %Y at %I:%M %p') %></p>
                      </div>
                    </div>
                  </div>
                </div>
              </li>
              
              <% if @job_application.applied_at.present? && @job_application.applied_at != @job_application.created_at %>
                <li>
                  <div class="relative pb-8">
                    <div class="relative flex space-x-3">
                      <div>
                        <span class="h-8 w-8 rounded-full bg-green-500 flex items-center justify-center ring-8 ring-white">
                          <svg class="h-5 w-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                          </svg>
                        </span>
                      </div>
                      <div class="min-w-0 flex-1 pt-1.5">
                        <div>
                          <p class="text-sm text-stone-500">Application submitted</p>
                          <p class="text-xs text-stone-400"><%= @job_application.applied_at.strftime('%B %d, %Y at %I:%M %p') %></p>
                        </div>
                      </div>
                    </div>
                  </div>
                </li>
              <% end %>

              <% if @job_application.accepted_at.present? %>
                <li>
                  <div class="relative">
                    <div class="relative flex space-x-3">
                      <div>
                        <span class="h-8 w-8 rounded-full bg-emerald-500 flex items-center justify-center ring-8 ring-white">
                          <svg class="h-5 w-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                          </svg>
                        </span>
                      </div>
                      <div class="min-w-0 flex-1 pt-1.5">
                        <div>
                          <p class="text-sm text-stone-500">Application accepted</p>
                          <p class="text-xs text-stone-400"><%= @job_application.accepted_at.strftime('%B %d, %Y at %I:%M %p') %></p>
                        </div>
                      </div>
                    </div>
                  </div>
                </li>
              <% elsif @job_application.rejected_at.present? %>
                <li>
                  <div class="relative">
                    <div class="relative flex space-x-3">
                      <div>
                        <span class="h-8 w-8 rounded-full bg-red-500 flex items-center justify-center ring-8 ring-white">
                          <svg class="h-5 w-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                          </svg>
                        </span>
                      </div>
                      <div class="min-w-0 flex-1 pt-1.5">
                        <div>
                          <p class="text-sm text-stone-500">Application rejected</p>
                          <p class="text-xs text-stone-400"><%= @job_application.rejected_at.strftime('%B %d, %Y at %I:%M %p') %></p>
                        </div>
                      </div>
                    </div>
                  </div>
                </li>
              <% end %>
            </ul>
          </div>
        </div>
      </div>

      <!-- Related Applications -->
      <% if @related_applications.any? %>
        <div class="bg-white shadow rounded-lg">
          <div class="px-6 py-4 border-b border-stone-200">
            <h2 class="text-lg font-medium text-stone-900">Other Applications by This User</h2>
          </div>
          <div class="px-6 py-4">
            <div class="space-y-3">
              <% @related_applications.each do |application| %>
                <div class="flex items-center justify-between">
                  <div>
                    <p class="text-sm font-medium text-stone-900">
                      <%= link_to truncate(application.job.title, length: 30), 
                          super_admin_admin_job_application_path(application), 
                          class: "text-blue-600 hover:text-blue-900" %>
                    </p>
                    <p class="text-xs text-stone-500">
                      <%= application.status.humanize %> • 
                      <%= time_ago_in_words(application.created_at) %> ago
                    </p>
                  </div>
                </div>
              <% end %>
            </div>
          </div>
        </div>
      <% end %>
    </div>
  </div>
</div>
