<% content_for :title, "Saved Job ##{@saved_job.id}" %>

<div class="bg-white rounded-lg shadow">
  <!-- Header -->
  <div class="px-6 py-4 border-b border-stone-200">
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-xl font-semibold text-stone-900">Saved Job #<%= @saved_job.id %></h1>
        <p class="text-sm text-stone-500 mt-1">View saved job details and engagement</p>
      </div>
      <div class="flex items-center space-x-3">
        <%= link_to "Back to List", super_admin_admin_saved_jobs_path, 
            class: "inline-flex items-center px-3 py-2 border border-stone-300 rounded-md text-sm font-medium text-stone-700 bg-white hover:bg-stone-50" %>
      </div>
    </div>
  </div>

  <div class="p-6">
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
      <!-- Left Column: User Details -->
      <div class="space-y-6">
        <!-- User Information -->
        <div>
          <h3 class="text-lg font-medium text-stone-900 mb-4">User Details</h3>
          <div class="bg-stone-50 rounded-lg p-4">
            <div class="space-y-4">
              <div class="flex items-center space-x-3">
                <% if @saved_job.user.avatar.attached? %>
                  <%= image_tag @saved_job.user.avatar, class: "h-12 w-12 rounded-full object-cover" %>
                <% else %>
                  <div class="h-12 w-12 rounded-full bg-stone-300 flex items-center justify-center">
                    <span class="text-stone-600 font-medium text-lg">
                      <%= @saved_job.user.first_name&.first&.upcase %>
                    </span>
                  </div>
                <% end %>
                <div>
                  <h4 class="font-medium text-stone-900"><%= @saved_job.user.name.full %></h4>
                  <p class="text-sm text-stone-600"><%= @saved_job.user.email %></p>
                </div>
              </div>
              
              <div class="space-y-2 text-sm">
                <div class="flex justify-between">
                  <span class="text-stone-600">User ID:</span>
                  <span class="text-stone-900 font-mono"><%= @saved_job.user.id %></span>
                </div>
                
                <div class="flex justify-between">
                  <span class="text-stone-600">Account Created:</span>
                  <span class="text-stone-900"><%= @saved_job.user.created_at.strftime('%B %d, %Y') %></span>
                </div>
                
                <div class="flex justify-between">
                  <span class="text-stone-600">Verified:</span>
                  <span class="text-stone-900">
                    <% if @saved_job.user.verified? %>
                      <span class="text-green-600">✓ Verified</span>
                    <% else %>
                      <span class="text-red-600">✗ Not Verified</span>
                    <% end %>
                  </span>
                </div>
                
                <div class="flex justify-between">
                  <span class="text-stone-600">Total Saved Jobs:</span>
                  <span class="text-stone-900"><%= @saved_job.user.saved_jobs.count %></span>
                </div>
                
                <div class="flex justify-between">
                  <span class="text-stone-600">Job Applications:</span>
                  <span class="text-stone-900"><%= @saved_job.user.job_applications.count %></span>
                </div>
              </div>
              
              <div class="mt-3">
                <%= link_to "View User Profile", super_admin_admin_user_path(@saved_job.user), 
                    class: "inline-flex items-center px-3 py-1 border border-stone-300 rounded-md text-sm font-medium text-stone-700 bg-white hover:bg-stone-50" %>
              </div>
            </div>
          </div>
        </div>

        <!-- User's Other Saved Jobs -->
        <div>
          <h3 class="text-lg font-medium text-stone-900 mb-4">User's Other Saved Jobs</h3>
          <div class="bg-stone-50 rounded-lg p-4">
            <% other_saved_jobs = @saved_job.user.saved_jobs.where.not(id: @saved_job.id).includes(:job).limit(5) %>
            <% if other_saved_jobs.any? %>
              <div class="space-y-3">
                <% other_saved_jobs.each do |saved_job| %>
                  <div class="flex items-center justify-between">
                    <div class="flex-1">
                      <div class="text-sm font-medium text-stone-900 truncate">
                        <%= saved_job.job.title %>
                      </div>
                      <div class="text-xs text-stone-500">
                        <%= saved_job.job.job_category&.humanize %> • <%= saved_job.job.organization.name %>
                      </div>
                    </div>
                    <span class="text-xs text-stone-500 ml-2">
                      <%= time_ago_in_words(saved_job.created_at) %> ago
                    </span>
                  </div>
                <% end %>
                
                <% if @saved_job.user.saved_jobs.count > 6 %>
                  <div class="text-sm text-stone-500 text-center pt-2 border-t border-stone-200">
                    +<%= @saved_job.user.saved_jobs.count - 6 %> more saved jobs
                  </div>
                <% end %>
              </div>
            <% else %>
              <p class="text-sm text-stone-500 italic">This is the user's only saved job.</p>
            <% end %>
          </div>
        </div>

        <!-- User Engagement Stats -->
        <div>
          <h3 class="text-lg font-medium text-stone-900 mb-4">User Engagement</h3>
          <div class="bg-stone-50 rounded-lg p-4">
            <div class="grid grid-cols-2 gap-4">
              <div class="text-center">
                <div class="text-2xl font-bold text-blue-600"><%= @saved_job.user.saved_jobs.count %></div>
                <div class="text-sm text-stone-500">Jobs Saved</div>
              </div>
              <div class="text-center">
                <div class="text-2xl font-bold text-green-600"><%= @saved_job.user.job_applications.count %></div>
                <div class="text-sm text-stone-500">Applications</div>
              </div>
            </div>
            
            <% if @saved_job.user.job_applications.exists?(job: @saved_job.job) %>
              <div class="mt-4 p-3 bg-green-100 rounded-lg">
                <div class="flex items-center">
                  <svg class="w-5 h-5 text-green-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                  </svg>
                  <span class="text-sm font-medium text-green-800">User applied to this job</span>
                </div>
              </div>
            <% end %>
          </div>
        </div>
      </div>

      <!-- Right Column: Job Details -->
      <div class="space-y-6">
        <!-- Job Information -->
        <div>
          <h3 class="text-lg font-medium text-stone-900 mb-4">Job Details</h3>
          <div class="bg-stone-50 rounded-lg p-4">
            <div class="space-y-4">
              <div>
                <h4 class="font-medium text-stone-900 text-lg"><%= @saved_job.job.title %></h4>
                <p class="text-sm text-stone-600"><%= @saved_job.job.organization.name %></p>
              </div>
              
              <div class="space-y-2 text-sm">
                <div class="flex justify-between">
                  <span class="text-stone-600">Job ID:</span>
                  <span class="text-stone-900 font-mono"><%= @saved_job.job.id %></span>
                </div>
                
                <div class="flex justify-between">
                  <span class="text-stone-600">Category:</span>
                  <span class="text-stone-900"><%= @saved_job.job.job_category&.humanize || 'Not specified' %></span>
                </div>
                
                <div class="flex justify-between">
                  <span class="text-stone-600">Status:</span>
                  <span class="text-stone-900">
                    <% case @saved_job.job.status %>
                    <% when 'published' %>
                      <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">Published</span>
                    <% when 'draft' %>
                      <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">Draft</span>
                    <% when 'expired' %>
                      <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">Expired</span>
                    <% end %>
                  </span>
                </div>
                
                <% if @saved_job.job.budget_range.present? %>
                  <div class="flex justify-between">
                    <span class="text-stone-600">Budget:</span>
                    <span class="text-stone-900"><%= @saved_job.job.budget_range.humanize %></span>
                  </div>
                <% end %>
                
                <% if @saved_job.job.platform.present? %>
                  <div class="flex justify-between">
                    <span class="text-stone-600">Platform:</span>
                    <span class="text-stone-900"><%= @saved_job.job.platform.humanize %></span>
                  </div>
                <% end %>
                
                <div class="flex justify-between">
                  <span class="text-stone-600">Created:</span>
                  <span class="text-stone-900"><%= @saved_job.job.created_at.strftime('%B %d, %Y') %></span>
                </div>
                
                <% if @saved_job.job.application_deadline.present? %>
                  <div class="flex justify-between">
                    <span class="text-stone-600">Application Deadline:</span>
                    <span class="text-stone-900"><%= @saved_job.job.application_deadline.strftime('%B %d, %Y') %></span>
                  </div>
                <% end %>
                
                <div class="flex justify-between">
                  <span class="text-stone-600">Total Saves:</span>
                  <span class="text-stone-900"><%= @saved_job.job.saved_jobs.count %></span>
                </div>
                
                <div class="flex justify-between">
                  <span class="text-stone-600">Applications:</span>
                  <span class="text-stone-900"><%= @saved_job.job.job_applications.count %></span>
                </div>
              </div>
              
              <% if @saved_job.job.description.present? %>
                <div>
                  <span class="text-sm font-medium text-stone-700">Description:</span>
                  <div class="mt-1 text-sm text-stone-600 max-h-32 overflow-y-auto">
                    <%= simple_format(@saved_job.job.description.truncate(300)) %>
                  </div>
                </div>
              <% end %>
              
              <div class="mt-3">
                <%= link_to "View Job Details", super_admin_admin_job_path(@saved_job.job), 
                    class: "inline-flex items-center px-3 py-1 border border-stone-300 rounded-md text-sm font-medium text-stone-700 bg-white hover:bg-stone-50" %>
              </div>
            </div>
          </div>
        </div>

        <!-- Job Popularity -->
        <div>
          <h3 class="text-lg font-medium text-stone-900 mb-4">Job Popularity</h3>
          <div class="bg-stone-50 rounded-lg p-4">
            <div class="grid grid-cols-2 gap-4">
              <div class="text-center">
                <div class="text-2xl font-bold text-purple-600"><%= @saved_job.job.saved_jobs.count %></div>
                <div class="text-sm text-stone-500">Total Saves</div>
              </div>
              <div class="text-center">
                <div class="text-2xl font-bold text-orange-600"><%= @saved_job.job.job_applications.count %></div>
                <div class="text-sm text-stone-500">Applications</div>
              </div>
            </div>
            
            <% if @saved_job.job.saved_jobs.count > 0 %>
              <div class="mt-4">
                <% conversion_rate = (@saved_job.job.job_applications.count.to_f / @saved_job.job.saved_jobs.count * 100).round(1) %>
                <div class="text-center">
                  <div class="text-lg font-bold text-blue-600"><%= conversion_rate %>%</div>
                  <div class="text-sm text-stone-500">Save-to-Apply Rate</div>
                </div>
              </div>
            <% end %>
          </div>
        </div>

        <!-- Save Timeline -->
        <div>
          <h3 class="text-lg font-medium text-stone-900 mb-4">Save Timeline</h3>
          <div class="bg-stone-50 rounded-lg p-4">
            <div class="space-y-3">
              <div class="flex items-center space-x-3">
                <div class="flex-shrink-0 w-2 h-2 bg-blue-500 rounded-full"></div>
                <div class="text-sm">
                  <span class="font-medium text-stone-900">Job Saved</span>
                  <span class="text-stone-500 ml-2"><%= @saved_job.created_at.strftime('%B %d, %Y at %I:%M %p') %></span>
                </div>
              </div>
              
              <div class="text-sm text-stone-500 ml-5">
                <%= @saved_job.user.name.full %> saved this job
                <%= time_ago_in_words(@saved_job.created_at) %> ago
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
