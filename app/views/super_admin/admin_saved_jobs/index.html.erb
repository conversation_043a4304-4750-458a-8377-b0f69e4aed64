<% content_for :title, "Saved Jobs" %>

<div class="bg-white rounded-lg shadow">
  <!-- Header -->
  <div class="px-6 py-4 border-b border-stone-200">
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-xl font-semibold text-stone-900">Saved Jobs</h1>
        <p class="text-sm text-stone-500 mt-1">Monitor talent job saving activity and engagement metrics</p>
      </div>
      <div class="flex items-center space-x-3">
        <%= link_to "Export CSV", super_admin_admin_saved_jobs_path(format: :csv, **request.query_parameters), 
            class: "inline-flex items-center px-3 py-2 border border-stone-300 rounded-md text-sm font-medium text-stone-700 bg-white hover:bg-stone-50" %>
      </div>
    </div>
  </div>

  <!-- Stats Cards -->
  <div class="px-6 py-4 border-b border-stone-200 bg-stone-50">
    <div class="grid grid-cols-1 md:grid-cols-6 gap-4">
      <div class="bg-white p-4 rounded-lg border border-stone-200">
        <div class="text-2xl font-bold text-stone-900"><%= @stats[:total] %></div>
        <div class="text-sm text-stone-500">Total Saves</div>
      </div>
      <div class="bg-white p-4 rounded-lg border border-stone-200">
        <div class="text-2xl font-bold text-blue-600"><%= @stats[:unique_users] %></div>
        <div class="text-sm text-stone-500">Active Savers</div>
      </div>
      <div class="bg-white p-4 rounded-lg border border-stone-200">
        <div class="text-2xl font-bold text-green-600"><%= @stats[:unique_jobs] %></div>
        <div class="text-sm text-stone-500">Saved Jobs</div>
      </div>
      <div class="bg-white p-4 rounded-lg border border-stone-200">
        <div class="text-2xl font-bold text-purple-600"><%= @stats[:avg_saves_per_user] %></div>
        <div class="text-sm text-stone-500">Avg per User</div>
      </div>
      <div class="bg-white p-4 rounded-lg border border-stone-200">
        <div class="text-2xl font-bold text-orange-600"><%= @stats[:saves_this_week] %></div>
        <div class="text-sm text-stone-500">This Week</div>
      </div>
      <div class="bg-white p-4 rounded-lg border border-stone-200">
        <div class="text-xs font-bold text-red-600 truncate" title="<%= @stats[:most_saved_job] %>">
          <%= @stats[:most_saved_job] %>
        </div>
        <div class="text-sm text-stone-500">Most Saved</div>
      </div>
    </div>
  </div>

  <!-- Filters -->
  <div class="px-6 py-4 border-b border-stone-200">
    <%= form_with url: super_admin_admin_saved_jobs_path, method: :get, local: true, class: "space-y-4" do |form| %>
      <!-- Search and Actions Row -->
      <div class="flex flex-col sm:flex-row gap-4">
        <div class="flex-1">
          <%= form.text_field :search, placeholder: "Search by user name, email, job title, or organization...", 
              value: params[:search], 
              class: "w-full px-3 py-2 border border-stone-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" %>
        </div>
        <div class="flex gap-2">
          <%= form.submit "Filter", class: "px-4 py-2 bg-blue-600 text-white rounded-md text-sm font-medium hover:bg-blue-700" %>
          <%= link_to "Clear", super_admin_admin_saved_jobs_path, class: "px-4 py-2 border border-stone-300 text-stone-700 rounded-md text-sm font-medium hover:bg-stone-50" %>
        </div>
      </div>

      <!-- Filter Dropdowns Row -->
      <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-4">
        <div>
          <%= form.select :user_id, options_from_collection_for_select([OpenStruct.new(id: 'all', display_name: 'All Users')] + @available_users.map { |u| OpenStruct.new(id: u.id, display_name: u.name.full) }, :id, :display_name, params[:user_id]), 
              {}, { class: "w-full px-3 py-2 border border-stone-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500" } %>
        </div>
        <div>
          <%= form.select :job_id, options_from_collection_for_select([OpenStruct.new(id: 'all', display_name: 'All Jobs')] + @available_jobs.map { |j| OpenStruct.new(id: j.id, display_name: j.title.truncate(30)) }, :id, :display_name, params[:job_id]), 
              {}, { class: "w-full px-3 py-2 border border-stone-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500" } %>
        </div>
        <div>
          <%= form.select :job_category, options_for_select([['All Categories', 'all']] + @job_categories.map { |cat| [cat.humanize, cat] }, params[:job_category]), 
              {}, { class: "w-full px-3 py-2 border border-stone-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500" } %>
        </div>
        <div>
          <%= form.select :job_status, options_for_select([['All Statuses', 'all']] + @job_statuses.map { |status| [status.humanize, status] }, params[:job_status]), 
              {}, { class: "w-full px-3 py-2 border border-stone-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500" } %>
        </div>
        <div>
          <%= form.select :date_range, options_for_select([
                ['All Time', 'all'],
                ['Today', 'today'],
                ['This Week', 'week'],
                ['This Month', 'month']
              ], params[:date_range]), 
              {}, { class: "w-full px-3 py-2 border border-stone-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500" } %>
        </div>
      </div>
    <% end %>
  </div>

  <!-- Bulk Actions -->
  <%= form_with url: bulk_update_super_admin_admin_saved_jobs_path, method: :post, local: true, id: "bulk-form" do |form| %>
    <div class="px-6 py-3 border-b border-stone-200 bg-stone-50">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
          <input type="checkbox" id="select-all" class="rounded border-stone-300 text-blue-600 focus:ring-blue-500">
          <label for="select-all" class="text-sm text-stone-700">Select All</label>
        </div>
        <div class="flex items-center space-x-2">
          <%= form.select :bulk_action, options_for_select([
                ['Delete Selected', 'delete']
              ]), 
              { prompt: 'Bulk Actions' }, 
              { class: "px-3 py-2 border border-stone-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500" } %>
          <%= form.submit "Apply", class: "px-4 py-2 bg-blue-600 text-white rounded-md text-sm font-medium hover:bg-blue-700", 
              onclick: "return confirm('Are you sure you want to apply this action to selected saved jobs?')" %>
        </div>
      </div>
    </div>

    <!-- Table -->
    <div class="overflow-x-auto">
      <% if @saved_jobs.any? %>
        <table class="min-w-full divide-y divide-stone-200">
          <thead class="bg-stone-50">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">
                <input type="checkbox" id="select-all-header" class="rounded border-stone-300 text-blue-600 focus:ring-blue-500">
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">User</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">Job</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">Job Details</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">Organization</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">Saved</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">Actions</th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-stone-200">
            <% @saved_jobs.each do |saved_job| %>
              <tr class="hover:bg-stone-50">
                <td class="px-6 py-4 whitespace-nowrap">
                  <%= check_box_tag "saved_job_ids[]", saved_job.id, false, 
                      class: "saved-job-checkbox rounded border-stone-300 text-blue-600 focus:ring-blue-500" %>
                </td>
                <td class="px-6 py-4">
                  <div class="flex items-center">
                    <% if saved_job.user.avatar.attached? %>
                      <%= image_tag saved_job.user.avatar, class: "h-8 w-8 rounded-full object-cover mr-3" %>
                    <% else %>
                      <div class="h-8 w-8 rounded-full bg-stone-300 flex items-center justify-center mr-3">
                        <span class="text-stone-600 text-xs font-medium">
                          <%= saved_job.user.first_name&.first&.upcase %>
                        </span>
                      </div>
                    <% end %>
                    <div>
                      <div class="text-sm font-medium text-stone-900"><%= saved_job.user.name.full %></div>
                      <div class="text-sm text-stone-500"><%= saved_job.user.email %></div>
                    </div>
                  </div>
                </td>
                <td class="px-6 py-4">
                  <div class="text-sm">
                    <div class="font-medium text-stone-900 truncate max-w-xs" title="<%= saved_job.job.title %>">
                      <%= saved_job.job.title %>
                    </div>
                    <div class="text-stone-500 text-xs">
                      ID: <%= saved_job.job.id %>
                    </div>
                  </div>
                </td>
                <td class="px-6 py-4">
                  <div class="text-sm">
                    <% if saved_job.job.job_category.present? %>
                      <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800 mb-1">
                        <%= saved_job.job.job_category.humanize %>
                      </span>
                    <% end %>
                    <% case saved_job.job.status %>
                    <% when 'published' %>
                      <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">Published</span>
                    <% when 'draft' %>
                      <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">Draft</span>
                    <% when 'expired' %>
                      <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">Expired</span>
                    <% end %>
                    <% if saved_job.job.budget_range.present? %>
                      <div class="text-xs text-stone-500 mt-1">
                        💰 <%= saved_job.job.budget_range.humanize %>
                      </div>
                    <% end %>
                  </div>
                </td>
                <td class="px-6 py-4">
                  <div class="text-sm text-stone-900"><%= saved_job.job.organization.name %></div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-stone-500">
                  <%= saved_job.created_at.strftime('%b %d, %Y') %>
                  <div class="text-xs text-stone-400">
                    <%= time_ago_in_words(saved_job.created_at) %> ago
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                  <%= link_to "View", super_admin_admin_saved_job_path(saved_job), 
                      class: "text-blue-600 hover:text-blue-900" %>
                  <%= link_to "Delete", super_admin_admin_saved_job_path(saved_job), 
                      method: :delete, 
                      confirm: "Are you sure you want to remove this saved job?", 
                      class: "text-red-600 hover:text-red-900" %>
                </td>
              </tr>
            <% end %>
          </tbody>
        </table>
      <% else %>
        <div class="text-center py-12">
          <svg class="mx-auto h-12 w-12 text-stone-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z"></path>
          </svg>
          <h3 class="mt-2 text-sm font-medium text-stone-900">No saved jobs found</h3>
          <p class="mt-1 text-sm text-stone-500">No saved jobs match your current filters.</p>
        </div>
      <% end %>
    </div>
  <% end %>

  <!-- Pagination -->
  <% if @saved_jobs.any? %>
    <div class="px-6 py-4 border-t border-stone-200 bg-stone-50">
      <%== pagy_nav(@pagy) if @pagy.pages > 1 %>
    </div>
  <% end %>
</div>

<script>
  // Bulk selection functionality
  document.addEventListener('DOMContentLoaded', function() {
    const selectAllCheckbox = document.getElementById('select-all');
    const savedJobCheckboxes = document.querySelectorAll('.saved-job-checkbox');
    
    selectAllCheckbox.addEventListener('change', function() {
      savedJobCheckboxes.forEach(checkbox => {
        checkbox.checked = this.checked;
      });
    });
    
    savedJobCheckboxes.forEach(checkbox => {
      checkbox.addEventListener('change', function() {
        const allChecked = Array.from(savedJobCheckboxes).every(cb => cb.checked);
        const noneChecked = Array.from(savedJobCheckboxes).every(cb => !cb.checked);
        
        selectAllCheckbox.checked = allChecked;
        selectAllCheckbox.indeterminate = !allChecked && !noneChecked;
      });
    });
  });
</script>
