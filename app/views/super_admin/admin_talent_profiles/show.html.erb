<% content_for :title, "#{@talent_profile.user.full_name} - Talent Profile" %>

<div class="space-y-6">
  <!-- Header with Breadcrumb -->
  <div class="border-b border-stone-200 pb-5">
    <div class="flex items-center justify-between">
      <div>
        <nav class="flex" aria-label="Breadcrumb">
          <ol role="list" class="flex items-center space-x-4">
            <li>
              <%= link_to super_admin_admin_talent_profiles_path, class: "text-stone-500 hover:text-stone-700" do %>
                Talent Profiles
              <% end %>
            </li>
            <li class="flex items-center">
              <svg class="flex-shrink-0 h-5 w-5 text-stone-400" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true">
                <path d="M5.555 17.776l8-16 .894.448-8 16-.894-.448z"/>
              </svg>
              <span class="ml-4 text-sm font-medium text-stone-900">
                <%= @talent_profile.user.full_name %>
              </span>
            </li>
          </ol>
        </nav>
        <h1 class="mt-2 text-2xl font-bold leading-7 text-stone-900 sm:truncate sm:text-3xl sm:tracking-tight">
          <%= @talent_profile.user.full_name %>
        </h1>
        <p class="mt-1 text-sm text-stone-600">
          Talent profile details and activity
        </p>
      </div>
    </div>
  </div>

  <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
    <!-- Main Content -->
    <div class="lg:col-span-2 space-y-6">
      <!-- Profile Overview -->
      <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-stone-200">
          <h2 class="text-lg font-medium text-stone-900">Profile Overview</h2>
        </div>
        <div class="px-6 py-4 space-y-4">
          <!-- User Information -->
          <div class="flex items-center space-x-4">
            <div class="flex-shrink-0 h-16 w-16">
              <div class="h-16 w-16 rounded-full bg-stone-100 flex items-center justify-center">
                <span class="text-lg font-medium text-stone-600">
                  <%= @talent_profile.user.initials %>
                </span>
              </div>
            </div>
            <div>
              <div class="text-lg font-medium text-stone-900">
                <%= @talent_profile.user.full_name %>
              </div>
              <div class="text-sm text-stone-500">
                <%= @talent_profile.user.email %>
              </div>
              <% if @talent_profile.headline.present? %>
                <div class="text-sm text-stone-700 mt-1">
                  <%= @talent_profile.headline %>
                </div>
              <% end %>
            </div>
          </div>

          <!-- Bio -->
          <% if @talent_profile.bio.present? %>
            <div>
              <h3 class="text-sm font-medium text-stone-500 mb-2">Bio</h3>
              <div class="text-sm text-stone-900 whitespace-pre-wrap">
                <%= @talent_profile.bio %>
              </div>
            </div>
          <% end %>

          <!-- About -->
          <% if @talent_profile.about.present? %>
            <div>
              <h3 class="text-sm font-medium text-stone-500 mb-2">About</h3>
              <div class="text-sm text-stone-900 whitespace-pre-wrap">
                <%= @talent_profile.about %>
              </div>
            </div>
          <% end %>

          <!-- Looking For -->
          <% if @talent_profile.looking_for.present? %>
            <div>
              <h3 class="text-sm font-medium text-stone-500 mb-2">Looking For</h3>
              <div class="text-sm text-stone-900 whitespace-pre-wrap">
                <%= @talent_profile.looking_for %>
              </div>
            </div>
          <% end %>
        </div>
      </div>

      <!-- Skills and Specialties -->
      <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-stone-200">
          <h2 class="text-lg font-medium text-stone-900">Skills & Specialties</h2>
        </div>
        <div class="px-6 py-4 space-y-4">
          <!-- Skills -->
          <% if @talent_profile.skills.present? && @talent_profile.skills.any? %>
            <div>
              <h3 class="text-sm font-medium text-stone-500 mb-2">Skills</h3>
              <div class="flex flex-wrap gap-2">
                <% @talent_profile.skills.each do |skill| %>
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                    <%= skill %>
                  </span>
                <% end %>
              </div>
            </div>
          <% end %>

          <!-- Niches -->
          <% if @talent_profile.niches.present? && @talent_profile.niches.any? %>
            <div>
              <h3 class="text-sm font-medium text-stone-500 mb-2">Niches</h3>
              <div class="flex flex-wrap gap-2">
                <% @talent_profile.niches.each do |niche| %>
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    <%= niche %>
                  </span>
                <% end %>
              </div>
            </div>
          <% end %>

          <!-- Ghostwriter Types -->
          <% if @talent_profile.ghostwriter_type.present? && @talent_profile.ghostwriter_type.any? %>
            <div>
              <h3 class="text-sm font-medium text-stone-500 mb-2">Ghostwriter Types</h3>
              <div class="flex flex-wrap gap-2">
                <% @talent_profile.ghostwriter_type.each do |type| %>
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                    <%= type %>
                  </span>
                <% end %>
              </div>
            </div>
          <% end %>

          <!-- Social Media Specialties -->
          <% if @talent_profile.social_media_specialty.present? && @talent_profile.social_media_specialty.any? %>
            <div>
              <h3 class="text-sm font-medium text-stone-500 mb-2">Social Media Specialties</h3>
              <div class="flex flex-wrap gap-2">
                <% @talent_profile.social_media_specialty.each do |specialty| %>
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-pink-100 text-pink-800">
                    <%= specialty %>
                  </span>
                <% end %>
              </div>
            </div>
          <% end %>
        </div>
      </div>

      <!-- Professional Details -->
      <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-stone-200">
          <h2 class="text-lg font-medium text-stone-900">Professional Details</h2>
        </div>
        <div class="px-6 py-4">
          <dl class="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <div>
              <dt class="text-sm font-medium text-stone-500">Availability Status</dt>
              <dd class="mt-1">
                <% case @talent_profile.availability_status %>
                <% when 'available' %>
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    Available
                  </span>
                <% when 'limited' %>
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                    Limited
                  </span>
                <% when 'unavailable' %>
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                    Unavailable
                  </span>
                <% end %>
              </dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-stone-500">Pricing Model</dt>
              <dd class="mt-1 text-sm text-stone-900"><%= @talent_profile.pricing_model.humanize %></dd>
            </div>
            <% if @talent_profile.price_range_min.present? || @talent_profile.price_range_max.present? %>
              <div>
                <dt class="text-sm font-medium text-stone-500">Price Range</dt>
                <dd class="mt-1 text-sm text-stone-900">
                  <% if @talent_profile.price_range_min.present? && @talent_profile.price_range_max.present? %>
                    $<%= @talent_profile.price_range_min.to_i %> - $<%= @talent_profile.price_range_max.to_i %>
                  <% elsif @talent_profile.price_range_min.present? %>
                    From $<%= @talent_profile.price_range_min.to_i %>
                  <% elsif @talent_profile.price_range_max.present? %>
                    Up to $<%= @talent_profile.price_range_max.to_i %>
                  <% end %>
                </dd>
              </div>
            <% end %>
            <div>
              <dt class="text-sm font-medium text-stone-500">Location</dt>
              <dd class="mt-1 text-sm text-stone-900">
                <%= @talent_profile.location.present? ? @talent_profile.location : "Not specified" %>
              </dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-stone-500">Location Preference</dt>
              <dd class="mt-1 text-sm text-stone-900"><%= @talent_profile.location_preference.humanize %></dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-stone-500">Type</dt>
              <dd class="mt-1">
                <% if @talent_profile.is_agency %>
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                    Agency
                  </span>
                <% else %>
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-stone-100 text-stone-800">
                    Individual
                  </span>
                <% end %>
              </dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-stone-500">Premium Status</dt>
              <dd class="mt-1">
                <% if @talent_profile.is_premium %>
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                    Premium
                  </span>
                <% else %>
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-stone-100 text-stone-800">
                    Standard
                  </span>
                <% end %>
              </dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-stone-500">Profile Created</dt>
              <dd class="mt-1 text-sm text-stone-900">
                <%= @talent_profile.created_at.strftime("%B %d, %Y at %I:%M %p") %>
              </dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-stone-500">Last Updated</dt>
              <dd class="mt-1 text-sm text-stone-900">
                <%= @talent_profile.updated_at.strftime("%B %d, %Y at %I:%M %p") %>
              </dd>
            </div>
          </dl>
        </div>
      </div>
    </div>

    <!-- Sidebar -->
    <div class="space-y-6">
      <!-- Quick Actions -->
      <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-stone-200">
          <h2 class="text-lg font-medium text-stone-900">Quick Actions</h2>
        </div>
        <div class="px-6 py-4 space-y-3">
          <%= link_to super_admin_admin_user_path(@user), 
              class: "block w-full text-center px-4 py-2 border border-stone-300 rounded-md shadow-sm text-sm font-medium text-stone-700 bg-white hover:bg-stone-50" do %>
            View User Profile
          <% end %>
        </div>
      </div>

      <!-- Statistics -->
      <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-stone-200">
          <h2 class="text-lg font-medium text-stone-900">Statistics</h2>
        </div>
        <div class="px-6 py-4 space-y-4">
          <div class="flex justify-between">
            <span class="text-sm text-stone-500">Applications Submitted</span>
            <span class="text-sm font-medium text-stone-900"><%= @user.job_applications.count %></span>
          </div>
          <div class="flex justify-between">
            <span class="text-sm text-stone-500">Bookmarked By</span>
            <span class="text-sm font-medium text-stone-900"><%= @bookmarked_by_count %></span>
          </div>
          <div class="flex justify-between">
            <span class="text-sm text-stone-500">Notes Count</span>
            <span class="text-sm font-medium text-stone-900"><%= @notes_count %></span>
          </div>
        </div>
      </div>

      <!-- Recent Applications -->
      <% if @recent_applications.any? %>
        <div class="bg-white shadow rounded-lg">
          <div class="px-6 py-4 border-b border-stone-200">
            <h2 class="text-lg font-medium text-stone-900">Recent Applications</h2>
          </div>
          <div class="px-6 py-4">
            <div class="space-y-3">
              <% @recent_applications.each do |application| %>
                <div class="border-l-2 border-stone-200 pl-3">
                  <div class="text-sm font-medium text-stone-900">
                    <%= link_to application.job.title, super_admin_admin_job_path(application.job), 
                        class: "text-indigo-600 hover:text-indigo-900" %>
                  </div>
                  <div class="text-xs text-stone-500">
                    <%= time_ago_in_words(application.created_at) %> ago
                  </div>
                  <div class="text-xs">
                    <% case application.status %>
                    <% when 'pending' %>
                      <span class="inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                        Pending
                      </span>
                    <% when 'reviewing' %>
                      <span class="inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                        Reviewing
                      </span>
                    <% when 'accepted' %>
                      <span class="inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        Accepted
                      </span>
                    <% when 'rejected' %>
                      <span class="inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                        Rejected
                      </span>
                    <% end %>
                  </div>
                </div>
              <% end %>
            </div>
          </div>
        </div>
      <% end %>
    </div>
  </div>
</div>
