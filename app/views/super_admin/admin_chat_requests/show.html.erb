<% content_for :title, "Chat Request ##{@chat_request.id}" %>

<div class="max-w-7xl mx-auto">
  <!-- Header -->
  <div class="mb-8">
    <div class="flex items-center justify-between">
      <div>
        <nav class="flex" aria-label="Breadcrumb">
          <ol class="flex items-center space-x-4">
            <li>
              <%= link_to "Chat Requests", super_admin_admin_chat_requests_path, class: "text-stone-500 hover:text-stone-700" %>
            </li>
            <li>
              <div class="flex items-center">
                <svg class="flex-shrink-0 h-5 w-5 text-stone-400" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                </svg>
                <span class="ml-4 text-sm font-medium text-stone-900">Request #<%= @chat_request.id %></span>
              </div>
            </li>
          </ol>
        </nav>
        <h1 class="mt-2 text-3xl font-bold text-stone-900">Chat Request #<%= @chat_request.id %></h1>
        <p class="mt-1 text-stone-600">
          <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <%= @chat_request.status == 'accepted' ? 'bg-green-100 text-green-800' : @chat_request.status == 'pending' ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800' %>">
            <%= @chat_request.status.humanize %>
          </span>
        </p>
      </div>
    </div>
  </div>

  <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
    <!-- Main Content -->
    <div class="lg:col-span-2 space-y-8">
      <!-- Chat Request Details -->
      <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-stone-200">
          <h2 class="text-lg font-medium text-stone-900">Request Details</h2>
        </div>
        <div class="px-6 py-4">
          <dl class="grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2">
            <div>
              <dt class="text-sm font-medium text-stone-500">Request ID</dt>
              <dd class="mt-1 text-sm text-stone-900"><%= @chat_request.id %></dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-stone-500">Status</dt>
              <dd class="mt-1">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <%= @chat_request.status == 'accepted' ? 'bg-green-100 text-green-800' : @chat_request.status == 'pending' ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800' %>">
                  <%= @chat_request.status.humanize %>
                </span>
              </dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-stone-500">Requested At</dt>
              <dd class="mt-1 text-sm text-stone-900">
                <%= @chat_request.requested_at&.strftime('%B %d, %Y at %I:%M %p') || "Not set" %>
              </dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-stone-500">Created At</dt>
              <dd class="mt-1 text-sm text-stone-900"><%= @chat_request.created_at.strftime('%B %d, %Y at %I:%M %p') %></dd>
            </div>
            <% if @chat_request.accepted_at %>
              <div>
                <dt class="text-sm font-medium text-stone-500">Accepted At</dt>
                <dd class="mt-1 text-sm text-green-600"><%= @chat_request.accepted_at.strftime('%B %d, %Y at %I:%M %p') %></dd>
              </div>
            <% end %>
            <% if @chat_request.declined_at %>
              <div>
                <dt class="text-sm font-medium text-stone-500">Declined At</dt>
                <dd class="mt-1 text-sm text-red-600"><%= @chat_request.declined_at.strftime('%B %d, %Y at %I:%M %p') %></dd>
              </div>
            <% end %>
          </dl>
        </div>
      </div>

      <!-- Pitch -->
      <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-stone-200">
          <h2 class="text-lg font-medium text-stone-900">Pitch Message</h2>
        </div>
        <div class="px-6 py-4">
          <% if @chat_request.pitch.present? %>
            <div class="prose max-w-none text-stone-900">
              <%= simple_format(@chat_request.pitch) %>
            </div>
          <% else %>
            <p class="text-stone-500 italic">No pitch message provided</p>
          <% end %>
        </div>
      </div>

      <!-- Related Conversations -->
      <% if @related_conversations.any? %>
        <div class="bg-white shadow rounded-lg">
          <div class="px-6 py-4 border-b border-stone-200">
            <h2 class="text-lg font-medium text-stone-900">Related Conversations</h2>
          </div>
          <div class="overflow-hidden">
            <table class="min-w-full divide-y divide-stone-200">
              <thead class="bg-stone-50">
                <tr>
                  <th class="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">Conversation</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">Messages</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">Created</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">Actions</th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-stone-200">
                <% @related_conversations.each do |conversation| %>
                  <tr>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm font-medium text-stone-900">Conversation #<%= conversation.id %></div>
                      <div class="text-sm text-stone-500">
                        <%= conversation.users.map(&:name).join(', ') %>
                      </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-stone-900">
                      <%= conversation.messages.count %> messages
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-stone-500">
                      <%= time_ago_in_words(conversation.created_at) %> ago
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <%= link_to "View", super_admin_admin_conversation_path(conversation), 
                          class: "text-blue-600 hover:text-blue-900" %>
                    </td>
                  </tr>
                <% end %>
              </tbody>
            </table>
          </div>
        </div>
      <% end %>
    </div>

    <!-- Sidebar -->
    <div class="space-y-8">
      <!-- Scout Information -->
      <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-stone-200">
          <h2 class="text-lg font-medium text-stone-900">Scout</h2>
        </div>
        <div class="px-6 py-4">
          <div class="flex items-center">
            <div class="flex-shrink-0 h-12 w-12">
              <% if @chat_request.scout.avatar.attached? %>
                <%= image_tag @chat_request.scout.avatar, class: "h-12 w-12 rounded-full object-cover" %>
              <% else %>
                <div class="h-12 w-12 rounded-full bg-stone-300 flex items-center justify-center">
                  <span class="text-sm font-medium text-stone-700"><%= @chat_request.scout.initials %></span>
                </div>
              <% end %>
            </div>
            <div class="ml-4">
              <div class="text-sm font-medium text-stone-900"><%= @chat_request.scout.name %></div>
              <div class="text-sm text-stone-500"><%= @chat_request.scout.email %></div>
            </div>
          </div>
          <div class="mt-4">
            <%= link_to "View Scout Profile", super_admin_admin_user_path(@chat_request.scout), 
                class: "text-blue-600 hover:text-blue-900 text-sm" %>
          </div>
        </div>
      </div>

      <!-- Talent Information -->
      <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-stone-200">
          <h2 class="text-lg font-medium text-stone-900">Talent</h2>
        </div>
        <div class="px-6 py-4">
          <div class="flex items-center">
            <div class="flex-shrink-0 h-12 w-12">
              <% if @chat_request.talent.avatar.attached? %>
                <%= image_tag @chat_request.talent.avatar, class: "h-12 w-12 rounded-full object-cover" %>
              <% else %>
                <div class="h-12 w-12 rounded-full bg-stone-300 flex items-center justify-center">
                  <span class="text-sm font-medium text-stone-700"><%= @chat_request.talent.initials %></span>
                </div>
              <% end %>
            </div>
            <div class="ml-4">
              <div class="text-sm font-medium text-stone-900"><%= @chat_request.talent.name %></div>
              <div class="text-sm text-stone-500"><%= @chat_request.talent.email %></div>
            </div>
          </div>
          <div class="mt-4">
            <%= link_to "View Talent Profile", super_admin_admin_user_path(@chat_request.talent), 
                class: "text-blue-600 hover:text-blue-900 text-sm" %>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
