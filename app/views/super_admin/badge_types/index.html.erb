<% content_for :title, "Badge Types" %>

<div class="container px-4 mx-auto">
  <div class="flex items-center justify-between mb-4">
    <h1 class="text-2xl font-bold">Badge Types</h1>
    <%= link_to "New Badge Type", new_super_admin_badge_type_path, class: "btn-primary" %>
  </div>

  <div class="overflow-hidden bg-white rounded-lg shadow-md">
    <table class="min-w-full divide-y divide-gray-200">
      <thead class="bg-gray-50">
        <tr>
          <th scope="col" class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">Preview</th>
          <th scope="col" class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">Name</th>
          <th scope="col" class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">Assignments</th>
          <th scope="col" class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">Priority</th>
          <th scope="col" class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">Status</th>
          <th scope="col" class="relative px-6 py-3">
            <span class="sr-only">Edit</span>
          </th>
        </tr>
      </thead>
      <tbody class="bg-white divide-y divide-gray-200">
        <% @badge_types.each do |badge_type| %>
          <tr>
            <td class="px-6 py-4 whitespace-nowrap">
              <span class="px-3 py-1 text-sm font-medium rounded-md" style="background-color: <%= badge_type.background_color %>; color: <%= badge_type.text_color %>;">
                <i class="<%= badge_type.icon %> mr-1.5"></i>
                <%= badge_type.name %>
              </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <%= link_to badge_type.name, super_admin_badge_type_path(badge_type), class: "text-blue-600 hover:underline" %>
            </td>
            <td class="px-6 py-4 whitespace-nowrap"><%= badge_type.badge_assignments.count %></td>
            <td class="px-6 py-4 whitespace-nowrap"><%= badge_type.priority %></td>
            <td class="px-6 py-4 whitespace-nowrap">
              <% if badge_type.active? %>
                <span class="inline-flex px-2 text-xs font-semibold leading-5 text-green-800 bg-green-100 rounded-full">
                  Active
                </span>
              <% else %>
                <span class="inline-flex px-2 text-xs font-semibold leading-5 text-red-800 bg-red-100 rounded-full">
                  Inactive
                </span>
              <% end %>
            </td>
            <td class="px-6 py-4 text-sm font-medium text-right whitespace-nowrap">
              <%= link_to "Edit", edit_super_admin_badge_type_path(badge_type), class: "text-indigo-600 hover:text-indigo-900" %>
            </td>
          </tr>
        <% end %>
      </tbody>
    </table>
  </div>
</div>