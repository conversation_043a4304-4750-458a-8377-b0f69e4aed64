<%# Enhanced Badge Preview Component %>
<%# This partial provides a live preview using the actual badge components and Stimulus controllers %>

<div class="bg-white border border-stone-200 rounded-lg p-6 shadow-sm">
  <div class="mb-4">
    <h3 class="text-lg font-medium text-stone-900 mb-2">Live Preview</h3>
    <p class="text-sm text-stone-600">See how your badge will appear to users with all visual effects</p>
  </div>

  <%# Main Badge Preview %>
  <div class="space-y-6">
    <%# Full Size Badge Preview - Using actual badge component %>
    <div>
      <h4 class="text-sm font-medium text-stone-700 mb-3">Full Size Badge (Profile Display)</h4>
      <div class="flex items-center justify-center p-8 bg-gradient-to-br from-stone-50 to-stone-100 rounded-lg border border-stone-200">
        <%# Create a mock BadgeType object for preview %>
        <%
          preview_badge = OpenStruct.new(
            name: "Sample Badge",
            description: "This is a sample badge description",
            background_color: "#3B82F6",
            text_color: "#FFFFFF",
            icon: "ph-star-fill"
          )
        %>

        <%# Use the actual badge component with Stimulus controller %>
        <div data-controller="badge" class="badge-preview-full">
          <span
            class="badge inline-flex items-center font-medium rounded-lg border shadow-sm backdrop-filter backdrop-blur-sm bg-opacity-90 transition-all duration-200 ease-out transform-gpu will-change-transform cursor-default px-3 py-1.5 text-sm"
            style="background-color: #3B82F6; color: #FFFFFF; border-color: #2563EB;"
            data-badge-target="badge"
            role="img"
            aria-label="Sample Badge badge"
          >
            <%= phosphor_icon "star", style: "fill", class: "badge-icon badge-preview-icon text-sm mr-1.5 transition-transform duration-200 ease-out", "aria-hidden": "true" %>
            <span class="badge-name badge-preview-name font-medium">Sample Badge</span>
          </span>
        </div>
      </div>
      <p class="text-xs text-stone-500 mt-2 text-center">Hover to see parallax and tilt effects</p>
    </div>

    <%# Compact Badge Preview - Using actual compact badge component %>
    <div>
      <h4 class="text-sm font-medium text-stone-700 mb-3">Compact Size (Search Results)</h4>
      <div class="flex items-center justify-center p-6 bg-gradient-to-br from-stone-50 to-stone-100 rounded-lg border border-stone-200">
        <%# Use the actual compact badge component with Stimulus controller %>
        <div data-controller="badge" class="badge-preview-compact">
          <span
            class="badge-compact inline-flex items-center font-medium rounded-md border shadow-sm backdrop-filter backdrop-blur-sm bg-opacity-90 transition-all duration-200 ease-out transform-gpu will-change-transform cursor-default px-2 py-0.5 text-xs"
            style="background-color: #3B82F6; color: #FFFFFF; border-color: #2563EB; max-width: 120px;"
            data-badge-target="badge"
            role="img"
            aria-label="Sample Badge badge"
          >
            <%= phosphor_icon "star", style: "fill", class: "badge-icon badge-preview-icon text-xs mr-1 transition-transform duration-200 ease-out", "aria-hidden": "true" %>
            <span class="badge-name badge-preview-name font-medium">Sample Badge</span>
          </span>
        </div>
      </div>
      <p class="text-xs text-stone-500 mt-2 text-center">Hover to see compact badge effects</p>
    </div>

    <%# Badge Description Preview with Tooltip Simulation %>
    <div>
      <h4 class="text-sm font-medium text-stone-700 mb-3">Description (Tooltip Preview)</h4>
      <div class="p-4 bg-gradient-to-br from-stone-50 to-stone-100 rounded-lg border border-stone-200">
        <div class="relative inline-block">
          <div class="bg-stone-900 text-white text-xs rounded-md px-3 py-2 shadow-lg max-w-xs">
            <p class="badge-preview-description">
              This is a sample badge description
            </p>
            <%# Tooltip arrow %>
            <div class="absolute -bottom-1 left-1/2 transform -translate-x-1/2">
              <div class="w-2 h-2 bg-stone-900 rotate-45"></div>
            </div>
          </div>
        </div>
        <p class="text-xs text-stone-500 mt-3">This appears when users hover over the badge</p>
      </div>
    </div>

    <%# Usage Context Examples %>
    <div>
      <h4 class="text-sm font-medium text-stone-700 mb-3">Usage Examples</h4>
      <div class="space-y-4">
        <%# Profile Context %>
        <div class="p-4 bg-white rounded-lg border border-stone-200 shadow-sm">
          <h5 class="text-xs font-medium text-stone-500 uppercase tracking-wide mb-2">On Talent Profile</h5>
          <div class="flex items-center space-x-3">
            <div class="w-12 h-12 bg-stone-200 rounded-full flex items-center justify-center">
              <svg class="w-6 h-6 text-stone-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd" />
              </svg>
            </div>
            <div>
              <h6 class="font-medium text-stone-900">John Doe</h6>
              <div class="flex items-center space-x-2 mt-1">
                <div data-controller="badge" class="badge-preview-profile-context">
                  <span
                    class="badge inline-flex items-center font-medium rounded-lg border shadow-sm backdrop-filter backdrop-blur-sm bg-opacity-90 transition-all duration-200 ease-out transform-gpu will-change-transform cursor-default px-3 py-1.5 text-sm"
                    style="background-color: #3B82F6; color: #FFFFFF; border-color: #2563EB;"
                    data-badge-target="badge"
                  >
                    <i class="badge-icon badge-preview-icon fas fa-star text-sm mr-1.5 transition-transform duration-200 ease-out"></i>
                    <span class="badge-name badge-preview-name font-medium">Sample Badge</span>
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <%# Search Results Context %>
        <div class="p-4 bg-white rounded-lg border border-stone-200 shadow-sm">
          <h5 class="text-xs font-medium text-stone-500 uppercase tracking-wide mb-2">In Search Results</h5>
          <div class="flex items-center justify-between">
            <div class="flex items-center space-x-3">
              <div class="w-8 h-8 bg-stone-200 rounded-full flex items-center justify-center">
                <svg class="w-4 h-4 text-stone-400" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd" />
                </svg>
              </div>
              <div>
                <div class="flex items-center space-x-2">
                  <span class="font-medium text-stone-900">Jane Smith</span>
                  <div data-controller="badge" class="badge-preview-search-context">
                    <span
                      class="badge-compact inline-flex items-center font-medium rounded-md border shadow-sm backdrop-filter backdrop-blur-sm bg-opacity-90 transition-all duration-200 ease-out transform-gpu will-change-transform cursor-default px-2 py-0.5 text-xs"
                      style="background-color: #3B82F6; color: #FFFFFF; border-color: #2563EB; max-width: 120px;"
                      data-badge-target="badge"
                    >
                      <i class="badge-icon badge-preview-icon fas fa-star text-xs mr-1 transition-transform duration-200 ease-out"></i>
                      <span class="badge-name badge-preview-name font-medium truncate" style="max-width: 80px;">Sample Badge</span>
                    </span>
                  </div>
                </div>
                <p class="text-sm text-stone-500">Ghostwriter • Available</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <%# Color Contrast Warning %>
    <div class="contrast-warning hidden p-3 bg-amber-50 border border-amber-200 rounded-lg">
      <div class="flex items-start">
        <div class="flex-shrink-0">
          <svg class="w-5 h-5 text-amber-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
          </svg>
        </div>
        <div class="ml-3">
          <h3 class="text-sm font-medium text-amber-800">Accessibility Warning</h3>
          <div class="mt-1 text-sm text-amber-700 contrast-warning-message">
            <!-- Warning message will be populated by JavaScript -->
          </div>
        </div>
      </div>
    </div>

    <%# Badge Properties Summary %>
    <div>
      <h4 class="text-sm font-medium text-stone-700 mb-3">Badge Properties</h4>
      <div class="bg-stone-50 rounded-lg border border-stone-200 p-4">
        <dl class="grid grid-cols-1 gap-3 sm:grid-cols-2">
          <div>
            <dt class="text-xs font-medium text-stone-500 uppercase tracking-wide">Background Color</dt>
            <dd class="mt-1 flex items-center">
              <div class="badge-preview-bg-color w-4 h-4 rounded border border-stone-300 mr-2" 
                   style="background-color: #3B82F6;"></div>
              <span class="badge-preview-bg-value text-sm text-stone-900 font-mono">#3B82F6</span>
            </dd>
          </div>
          <div>
            <dt class="text-xs font-medium text-stone-500 uppercase tracking-wide">Text Color</dt>
            <dd class="mt-1 flex items-center">
              <div class="badge-preview-text-color w-4 h-4 rounded border border-stone-300 mr-2" 
                   style="background-color: #FFFFFF;"></div>
              <span class="badge-preview-text-value text-sm text-stone-900 font-mono">#FFFFFF</span>
            </dd>
          </div>
          <div>
            <dt class="text-xs font-medium text-stone-500 uppercase tracking-wide">Icon Class</dt>
            <dd class="mt-1">
              <span class="badge-preview-icon-value text-sm text-stone-900 font-mono">fas fa-star</span>
            </dd>
          </div>
          <div>
            <dt class="text-xs font-medium text-stone-500 uppercase tracking-wide">Priority</dt>
            <dd class="mt-1">
              <span class="badge-preview-priority-value text-sm text-stone-900">0</span>
            </dd>
          </div>
        </dl>
      </div>
    </div>

    <%# Preview Actions %>
    <div class="flex justify-between items-center pt-4 border-t border-stone-200">
      <div class="text-xs text-stone-500">
        Preview updates automatically as you type
      </div>
      <button type="button" 
              data-action="click->badge-preview#resetPreview"
              class="inline-flex items-center px-3 py-1.5 border border-stone-300 shadow-sm text-xs font-medium rounded text-stone-700 bg-white hover:bg-stone-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
        <svg class="w-3 h-3 mr-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
        </svg>
        Reset to Defaults
      </button>
    </div>
  </div>
</div>

<%# Enhanced CSS for preview styling with full badge component support %>
<style>
  /* Preview container enhancements */
  .badge-preview-full,
  .badge-preview-compact,
  .badge-preview-profile-context,
  .badge-preview-search-context {
    /* Ensure proper badge component rendering */
    position: relative;
  }

  /* Enhanced badge styling for preview - matches actual badge component */
  .badge-preview-full .badge,
  .badge-preview-compact .badge-compact,
  .badge-preview-profile-context .badge,
  .badge-preview-search-context .badge-compact {
    /* Ensure all visual effects are visible */
    position: relative;
    overflow: hidden;
  }

  /* Glassy effect overlay - matches badge component CSS */
  .badge-preview-full .badge::before,
  .badge-preview-compact .badge-compact::before,
  .badge-preview-profile-context .badge::before,
  .badge-preview-search-context .badge-compact::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
    pointer-events: none;
    border-radius: inherit;
  }

  /* Badge preview icon styling - Phosphor icons are handled by the gem */
  .badge-preview-icon {
    /* Phosphor icons are loaded via the phosphor_icons gem, no custom font-family needed */
  }

  /* Color swatch styling */
  .badge-preview-bg-color,
  .badge-preview-text-color {
    box-shadow: inset 0 0 0 1px rgba(0, 0, 0, 0.1);
  }

  /* Preview section backgrounds */
  .badge-preview-full,
  .badge-preview-compact {
    /* Add subtle animation to draw attention to interactive elements */
    transition: all 0.3s ease;
  }

  .badge-preview-full:hover,
  .badge-preview-compact:hover {
    transform: scale(1.02);
  }

  /* Tooltip preview styling */
  .badge-preview-description {
    line-height: 1.4;
  }

  /* Usage context styling */
  .badge-preview-profile-context,
  .badge-preview-search-context {
    /* Ensure context examples are clearly visible */
    z-index: 1;
  }

  /* Responsive adjustments for preview */
  @media (max-width: 640px) {
    .badge-preview-full .badge,
    .badge-preview-profile-context .badge {
      /* Slightly smaller on mobile to match actual responsive behavior */
      padding: 0.375rem 0.625rem;
      font-size: 0.75rem;
    }

    .badge-preview-full .badge .badge-icon,
    .badge-preview-profile-context .badge .badge-icon {
      font-size: 0.75rem;
      margin-right: 0.25rem;
    }
  }

  /* Accessibility: Respect reduced motion preferences */
  @media (prefers-reduced-motion: reduce) {
    .badge-preview-full,
    .badge-preview-compact,
    .badge-preview-profile-context,
    .badge-preview-search-context {
      transition: none !important;
    }

    .badge-preview-full:hover,
    .badge-preview-compact:hover {
      transform: none !important;
    }
  }

  /* High contrast mode support */
  @media (prefers-contrast: high) {
    .badge-preview-full .badge,
    .badge-preview-compact .badge-compact,
    .badge-preview-profile-context .badge,
    .badge-preview-search-context .badge-compact {
      border-width: 2px;
      backdrop-filter: none;
      background-opacity: 1 !important;
    }
  }
</style>
