<% content_for :title, "Message ##{@message.id}" %>

<div class="space-y-6">
  <!-- Header with Breadcrumb -->
  <div class="border-b border-stone-200 pb-5">
    <div class="flex items-center justify-between">
      <div>
        <nav class="flex" aria-label="Breadcrumb">
          <ol role="list" class="flex items-center space-x-4">
            <li>
              <%= link_to super_admin_admin_messages_path, class: "text-stone-500 hover:text-stone-700" do %>
                Messages
              <% end %>
            </li>
            <li class="flex items-center">
              <svg class="flex-shrink-0 h-5 w-5 text-stone-400" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true">
                <path d="M5.555 17.776l8-16 .894.448-8 16-.894-.448z"/>
              </svg>
              <span class="ml-4 text-sm font-medium text-stone-900">
                Message #<%= @message.id %>
              </span>
            </li>
          </ol>
        </nav>
        <h1 class="mt-2 text-2xl font-bold leading-7 text-stone-900 sm:truncate sm:text-3xl sm:tracking-tight">
          Message #<%= @message.id %>
        </h1>
        <p class="mt-1 text-sm text-stone-600">
          Message details and conversation context
        </p>
      </div>
    </div>
  </div>

  <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
    <!-- Main Content -->
    <div class="lg:col-span-2 space-y-6">
      <!-- Message Details -->
      <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-stone-200">
          <h2 class="text-lg font-medium text-stone-900">Message Details</h2>
        </div>
        <div class="px-6 py-4 space-y-4">
          <!-- Sender Information -->
          <div class="flex items-center space-x-3">
            <div class="flex-shrink-0 h-10 w-10">
              <div class="h-10 w-10 rounded-full bg-stone-100 flex items-center justify-center">
                <span class="text-sm font-medium text-stone-600">
                  <%= @message.user.initials %>
                </span>
              </div>
            </div>
            <div>
              <div class="text-sm font-medium text-stone-900">
                <%= @message.user.full_name %>
              </div>
              <div class="text-sm text-stone-500">
                <%= @message.user.email %>
              </div>
            </div>
          </div>

          <!-- Message Content -->
          <div class="bg-stone-50 rounded-lg p-4">
            <div class="text-sm text-stone-900 whitespace-pre-wrap">
              <%= @message.body %>
            </div>
          </div>

          <!-- Message Metadata -->
          <dl class="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <div>
              <dt class="text-sm font-medium text-stone-500">Message ID</dt>
              <dd class="mt-1 text-sm text-stone-900"><%= @message.id %></dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-stone-500">Status</dt>
              <dd class="mt-1">
                <% if @message.read_at %>
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    Read
                  </span>
                <% else %>
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                    Unread
                  </span>
                <% end %>
              </dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-stone-500">Created At</dt>
              <dd class="mt-1 text-sm text-stone-900">
                <%= @message.created_at.strftime("%B %d, %Y at %I:%M %p") %>
              </dd>
            </div>
            <% if @message.read_at %>
              <div>
                <dt class="text-sm font-medium text-stone-500">Read At</dt>
                <dd class="mt-1 text-sm text-stone-900">
                  <%= @message.read_at.strftime("%B %d, %Y at %I:%M %p") %>
                </dd>
              </div>
            <% end %>
          </dl>
        </div>
      </div>

      <!-- Conversation Context -->
      <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-stone-200">
          <h2 class="text-lg font-medium text-stone-900">Conversation Context</h2>
        </div>
        <div class="px-6 py-4">
          <div class="space-y-4">
            <!-- Conversation Info -->
            <dl class="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div>
                <dt class="text-sm font-medium text-stone-500">Conversation ID</dt>
                <dd class="mt-1 text-sm text-stone-900">
                  <%= link_to @conversation.id, super_admin_admin_conversation_path(@conversation), 
                      class: "text-indigo-600 hover:text-indigo-900" %>
                </dd>
              </div>
              <div>
                <dt class="text-sm font-medium text-stone-500">Participants</dt>
                <dd class="mt-1 text-sm text-stone-900">
                  <%= @conversation.users.count %> participants
                </dd>
              </div>
              <% if @conversation.job %>
                <div>
                  <dt class="text-sm font-medium text-stone-500">Related Job</dt>
                  <dd class="mt-1 text-sm text-stone-900">
                    <%= link_to @conversation.job.title, super_admin_admin_job_path(@conversation.job), 
                        class: "text-indigo-600 hover:text-indigo-900" %>
                  </dd>
                </div>
              <% end %>
              <div>
                <dt class="text-sm font-medium text-stone-500">Last Updated</dt>
                <dd class="mt-1 text-sm text-stone-900">
                  <%= @conversation.updated_at.strftime("%B %d, %Y at %I:%M %p") %>
                </dd>
              </div>
            </dl>

            <!-- Participants List -->
            <div>
              <h3 class="text-sm font-medium text-stone-500 mb-2">Participants</h3>
              <div class="space-y-2">
                <% @conversation.users.each do |user| %>
                  <div class="flex items-center space-x-3">
                    <div class="flex-shrink-0 h-8 w-8">
                      <div class="h-8 w-8 rounded-full bg-stone-100 flex items-center justify-center">
                        <span class="text-xs font-medium text-stone-600">
                          <%= user.initials %>
                        </span>
                      </div>
                    </div>
                    <div>
                      <div class="text-sm font-medium text-stone-900">
                        <%= link_to user.full_name, super_admin_admin_user_path(user), 
                            class: "text-indigo-600 hover:text-indigo-900" %>
                      </div>
                      <div class="text-sm text-stone-500">
                        <%= user.email %>
                      </div>
                    </div>
                  </div>
                <% end %>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Sidebar -->
    <div class="space-y-6">
      <!-- Quick Actions -->
      <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-stone-200">
          <h2 class="text-lg font-medium text-stone-900">Quick Actions</h2>
        </div>
        <div class="px-6 py-4 space-y-3">
          <%= link_to super_admin_admin_conversation_path(@conversation), 
              class: "block w-full text-center px-4 py-2 border border-stone-300 rounded-md shadow-sm text-sm font-medium text-stone-700 bg-white hover:bg-stone-50" do %>
            View Full Conversation
          <% end %>
          <%= link_to super_admin_admin_user_path(@message.user), 
              class: "block w-full text-center px-4 py-2 border border-stone-300 rounded-md shadow-sm text-sm font-medium text-stone-700 bg-white hover:bg-stone-50" do %>
            View Sender Profile
          <% end %>
          <% if @conversation.job %>
            <%= link_to super_admin_admin_job_path(@conversation.job), 
                class: "block w-full text-center px-4 py-2 border border-stone-300 rounded-md shadow-sm text-sm font-medium text-stone-700 bg-white hover:bg-stone-50" do %>
              View Related Job
            <% end %>
          <% end %>
        </div>
      </div>

      <!-- Recent Messages in Conversation -->
      <% if @related_messages.any? %>
        <div class="bg-white shadow rounded-lg">
          <div class="px-6 py-4 border-b border-stone-200">
            <h2 class="text-lg font-medium text-stone-900">Recent Messages</h2>
          </div>
          <div class="px-6 py-4">
            <div class="space-y-3">
              <% @related_messages.limit(5).each do |msg| %>
                <div class="border-l-2 border-stone-200 pl-3">
                  <div class="flex items-center justify-between">
                    <div class="text-sm font-medium text-stone-900">
                      <%= msg.user.full_name %>
                    </div>
                    <div class="text-xs text-stone-500">
                      <%= time_ago_in_words(msg.created_at) %> ago
                    </div>
                  </div>
                  <div class="text-sm text-stone-600 mt-1 truncate">
                    <%= msg.body %>
                  </div>
                </div>
              <% end %>
            </div>
          </div>
        </div>
      <% end %>
    </div>
  </div>
</div>
