<% content_for :title, "Edit Admin Roles - #{@user.full_name}" %>

<div class="flex h-screen bg-stone-50">
  <!-- Sidebar -->
  <div class="w-64 bg-white shadow-sm border-r border-stone-200">
    <%= render 'shared/admin_sidebar' %>
  </div>

  <!-- Main Content -->
  <div class="flex-1 overflow-auto">
    <div class="p-8">
      <!-- Header -->
      <div class="mb-8">
        <div class="flex items-center justify-between">
          <div>
            <nav class="flex" aria-label="Breadcrumb">
              <ol class="flex items-center space-x-4">
                <li>
                  <%= link_to "Admin Roles", super_admin_admin_roles_path, 
                      class: "text-stone-500 hover:text-stone-700" %>
                </li>
                <li>
                  <div class="flex items-center">
                    <svg class="flex-shrink-0 h-5 w-5 text-stone-400" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                    </svg>
                    <%= link_to @user.full_name, super_admin_admin_role_path(@user), 
                        class: "ml-4 text-stone-500 hover:text-stone-700" %>
                  </div>
                </li>
                <li>
                  <div class="flex items-center">
                    <svg class="flex-shrink-0 h-5 w-5 text-stone-400" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                    </svg>
                    <span class="ml-4 text-sm font-medium text-stone-900">Edit Roles</span>
                  </div>
                </li>
              </ol>
            </nav>
            <h1 class="mt-2 text-2xl font-bold text-stone-900">Edit Admin Roles</h1>
          </div>
        </div>
      </div>

      <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- User Information -->
        <div class="lg:col-span-1">
          <div class="bg-white shadow rounded-lg">
            <div class="px-6 py-4 border-b border-stone-200">
              <h2 class="text-lg font-medium text-stone-900">User Information</h2>
            </div>
            <div class="px-6 py-4">
              <div class="flex items-center mb-6">
                <div class="flex-shrink-0 h-16 w-16">
                  <div class="h-16 w-16 rounded-full <%= @user.avatar_color.join(' ') %> flex items-center justify-center">
                    <span class="text-xl font-medium"><%= @user.initials %></span>
                  </div>
                </div>
                <div class="ml-4">
                  <div class="text-lg font-medium text-stone-900">
                    <%= @user.full_name %>
                  </div>
                  <div class="text-sm text-stone-500">
                    <%= @user.email %>
                  </div>
                </div>
              </div>

              <dl class="space-y-4">
                <div>
                  <dt class="text-sm font-medium text-stone-500">User ID</dt>
                  <dd class="mt-1 text-sm text-stone-900"><%= @user.id %></dd>
                </div>
                <div>
                  <dt class="text-sm font-medium text-stone-500">Current Roles</dt>
                  <dd class="mt-1">
                    <% if @user.roles.any? %>
                      <div class="flex flex-wrap gap-1">
                        <% @user.roles.each do |role| %>
                          <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                            <%= role.name.humanize %>
                          </span>
                        <% end %>
                      </div>
                    <% else %>
                      <span class="text-sm text-stone-500">No roles assigned</span>
                    <% end %>
                  </dd>
                </div>
              </dl>
            </div>
          </div>

          <!-- Warning Box -->
          <div class="mt-6 bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <div class="flex">
              <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                </svg>
              </div>
              <div class="ml-3">
                <h3 class="text-sm font-medium text-yellow-800">
                  Important
                </h3>
                <div class="mt-2 text-sm text-yellow-700">
                  <p>
                    Changing admin roles will immediately affect the user's access to administrative functions. 
                    Make sure you understand the implications before saving changes.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Role Assignment Form -->
        <div class="lg:col-span-2">
          <div class="bg-white shadow rounded-lg">
            <div class="px-6 py-4 border-b border-stone-200">
              <h2 class="text-lg font-medium text-stone-900">Assign Admin Roles</h2>
              <p class="mt-1 text-sm text-stone-600">
                Select the administrative roles to assign to this user.
              </p>
            </div>

            <%= form_with model: @user, url: super_admin_admin_role_path(@user), method: :patch, local: true, class: "px-6 py-4" do |form| %>
              <div class="space-y-6">
                <!-- Role Selection -->
                <div>
                  <fieldset>
                    <legend class="text-base font-medium text-stone-900 mb-4">Available Roles</legend>
                    <div class="space-y-4">
                      <% @available_roles.each do |role| %>
                        <div class="relative flex items-start p-4 border border-stone-200 rounded-lg hover:bg-stone-50">
                          <div class="flex items-center h-5">
                            <%= check_box_tag "user[role_ids][]", 
                                role.id, 
                                @user_roles.include?(role.id),
                                id: "role_#{role.id}",
                                class: "h-4 w-4 text-blue-600 focus:ring-blue-500 border-stone-300 rounded" %>
                          </div>
                          <div class="ml-3 text-sm">
                            <%= label_tag "role_#{role.id}", class: "font-medium text-stone-900" do %>
                              <%= role.name.humanize %>
                            <% end %>
                            <p class="text-stone-500">
                              <%= User.admin_role_description(role.name) %>
                            </p>
                            
                            <!-- Show permissions for this role -->
                            <% role_permissions = AdminPermissions::ADMIN_ROLES[role.name.to_sym]&.dig(:permissions) %>
                            <% if role_permissions %>
                              <div class="mt-2">
                                <% if role_permissions == :all %>
                                  <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-green-100 text-green-800">
                                    All Permissions
                                  </span>
                                <% else %>
                                  <div class="flex flex-wrap gap-1 mt-1">
                                    <% role_permissions.first(3).each do |permission| %>
                                      <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-blue-100 text-blue-800">
                                        <%= permission.humanize %>
                                      </span>
                                    <% end %>
                                    <% if role_permissions.count > 3 %>
                                      <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-stone-100 text-stone-600">
                                        +<%= role_permissions.count - 3 %> more
                                      </span>
                                    <% end %>
                                  </div>
                                <% end %>
                              </div>
                            <% end %>
                          </div>
                        </div>
                      <% end %>
                    </div>
                  </fieldset>
                </div>

                <!-- Hidden field to ensure empty array is sent when no roles are selected -->
                <%= hidden_field_tag "user[role_ids][]", "" %>

                <!-- Form Actions -->
                <div class="flex justify-between pt-6 border-t border-stone-200">
                  <div>
                    <%= link_to "Cancel", super_admin_admin_role_path(@user), 
                        class: "inline-flex items-center px-4 py-2 border border-stone-300 rounded-md shadow-sm text-sm font-medium text-stone-700 bg-white hover:bg-stone-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500" %>
                  </div>
                  <div class="flex space-x-3">
                    <%= link_to "View User", super_admin_admin_role_path(@user), 
                        class: "inline-flex items-center px-4 py-2 border border-stone-300 rounded-md shadow-sm text-sm font-medium text-stone-700 bg-white hover:bg-stone-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500" %>
                    <%= form.submit "Update Roles", 
                        class: "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",
                        data: { 
                          confirm: "Are you sure you want to update the admin roles for #{@user.full_name}? This will immediately change their access permissions." 
                        } %>
                  </div>
                </div>
              </div>
            <% end %>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
