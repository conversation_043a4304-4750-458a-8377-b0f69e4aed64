<% content_for :title, "Admin Role Details - #{@user.full_name}" %>

<div class="flex h-screen bg-stone-50">
  <!-- Sidebar -->
  <div class="w-64 bg-white shadow-sm border-r border-stone-200">
    <%= render 'shared/admin_sidebar' %>
  </div>

  <!-- Main Content -->
  <div class="flex-1 overflow-auto">
    <div class="p-8">
      <!-- Header -->
      <div class="mb-8">
        <div class="flex items-center justify-between">
          <div>
            <nav class="flex" aria-label="Breadcrumb">
              <ol class="flex items-center space-x-4">
                <li>
                  <%= link_to "Admin Roles", super_admin_admin_roles_path, 
                      class: "text-stone-500 hover:text-stone-700" %>
                </li>
                <li>
                  <div class="flex items-center">
                    <svg class="flex-shrink-0 h-5 w-5 text-stone-400" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                    </svg>
                    <span class="ml-4 text-sm font-medium text-stone-900">
                      <%= @user.full_name %>
                    </span>
                  </div>
                </li>
              </ol>
            </nav>
            <h1 class="mt-2 text-2xl font-bold text-stone-900">Admin Role Details</h1>
          </div>
          <div class="flex space-x-3">
            <%= link_to "Edit Roles", edit_super_admin_admin_role_path(@user), 
                class: "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500" %>
          </div>
        </div>
      </div>

      <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- User Information -->
        <div class="lg:col-span-1">
          <div class="bg-white shadow rounded-lg">
            <div class="px-6 py-4 border-b border-stone-200">
              <h2 class="text-lg font-medium text-stone-900">User Information</h2>
            </div>
            <div class="px-6 py-4">
              <div class="flex items-center mb-6">
                <div class="flex-shrink-0 h-16 w-16">
                  <div class="h-16 w-16 rounded-full <%= @user.avatar_color.join(' ') %> flex items-center justify-center">
                    <span class="text-xl font-medium"><%= @user.initials %></span>
                  </div>
                </div>
                <div class="ml-4">
                  <div class="text-lg font-medium text-stone-900">
                    <%= @user.full_name %>
                  </div>
                  <div class="text-sm text-stone-500">
                    <%= @user.email %>
                  </div>
                </div>
              </div>

              <dl class="space-y-4">
                <div>
                  <dt class="text-sm font-medium text-stone-500">User ID</dt>
                  <dd class="mt-1 text-sm text-stone-900"><%= @user.id %></dd>
                </div>
                <div>
                  <dt class="text-sm font-medium text-stone-500">Status</dt>
                  <dd class="mt-1">
                    <% if @user.verified? %>
                      <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        Verified
                      </span>
                    <% else %>
                      <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                        Unverified
                      </span>
                    <% end %>
                  </dd>
                </div>
                <div>
                  <dt class="text-sm font-medium text-stone-500">Member Since</dt>
                  <dd class="mt-1 text-sm text-stone-900">
                    <%= @user.created_at.strftime("%B %d, %Y") %>
                  </dd>
                </div>
                <div>
                  <dt class="text-sm font-medium text-stone-500">Last Updated</dt>
                  <dd class="mt-1 text-sm text-stone-900">
                    <%= time_ago_in_words(@user.updated_at) %> ago
                  </dd>
                </div>
              </dl>
            </div>
          </div>
        </div>

        <!-- Current Roles and Permissions -->
        <div class="lg:col-span-2">
          <div class="space-y-6">
            <!-- Current Roles -->
            <div class="bg-white shadow rounded-lg">
              <div class="px-6 py-4 border-b border-stone-200">
                <h2 class="text-lg font-medium text-stone-900">Current Admin Roles</h2>
              </div>
              <div class="px-6 py-4">
                <% if @user_roles.any? %>
                  <div class="space-y-4">
                    <% @user_roles.each do |role| %>
                      <div class="flex items-center justify-between p-4 border border-stone-200 rounded-lg">
                        <div>
                          <h3 class="text-sm font-medium text-stone-900">
                            <%= role.name.humanize %>
                          </h3>
                          <p class="text-sm text-stone-500">
                            <%= User.admin_role_description(role.name) %>
                          </p>
                        </div>
                        <div class="flex items-center space-x-2">
                          <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                            Active
                          </span>
                          <%= link_to super_admin_admin_role_path(@user, role_id: role.id), 
                              method: :delete,
                              data: { 
                                confirm: "Are you sure you want to remove the #{role.name.humanize} role from #{@user.full_name}?" 
                              },
                              class: "text-red-600 hover:text-red-900 text-sm" do %>
                            Remove
                          <% end %>
                        </div>
                      </div>
                    <% end %>
                  </div>
                <% else %>
                  <div class="text-center py-8">
                    <svg class="mx-auto h-12 w-12 text-stone-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0z"></path>
                    </svg>
                    <h3 class="mt-2 text-sm font-medium text-stone-900">No admin roles assigned</h3>
                    <p class="mt-1 text-sm text-stone-500">
                      This user does not have any administrative roles.
                    </p>
                  </div>
                <% end %>
              </div>
            </div>

            <!-- Permissions Summary -->
            <div class="bg-white shadow rounded-lg">
              <div class="px-6 py-4 border-b border-stone-200">
                <h2 class="text-lg font-medium text-stone-900">Permissions Summary</h2>
              </div>
              <div class="px-6 py-4">
                <% if @user.admin_permissions == :all %>
                  <div class="flex items-center p-4 bg-green-50 border border-green-200 rounded-lg">
                    <svg class="h-5 w-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                    </svg>
                    <div class="ml-3">
                      <h3 class="text-sm font-medium text-green-800">Full Administrative Access</h3>
                      <p class="text-sm text-green-700">This user has unrestricted access to all administrative functions.</p>
                    </div>
                  </div>
                <% elsif @user.admin_permissions.any? %>
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-2">
                    <% @user.admin_permissions.each do |permission| %>
                      <div class="flex items-center p-2 bg-blue-50 border border-blue-200 rounded">
                        <svg class="h-4 w-4 text-blue-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
                          <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        <span class="text-sm text-blue-800"><%= permission.humanize %></span>
                      </div>
                    <% end %>
                  </div>
                <% else %>
                  <div class="text-center py-8">
                    <svg class="mx-auto h-12 w-12 text-stone-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                    </svg>
                    <h3 class="mt-2 text-sm font-medium text-stone-900">No permissions</h3>
                    <p class="mt-1 text-sm text-stone-500">
                      This user does not have any administrative permissions.
                    </p>
                  </div>
                <% end %>
              </div>
            </div>

            <!-- Available Roles to Assign -->
            <% if @available_roles.any? %>
              <div class="bg-white shadow rounded-lg">
                <div class="px-6 py-4 border-b border-stone-200">
                  <h2 class="text-lg font-medium text-stone-900">Available Roles</h2>
                </div>
                <div class="px-6 py-4">
                  <%= form_with url: super_admin_admin_roles_path, method: :post, local: true do |form| %>
                    <%= form.hidden_field :user_id, value: @user.id %>
                    <div class="space-y-3">
                      <% @available_roles.each do |role| %>
                        <div class="flex items-center">
                          <%= form.check_box :role_ids, 
                              { multiple: true, checked: false }, 
                              role.id, 
                              nil,
                              class: "h-4 w-4 text-blue-600 focus:ring-blue-500 border-stone-300 rounded" %>
                          <%= form.label "role_ids_#{role.id}", class: "ml-3 block text-sm text-stone-900" do %>
                            <span class="font-medium"><%= role.name.humanize %></span>
                            <span class="text-stone-500 block">
                              <%= User.admin_role_description(role.name) %>
                            </span>
                          <% end %>
                        </div>
                      <% end %>
                    </div>
                    <div class="mt-4">
                      <%= form.submit "Assign Selected Roles", 
                          class: "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500" %>
                    </div>
                  <% end %>
                </div>
              </div>
            <% end %>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
