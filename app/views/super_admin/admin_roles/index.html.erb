<% content_for :title, "Admin Role Management" %>

<div class="flex h-screen bg-stone-50">
  <!-- Sidebar -->
  <div class="w-64 bg-white shadow-sm border-r border-stone-200">
    <%= render 'shared/admin_sidebar' %>
  </div>

  <!-- Main Content -->
  <div class="flex-1 overflow-auto">
    <div class="p-8">
      <!-- Header -->
      <div class="mb-8">
        <div class="flex items-center justify-between">
          <div>
            <h1 class="text-2xl font-bold text-stone-900">Admin Role Management</h1>
            <p class="mt-1 text-sm text-stone-600">
              Manage administrative roles and permissions for users
            </p>
          </div>
          <div class="flex space-x-3">
            <%= link_to "Assign Roles", new_super_admin_admin_role_path, 
                class: "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500" %>
          </div>
        </div>
      </div>

      <!-- Search and Filters -->
      <div class="bg-white shadow rounded-lg mb-6">
        <%= form_with url: super_admin_admin_roles_path, method: :get, local: true, class: "p-6" do |form| %>
          <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <!-- Search -->
            <div>
              <%= form.label :q, "Search", class: "block text-sm font-medium text-stone-700 mb-1" %>
              <%= form.text_field :q, value: @q, placeholder: "Name, email, or ID...", 
                  class: "block w-full border-stone-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm" %>
            </div>

            <!-- Role Filter -->
            <div>
              <%= form.label :role_filter, "Role", class: "block text-sm font-medium text-stone-700 mb-1" %>
              <%= form.select :role_filter, 
                  options_for_select([['All Roles', 'all']] + @available_roles.map { |role| [role.humanize, role] }, @role_filter),
                  {},
                  { class: "block w-full border-stone-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm" } %>
            </div>

            <!-- Sort -->
            <div>
              <%= form.label :sort_by, "Sort by", class: "block text-sm font-medium text-stone-700 mb-1" %>
              <%= form.select :sort_by, 
                  options_for_select([
                    ['Name', 'name'],
                    ['Email', 'email'],
                    ['Created Date', 'created_at']
                  ], @sort_by),
                  {},
                  { class: "block w-full border-stone-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm" } %>
            </div>

            <!-- Actions -->
            <div class="flex items-end space-x-2">
              <%= form.submit "Filter", 
                  class: "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500" %>
              <%= link_to "Clear", super_admin_admin_roles_path, 
                  class: "inline-flex items-center px-4 py-2 border border-stone-300 rounded-md shadow-sm text-sm font-medium text-stone-700 bg-white hover:bg-stone-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500" %>
            </div>
          </div>
        <% end %>
      </div>

      <!-- Results Summary and Export -->
      <div class="mb-4 flex justify-between items-center">
        <p class="text-sm text-stone-600">
          Showing <%= @pagy.count %> of <%= @pagy.count %> admin users
        </p>
        <div class="flex space-x-2">
          <%= link_to "Export CSV", request.params.merge(format: :csv),
              class: "inline-flex items-center px-3 py-2 border border-stone-300 shadow-sm text-sm leading-4 font-medium rounded-md text-stone-700 bg-white hover:bg-stone-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" %>
        </div>
      </div>

      <!-- Users Table -->
      <div class="bg-white shadow rounded-lg overflow-hidden">
        <table class="min-w-full divide-y divide-stone-200">
          <thead class="bg-stone-50">
            <tr>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">
                User
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">
                Admin Roles
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">
                Permissions
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">
                Last Active
              </th>
              <th scope="col" class="relative px-6 py-3">
                <span class="sr-only">Actions</span>
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-stone-200">
            <% if @users.any? %>
              <% @users.each do |user| %>
                <tr class="hover:bg-stone-50">
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center">
                      <div class="flex-shrink-0 h-10 w-10">
                        <div class="h-10 w-10 rounded-full <%= user.avatar_color.join(' ') %> flex items-center justify-center">
                          <span class="text-sm font-medium"><%= user.initials %></span>
                        </div>
                      </div>
                      <div class="ml-4">
                        <div class="text-sm font-medium text-stone-900">
                          <%= user.full_name %>
                        </div>
                        <div class="text-sm text-stone-500">
                          <%= user.email %>
                        </div>
                      </div>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex flex-wrap gap-1">
                      <% user.roles.each do |role| %>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                          <%= role.name.humanize %>
                        </span>
                      <% end %>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-stone-500">
                    <% if user.admin_permissions == :all %>
                      <span class="text-green-600 font-medium">All Permissions</span>
                    <% else %>
                      <%= pluralize(user.admin_permissions.count, 'permission') %>
                    <% end %>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-stone-500">
                    <%= time_ago_in_words(user.updated_at) %> ago
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div class="flex justify-end space-x-2">
                      <%= link_to "View", super_admin_admin_role_path(user),
                          class: "text-blue-600 hover:text-blue-900" %>
                      <%= link_to "Edit", edit_super_admin_admin_role_path(user),
                          class: "text-stone-600 hover:text-stone-900" %>
                    </div>
                  </td>
                </tr>
              <% end %>
            <% else %>
              <tr>
                <td colspan="5" class="px-6 py-12 text-sm text-center text-stone-900">
                  <div class="text-stone-500">
                    <svg class="w-12 h-12 mx-auto mb-4 text-stone-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                    </svg>
                    <p class="text-sm font-medium">No admin users found</p>
                    <p class="text-xs">Try adjusting your search or filter criteria</p>
                  </div>
                </td>
              </tr>
            <% end %>
          </tbody>
        </table>

        <!-- Pagination -->
        <% if @pagy.pages > 1 %>
          <div class="bg-white px-4 py-3 border-t border-stone-200 sm:px-6">
            <%== pagy_nav(@pagy) %>
          </div>
        <% end %>
      </div>
    </div>
  </div>
</div>
