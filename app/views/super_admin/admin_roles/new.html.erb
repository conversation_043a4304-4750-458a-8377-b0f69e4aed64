<% content_for :title, "Assign Admin Roles" %>

<div class="flex h-screen bg-stone-50">
  <!-- Sidebar -->
  <div class="w-64 bg-white shadow-sm border-r border-stone-200">
    <%= render 'shared/admin_sidebar' %>
  </div>

  <!-- Main Content -->
  <div class="flex-1 overflow-auto">
    <div class="p-8">
      <!-- Header -->
      <div class="mb-8">
        <div class="flex items-center justify-between">
          <div>
            <nav class="flex" aria-label="Breadcrumb">
              <ol class="flex items-center space-x-4">
                <li>
                  <%= link_to "Admin Roles", super_admin_admin_roles_path, 
                      class: "text-stone-500 hover:text-stone-700" %>
                </li>
                <li>
                  <div class="flex items-center">
                    <svg class="flex-shrink-0 h-5 w-5 text-stone-400" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                    </svg>
                    <span class="ml-4 text-sm font-medium text-stone-900">Assign Roles</span>
                  </div>
                </li>
              </ol>
            </nav>
            <h1 class="mt-2 text-2xl font-bold text-stone-900">Assign Admin Roles</h1>
            <p class="mt-1 text-sm text-stone-600">
              Search for a user and assign administrative roles to them.
            </p>
          </div>
        </div>
      </div>

      <div class="max-w-4xl">
        <!-- User Search -->
        <div class="bg-white shadow rounded-lg mb-6">
          <div class="px-6 py-4 border-b border-stone-200">
            <h2 class="text-lg font-medium text-stone-900">Select User</h2>
            <p class="mt-1 text-sm text-stone-600">
              Search for the user you want to assign admin roles to.
            </p>
          </div>
          <div class="px-6 py-4">
            <div id="user-search">
              <div class="mb-4">
                <label for="user_search" class="block text-sm font-medium text-stone-700 mb-2">
                  Search Users
                </label>
                <input type="text" 
                       id="user_search" 
                       placeholder="Type name, email, or user ID..."
                       class="block w-full border-stone-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
              </div>
              
              <div id="search-results" class="hidden">
                <h3 class="text-sm font-medium text-stone-900 mb-3">Search Results</h3>
                <div id="user-list" class="space-y-2 max-h-60 overflow-y-auto">
                  <!-- Search results will be populated here -->
                </div>
              </div>
              
              <div id="selected-user" class="hidden">
                <h3 class="text-sm font-medium text-stone-900 mb-3">Selected User</h3>
                <div id="user-info" class="p-4 border border-stone-200 rounded-lg bg-stone-50">
                  <!-- Selected user info will be populated here -->
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Role Assignment Form -->
        <div id="role-assignment-form" class="bg-white shadow rounded-lg hidden">
          <div class="px-6 py-4 border-b border-stone-200">
            <h2 class="text-lg font-medium text-stone-900">Assign Roles</h2>
            <p class="mt-1 text-sm text-stone-600">
              Select the administrative roles to assign to the selected user.
            </p>
          </div>

          <%= form_with url: super_admin_admin_roles_path, method: :post, local: true, id: "assign-roles-form", class: "px-6 py-4" do |form| %>
            <%= form.hidden_field :user_id, id: "selected_user_id" %>
            
            <div class="space-y-6">
              <!-- Available Roles -->
              <div>
                <fieldset>
                  <legend class="text-base font-medium text-stone-900 mb-4">Available Admin Roles</legend>
                  <div class="space-y-4">
                    <% @available_roles.each do |role| %>
                      <div class="relative flex items-start p-4 border border-stone-200 rounded-lg hover:bg-stone-50">
                        <div class="flex items-center h-5">
                          <%= check_box_tag "role_ids[]", 
                              role.id, 
                              false,
                              id: "assign_role_#{role.id}",
                              class: "h-4 w-4 text-blue-600 focus:ring-blue-500 border-stone-300 rounded" %>
                        </div>
                        <div class="ml-3 text-sm">
                          <%= label_tag "assign_role_#{role.id}", class: "font-medium text-stone-900" do %>
                            <%= role.name.humanize %>
                          <% end %>
                          <p class="text-stone-500">
                            <%= User.admin_role_description(role.name) %>
                          </p>
                          
                          <!-- Show permissions for this role -->
                          <% role_permissions = AdminPermissions::ADMIN_ROLES[role.name.to_sym]&.dig(:permissions) %>
                          <% if role_permissions %>
                            <div class="mt-2">
                              <% if role_permissions == :all %>
                                <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-green-100 text-green-800">
                                  All Permissions
                                </span>
                              <% else %>
                                <div class="flex flex-wrap gap-1 mt-1">
                                  <% role_permissions.first(4).each do |permission| %>
                                    <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-blue-100 text-blue-800">
                                      <%= permission.humanize %>
                                    </span>
                                  <% end %>
                                  <% if role_permissions.count > 4 %>
                                    <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-stone-100 text-stone-600">
                                      +<%= role_permissions.count - 4 %> more
                                    </span>
                                  <% end %>
                                </div>
                              <% end %>
                            </div>
                          <% end %>
                        </div>
                      </div>
                    <% end %>
                  </div>
                </fieldset>
              </div>

              <!-- Form Actions -->
              <div class="flex justify-between pt-6 border-t border-stone-200">
                <div>
                  <%= link_to "Cancel", super_admin_admin_roles_path, 
                      class: "inline-flex items-center px-4 py-2 border border-stone-300 rounded-md shadow-sm text-sm font-medium text-stone-700 bg-white hover:bg-stone-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500" %>
                </div>
                <div>
                  <%= form.submit "Assign Roles", 
                      class: "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",
                      data: { 
                        confirm: "Are you sure you want to assign the selected roles to this user? This will immediately grant them administrative access." 
                      } %>
                </div>
              </div>
            </div>
          <% end %>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
  const searchInput = document.getElementById('user_search');
  const searchResults = document.getElementById('search-results');
  const userList = document.getElementById('user-list');
  const selectedUser = document.getElementById('selected-user');
  const userInfo = document.getElementById('user-info');
  const roleForm = document.getElementById('role-assignment-form');
  const selectedUserIdInput = document.getElementById('selected_user_id');
  
  let searchTimeout;
  
  searchInput.addEventListener('input', function() {
    clearTimeout(searchTimeout);
    const query = this.value.trim();
    
    if (query.length < 2) {
      searchResults.classList.add('hidden');
      return;
    }
    
    searchTimeout = setTimeout(() => {
      searchUsers(query);
    }, 300);
  });
  
  function searchUsers(query) {
    fetch(`/super_admin/admin_users?q=${encodeURIComponent(query)}&format=json`)
      .then(response => response.json())
      .then(data => {
        displaySearchResults(data.users);
      })
      .catch(error => {
        console.error('Search error:', error);
      });
  }
  
  function displaySearchResults(users) {
    userList.innerHTML = '';
    
    if (users.length === 0) {
      userList.innerHTML = '<p class="text-sm text-stone-500">No users found</p>';
    } else {
      users.forEach(user => {
        const userElement = createUserElement(user);
        userList.appendChild(userElement);
      });
    }
    
    searchResults.classList.remove('hidden');
  }
  
  function createUserElement(user) {
    const div = document.createElement('div');
    div.className = 'flex items-center justify-between p-3 border border-stone-200 rounded-lg hover:bg-stone-50 cursor-pointer';
    div.innerHTML = `
      <div class="flex items-center">
        <div class="flex-shrink-0 h-8 w-8 bg-stone-100 rounded-full flex items-center justify-center">
          <span class="text-xs font-medium text-stone-600">${user.initials}</span>
        </div>
        <div class="ml-3">
          <div class="text-sm font-medium text-stone-900">${user.full_name}</div>
          <div class="text-sm text-stone-500">${user.email}</div>
        </div>
      </div>
      <button type="button" class="text-blue-600 hover:text-blue-900 text-sm font-medium">
        Select
      </button>
    `;
    
    div.addEventListener('click', () => selectUser(user));
    return div;
  }
  
  function selectUser(user) {
    selectedUserIdInput.value = user.id;
    
    userInfo.innerHTML = `
      <div class="flex items-center">
        <div class="flex-shrink-0 h-10 w-10 bg-stone-100 rounded-full flex items-center justify-center">
          <span class="text-sm font-medium text-stone-600">${user.initials}</span>
        </div>
        <div class="ml-3">
          <div class="text-sm font-medium text-stone-900">${user.full_name}</div>
          <div class="text-sm text-stone-500">${user.email}</div>
          <div class="text-xs text-stone-400">ID: ${user.id}</div>
        </div>
      </div>
    `;
    
    searchResults.classList.add('hidden');
    selectedUser.classList.remove('hidden');
    roleForm.classList.remove('hidden');
    searchInput.value = '';
  }
});
</script>
