<div class="px-4 mx-auto max-w-7xl sm:px-6 lg:px-8">
  <div class="py-6">
    <h1 class="text-3xl font-bold text-gray-900">Admin Dashboard</h1>
    
    <!-- Stats Grid -->
    <div class="grid grid-cols-1 gap-5 mt-8 sm:grid-cols-2 lg:grid-cols-4">
      <div class="overflow-hidden bg-white rounded-lg shadow">
        <div class="p-5">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="flex items-center justify-center w-8 h-8 bg-blue-500 rounded-md">
                <span class="font-semibold text-white">U</span>
              </div>
            </div>
            <div class="flex-1 w-0 ml-5">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">Total Users</dt>
                <dd class="text-lg font-medium text-gray-900"><%= @user_count %></dd>
              </dl>
            </div>
          </div>
        </div>
      </div>

      <div class="overflow-hidden bg-white rounded-lg shadow">
        <div class="p-5">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="flex items-center justify-center w-8 h-8 bg-green-500 rounded-md">
                <span class="font-semibold text-white">O</span>
              </div>
            </div>
            <div class="flex-1 w-0 ml-5">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">Organizations</dt>
                <dd class="text-lg font-medium text-gray-900"><%= @organization_count %></dd>
              </dl>
            </div>
          </div>
        </div>
      </div>

      <div class="overflow-hidden bg-white rounded-lg shadow">
        <div class="p-5">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="flex items-center justify-center w-8 h-8 bg-purple-500 rounded-md">
                <span class="font-semibold text-white">J</span>
              </div>
            </div>
            <div class="flex-1 w-0 ml-5">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">Total Jobs</dt>
                <dd class="text-lg font-medium text-gray-900"><%= @job_count %></dd>
              </dl>
            </div>
          </div>
        </div>
      </div>

      <div class="overflow-hidden bg-white rounded-lg shadow">
        <div class="p-5">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="flex items-center justify-center w-8 h-8 bg-orange-500 rounded-md">
                <span class="font-semibold text-white">T</span>
              </div>
            </div>
            <div class="flex-1 w-0 ml-5">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">Talent Profiles</dt>
                <dd class="text-lg font-medium text-gray-900"><%= @talent_profile_count %></dd>
              </dl>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Recent Activity -->
    <div class="grid grid-cols-1 gap-6 mt-8 lg:grid-cols-2">
      <!-- Recent Users -->
      <div class="bg-white rounded-lg shadow">
        <div class="px-4 py-5 sm:p-6">
          <h3 class="text-lg font-medium leading-6 text-gray-900">Recent Users</h3>
          <div class="mt-5">
            <div class="flow-root">
              <ul class="-my-5 divide-y divide-gray-200">
                <% @recent_users.each do |user| %>
                  <li class="py-4">
                    <div class="flex items-center space-x-4">
                      <div class="flex-1 min-w-0">
                        <p class="text-sm font-medium text-gray-900 truncate">
                          <%= user.full_name.presence || user.email %>
                        </p>
                        <p class="text-sm text-gray-500 truncate">
                          <%= user.email %>
                        </p>
                      </div>
                      <div class="flex-shrink-0 text-sm text-gray-500">
                        <%= time_ago_in_words(user.created_at) %> ago
                      </div>
                    </div>
                  </li>
                <% end %>
              </ul>
            </div>
          </div>
        </div>
      </div>

      <!-- Recent Jobs -->
      <div class="bg-white rounded-lg shadow">
        <div class="px-4 py-5 sm:p-6">
          <h3 class="text-lg font-medium leading-6 text-gray-900">Recent Jobs</h3>
          <div class="mt-5">
            <div class="flow-root">
              <ul class="-my-5 divide-y divide-gray-200">
                <% @recent_jobs.each do |job| %>
                  <li class="py-4">
                    <div class="flex items-center space-x-4">
                      <div class="flex-1 min-w-0">
                        <p class="text-sm font-medium text-gray-900 truncate">
                          <%= job.title %>
                        </p>
                        <p class="text-sm text-gray-500 truncate">
                          <%= job.job_category&.humanize %>
                        </p>
                      </div>
                      <div class="flex-shrink-0 text-sm text-gray-500">
                        <%= time_ago_in_words(job.created_at) %> ago
                      </div>
                    </div>
                  </li>
                <% end %>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Quick Actions -->
    <div class="mt-8">
      <div class="bg-white rounded-lg shadow">
        <div class="px-4 py-5 sm:p-6">
          <h3 class="text-lg font-medium leading-6 text-gray-900">Quick Actions</h3>
          <div class="grid grid-cols-1 gap-4 mt-5 sm:grid-cols-2 lg:grid-cols-4">
            <%= link_to super_admin_madmin_users_path, class: "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700" do %>
              Manage Users
            <% end %>
            <%= link_to super_admin_madmin_organizations_path, class: "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700" do %>
              Manage Organizations
            <% end %>
            <%= link_to super_admin_madmin_jobs_path, class: "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700" do %>
              Manage Jobs
            <% end %>
            <%= link_to super_admin_madmin_roles_path, class: "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-orange-600 hover:bg-orange-700" do %>
              Manage Roles
            <% end %>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
