<div class="bg-white rounded-lg shadow">
  <!-- <PERSON>er -->
  <div class="px-6 py-4 border-b border-stone-200">
    <h1 class="text-2xl font-bold text-stone-900">Admin Sessions</h1>
    <p class="mt-1 text-sm text-stone-600">Monitor and manage admin user sessions</p>
  </div>

  <!-- Statistics Cards -->
  <div class="p-6 border-b border-stone-200">
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
      <div class="bg-blue-50 rounded-lg p-4">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <svg class="h-8 w-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-blue-600">Total Sessions</p>
            <p class="text-2xl font-bold text-blue-900"><%= @stats[:total] %></p>
          </div>
        </div>
      </div>

      <div class="bg-green-50 rounded-lg p-4">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <svg class="h-8 w-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-green-600">Active Sessions</p>
            <p class="text-2xl font-bold text-green-900"><%= @stats[:active] %></p>
          </div>
        </div>
      </div>

      <div class="bg-red-50 rounded-lg p-4">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <svg class="h-8 w-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
            </svg>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-red-600">Locked Sessions</p>
            <p class="text-2xl font-bold text-red-900"><%= @stats[:locked] %></p>
          </div>
        </div>
      </div>

      <div class="bg-yellow-50 rounded-lg p-4">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <svg class="h-8 w-8 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
            </svg>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-yellow-600">With Warnings</p>
            <p class="text-2xl font-bold text-yellow-900"><%= @stats[:with_warnings] %></p>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Filters Section -->
  <div class="p-6 border-b border-stone-200">
    <%= form_with url: super_admin_admin_sessions_path, method: :get, local: true, class: "space-y-4" do |form| %>
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <!-- Status Filter -->
        <div>
          <%= form.label :status, "Status", class: "block text-sm font-medium text-stone-700 mb-1" %>
          <%= form.select :status, 
                          options_for_select([
                            ['All Statuses', 'all'],
                            ['Active', 'active'],
                            ['Inactive', 'inactive'],
                            ['Locked', 'locked']
                          ], params[:status]),
                          {}, 
                          { class: "w-full rounded-md border-stone-300 shadow-sm focus:border-blue-500 focus:ring-blue-500" } %>
        </div>

        <!-- User Filter -->
        <div>
          <%= form.label :user_filter, "User", class: "block text-sm font-medium text-stone-700 mb-1" %>
          <%= form.select :user_filter, 
                          options_for_select([['All Users', 'all']] + @available_users.map { |user| [user.email, user.id] }, params[:user_filter]),
                          {}, 
                          { class: "w-full rounded-md border-stone-300 shadow-sm focus:border-blue-500 focus:ring-blue-500" } %>
        </div>

        <!-- IP Address Filter -->
        <div>
          <%= form.label :ip_filter, "IP Address", class: "block text-sm font-medium text-stone-700 mb-1" %>
          <%= form.select :ip_filter, 
                          options_for_select([['All IPs', '']] + @available_ips.map { |ip| [ip, ip] }, params[:ip_filter]),
                          {}, 
                          { class: "w-full rounded-md border-stone-300 shadow-sm focus:border-blue-500 focus:ring-blue-500" } %>
        </div>
      </div>

      <div class="flex justify-between items-center">
        <div class="flex space-x-2">
          <%= form.submit "Apply Filters", class: "px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2" %>
          <%= link_to "Clear Filters", super_admin_admin_sessions_path, class: "px-4 py-2 text-stone-600 border border-stone-300 rounded-md hover:bg-stone-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2" %>
        </div>
      </div>
    <% end %>
  </div>

  <!-- Sessions Table -->
  <div class="p-6">
    <h2 class="text-lg font-medium text-stone-900 mb-4">
      Admin Sessions (<%= @pagy.count %> total)
    </h2>

    <div class="overflow-x-auto">
      <table class="min-w-full divide-y divide-stone-200">
        <thead class="bg-stone-50">
          <tr>
            <th class="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">User</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">Status</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">IP Address</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">User Agent</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">Last Activity</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">Warnings</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">Actions</th>
          </tr>
        </thead>
        <tbody class="bg-white divide-y divide-stone-200">
          <% if @sessions.any? %>
            <% @sessions.each do |session| %>
              <tr class="hover:bg-stone-50">
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="flex items-center">
                    <div class="flex-shrink-0 h-10 w-10">
                      <div class="h-10 w-10 rounded-full bg-stone-200 flex items-center justify-center">
                        <span class="text-sm font-medium text-stone-700">
                          <%= session.user.initials %>
                        </span>
                      </div>
                    </div>
                    <div class="ml-4">
                      <div class="text-sm font-medium text-stone-900"><%= session.user.full_name %></div>
                      <div class="text-sm text-stone-500"><%= session.user.email %></div>
                    </div>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <% if session.locked? %>
                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">
                      Locked
                    </span>
                  <% elsif session.active? %>
                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                      Active
                    </span>
                  <% else %>
                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-stone-100 text-stone-800">
                      Inactive
                    </span>
                  <% end %>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-stone-900">
                  <%= session.ip_address %>
                </td>
                <td class="px-6 py-4 text-sm text-stone-500 max-w-xs truncate">
                  <%= session.user_agent %>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-stone-500">
                  <%= time_ago_in_words(session.updated_at) %> ago
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-stone-900">
                  <% if session.security_warnings_count > 0 %>
                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">
                      <%= session.security_warnings_count %> warnings
                    </span>
                  <% else %>
                    <span class="text-stone-400">None</span>
                  <% end %>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                  <%= link_to "View", super_admin_admin_session_path(session),
                      class: "text-blue-600 hover:text-blue-900" %>
                  <% if session.locked? %>
                    <%= link_to "Unlock", unlock_super_admin_admin_session_path(session),
                        method: :patch,
                        class: "text-green-600 hover:text-green-900",
                        data: { confirm: "Are you sure you want to unlock this session?" } %>
                  <% end %>
                  <%= link_to "Terminate", super_admin_admin_session_path(session),
                      method: :delete,
                      class: "text-red-600 hover:text-red-900",
                      data: { confirm: "Are you sure you want to terminate this session?" } %>
                </td>
              </tr>
            <% end %>
          <% else %>
            <tr>
              <td colspan="7" class="px-6 py-12 text-center">
                <div class="flex flex-col items-center">
                  <svg class="h-12 w-12 text-stone-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                  </svg>
                  <h3 class="text-lg font-medium text-stone-900 mb-1">No admin sessions found</h3>
                  <p class="text-stone-500">Try adjusting your search or filter criteria</p>
                </div>
              </td>
            </tr>
          <% end %>
        </tbody>
      </table>
    </div>

    <!-- Pagination -->
    <% if @pagy.pages > 1 %>
      <div class="mt-6">
        <%= render 'shared/admin/pagination', collection: @pagy %>
      </div>
    <% end %>
  </div>
</div>
