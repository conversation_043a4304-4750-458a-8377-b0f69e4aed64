<% content_for :title, "Talent Notes" %>

<div class="bg-white rounded-lg shadow">
  <!-- Header -->
  <div class="px-6 py-4 border-b border-stone-200">
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-xl font-semibold text-stone-900">Talent Notes</h1>
        <p class="text-sm text-stone-500 mt-1">Monitor talent notes and content moderation</p>
      </div>
      <div class="flex items-center space-x-3">
        <%= link_to "Export CSV", super_admin_admin_talent_notes_path(format: :csv, **request.query_parameters), 
            class: "inline-flex items-center px-3 py-2 border border-stone-300 rounded-md text-sm font-medium text-stone-700 bg-white hover:bg-stone-50" %>
      </div>
    </div>
  </div>

  <!-- Stats Cards -->
  <div class="px-6 py-4 border-b border-stone-200 bg-stone-50">
    <div class="grid grid-cols-1 md:grid-cols-6 gap-4">
      <div class="bg-white p-4 rounded-lg border border-stone-200">
        <div class="text-2xl font-bold text-stone-900"><%= @stats[:total] %></div>
        <div class="text-sm text-stone-500">Total Notes</div>
      </div>
      <div class="bg-white p-4 rounded-lg border border-stone-200">
        <div class="text-2xl font-bold text-yellow-600"><%= @stats[:pinned] %></div>
        <div class="text-sm text-stone-500">Pinned Notes</div>
      </div>
      <div class="bg-white p-4 rounded-lg border border-stone-200">
        <div class="text-2xl font-bold text-blue-600"><%= @stats[:unique_authors] %></div>
        <div class="text-sm text-stone-500">Authors</div>
      </div>
      <div class="bg-white p-4 rounded-lg border border-stone-200">
        <div class="text-2xl font-bold text-green-600"><%= @stats[:unique_talents] %></div>
        <div class="text-sm text-stone-500">Talents</div>
      </div>
      <div class="bg-white p-4 rounded-lg border border-stone-200">
        <div class="text-2xl font-bold text-purple-600"><%= @stats[:notes_this_week] %></div>
        <div class="text-sm text-stone-500">This Week</div>
      </div>
      <div class="bg-white p-4 rounded-lg border border-stone-200">
        <div class="text-2xl font-bold text-orange-600"><%= @stats[:avg_notes_per_talent] %></div>
        <div class="text-sm text-stone-500">Avg per Talent</div>
      </div>
    </div>
  </div>

  <!-- Filters -->
  <div class="px-6 py-4 border-b border-stone-200">
    <%= form_with url: super_admin_admin_talent_notes_path, method: :get, local: true, class: "space-y-4" do |form| %>
      <!-- Search and Actions Row -->
      <div class="flex flex-col sm:flex-row gap-4">
        <div class="flex-1">
          <%= form.text_field :search, placeholder: "Search by content, author, talent name, email, or organization...", 
              value: params[:search], 
              class: "w-full px-3 py-2 border border-stone-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" %>
        </div>
        <div class="flex gap-2">
          <%= form.submit "Filter", class: "px-4 py-2 bg-blue-600 text-white rounded-md text-sm font-medium hover:bg-blue-700" %>
          <%= link_to "Clear", super_admin_admin_talent_notes_path, class: "px-4 py-2 border border-stone-300 text-stone-700 rounded-md text-sm font-medium hover:bg-stone-50" %>
        </div>
      </div>

      <!-- Filter Dropdowns Row -->
      <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-6 gap-4">
        <div>
          <%= form.select :user_id, options_from_collection_for_select([OpenStruct.new(id: 'all', display_name: 'All Authors')] + @available_users.map { |u| OpenStruct.new(id: u.id, display_name: u.name.full) }, :id, :display_name, params[:user_id]), 
              {}, { class: "w-full px-3 py-2 border border-stone-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500" } %>
        </div>
        <div>
          <%= form.select :talent_profile_id, options_from_collection_for_select([OpenStruct.new(id: 'all', display_name: 'All Talents')] + @available_talents.map { |t| OpenStruct.new(id: t.id, display_name: t.user.name.full) }, :id, :display_name, params[:talent_profile_id]), 
              {}, { class: "w-full px-3 py-2 border border-stone-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500" } %>
        </div>
        <div>
          <%= form.select :organization_id, options_from_collection_for_select([OpenStruct.new(id: 'all', display_name: 'All Organizations')] + @available_organizations.map { |o| OpenStruct.new(id: o.id, display_name: o.name) }, :id, :display_name, params[:organization_id]), 
              {}, { class: "w-full px-3 py-2 border border-stone-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500" } %>
        </div>
        <div>
          <%= form.select :category, options_for_select([['All Categories', 'all']] + @note_categories.map { |cat| [cat.humanize, cat] }, params[:category]), 
              {}, { class: "w-full px-3 py-2 border border-stone-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500" } %>
        </div>
        <div>
          <%= form.select :pinned, options_for_select([['All Notes', 'all'], ['Pinned Only', 'true'], ['Not Pinned', 'false']], params[:pinned]), 
              {}, { class: "w-full px-3 py-2 border border-stone-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500" } %>
        </div>
        <div>
          <%= form.select :date_range, options_for_select([
                ['All Time', 'all'],
                ['Today', 'today'],
                ['This Week', 'week'],
                ['This Month', 'month']
              ], params[:date_range]), 
              {}, { class: "w-full px-3 py-2 border border-stone-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500" } %>
        </div>
      </div>
    <% end %>
  </div>

  <!-- Bulk Actions -->
  <%= form_with url: bulk_update_super_admin_admin_talent_notes_path, method: :post, local: true, id: "bulk-form" do |form| %>
    <div class="px-6 py-3 border-b border-stone-200 bg-stone-50">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
          <input type="checkbox" id="select-all" class="rounded border-stone-300 text-blue-600 focus:ring-blue-500">
          <label for="select-all" class="text-sm text-stone-700">Select All</label>
        </div>
        <div class="flex items-center space-x-2">
          <%= form.select :bulk_action, options_for_select([
                ['Pin Selected', 'pin'],
                ['Unpin Selected', 'unpin'],
                ['Delete Selected', 'delete']
              ]), 
              { prompt: 'Bulk Actions' }, 
              { class: "px-3 py-2 border border-stone-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500" } %>
          <%= form.submit "Apply", class: "px-4 py-2 bg-blue-600 text-white rounded-md text-sm font-medium hover:bg-blue-700", 
              onclick: "return confirm('Are you sure you want to apply this action to selected notes?')" %>
        </div>
      </div>
    </div>

    <!-- Table -->
    <div class="overflow-x-auto">
      <% if @talent_notes.any? %>
        <table class="min-w-full divide-y divide-stone-200">
          <thead class="bg-stone-50">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">
                <input type="checkbox" id="select-all-header" class="rounded border-stone-300 text-blue-600 focus:ring-blue-500">
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">Author</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">Talent</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">Content</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">Category</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">Organization</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">Created</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">Actions</th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-stone-200">
            <% @talent_notes.each do |note| %>
              <tr class="hover:bg-stone-50">
                <td class="px-6 py-4 whitespace-nowrap">
                  <%= check_box_tag "talent_note_ids[]", note.id, false, 
                      class: "talent-note-checkbox rounded border-stone-300 text-blue-600 focus:ring-blue-500" %>
                </td>
                <td class="px-6 py-4">
                  <div class="flex items-center">
                    <% if note.user.avatar.attached? %>
                      <%= image_tag note.user.avatar, class: "h-8 w-8 rounded-full object-cover mr-3" %>
                    <% else %>
                      <div class="h-8 w-8 rounded-full bg-stone-300 flex items-center justify-center mr-3">
                        <span class="text-stone-600 text-xs font-medium">
                          <%= note.user.first_name&.first&.upcase %>
                        </span>
                      </div>
                    <% end %>
                    <div>
                      <div class="text-sm font-medium text-stone-900"><%= note.user.name.full %></div>
                      <div class="text-sm text-stone-500"><%= note.user.email %></div>
                    </div>
                  </div>
                </td>
                <td class="px-6 py-4">
                  <div class="flex items-center">
                    <% if note.talent_profile.user.avatar.attached? %>
                      <%= image_tag note.talent_profile.user.avatar, class: "h-8 w-8 rounded-full object-cover mr-3" %>
                    <% else %>
                      <div class="h-8 w-8 rounded-full bg-stone-300 flex items-center justify-center mr-3">
                        <span class="text-stone-600 text-xs font-medium">
                          <%= note.talent_profile.user.first_name&.first&.upcase %>
                        </span>
                      </div>
                    <% end %>
                    <div>
                      <div class="text-sm font-medium text-stone-900"><%= note.talent_profile.user.name.full %></div>
                      <div class="text-sm text-stone-500"><%= note.talent_profile.user.email %></div>
                    </div>
                  </div>
                </td>
                <td class="px-6 py-4">
                  <div class="text-sm text-stone-900 max-w-xs">
                    <% content_preview = note.content.respond_to?(:to_plain_text) ? note.content.to_plain_text : note.content.to_s %>
                    <div class="truncate" title="<%= content_preview %>">
                      <%= content_preview.truncate(80) %>
                    </div>
                    <% if note.pinned? %>
                      <div class="mt-1">
                        <span class="inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">
                          📌 Pinned
                        </span>
                      </div>
                    <% end %>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <% case note.category %>
                  <% when 'general' %>
                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-stone-100 text-stone-800">General</span>
                  <% when 'interview_feedback' %>
                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">Interview</span>
                  <% when 'follow_up' %>
                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">Follow Up</span>
                  <% when 'skill_assessment' %>
                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-purple-100 text-purple-800">Skills</span>
                  <% when 'reference_check' %>
                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-orange-100 text-orange-800">Reference</span>
                  <% when 'hiring_decision' %>
                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">Hiring</span>
                  <% end %>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-stone-900">
                  <%= note.organization.name %>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-stone-500">
                  <%= note.created_at.strftime('%b %d, %Y') %>
                  <div class="text-xs text-stone-400">
                    <%= time_ago_in_words(note.created_at) %> ago
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                  <%= link_to "View", super_admin_admin_talent_note_path(note), 
                      class: "text-blue-600 hover:text-blue-900" %>
                  <%= link_to "Edit", edit_super_admin_admin_talent_note_path(note), 
                      class: "text-indigo-600 hover:text-indigo-900" %>
                  <%= link_to "Delete", super_admin_admin_talent_note_path(note), 
                      method: :delete, 
                      confirm: "Are you sure you want to delete this note?", 
                      class: "text-red-600 hover:text-red-900" %>
                </td>
              </tr>
            <% end %>
          </tbody>
        </table>
      <% else %>
        <div class="text-center py-12">
          <svg class="mx-auto h-12 w-12 text-stone-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
          </svg>
          <h3 class="mt-2 text-sm font-medium text-stone-900">No talent notes found</h3>
          <p class="mt-1 text-sm text-stone-500">No notes match your current filters.</p>
        </div>
      <% end %>
    </div>
  <% end %>

  <!-- Pagination -->
  <% if @talent_notes.any? %>
    <div class="px-6 py-4 border-t border-stone-200 bg-stone-50">
      <%== pagy_nav(@pagy) if @pagy.pages > 1 %>
    </div>
  <% end %>
</div>

<script>
  // Bulk selection functionality
  document.addEventListener('DOMContentLoaded', function() {
    const selectAllCheckbox = document.getElementById('select-all');
    const talentNoteCheckboxes = document.querySelectorAll('.talent-note-checkbox');
    
    selectAllCheckbox.addEventListener('change', function() {
      talentNoteCheckboxes.forEach(checkbox => {
        checkbox.checked = this.checked;
      });
    });
    
    talentNoteCheckboxes.forEach(checkbox => {
      checkbox.addEventListener('change', function() {
        const allChecked = Array.from(talentNoteCheckboxes).every(cb => cb.checked);
        const noneChecked = Array.from(talentNoteCheckboxes).every(cb => !cb.checked);
        
        selectAllCheckbox.checked = allChecked;
        selectAllCheckbox.indeterminate = !allChecked && !noneChecked;
      });
    });
  });
</script>
