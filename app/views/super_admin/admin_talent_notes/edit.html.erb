<% content_for :title, "Edit Talent Note ##{@talent_note.id}" %>

<div class="bg-white rounded-lg shadow">
  <!-- Header -->
  <div class="px-6 py-4 border-b border-stone-200">
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-xl font-semibold text-stone-900">Edit Talent Note #<%= @talent_note.id %></h1>
        <p class="text-sm text-stone-500 mt-1">Modify note content and settings</p>
      </div>
      <div class="flex items-center space-x-3">
        <%= link_to "View Note", super_admin_admin_talent_note_path(@talent_note), 
            class: "inline-flex items-center px-3 py-2 border border-stone-300 rounded-md text-sm font-medium text-stone-700 bg-white hover:bg-stone-50" %>
        <%= link_to "Back to List", super_admin_admin_talent_notes_path, 
            class: "inline-flex items-center px-3 py-2 border border-stone-300 rounded-md text-sm font-medium text-stone-700 bg-white hover:bg-stone-50" %>
      </div>
    </div>
  </div>

  <div class="p-6">
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
      <!-- Left Column: Edit Form -->
      <div class="lg:col-span-2">
        <%= form_with model: [:super_admin, @talent_note], local: true, class: "space-y-6" do |form| %>
          <% if @talent_note.errors.any? %>
            <div class="bg-red-50 border border-red-200 rounded-md p-4">
              <div class="flex">
                <div class="flex-shrink-0">
                  <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                  </svg>
                </div>
                <div class="ml-3">
                  <h3 class="text-sm font-medium text-red-800">
                    There were <%= pluralize(@talent_note.errors.count, "error") %> with your submission:
                  </h3>
                  <div class="mt-2 text-sm text-red-700">
                    <ul class="list-disc pl-5 space-y-1">
                      <% @talent_note.errors.full_messages.each do |message| %>
                        <li><%= message %></li>
                      <% end %>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          <% end %>

          <!-- Content Field -->
          <div>
            <%= form.label :content, "Note Content", class: "block text-sm font-medium text-stone-700 mb-2" %>
            <%= form.rich_text_area :content, 
                placeholder: "Enter the note content...", 
                class: "w-full rounded-md border-stone-300 shadow-sm focus:border-blue-500 focus:ring-blue-500",
                style: "min-height: 200px;" %>
            <p class="mt-1 text-sm text-stone-500">Use the rich text editor to format your note content.</p>
          </div>

          <!-- Category Field -->
          <div>
            <%= form.label :category, "Category", class: "block text-sm font-medium text-stone-700 mb-2" %>
            <%= form.select :category, 
                options_for_select(TalentNote.categories.keys.map { |k| [k.humanize, k] }, @talent_note.category), 
                {}, 
                { class: "w-full px-3 py-2 border border-stone-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" } %>
            <p class="mt-1 text-sm text-stone-500">Select the appropriate category for this note.</p>
          </div>

          <!-- Pinned Field -->
          <div class="flex items-start">
            <div class="flex items-center h-5">
              <%= form.check_box :pinned, 
                  class: "h-4 w-4 rounded border-stone-300 text-blue-600 focus:ring-blue-500" %>
            </div>
            <div class="ml-3 text-sm">
              <%= form.label :pinned, "Pin this note", class: "font-medium text-stone-700" %>
              <p class="text-stone-500">Pinned notes appear at the top of the talent's note list.</p>
            </div>
          </div>

          <!-- Form Actions -->
          <div class="flex items-center justify-end space-x-3 pt-6 border-t border-stone-200">
            <%= link_to "Cancel", super_admin_admin_talent_note_path(@talent_note), 
                class: "inline-flex items-center px-4 py-2 border border-stone-300 rounded-md text-sm font-medium text-stone-700 bg-white hover:bg-stone-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500" %>
            <%= form.submit "Update Note", 
                class: "inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500" %>
          </div>
        <% end %>
      </div>

      <!-- Right Column: Context Information -->
      <div class="space-y-6">
        <!-- Note Context -->
        <div>
          <h3 class="text-lg font-medium text-stone-900 mb-4">Note Context</h3>
          <div class="bg-stone-50 rounded-lg p-4">
            <div class="space-y-3 text-sm">
              <div class="flex justify-between">
                <span class="font-medium text-stone-600">Note ID:</span>
                <span class="text-stone-900 font-mono"><%= @talent_note.id %></span>
              </div>
              
              <div class="flex justify-between">
                <span class="font-medium text-stone-600">Created:</span>
                <span class="text-stone-900"><%= @talent_note.created_at.strftime('%b %d, %Y') %></span>
              </div>
              
              <div class="flex justify-between">
                <span class="font-medium text-stone-600">Last Updated:</span>
                <span class="text-stone-900"><%= @talent_note.updated_at.strftime('%b %d, %Y') %></span>
              </div>
              
              <% if @talent_note.last_modified_by.present? %>
                <div class="flex justify-between">
                  <span class="font-medium text-stone-600">Last Modified By:</span>
                  <span class="text-stone-900"><%= @talent_note.last_modified_by.name.full %></span>
                </div>
              <% end %>
            </div>
          </div>
        </div>

        <!-- Author Information -->
        <div>
          <h3 class="text-lg font-medium text-stone-900 mb-4">Original Author</h3>
          <div class="bg-stone-50 rounded-lg p-4">
            <div class="flex items-center space-x-3 mb-3">
              <% if @talent_note.user.avatar.attached? %>
                <%= image_tag @talent_note.user.avatar, class: "h-10 w-10 rounded-full object-cover" %>
              <% else %>
                <div class="h-10 w-10 rounded-full bg-stone-300 flex items-center justify-center">
                  <span class="text-stone-600 font-medium">
                    <%= @talent_note.user.first_name&.first&.upcase %>
                  </span>
                </div>
              <% end %>
              <div>
                <div class="font-medium text-stone-900"><%= @talent_note.user.name.full %></div>
                <div class="text-sm text-stone-600"><%= @talent_note.user.email %></div>
              </div>
            </div>
            
            <div class="text-sm text-stone-600">
              <div class="flex justify-between">
                <span>Organization:</span>
                <span class="text-stone-900"><%= @talent_note.organization.name %></span>
              </div>
            </div>
          </div>
        </div>

        <!-- Talent Information -->
        <div>
          <h3 class="text-lg font-medium text-stone-900 mb-4">About Talent</h3>
          <div class="bg-stone-50 rounded-lg p-4">
            <div class="flex items-center space-x-3 mb-3">
              <% if @talent_note.talent_profile.user.avatar.attached? %>
                <%= image_tag @talent_note.talent_profile.user.avatar, class: "h-10 w-10 rounded-full object-cover" %>
              <% else %>
                <div class="h-10 w-10 rounded-full bg-stone-300 flex items-center justify-center">
                  <span class="text-stone-600 font-medium">
                    <%= @talent_note.talent_profile.user.first_name&.first&.upcase %>
                  </span>
                </div>
              <% end %>
              <div>
                <div class="font-medium text-stone-900"><%= @talent_note.talent_profile.user.name.full %></div>
                <div class="text-sm text-stone-600"><%= @talent_note.talent_profile.user.email %></div>
              </div>
            </div>
            
            <div class="space-y-2 text-sm">
              <% if @talent_note.talent_profile.headline.present? %>
                <div>
                  <span class="font-medium text-stone-600">Headline:</span>
                  <div class="text-stone-900 mt-1"><%= @talent_note.talent_profile.headline %></div>
                </div>
              <% end %>
              
              <div class="flex justify-between">
                <span class="font-medium text-stone-600">Total Notes:</span>
                <span class="text-stone-900"><%= @talent_note.talent_profile.talent_notes.count %></span>
              </div>
              
              <div class="flex justify-between">
                <span class="font-medium text-stone-600">Availability:</span>
                <span class="text-stone-900">
                  <% case @talent_note.talent_profile.availability_status %>
                  <% when 'available' %>
                    <span class="text-green-600">Available</span>
                  <% when 'limited' %>
                    <span class="text-yellow-600">Limited</span>
                  <% when 'unavailable' %>
                    <span class="text-red-600">Unavailable</span>
                  <% end %>
                </span>
              </div>
            </div>
            
            <div class="mt-3">
              <%= link_to "View Talent Profile", super_admin_admin_talent_profile_path(@talent_note.talent_profile), 
                  class: "inline-flex items-center px-3 py-1 border border-stone-300 rounded-md text-sm font-medium text-stone-700 bg-white hover:bg-stone-50" %>
            </div>
          </div>
        </div>

        <!-- Moderation Guidelines -->
        <div>
          <h3 class="text-lg font-medium text-stone-900 mb-4">Moderation Guidelines</h3>
          <div class="bg-blue-50 rounded-lg p-4">
            <div class="text-sm text-blue-800 space-y-2">
              <p><strong>Content Review:</strong></p>
              <ul class="list-disc pl-4 space-y-1">
                <li>Ensure content is professional and appropriate</li>
                <li>Remove any personally identifiable information if inappropriate</li>
                <li>Verify accuracy of assessment-related notes</li>
                <li>Check for compliance with company policies</li>
              </ul>
              
              <p class="mt-3"><strong>Category Guidelines:</strong></p>
              <ul class="list-disc pl-4 space-y-1">
                <li><strong>General:</strong> Basic observations and comments</li>
                <li><strong>Interview:</strong> Feedback from interviews</li>
                <li><strong>Follow Up:</strong> Action items and next steps</li>
                <li><strong>Skills:</strong> Technical assessments</li>
                <li><strong>Reference:</strong> Reference check results</li>
                <li><strong>Hiring:</strong> Final hiring decisions</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
