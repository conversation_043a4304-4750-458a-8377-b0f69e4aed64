<% content_for :title, "Talent Note ##{@talent_note.id}" %>

<div class="bg-white rounded-lg shadow">
  <!-- Header -->
  <div class="px-6 py-4 border-b border-stone-200">
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-xl font-semibold text-stone-900">Talent Note #<%= @talent_note.id %></h1>
        <p class="text-sm text-stone-500 mt-1">View talent note details and content</p>
      </div>
      <div class="flex items-center space-x-3">
        <%= link_to "Edit Note", edit_super_admin_admin_talent_note_path(@talent_note), 
            class: "inline-flex items-center px-3 py-2 border border-stone-300 rounded-md text-sm font-medium text-stone-700 bg-white hover:bg-stone-50" %>
        <%= link_to "Back to List", super_admin_admin_talent_notes_path, 
            class: "inline-flex items-center px-3 py-2 border border-stone-300 rounded-md text-sm font-medium text-stone-700 bg-white hover:bg-stone-50" %>
      </div>
    </div>
  </div>

  <div class="p-6">
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
      <!-- Left Column: Note Content -->
      <div class="space-y-6">
        <!-- Note Content -->
        <div>
          <h3 class="text-lg font-medium text-stone-900 mb-4">Note Content</h3>
          <div class="bg-stone-50 rounded-lg p-6">
            <div class="prose prose-sm max-w-none">
              <%= @talent_note.content %>
            </div>
          </div>
        </div>

        <!-- Note Metadata -->
        <div>
          <h3 class="text-lg font-medium text-stone-900 mb-4">Note Details</h3>
          <div class="bg-stone-50 rounded-lg p-4">
            <div class="space-y-3">
              <div class="flex justify-between items-center">
                <span class="text-sm font-medium text-stone-600">Category:</span>
                <div>
                  <% case @talent_note.category %>
                  <% when 'general' %>
                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-stone-100 text-stone-800">General</span>
                  <% when 'interview_feedback' %>
                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">Interview Feedback</span>
                  <% when 'follow_up' %>
                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">Follow Up</span>
                  <% when 'skill_assessment' %>
                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-purple-100 text-purple-800">Skill Assessment</span>
                  <% when 'reference_check' %>
                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-orange-100 text-orange-800">Reference Check</span>
                  <% when 'hiring_decision' %>
                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">Hiring Decision</span>
                  <% end %>
                </div>
              </div>

              <div class="flex justify-between items-center">
                <span class="text-sm font-medium text-stone-600">Status:</span>
                <div>
                  <% if @talent_note.pinned? %>
                    <span class="inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">
                      📌 Pinned
                    </span>
                  <% else %>
                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-stone-100 text-stone-800">Normal</span>
                  <% end %>
                </div>
              </div>

              <div class="flex justify-between">
                <span class="text-sm font-medium text-stone-600">Note ID:</span>
                <span class="text-sm text-stone-900 font-mono"><%= @talent_note.id %></span>
              </div>

              <div class="flex justify-between">
                <span class="text-sm font-medium text-stone-600">Created:</span>
                <span class="text-sm text-stone-900"><%= @talent_note.created_at.strftime('%B %d, %Y at %I:%M %p') %></span>
              </div>

              <div class="flex justify-between">
                <span class="text-sm font-medium text-stone-600">Last Updated:</span>
                <span class="text-sm text-stone-900"><%= @talent_note.updated_at.strftime('%B %d, %Y at %I:%M %p') %></span>
              </div>

              <% if @talent_note.last_modified_by.present? %>
                <div class="flex justify-between">
                  <span class="text-sm font-medium text-stone-600">Last Modified By:</span>
                  <span class="text-sm text-stone-900"><%= @talent_note.last_modified_by.name.full %></span>
                </div>
              <% end %>
            </div>
          </div>
        </div>

        <!-- Organization Context -->
        <div>
          <h3 class="text-lg font-medium text-stone-900 mb-4">Organization Context</h3>
          <div class="bg-stone-50 rounded-lg p-4">
            <div class="space-y-3">
              <div class="flex justify-between">
                <span class="text-sm font-medium text-stone-600">Organization:</span>
                <span class="text-sm text-stone-900"><%= @talent_note.organization.name %></span>
              </div>

              <div class="flex justify-between">
                <span class="text-sm font-medium text-stone-600">Organization ID:</span>
                <span class="text-sm text-stone-900 font-mono"><%= @talent_note.organization.id %></span>
              </div>

              <div class="mt-3">
                <%= link_to "View Organization", super_admin_admin_organization_path(@talent_note.organization), 
                    class: "inline-flex items-center px-3 py-1 border border-stone-300 rounded-md text-sm font-medium text-stone-700 bg-white hover:bg-stone-50" %>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Right Column: People Details -->
      <div class="space-y-6">
        <!-- Author Information -->
        <div>
          <h3 class="text-lg font-medium text-stone-900 mb-4">Note Author</h3>
          <div class="bg-stone-50 rounded-lg p-4">
            <div class="space-y-4">
              <div class="flex items-center space-x-3">
                <% if @talent_note.user.avatar.attached? %>
                  <%= image_tag @talent_note.user.avatar, class: "h-12 w-12 rounded-full object-cover" %>
                <% else %>
                  <div class="h-12 w-12 rounded-full bg-stone-300 flex items-center justify-center">
                    <span class="text-stone-600 font-medium text-lg">
                      <%= @talent_note.user.first_name&.first&.upcase %>
                    </span>
                  </div>
                <% end %>
                <div>
                  <h4 class="font-medium text-stone-900"><%= @talent_note.user.name.full %></h4>
                  <p class="text-sm text-stone-600"><%= @talent_note.user.email %></p>
                </div>
              </div>
              
              <div class="space-y-2 text-sm">
                <div class="flex justify-between">
                  <span class="text-stone-600">User ID:</span>
                  <span class="text-stone-900 font-mono"><%= @talent_note.user.id %></span>
                </div>
                
                <div class="flex justify-between">
                  <span class="text-stone-600">Account Created:</span>
                  <span class="text-stone-900"><%= @talent_note.user.created_at.strftime('%B %d, %Y') %></span>
                </div>
                
                <div class="flex justify-between">
                  <span class="text-stone-600">Verified:</span>
                  <span class="text-stone-900">
                    <% if @talent_note.user.verified? %>
                      <span class="text-green-600">✓ Verified</span>
                    <% else %>
                      <span class="text-red-600">✗ Not Verified</span>
                    <% end %>
                  </span>
                </div>
                
                <div class="flex justify-between">
                  <span class="text-stone-600">Notes Written:</span>
                  <span class="text-stone-900"><%= @talent_note.user.talent_notes.count %></span>
                </div>
              </div>
              
              <div class="mt-3">
                <%= link_to "View User Profile", super_admin_admin_user_path(@talent_note.user), 
                    class: "inline-flex items-center px-3 py-1 border border-stone-300 rounded-md text-sm font-medium text-stone-700 bg-white hover:bg-stone-50" %>
              </div>
            </div>
          </div>
        </div>

        <!-- Talent Information -->
        <div>
          <h3 class="text-lg font-medium text-stone-900 mb-4">Talent Profile</h3>
          <div class="bg-stone-50 rounded-lg p-4">
            <div class="space-y-4">
              <div class="flex items-center space-x-3">
                <% if @talent_note.talent_profile.user.avatar.attached? %>
                  <%= image_tag @talent_note.talent_profile.user.avatar, class: "h-12 w-12 rounded-full object-cover" %>
                <% else %>
                  <div class="h-12 w-12 rounded-full bg-stone-300 flex items-center justify-center">
                    <span class="text-stone-600 font-medium text-lg">
                      <%= @talent_note.talent_profile.user.first_name&.first&.upcase %>
                    </span>
                  </div>
                <% end %>
                <div>
                  <h4 class="font-medium text-stone-900"><%= @talent_note.talent_profile.user.name.full %></h4>
                  <p class="text-sm text-stone-600"><%= @talent_note.talent_profile.user.email %></p>
                </div>
              </div>
              
              <div class="space-y-2 text-sm">
                <div class="flex justify-between">
                  <span class="text-stone-600">Talent ID:</span>
                  <span class="text-stone-900 font-mono"><%= @talent_note.talent_profile.id %></span>
                </div>
                
                <% if @talent_note.talent_profile.headline.present? %>
                  <div class="flex justify-between">
                    <span class="text-stone-600">Headline:</span>
                    <span class="text-stone-900 text-right max-w-xs truncate" title="<%= @talent_note.talent_profile.headline %>">
                      <%= @talent_note.talent_profile.headline %>
                    </span>
                  </div>
                <% end %>
                
                <% if @talent_note.talent_profile.location.present? %>
                  <div class="flex justify-between">
                    <span class="text-stone-600">Location:</span>
                    <span class="text-stone-900"><%= @talent_note.talent_profile.location %></span>
                  </div>
                <% end %>
                
                <div class="flex justify-between">
                  <span class="text-stone-600">Availability:</span>
                  <span class="text-stone-900">
                    <% case @talent_note.talent_profile.availability_status %>
                    <% when 'available' %>
                      <span class="text-green-600">Available</span>
                    <% when 'limited' %>
                      <span class="text-yellow-600">Limited</span>
                    <% when 'unavailable' %>
                      <span class="text-red-600">Unavailable</span>
                    <% end %>
                  </span>
                </div>
                
                <div class="flex justify-between">
                  <span class="text-stone-600">Total Notes:</span>
                  <span class="text-stone-900"><%= @talent_note.talent_profile.talent_notes.count %></span>
                </div>
                
                <div class="flex justify-between">
                  <span class="text-stone-600">Bookmarks:</span>
                  <span class="text-stone-900"><%= @talent_note.talent_profile.talent_bookmarks.count %></span>
                </div>
              </div>
              
              <div class="mt-3">
                <%= link_to "View Talent Profile", super_admin_admin_talent_profile_path(@talent_note.talent_profile), 
                    class: "inline-flex items-center px-3 py-1 border border-stone-300 rounded-md text-sm font-medium text-stone-700 bg-white hover:bg-stone-50" %>
              </div>
            </div>
          </div>
        </div>

        <!-- Related Notes -->
        <div>
          <h3 class="text-lg font-medium text-stone-900 mb-4">Other Notes About This Talent</h3>
          <div class="bg-stone-50 rounded-lg p-4">
            <% other_notes = @talent_note.talent_profile.talent_notes.where.not(id: @talent_note.id).includes(:user).order(created_at: :desc).limit(5) %>
            <% if other_notes.any? %>
              <div class="space-y-3">
                <% other_notes.each do |note| %>
                  <div class="flex items-start justify-between">
                    <div class="flex-1">
                      <div class="text-sm text-stone-900">
                        <% content_preview = note.content.respond_to?(:to_plain_text) ? note.content.to_plain_text : note.content.to_s %>
                        <%= content_preview.truncate(60) %>
                      </div>
                      <div class="text-xs text-stone-500 mt-1">
                        by <%= note.user.name.full %> • <%= note.category.humanize %>
                        <% if note.pinned? %>
                          • 📌 Pinned
                        <% end %>
                      </div>
                    </div>
                    <div class="text-xs text-stone-500 ml-2">
                      <%= time_ago_in_words(note.created_at) %> ago
                    </div>
                  </div>
                <% end %>
                
                <% if @talent_note.talent_profile.talent_notes.count > 6 %>
                  <div class="text-sm text-stone-500 text-center pt-2 border-t border-stone-200">
                    +<%= @talent_note.talent_profile.talent_notes.count - 6 %> more notes
                  </div>
                <% end %>
              </div>
            <% else %>
              <p class="text-sm text-stone-500 italic">This is the only note about this talent.</p>
            <% end %>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
