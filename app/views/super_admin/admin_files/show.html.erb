<% content_for :title, "File Details - #{@blob.filename}" %>

<div class="bg-white rounded-lg shadow">
  <!-- Header -->
  <div class="px-6 py-4 border-b border-stone-200">
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-xl font-semibold text-stone-900">File Details</h1>
        <p class="text-sm text-stone-500 mt-1"><%= @blob.filename %></p>
      </div>
      <div class="flex items-center space-x-3">
        <%= link_to "Download", download_super_admin_admin_file_path(@attachment), 
            class: "inline-flex items-center px-3 py-2 border border-stone-300 rounded-md text-sm font-medium text-stone-700 bg-white hover:bg-stone-50" %>
        <%= link_to "Back to Files", super_admin_admin_files_path, 
            class: "inline-flex items-center px-3 py-2 border border-stone-300 rounded-md text-sm font-medium text-stone-700 bg-white hover:bg-stone-50" %>
      </div>
    </div>
  </div>

  <div class="p-6">
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
      <!-- Left Column: File Preview and Details -->
      <div class="space-y-6">
        <!-- File Preview -->
        <div>
          <h3 class="text-lg font-medium text-stone-900 mb-4">File Preview</h3>
          <div class="bg-stone-50 rounded-lg p-6 text-center">
            <% if @blob.image? %>
              <div class="max-w-md mx-auto">
                <%= image_tag @blob.representation(resize_to_limit: [400, 300]), 
                    class: "max-w-full h-auto rounded-lg shadow-sm" %>
              </div>
            <% elsif @blob.video? %>
              <div class="max-w-md mx-auto">
                <%= video_tag rails_blob_path(@blob), 
                    controls: true, 
                    class: "max-w-full h-auto rounded-lg shadow-sm" %>
              </div>
            <% elsif @blob.audio? %>
              <div class="max-w-md mx-auto">
                <%= audio_tag rails_blob_path(@blob), 
                    controls: true, 
                    class: "w-full" %>
              </div>
            <% elsif @blob.content_type == 'application/pdf' %>
              <div class="text-center">
                <svg class="h-24 w-24 text-red-400 mx-auto mb-4" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z" />
                </svg>
                <p class="text-stone-600 mb-4">PDF Document</p>
                <%= link_to "Open PDF", rails_blob_path(@blob), 
                    target: "_blank", 
                    class: "inline-flex items-center px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700" %>
              </div>
            <% else %>
              <div class="text-center">
                <svg class="h-24 w-24 text-stone-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
                <p class="text-stone-600 mb-2">File preview not available</p>
                <p class="text-sm text-stone-500"><%= @blob.content_type %></p>
              </div>
            <% end %>
          </div>
        </div>

        <!-- File Information -->
        <div>
          <h3 class="text-lg font-medium text-stone-900 mb-4">File Information</h3>
          <div class="bg-stone-50 rounded-lg p-4">
            <div class="space-y-3">
              <div class="flex justify-between">
                <span class="text-sm font-medium text-stone-600">Filename:</span>
                <span class="text-sm text-stone-900 font-mono"><%= @blob.filename %></span>
              </div>

              <div class="flex justify-between">
                <span class="text-sm font-medium text-stone-600">Content Type:</span>
                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full 
                  <% if @blob.content_type&.start_with?('image/') %>
                    bg-blue-100 text-blue-800
                  <% elsif @blob.content_type&.include?('pdf') || @blob.content_type&.include?('document') %>
                    bg-green-100 text-green-800
                  <% elsif @blob.content_type&.start_with?('video/') %>
                    bg-purple-100 text-purple-800
                  <% elsif @blob.content_type&.start_with?('audio/') %>
                    bg-orange-100 text-orange-800
                  <% else %>
                    bg-stone-100 text-stone-800
                  <% end %>">
                  <%= @blob.content_type %>
                </span>
              </div>

              <div class="flex justify-between">
                <span class="text-sm font-medium text-stone-600">File Size:</span>
                <span class="text-sm text-stone-900"><%= number_to_human_size(@blob.byte_size) %></span>
              </div>

              <div class="flex justify-between">
                <span class="text-sm font-medium text-stone-600">Checksum:</span>
                <span class="text-sm text-stone-900 font-mono"><%= @blob.checksum %></span>
              </div>

              <div class="flex justify-between">
                <span class="text-sm font-medium text-stone-600">Storage Key:</span>
                <span class="text-sm text-stone-900 font-mono break-all"><%= @blob.key %></span>
              </div>

              <div class="flex justify-between">
                <span class="text-sm font-medium text-stone-600">Service:</span>
                <span class="text-sm text-stone-900"><%= @blob.service_name %></span>
              </div>

              <div class="flex justify-between">
                <span class="text-sm font-medium text-stone-600">Uploaded:</span>
                <span class="text-sm text-stone-900"><%= @blob.created_at.strftime('%B %d, %Y at %I:%M %p') %></span>
              </div>

              <div class="flex justify-between">
                <span class="text-sm font-medium text-stone-600">Attachment ID:</span>
                <span class="text-sm text-stone-900 font-mono"><%= @attachment.id %></span>
              </div>

              <div class="flex justify-between">
                <span class="text-sm font-medium text-stone-600">Attachment Name:</span>
                <span class="text-sm text-stone-900"><%= @attachment.name %></span>
              </div>
            </div>
          </div>
        </div>

        <!-- Metadata -->
        <% if @metadata.any? %>
          <div>
            <h3 class="text-lg font-medium text-stone-900 mb-4">File Metadata</h3>
            <div class="bg-stone-50 rounded-lg p-4">
              <div class="space-y-2">
                <% @metadata.each do |key, value| %>
                  <div class="flex justify-between">
                    <span class="text-sm font-medium text-stone-600"><%= key.humanize %>:</span>
                    <span class="text-sm text-stone-900"><%= value %></span>
                  </div>
                <% end %>
              </div>
            </div>
          </div>
        <% end %>
      </div>

      <!-- Right Column: Attachment Details -->
      <div class="space-y-6">
        <!-- Attached Record Information -->
        <div>
          <h3 class="text-lg font-medium text-stone-900 mb-4">Attached To</h3>
          <div class="bg-stone-50 rounded-lg p-4">
            <% if @record %>
              <div class="space-y-4">
                <div class="flex items-center space-x-3">
                  <div class="flex-shrink-0">
                    <div class="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
                      <span class="text-blue-600 font-medium text-sm">
                        <%= @attachment.record_type.first.upcase %>
                      </span>
                    </div>
                  </div>
                  <div>
                    <h4 class="font-medium text-stone-900"><%= @attachment.record_type.humanize %></h4>
                    <p class="text-sm text-stone-600">ID: <%= @attachment.record_id %></p>
                  </div>
                </div>
                
                <div class="space-y-2 text-sm">
                  <% if @record.respond_to?(:name) && @record.name.present? %>
                    <div class="flex justify-between">
                      <span class="text-stone-600">Name:</span>
                      <span class="text-stone-900"><%= @record.name %></span>
                    </div>
                  <% end %>
                  
                  <% if @record.respond_to?(:email) && @record.email.present? %>
                    <div class="flex justify-between">
                      <span class="text-stone-600">Email:</span>
                      <span class="text-stone-900"><%= @record.email %></span>
                    </div>
                  <% end %>
                  
                  <% if @record.respond_to?(:title) && @record.title.present? %>
                    <div class="flex justify-between">
                      <span class="text-stone-600">Title:</span>
                      <span class="text-stone-900"><%= @record.title %></span>
                    </div>
                  <% end %>
                  
                  <% if @record.respond_to?(:created_at) %>
                    <div class="flex justify-between">
                      <span class="text-stone-600">Record Created:</span>
                      <span class="text-stone-900"><%= @record.created_at.strftime('%B %d, %Y') %></span>
                    </div>
                  <% end %>
                </div>
                
                <div class="mt-3">
                  <% case @attachment.record_type %>
                  <% when 'User' %>
                    <%= link_to "View User Profile", super_admin_admin_user_path(@record), 
                        class: "inline-flex items-center px-3 py-1 border border-stone-300 rounded-md text-sm font-medium text-stone-700 bg-white hover:bg-stone-50" %>
                  <% when 'TalentProfile' %>
                    <%= link_to "View Talent Profile", super_admin_admin_talent_profile_path(@record), 
                        class: "inline-flex items-center px-3 py-1 border border-stone-300 rounded-md text-sm font-medium text-stone-700 bg-white hover:bg-stone-50" %>
                  <% when 'Job' %>
                    <%= link_to "View Job", super_admin_admin_job_path(@record), 
                        class: "inline-flex items-center px-3 py-1 border border-stone-300 rounded-md text-sm font-medium text-stone-700 bg-white hover:bg-stone-50" %>
                  <% end %>
                </div>
              </div>
            <% else %>
              <div class="text-center py-4">
                <svg class="h-12 w-12 text-red-400 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                </svg>
                <h4 class="text-lg font-medium text-red-900 mb-1">Orphaned File</h4>
                <p class="text-sm text-red-600">This file is not attached to any record.</p>
                <p class="text-xs text-stone-500 mt-2">
                  Record Type: <%= @attachment.record_type %><br>
                  Record ID: <%= @attachment.record_id %>
                </p>
              </div>
            <% end %>
          </div>
        </div>

        <!-- Related Files -->
        <% if @related_attachments.any? %>
          <div>
            <h3 class="text-lg font-medium text-stone-900 mb-4">Other Files from Same Record</h3>
            <div class="bg-stone-50 rounded-lg p-4">
              <div class="space-y-3">
                <% @related_attachments.each do |related| %>
                  <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                      <div class="flex-shrink-0 h-8 w-8">
                        <% if related.blob.image? %>
                          <%= image_tag related.blob.representation(resize_to_limit: [32, 32]), 
                              class: "h-8 w-8 rounded object-cover" %>
                        <% else %>
                          <div class="h-8 w-8 rounded bg-stone-200 flex items-center justify-center">
                            <svg class="h-4 w-4 text-stone-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                          </div>
                        <% end %>
                      </div>
                      <div>
                        <div class="text-sm font-medium text-stone-900">
                          <%= related.blob.filename.to_s.truncate(30) %>
                        </div>
                        <div class="text-xs text-stone-500">
                          <%= number_to_human_size(related.blob.byte_size) %>
                        </div>
                      </div>
                    </div>
                    <div class="text-xs text-stone-500">
                      <%= time_ago_in_words(related.created_at) %> ago
                    </div>
                  </div>
                <% end %>
              </div>
            </div>
          </div>
        <% end %>

        <!-- File Actions -->
        <div>
          <h3 class="text-lg font-medium text-stone-900 mb-4">File Actions</h3>
          <div class="bg-stone-50 rounded-lg p-4">
            <div class="space-y-3">
              <%= link_to download_super_admin_admin_file_path(@attachment), 
                  class: "flex items-center justify-between p-3 border border-stone-300 rounded-md hover:bg-white transition-colors" do %>
                <div class="flex items-center">
                  <svg class="h-5 w-5 text-green-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                  </svg>
                  <span class="text-sm font-medium text-stone-900">Download File</span>
                </div>
                <svg class="h-4 w-4 text-stone-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
              <% end %>

              <%= link_to rails_blob_path(@blob), target: "_blank", 
                  class: "flex items-center justify-between p-3 border border-stone-300 rounded-md hover:bg-white transition-colors" do %>
                <div class="flex items-center">
                  <svg class="h-5 w-5 text-blue-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
                  </svg>
                  <span class="text-sm font-medium text-stone-900">Open in New Tab</span>
                </div>
                <svg class="h-4 w-4 text-stone-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
              <% end %>

              <%= link_to super_admin_admin_file_path(@attachment), 
                  method: :delete,
                  data: { confirm: "Are you sure you want to delete this file? This action cannot be undone." },
                  class: "flex items-center justify-between p-3 border border-red-300 rounded-md hover:bg-red-50 transition-colors" do %>
                <div class="flex items-center">
                  <svg class="h-5 w-5 text-red-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                  </svg>
                  <span class="text-sm font-medium text-red-900">Delete File</span>
                </div>
                <svg class="h-4 w-4 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
              <% end %>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
