<% content_for :title, "File Management" %>

<div class="bg-white rounded-lg shadow">
  <!-- Header -->
  <div class="px-6 py-4 border-b border-stone-200">
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-xl font-semibold text-stone-900">File Management</h1>
        <p class="text-sm text-stone-500 mt-1">Manage uploaded files and attachments</p>
      </div>
      <div class="flex items-center space-x-3">
        <%= link_to "Export CSV", super_admin_admin_files_path(format: :csv, **request.query_parameters), 
            class: "inline-flex items-center px-3 py-2 border border-stone-300 rounded-md text-sm font-medium text-stone-700 bg-white hover:bg-stone-50" %>
      </div>
    </div>
  </div>

  <!-- Stats Dashboard -->
  <div class="px-6 py-4 bg-stone-50 border-b border-stone-200">
    <div class="grid grid-cols-2 gap-4 md:grid-cols-4 lg:grid-cols-7">
      <div class="text-center">
        <div class="text-2xl font-bold text-stone-900"><%= number_with_delimiter(@stats[:total_files]) %></div>
        <div class="text-xs text-stone-600">Total Files</div>
      </div>
      <div class="text-center">
        <div class="text-2xl font-bold text-stone-900"><%= number_to_human_size(@stats[:total_size]) %></div>
        <div class="text-xs text-stone-600">Total Size</div>
      </div>
      <div class="text-center">
        <div class="text-2xl font-bold text-stone-900"><%= number_to_human_size(@stats[:avg_size]) %></div>
        <div class="text-xs text-stone-600">Avg Size</div>
      </div>
      <div class="text-center">
        <div class="text-2xl font-bold text-blue-600"><%= number_with_delimiter(@stats[:images_count]) %></div>
        <div class="text-xs text-stone-600">Images</div>
      </div>
      <div class="text-center">
        <div class="text-2xl font-bold text-green-600"><%= number_with_delimiter(@stats[:documents_count]) %></div>
        <div class="text-xs text-stone-600">Documents</div>
      </div>
      <div class="text-center">
        <div class="text-2xl font-bold text-purple-600"><%= number_with_delimiter(@stats[:videos_count]) %></div>
        <div class="text-xs text-stone-600">Videos</div>
      </div>
      <div class="text-center">
        <div class="text-2xl font-bold text-orange-600"><%= number_with_delimiter(@stats[:this_week]) %></div>
        <div class="text-xs text-stone-600">This Week</div>
      </div>
    </div>
    
    <% if @stats[:orphaned_files] > 0 %>
      <div class="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
        <div class="flex items-center">
          <svg class="h-5 w-5 text-yellow-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
          </svg>
          <span class="text-sm text-yellow-800">
            <strong><%= @stats[:orphaned_files] %></strong> orphaned files detected (blobs without attachments)
          </span>
        </div>
      </div>
    <% end %>
  </div>

  <!-- Filters -->
  <div class="px-6 py-4 border-b border-stone-200">
    <%= form_with url: super_admin_admin_files_path, method: :get, local: true, class: "space-y-4" do |form| %>
      <!-- Search and Actions Row -->
      <div class="flex flex-col sm:flex-row gap-4">
        <div class="flex-1">
          <%= form.text_field :q, value: @q, placeholder: "Search files by name, type, or ID...", 
              class: "w-full px-3 py-2 border border-stone-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" %>
        </div>
        <div class="flex gap-2">
          <%= form.submit "Filter", class: "px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500" %>
          <%= link_to "Clear", super_admin_admin_files_path, class: "px-4 py-2 border border-stone-300 text-stone-700 rounded-md hover:bg-stone-50 focus:outline-none focus:ring-2 focus:ring-blue-500" %>
        </div>
      </div>

      <!-- Filter Dropdowns Row -->
      <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
        <div>
          <%= form.select :record_type_filter, 
              options_for_select([['All Record Types', 'all']] + @available_record_types.map { |type| [type.humanize, type] }, @record_type_filter), 
              {}, 
              { class: "w-full px-3 py-2 border border-stone-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" } %>
        </div>
        
        <div>
          <%= form.select :content_type_filter, 
              options_for_select([
                ['All File Types', 'all'],
                ['Images', 'images'],
                ['Documents', 'documents'],
                ['Videos', 'videos'],
                ['Audio', 'audio']
              ], @content_type_filter), 
              {}, 
              { class: "w-full px-3 py-2 border border-stone-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" } %>
        </div>
        
        <div>
          <%= form.select :size_filter, 
              options_for_select([
                ['All Sizes', 'all'],
                ['Small (< 1MB)', 'small'],
                ['Medium (1-10MB)', 'medium'],
                ['Large (10-100MB)', 'large'],
                ['X-Large (> 100MB)', 'xlarge']
              ], @size_filter), 
              {}, 
              { class: "w-full px-3 py-2 border border-stone-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" } %>
        </div>
        
        <div>
          <%= form.select :date_filter, 
              options_for_select([
                ['All Dates', 'all'],
                ['Today', 'today'],
                ['This Week', 'week'],
                ['This Month', 'month'],
                ['Last 3 Months', 'quarter']
              ], @date_filter), 
              {}, 
              { class: "w-full px-3 py-2 border border-stone-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" } %>
        </div>
      </div>
    <% end %>
  </div>

  <!-- Bulk Actions -->
  <div class="px-6 py-3 bg-stone-50 border-b border-stone-200">
    <%= form_with url: bulk_update_super_admin_admin_files_path, method: :post, local: true, id: "bulk-actions-form", class: "flex items-center justify-between" do |form| %>
      <div class="flex items-center space-x-4">
        <label class="flex items-center">
          <input type="checkbox" id="select-all" class="rounded border-stone-300 text-blue-600 focus:ring-blue-500">
          <span class="ml-2 text-sm text-stone-600">Select All</span>
        </label>
        <span id="selected-count" class="text-sm text-stone-600">0 selected</span>
      </div>
      <div class="flex items-center space-x-2">
        <%= form.select :bulk_action, 
            options_for_select([
              ['Choose Action', ''],
              ['Delete Selected', 'delete']
            ]), 
            {}, 
            { class: "px-3 py-2 border border-stone-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500", disabled: true, id: "bulk-action-select" } %>
        <%= form.submit "Apply", class: "px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed", disabled: true, id: "bulk-submit", data: { confirm: "Are you sure you want to perform this action on the selected files?" } %>
      </div>
    <% end %>
  </div>

  <!-- Files Table -->
  <div class="overflow-x-auto">
    <table class="min-w-full divide-y divide-stone-200">
      <thead class="bg-stone-50">
        <tr>
          <th class="px-6 py-3 text-left">
            <span class="sr-only">Select</span>
          </th>
          <th class="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">
            <%= link_to "File", super_admin_admin_files_path(sort_by: 'filename', sort_direction: @sort_by == 'filename' && @sort_direction == 'asc' ? 'desc' : 'asc', **request.query_parameters.except(:sort_by, :sort_direction)), 
                class: "hover:text-stone-700" %>
          </th>
          <th class="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">
            <%= link_to "Type", super_admin_admin_files_path(sort_by: 'content_type', sort_direction: @sort_by == 'content_type' && @sort_direction == 'asc' ? 'desc' : 'asc', **request.query_parameters.except(:sort_by, :sort_direction)), 
                class: "hover:text-stone-700" %>
          </th>
          <th class="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">
            <%= link_to "Size", super_admin_admin_files_path(sort_by: 'size', sort_direction: @sort_by == 'size' && @sort_direction == 'asc' ? 'desc' : 'asc', **request.query_parameters.except(:sort_by, :sort_direction)), 
                class: "hover:text-stone-700" %>
          </th>
          <th class="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">
            <%= link_to "Attached To", super_admin_admin_files_path(sort_by: 'record_type', sort_direction: @sort_by == 'record_type' && @sort_direction == 'asc' ? 'desc' : 'asc', **request.query_parameters.except(:sort_by, :sort_direction)), 
                class: "hover:text-stone-700" %>
          </th>
          <th class="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">
            <%= link_to "Uploaded", super_admin_admin_files_path(sort_by: 'created_at', sort_direction: @sort_by == 'created_at' && @sort_direction == 'asc' ? 'desc' : 'asc', **request.query_parameters.except(:sort_by, :sort_direction)), 
                class: "hover:text-stone-700" %>
          </th>
          <th class="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">Actions</th>
        </tr>
      </thead>
      <tbody class="bg-white divide-y divide-stone-200">
        <% if @attachments.any? %>
          <% @attachments.each do |attachment| %>
            <tr class="hover:bg-stone-50">
              <td class="px-6 py-4">
                <input type="checkbox" name="attachment_ids[]" value="<%= attachment.id %>" 
                       class="file-checkbox rounded border-stone-300 text-blue-600 focus:ring-blue-500">
              </td>
              <td class="px-6 py-4">
                <div class="flex items-center">
                  <div class="flex-shrink-0 h-10 w-10">
                    <% if attachment.blob.image? %>
                      <%= image_tag attachment.blob.representation(resize_to_limit: [40, 40]), 
                          class: "h-10 w-10 rounded object-cover" %>
                    <% else %>
                      <div class="h-10 w-10 rounded bg-stone-200 flex items-center justify-center">
                        <svg class="h-6 w-6 text-stone-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                      </div>
                    <% end %>
                  </div>
                  <div class="ml-4">
                    <div class="text-sm font-medium text-stone-900">
                      <%= link_to attachment.blob.filename, super_admin_admin_file_path(attachment), 
                          class: "hover:text-blue-600" %>
                    </div>
                    <div class="text-sm text-stone-500">ID: <%= attachment.id %></div>
                  </div>
                </div>
              </td>
              <td class="px-6 py-4">
                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full 
                  <% if attachment.blob.content_type&.start_with?('image/') %>
                    bg-blue-100 text-blue-800
                  <% elsif attachment.blob.content_type&.include?('pdf') || attachment.blob.content_type&.include?('document') %>
                    bg-green-100 text-green-800
                  <% elsif attachment.blob.content_type&.start_with?('video/') %>
                    bg-purple-100 text-purple-800
                  <% elsif attachment.blob.content_type&.start_with?('audio/') %>
                    bg-orange-100 text-orange-800
                  <% else %>
                    bg-stone-100 text-stone-800
                  <% end %>">
                  <%= attachment.blob.content_type %>
                </span>
              </td>
              <td class="px-6 py-4 text-sm text-stone-900">
                <%= number_to_human_size(attachment.blob.byte_size) %>
              </td>
              <td class="px-6 py-4">
                <div class="text-sm text-stone-900">
                  <%= attachment.record_type.humanize %>
                  <% if attachment.record %>
                    <div class="text-xs text-stone-500">
                      ID: <%= attachment.record_id %>
                      <% if attachment.record.respond_to?(:name) %>
                        • <%= attachment.record.name.truncate(20) %>
                      <% elsif attachment.record.respond_to?(:email) %>
                        • <%= attachment.record.email %>
                      <% elsif attachment.record.respond_to?(:title) %>
                        • <%= attachment.record.title.truncate(20) %>
                      <% end %>
                    </div>
                  <% else %>
                    <div class="text-xs text-red-500">Record not found</div>
                  <% end %>
                </div>
              </td>
              <td class="px-6 py-4 text-sm text-stone-500">
                <%= time_ago_in_words(attachment.created_at) %> ago
              </td>
              <td class="px-6 py-4 text-sm font-medium space-x-2">
                <%= link_to "View", super_admin_admin_file_path(attachment), 
                    class: "text-blue-600 hover:text-blue-900" %>
                <%= link_to "Download", download_super_admin_admin_file_path(attachment), 
                    class: "text-green-600 hover:text-green-900" %>
                <%= link_to "Delete", super_admin_admin_file_path(attachment), 
                    method: :delete, 
                    class: "text-red-600 hover:text-red-900",
                    data: { confirm: "Are you sure you want to delete this file?" } %>
              </td>
            </tr>
          <% end %>
        <% else %>
          <tr>
            <td colspan="7" class="px-6 py-12 text-center">
              <div class="flex flex-col items-center">
                <svg class="h-12 w-12 text-stone-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
                <h3 class="text-lg font-medium text-stone-900 mb-2">No files found</h3>
                <p class="text-stone-500">No files match your current filters.</p>
              </div>
            </td>
          </tr>
        <% end %>
      </tbody>
    </table>
  </div>

  <!-- Pagination -->
  <% if @attachments.respond_to?(:count) && @attachments.count > 0 %>
    <div class="px-6 py-4 border-t border-stone-200 bg-stone-50">
      <%== pagy_nav(@pagy) if @pagy %>
    </div>
  <% end %>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
  const selectAllCheckbox = document.getElementById('select-all');
  const fileCheckboxes = document.querySelectorAll('.file-checkbox');
  const selectedCountSpan = document.getElementById('selected-count');
  const bulkActionSelect = document.getElementById('bulk-action-select');
  const bulkSubmit = document.getElementById('bulk-submit');
  const bulkForm = document.getElementById('bulk-actions-form');

  function updateBulkActions() {
    const checkedBoxes = document.querySelectorAll('.file-checkbox:checked');
    const count = checkedBoxes.length;
    
    selectedCountSpan.textContent = `${count} selected`;
    
    if (count > 0) {
      bulkActionSelect.disabled = false;
      if (bulkActionSelect.value) {
        bulkSubmit.disabled = false;
      }
    } else {
      bulkActionSelect.disabled = true;
      bulkSubmit.disabled = true;
    }
  }

  selectAllCheckbox.addEventListener('change', function() {
    fileCheckboxes.forEach(checkbox => {
      checkbox.checked = this.checked;
    });
    updateBulkActions();
  });

  fileCheckboxes.forEach(checkbox => {
    checkbox.addEventListener('change', updateBulkActions);
  });

  bulkActionSelect.addEventListener('change', function() {
    const checkedBoxes = document.querySelectorAll('.file-checkbox:checked');
    bulkSubmit.disabled = !(this.value && checkedBoxes.length > 0);
  });

  bulkForm.addEventListener('submit', function(e) {
    const checkedBoxes = document.querySelectorAll('.file-checkbox:checked');
    if (checkedBoxes.length === 0) {
      e.preventDefault();
      alert('Please select at least one file.');
    }
  });
});
</script>
