<% content_for :title, "Talent Bookmark ##{@talent_bookmark.id}" %>

<div class="bg-white rounded-lg shadow">
  <!-- Header -->
  <div class="px-6 py-4 border-b border-stone-200">
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-xl font-semibold text-stone-900">Talent Bookmark #<%= @talent_bookmark.id %></h1>
        <p class="text-sm text-stone-500 mt-1">View bookmark details and relationship</p>
      </div>
      <div class="flex items-center space-x-3">
        <%= link_to "Back to List", super_admin_admin_talent_bookmarks_path, 
            class: "inline-flex items-center px-3 py-2 border border-stone-300 rounded-md text-sm font-medium text-stone-700 bg-white hover:bg-stone-50" %>
      </div>
    </div>
  </div>

  <div class="p-6">
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
      <!-- Left Column: Scout Details -->
      <div class="space-y-6">
        <!-- Scout Information -->
        <div>
          <h3 class="text-lg font-medium text-stone-900 mb-4">Scout Details</h3>
          <div class="bg-stone-50 rounded-lg p-4">
            <div class="space-y-4">
              <div class="flex items-center space-x-3">
                <% if @talent_bookmark.user.avatar.attached? %>
                  <%= image_tag @talent_bookmark.user.avatar, class: "h-12 w-12 rounded-full object-cover" %>
                <% else %>
                  <div class="h-12 w-12 rounded-full bg-stone-300 flex items-center justify-center">
                    <span class="text-stone-600 font-medium text-lg">
                      <%= @talent_bookmark.user.first_name&.first&.upcase %>
                    </span>
                  </div>
                <% end %>
                <div>
                  <h4 class="font-medium text-stone-900"><%= @talent_bookmark.user.name.full %></h4>
                  <p class="text-sm text-stone-600"><%= @talent_bookmark.user.email %></p>
                </div>
              </div>
              
              <div class="space-y-2 text-sm">
                <div class="flex justify-between">
                  <span class="text-stone-600">User ID:</span>
                  <span class="text-stone-900 font-mono"><%= @talent_bookmark.user.id %></span>
                </div>
                
                <div class="flex justify-between">
                  <span class="text-stone-600">Account Created:</span>
                  <span class="text-stone-900"><%= @talent_bookmark.user.created_at.strftime('%B %d, %Y') %></span>
                </div>
                
                <div class="flex justify-between">
                  <span class="text-stone-600">Verified:</span>
                  <span class="text-stone-900">
                    <% if @talent_bookmark.user.verified? %>
                      <span class="text-green-600">✓ Verified</span>
                    <% else %>
                      <span class="text-red-600">✗ Not Verified</span>
                    <% end %>
                  </span>
                </div>
                
                <div class="flex justify-between">
                  <span class="text-stone-600">Total Bookmarks:</span>
                  <span class="text-stone-900"><%= @talent_bookmark.user.talent_bookmarks.count %></span>
                </div>
              </div>
              
              <div class="mt-3">
                <%= link_to "View Scout Profile", super_admin_admin_user_path(@talent_bookmark.user), 
                    class: "inline-flex items-center px-3 py-1 border border-stone-300 rounded-md text-sm font-medium text-stone-700 bg-white hover:bg-stone-50" %>
              </div>
            </div>
          </div>
        </div>

        <!-- Scout's Other Bookmarks -->
        <div>
          <h3 class="text-lg font-medium text-stone-900 mb-4">Scout's Other Bookmarks</h3>
          <div class="bg-stone-50 rounded-lg p-4">
            <% other_bookmarks = @talent_bookmark.user.talent_bookmarks.where.not(id: @talent_bookmark.id).includes(talent_profile: :user).limit(5) %>
            <% if other_bookmarks.any? %>
              <div class="space-y-3">
                <% other_bookmarks.each do |bookmark| %>
                  <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-2">
                      <% if bookmark.talent_profile.user.avatar.attached? %>
                        <%= image_tag bookmark.talent_profile.user.avatar, class: "h-6 w-6 rounded-full object-cover" %>
                      <% else %>
                        <div class="h-6 w-6 rounded-full bg-stone-300 flex items-center justify-center">
                          <span class="text-stone-600 text-xs">
                            <%= bookmark.talent_profile.user.first_name&.first&.upcase %>
                          </span>
                        </div>
                      <% end %>
                      <div>
                        <span class="text-sm text-stone-900"><%= bookmark.talent_profile.user.name.full %></span>
                        <% if bookmark.talent_profile.headline.present? %>
                          <div class="text-xs text-stone-500 truncate max-w-xs">
                            <%= bookmark.talent_profile.headline %>
                          </div>
                        <% end %>
                      </div>
                    </div>
                    <span class="text-xs text-stone-500">
                      <%= time_ago_in_words(bookmark.created_at) %> ago
                    </span>
                  </div>
                <% end %>
                
                <% if @talent_bookmark.user.talent_bookmarks.count > 6 %>
                  <div class="text-sm text-stone-500 text-center pt-2 border-t border-stone-200">
                    +<%= @talent_bookmark.user.talent_bookmarks.count - 6 %> more bookmarks
                  </div>
                <% end %>
              </div>
            <% else %>
              <p class="text-sm text-stone-500 italic">This is the scout's only bookmark.</p>
            <% end %>
          </div>
        </div>
      </div>

      <!-- Right Column: Talent Details -->
      <div class="space-y-6">
        <!-- Talent Information -->
        <div>
          <h3 class="text-lg font-medium text-stone-900 mb-4">Talent Details</h3>
          <div class="bg-stone-50 rounded-lg p-4">
            <div class="space-y-4">
              <div class="flex items-center space-x-3">
                <% if @talent_bookmark.talent_profile.user.avatar.attached? %>
                  <%= image_tag @talent_bookmark.talent_profile.user.avatar, class: "h-12 w-12 rounded-full object-cover" %>
                <% else %>
                  <div class="h-12 w-12 rounded-full bg-stone-300 flex items-center justify-center">
                    <span class="text-stone-600 font-medium text-lg">
                      <%= @talent_bookmark.talent_profile.user.first_name&.first&.upcase %>
                    </span>
                  </div>
                <% end %>
                <div>
                  <h4 class="font-medium text-stone-900"><%= @talent_bookmark.talent_profile.user.name.full %></h4>
                  <p class="text-sm text-stone-600"><%= @talent_bookmark.talent_profile.user.email %></p>
                  <% if @talent_bookmark.talent_profile.headline.present? %>
                    <p class="text-sm text-stone-500 italic"><%= @talent_bookmark.talent_profile.headline %></p>
                  <% end %>
                </div>
              </div>
              
              <div class="space-y-2 text-sm">
                <div class="flex justify-between">
                  <span class="text-stone-600">Talent Profile ID:</span>
                  <span class="text-stone-900 font-mono"><%= @talent_bookmark.talent_profile.id %></span>
                </div>
                
                <% if @talent_bookmark.talent_profile.location.present? %>
                  <div class="flex justify-between">
                    <span class="text-stone-600">Location:</span>
                    <span class="text-stone-900"><%= @talent_bookmark.talent_profile.location %></span>
                  </div>
                <% end %>
                
                <div class="flex justify-between">
                  <span class="text-stone-600">Availability:</span>
                  <span class="text-stone-900">
                    <% case @talent_bookmark.talent_profile.availability_status %>
                    <% when 'available' %>
                      <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">Available</span>
                    <% when 'limited' %>
                      <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">Limited</span>
                    <% when 'unavailable' %>
                      <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">Unavailable</span>
                    <% else %>
                      <span class="text-stone-500">Not specified</span>
                    <% end %>
                  </span>
                </div>
                
                <div class="flex justify-between">
                  <span class="text-stone-600">Total Bookmarks:</span>
                  <span class="text-stone-900"><%= @talent_bookmark.talent_profile.talent_bookmarks.count %></span>
                </div>
                
                <div class="flex justify-between">
                  <span class="text-stone-600">Profile Created:</span>
                  <span class="text-stone-900"><%= @talent_bookmark.talent_profile.created_at.strftime('%B %d, %Y') %></span>
                </div>
              </div>
              
              <% if @talent_bookmark.talent_profile.skills.present? %>
                <div>
                  <span class="text-sm font-medium text-stone-700">Skills:</span>
                  <div class="mt-1 flex flex-wrap gap-1">
                    <% @talent_bookmark.talent_profile.skills.first(5).each do |skill| %>
                      <span class="inline-flex px-2 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800">
                        <%= skill %>
                      </span>
                    <% end %>
                    <% if @talent_bookmark.talent_profile.skills.length > 5 %>
                      <span class="inline-flex px-2 py-1 text-xs font-medium rounded-full bg-stone-100 text-stone-600">
                        +<%= @talent_bookmark.talent_profile.skills.length - 5 %> more
                      </span>
                    <% end %>
                  </div>
                </div>
              <% end %>
              
              <div class="mt-3">
                <%= link_to "View Talent Profile", super_admin_admin_user_path(@talent_bookmark.talent_profile.user), 
                    class: "inline-flex items-center px-3 py-1 border border-stone-300 rounded-md text-sm font-medium text-stone-700 bg-white hover:bg-stone-50" %>
              </div>
            </div>
          </div>
        </div>

        <!-- Talent's Other Bookmarks -->
        <div>
          <h3 class="text-lg font-medium text-stone-900 mb-4">Who Else Bookmarked This Talent</h3>
          <div class="bg-stone-50 rounded-lg p-4">
            <% other_bookmarks = @talent_bookmark.talent_profile.talent_bookmarks.where.not(id: @talent_bookmark.id).includes(:user).limit(5) %>
            <% if other_bookmarks.any? %>
              <div class="space-y-3">
                <% other_bookmarks.each do |bookmark| %>
                  <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-2">
                      <% if bookmark.user.avatar.attached? %>
                        <%= image_tag bookmark.user.avatar, class: "h-6 w-6 rounded-full object-cover" %>
                      <% else %>
                        <div class="h-6 w-6 rounded-full bg-stone-300 flex items-center justify-center">
                          <span class="text-stone-600 text-xs">
                            <%= bookmark.user.first_name&.first&.upcase %>
                          </span>
                        </div>
                      <% end %>
                      <span class="text-sm text-stone-900"><%= bookmark.user.name.full %></span>
                    </div>
                    <span class="text-xs text-stone-500">
                      <%= time_ago_in_words(bookmark.created_at) %> ago
                    </span>
                  </div>
                <% end %>
                
                <% if @talent_bookmark.talent_profile.talent_bookmarks.count > 6 %>
                  <div class="text-sm text-stone-500 text-center pt-2 border-t border-stone-200">
                    +<%= @talent_bookmark.talent_profile.talent_bookmarks.count - 6 %> more scouts
                  </div>
                <% end %>
              </div>
            <% else %>
              <p class="text-sm text-stone-500 italic">This talent has only been bookmarked by this scout.</p>
            <% end %>
          </div>
        </div>

        <!-- Bookmark Timeline -->
        <div>
          <h3 class="text-lg font-medium text-stone-900 mb-4">Bookmark Timeline</h3>
          <div class="bg-stone-50 rounded-lg p-4">
            <div class="space-y-3">
              <div class="flex items-center space-x-3">
                <div class="flex-shrink-0 w-2 h-2 bg-blue-500 rounded-full"></div>
                <div class="text-sm">
                  <span class="font-medium text-stone-900">Talent Bookmarked</span>
                  <span class="text-stone-500 ml-2"><%= @talent_bookmark.created_at.strftime('%B %d, %Y at %I:%M %p') %></span>
                </div>
              </div>
              
              <div class="text-sm text-stone-500 ml-5">
                <%= @talent_bookmark.user.name.full %> bookmarked <%= @talent_bookmark.talent_profile.user.name.full %>
                <%= time_ago_in_words(@talent_bookmark.created_at) %> ago
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
