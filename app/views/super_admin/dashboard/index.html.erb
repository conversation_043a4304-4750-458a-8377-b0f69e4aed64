<% content_for :title, "Super Admin Dashboard" %>

<div class="mx-auto max-w-7xl">
  <div class="mb-8">
    <h1 class="text-3xl font-bold text-stone-900">Super Admin Dashboard</h1>
    <p class="mt-2 text-stone-600">Manage users and monitor system activity</p>
  </div>

  <!-- Stats Grid -->
  <div class="grid grid-cols-1 gap-6 mb-8 md:grid-cols-3">
    <div class="p-6 bg-white rounded-lg shadow">
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <div class="flex items-center justify-center w-8 h-8 rounded-md bg-stone-600">
            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0z"></path>
            </svg>
          </div>
        </div>
        <div class="flex-1 w-0 ml-5">
          <dl>
            <dt class="text-sm font-medium truncate text-stone-500">Total Users</dt>
            <dd class="text-lg font-medium text-stone-900"><%= @user_count %></dd>
          </dl>
        </div>
      </div>
    </div>

    <div class="p-6 bg-white rounded-lg shadow">
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <div class="flex items-center justify-center w-8 h-8 rounded-md bg-stone-600">
            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
            </svg>
          </div>
        </div>
        <div class="flex-1 w-0 ml-5">
          <dl>
            <dt class="text-sm font-medium truncate text-stone-500">Active Impersonations</dt>
            <dd class="text-lg font-medium text-stone-900"><%= @active_impersonations.count %></dd>
          </dl>
        </div>
      </div>
    </div>

    <div class="p-6 bg-white rounded-lg shadow">
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <div class="flex items-center justify-center w-8 h-8 rounded-md bg-stone-600">
            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
          </div>
        </div>
        <div class="flex-1 w-0 ml-5">
          <dl>
            <dt class="text-sm font-medium truncate text-stone-500">System Status</dt>
            <dd class="text-lg font-medium text-stone-600">Operational</dd>
          </dl>
        </div>
      </div>
    </div>
  </div>

  <!-- Analytics Dashboard -->
  <div class="grid grid-cols-1 gap-8 mb-8 lg:grid-cols-3">
    <!-- Growth Trends -->
    <div class="bg-white rounded-lg shadow">
      <div class="px-6 py-4 border-b border-stone-200">
        <h2 class="text-lg font-medium text-stone-900">Growth Trends</h2>
      </div>
      <div class="p-6">
        <div class="space-y-4">
          <div>
            <div class="flex items-center justify-between">
              <span class="text-sm font-medium text-stone-600">Users (Weekly)</span>
              <span class="text-sm <%= @analytics[:growth_trends][:users][:weekly][:change] >= 0 ? 'text-green-600' : 'text-red-600' %>">
                <%= @analytics[:growth_trends][:users][:weekly][:change] >= 0 ? '+' : '' %><%= @analytics[:growth_trends][:users][:weekly][:change] %>%
              </span>
            </div>
            <div class="text-lg font-semibold text-stone-900"><%= @analytics[:growth_trends][:users][:weekly][:this_week] %></div>
            <div class="text-xs text-stone-500">vs <%= @analytics[:growth_trends][:users][:weekly][:last_week] %> last week</div>
          </div>

          <div>
            <div class="flex items-center justify-between">
              <span class="text-sm font-medium text-stone-600">Jobs (Weekly)</span>
              <span class="text-sm <%= @analytics[:growth_trends][:jobs][:weekly][:change] >= 0 ? 'text-green-600' : 'text-red-600' %>">
                <%= @analytics[:growth_trends][:jobs][:weekly][:change] >= 0 ? '+' : '' %><%= @analytics[:growth_trends][:jobs][:weekly][:change] %>%
              </span>
            </div>
            <div class="text-lg font-semibold text-stone-900"><%= @analytics[:growth_trends][:jobs][:weekly][:this_week] %></div>
            <div class="text-xs text-stone-500">vs <%= @analytics[:growth_trends][:jobs][:weekly][:last_week] %> last week</div>
          </div>

          <div>
            <div class="flex items-center justify-between">
              <span class="text-sm font-medium text-stone-600">Organizations (Weekly)</span>
              <span class="text-sm <%= @analytics[:growth_trends][:organizations][:weekly][:change] >= 0 ? 'text-green-600' : 'text-red-600' %>">
                <%= @analytics[:growth_trends][:organizations][:weekly][:change] >= 0 ? '+' : '' %><%= @analytics[:growth_trends][:organizations][:weekly][:change] %>%
              </span>
            </div>
            <div class="text-lg font-semibold text-stone-900"><%= @analytics[:growth_trends][:organizations][:weekly][:this_week] %></div>
            <div class="text-xs text-stone-500">vs <%= @analytics[:growth_trends][:organizations][:weekly][:last_week] %> last week</div>
          </div>
        </div>
      </div>
    </div>

    <!-- User Engagement -->
    <div class="bg-white rounded-lg shadow">
      <div class="px-6 py-4 border-b border-stone-200">
        <h2 class="text-lg font-medium text-stone-900">User Engagement</h2>
      </div>
      <div class="p-6">
        <div class="space-y-4">
          <div>
            <div class="flex items-center justify-between">
              <span class="text-sm font-medium text-stone-600">7-Day Active Rate</span>
              <span class="text-lg font-semibold text-stone-900"><%= @analytics[:user_engagement][:engagement_rate_7d] %>%</span>
            </div>
            <div class="text-xs text-stone-500"><%= @analytics[:user_engagement][:active_7_days] %> of <%= @analytics[:user_engagement][:total_users] %> users</div>
          </div>

          <div>
            <div class="flex items-center justify-between">
              <span class="text-sm font-medium text-stone-600">Verification Rate</span>
              <span class="text-lg font-semibold text-stone-900"><%= @analytics[:user_engagement][:verification_rate] %>%</span>
            </div>
          </div>

          <div>
            <div class="flex items-center justify-between">
              <span class="text-sm font-medium text-stone-600">Profile Completion</span>
              <span class="text-lg font-semibold text-stone-900"><%= @analytics[:user_engagement][:profile_completion_rate] %>%</span>
            </div>
          </div>

          <div>
            <div class="flex items-center justify-between">
              <span class="text-sm font-medium text-stone-600">Organization Join Rate</span>
              <span class="text-lg font-semibold text-stone-900"><%= @analytics[:user_engagement][:organization_join_rate] %>%</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Conversion Rates -->
    <div class="bg-white rounded-lg shadow">
      <div class="px-6 py-4 border-b border-stone-200">
        <h2 class="text-lg font-medium text-stone-900">Conversion Rates</h2>
      </div>
      <div class="p-6">
        <div class="space-y-4">
          <div>
            <div class="flex items-center justify-between">
              <span class="text-sm font-medium text-stone-600">Job Application Acceptance</span>
              <span class="text-lg font-semibold text-stone-900"><%= @analytics[:conversion_rates][:job_applications][:acceptance_rate] %>%</span>
            </div>
            <div class="text-xs text-stone-500"><%= @analytics[:conversion_rates][:job_applications][:accepted] %> of <%= @analytics[:conversion_rates][:job_applications][:total] %> applications</div>
          </div>

          <div>
            <div class="flex items-center justify-between">
              <span class="text-sm font-medium text-stone-600">Chat Request Acceptance</span>
              <span class="text-lg font-semibold text-stone-900"><%= @analytics[:conversion_rates][:chat_requests][:acceptance_rate] %>%</span>
            </div>
            <div class="text-xs text-stone-500"><%= @analytics[:conversion_rates][:chat_requests][:accepted] %> of <%= @analytics[:conversion_rates][:chat_requests][:total] %> requests</div>
          </div>

          <div>
            <div class="flex items-center justify-between">
              <span class="text-sm font-medium text-stone-600">Job Application Rate</span>
              <span class="text-lg font-semibold text-stone-900"><%= @analytics[:job_metrics][:application_rate] %>%</span>
            </div>
            <div class="text-xs text-stone-500"><%= @analytics[:job_metrics][:jobs_with_applications] %> of <%= @analytics[:job_metrics][:total_jobs] %> jobs</div>
          </div>

          <div>
            <div class="flex items-center justify-between">
              <span class="text-sm font-medium text-stone-600">Conversation Activity</span>
              <span class="text-lg font-semibold text-stone-900"><%= @analytics[:conversion_rates][:conversations][:activity_rate] %>%</span>
            </div>
            <div class="text-xs text-stone-500"><%= @analytics[:conversion_rates][:conversations][:active_7d] %> active in 7 days</div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- Today's Activity Summary -->
  <div class="mb-8 bg-white rounded-lg shadow">
    <div class="px-6 py-4 border-b border-stone-200">
      <h2 class="text-lg font-medium text-stone-900">Today's Activity Summary</h2>
    </div>
    <div class="p-6">
      <div class="grid grid-cols-2 gap-6 md:grid-cols-3 lg:grid-cols-6">
        <div class="text-center">
          <div class="text-2xl font-bold text-blue-600"><%= @analytics[:daily_activity][:new_users] %></div>
          <div class="text-sm text-stone-600">New Users</div>
        </div>
        <div class="text-center">
          <div class="text-2xl font-bold text-green-600"><%= @analytics[:daily_activity][:new_jobs] %></div>
          <div class="text-sm text-stone-600">New Jobs</div>
        </div>
        <div class="text-center">
          <div class="text-2xl font-bold text-purple-600"><%= @analytics[:daily_activity][:applications] %></div>
          <div class="text-sm text-stone-600">Applications</div>
        </div>
        <div class="text-center">
          <div class="text-2xl font-bold text-orange-600"><%= @analytics[:daily_activity][:messages] %></div>
          <div class="text-sm text-stone-600">Messages</div>
        </div>
        <div class="text-center">
          <div class="text-2xl font-bold text-red-600"><%= @analytics[:daily_activity][:admin_actions] %></div>
          <div class="text-sm text-stone-600">Admin Actions</div>
        </div>
        <div class="text-center">
          <div class="text-2xl font-bold text-stone-600"><%= @analytics[:daily_activity][:peak_hour] %></div>
          <div class="text-sm text-stone-600">Peak Hour</div>
        </div>
      </div>
    </div>
  </div>

  <!-- Recent Activity Grid -->
  <div class="grid grid-cols-1 gap-8 mb-8 lg:grid-cols-2">
    <!-- Recent Users -->
    <div class="bg-white rounded-lg shadow">
      <div class="px-6 py-4 border-b border-stone-200">
        <h2 class="text-lg font-medium text-stone-900">Recent Users</h2>
      </div>
      <div class="p-6">
        <div class="space-y-4">
          <% @recent_users.each do |user| %>
            <div class="flex items-center space-x-3">
              <div class="flex-shrink-0">
                <div class="flex items-center justify-center w-8 h-8 rounded-full bg-stone-200">
                  <span class="text-xs font-medium text-stone-600">
                    <%= user.first_name&.first&.upcase %><%= user.last_name&.first&.upcase %>
                  </span>
                </div>
              </div>
              <div class="flex-1 min-w-0">
                <p class="text-sm font-medium text-stone-900 truncate">
                  <%= user.first_name %> <%= user.last_name %>
                </p>
                <p class="text-sm text-stone-500 truncate">
                  <%= user.email %>
                </p>
              </div>
              <div class="text-sm text-stone-500">
                <%= time_ago_in_words(user.created_at) %> ago
              </div>
            </div>
          <% end %>
        </div>
      </div>
    </div>

    <!-- Recent Jobs -->
    <div class="bg-white rounded-lg shadow">
      <div class="px-6 py-4 border-b border-stone-200">
        <h2 class="text-lg font-medium text-stone-900">Recent Jobs</h2>
      </div>
      <div class="p-6">
        <div class="space-y-4">
          <% @recent_jobs.each do |job| %>
            <div class="flex items-center justify-between">
              <div class="flex-1 min-w-0">
                <p class="text-sm font-medium text-stone-900 truncate">
                  <%= job.title %>
                </p>
                <div class="flex items-center space-x-2">
                  <span class="inline-flex items-center px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800">
                    <%= job.status&.humanize || 'Published' %>
                  </span>
                </div>
              </div>
              <div class="text-sm text-stone-500">
                <%= time_ago_in_words(job.created_at) %> ago
              </div>
            </div>
          <% end %>
        </div>
      </div>
    </div>
  </div>
  <!-- System Health -->
  <div class="mb-8 bg-white rounded-lg shadow">
    <div class="px-6 py-4 border-b border-stone-200 flex items-center justify-between">
      <h2 class="text-lg font-medium text-stone-900">System Health</h2>
      <span class="inline-flex items-center px-2 py-1 text-xs font-medium rounded-full bg-yellow-100 text-yellow-800">
        ⚠ Warning
      </span>
    </div>
    <div class="p-6">
      <div class="grid grid-cols-2 gap-6 md:grid-cols-5">
        <div class="text-center">
          <div class="flex items-center justify-center w-12 h-12 mx-auto mb-2 rounded-full bg-green-100">
            <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4"></path>
            </svg>
          </div>
          <div class="text-sm font-medium text-stone-900">Database</div>
          <div class="text-xs text-stone-500"><%= @system_health[:database][:response_time] %>ms</div>
        </div>

        <div class="text-center">
          <div class="flex items-center justify-center w-12 h-12 mx-auto mb-2 rounded-full bg-yellow-100">
            <svg class="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
            </svg>
          </div>
          <div class="text-sm font-medium text-stone-900">Search</div>
          <div class="text-xs text-stone-500"><%= @system_health[:search][:indices] %> indices</div>
        </div>

        <div class="text-center">
          <div class="flex items-center justify-center w-12 h-12 mx-auto mb-2 rounded-full bg-yellow-100">
            <svg class="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0H8m8 0v2a2 2 0 002 2M8 6v2a2 2 0 002 2m0 0h4m-4 0a2 2 0 00-2 2v4a2 2 0 002 2h4a2 2 0 002-2v-4a2 2 0 00-2-2"></path>
            </svg>
          </div>
          <div class="text-sm font-medium text-stone-900">Jobs</div>
          <div class="text-xs text-stone-500"><%= @system_health[:jobs][:pending] %> pending</div>
        </div>

        <div class="text-center">
          <div class="flex items-center justify-center w-12 h-12 mx-auto mb-2 rounded-full bg-green-100">
            <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
            </svg>
          </div>
          <div class="text-sm font-medium text-stone-900">Cache</div>
          <div class="text-xs text-stone-500"><%= @system_health[:cache][:hit_rate] %>ms</div>
        </div>

        <div class="text-center">
          <div class="flex items-center justify-center w-12 h-12 mx-auto mb-2 rounded-full bg-green-100">
            <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"></path>
            </svg>
          </div>
          <div class="text-sm font-medium text-stone-900">Storage</div>
          <div class="text-xs text-stone-500"><%= @system_health[:storage][:usage] %>ms</div>
        </div>
      </div>
    </div>
  </div>

  <!-- Additional Stats Grid -->
  <div class="grid grid-cols-1 gap-6 mb-8 md:grid-cols-4">
    <div class="p-6 bg-white rounded-lg shadow">
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <div class="flex items-center justify-center w-8 h-8 rounded-md bg-stone-600">
            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0z"></path>
            </svg>
          </div>
        </div>
        <div class="flex-1 w-0 ml-5">
          <dl>
            <dt class="text-sm font-medium truncate text-stone-500">Total Users</dt>
            <dd class="text-lg font-medium text-stone-900"><%= @user_count %></dd>
            <dd class="text-xs text-stone-500"><%= @analytics[:user_stats][:verified] %> verified, <%= @analytics[:user_stats][:scouts] %> scouts, <%= @analytics[:user_stats][:talents] %> talents</dd>
          </dl>
        </div>
      </div>
    </div>

    <div class="p-6 bg-white rounded-lg shadow">
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <div class="flex items-center justify-center w-8 h-8 rounded-md bg-stone-600">
            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0H8m8 0v2a2 2 0 002 2M8 6v2a2 2 0 002 2m0 0h4m-4 0a2 2 0 00-2 2v4a2 2 0 002 2h4a2 2 0 002-2v-4a2 2 0 00-2-2"></path>
            </svg>
          </div>
        </div>
        <div class="flex-1 w-0 ml-5">
          <dl>
            <dt class="text-sm font-medium truncate text-stone-500">Total Jobs</dt>
            <dd class="text-lg font-medium text-stone-900"><%= @analytics[:job_stats][:total] %></dd>
            <dd class="text-xs text-stone-500"><%= @analytics[:job_stats][:published] %> published, <%= @analytics[:job_stats][:draft] %> draft, <%= @analytics[:job_stats][:expired] %> expired</dd>
          </dl>
        </div>
      </div>
    </div>

    <div class="p-6 bg-white rounded-lg shadow">
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <div class="flex items-center justify-center w-8 h-8 rounded-md bg-stone-600">
            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
            </svg>
          </div>
        </div>
        <div class="flex-1 w-0 ml-5">
          <dl>
            <dt class="text-sm font-medium truncate text-stone-500">Organizations</dt>
            <dd class="text-lg font-medium text-stone-900"><%= @analytics[:organization_stats][:total] %></dd>
            <dd class="text-xs text-stone-500"><%= @analytics[:organization_stats][:with_jobs] %> with jobs</dd>
          </dl>
        </div>
      </div>
    </div>

    <div class="p-6 bg-white rounded-lg shadow">
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <div class="flex items-center justify-center w-8 h-8 rounded-md bg-stone-600">
            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
            </svg>
          </div>
        </div>
        <div class="flex-1 w-0 ml-5">
          <dl>
            <dt class="text-sm font-medium truncate text-stone-500">Communication</dt>
            <dd class="text-lg font-medium text-stone-900"><%= @analytics[:communication_stats][:total] %></dd>
            <dd class="text-xs text-stone-500"><%= @analytics[:communication_stats][:chat_requests] %> chat requests, <%= @analytics[:communication_stats][:messages] %> messages</dd>
          </dl>
        </div>
      </div>
    </div>
  </div>





</div>
