<% content_for :title, "Edit Organization Membership ##{@organization_membership.id}" %>

<div class="bg-white rounded-lg shadow">
  <!-- Header -->
  <div class="px-6 py-4 border-b border-stone-200">
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-xl font-semibold text-stone-900">Edit Organization Membership #<%= @organization_membership.id %></h1>
        <p class="text-sm text-stone-500 mt-1">Update user role in organization</p>
      </div>
      <div class="flex items-center space-x-3">
        <%= link_to "View", super_admin_admin_organization_membership_path(@organization_membership), 
            class: "inline-flex items-center px-3 py-2 border border-stone-300 rounded-md text-sm font-medium text-stone-700 bg-white hover:bg-stone-50" %>
        <%= link_to "Back to List", super_admin_admin_organization_memberships_path, 
            class: "inline-flex items-center px-3 py-2 border border-stone-300 rounded-md text-sm font-medium text-stone-700 bg-white hover:bg-stone-50" %>
      </div>
    </div>
  </div>

  <div class="p-6">
    <%= form_with model: @organization_membership, url: super_admin_admin_organization_membership_path(@organization_membership), method: :patch, local: true, class: "space-y-6" do |form| %>
      <% if @organization_membership.errors.any? %>
        <div class="bg-red-50 border border-red-200 rounded-md p-4">
          <div class="flex">
            <div class="flex-shrink-0">
              <svg class="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
              </svg>
            </div>
            <div class="ml-3">
              <h3 class="text-sm font-medium text-red-800">There were errors with your submission:</h3>
              <div class="mt-2 text-sm text-red-700">
                <ul class="list-disc pl-5 space-y-1">
                  <% @organization_membership.errors.full_messages.each do |message| %>
                    <li><%= message %></li>
                  <% end %>
                </ul>
              </div>
            </div>
          </div>
        </div>
      <% end %>

      <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- Left Column: Editable Fields -->
        <div class="space-y-6">
          <!-- Role Selection -->
          <div>
            <label class="block text-sm font-medium text-stone-700 mb-2">Organization Role</label>
            <%= form.select :org_role, 
                options_for_select([
                  ['Owner', 'owner'],
                  ['Admin', 'admin'],
                  ['Member', 'member']
                ], @organization_membership.org_role), 
                {}, 
                { class: "w-full px-3 py-2 border border-stone-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" } %>
            <p class="mt-1 text-sm text-stone-500">Select the user's role within the organization</p>
          </div>

          <!-- Role Descriptions -->
          <div>
            <h4 class="text-sm font-medium text-stone-700 mb-3">Role Permissions</h4>
            <div class="space-y-3">
              <div class="border border-purple-200 rounded-lg p-3 bg-purple-50">
                <div class="flex items-center mb-2">
                  <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-purple-100 text-purple-800 mr-2">Owner</span>
                  <span class="text-sm font-medium text-stone-900">Full Control</span>
                </div>
                <ul class="text-xs text-stone-600 space-y-1">
                  <li>• Complete organization management</li>
                  <li>• Manage all jobs and applications</li>
                  <li>• Add/remove team members and change roles</li>
                  <li>• Billing and subscription management</li>
                </ul>
              </div>

              <div class="border border-blue-200 rounded-lg p-3 bg-blue-50">
                <div class="flex items-center mb-2">
                  <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800 mr-2">Admin</span>
                  <span class="text-sm font-medium text-stone-900">Management Access</span>
                </div>
                <ul class="text-xs text-stone-600 space-y-1">
                  <li>• Manage jobs and applications</li>
                  <li>• View team member activity</li>
                  <li>• Cannot manage billing or remove owner</li>
                </ul>
              </div>

              <div class="border border-green-200 rounded-lg p-3 bg-green-50">
                <div class="flex items-center mb-2">
                  <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800 mr-2">Member</span>
                  <span class="text-sm font-medium text-stone-900">Basic Access</span>
                </div>
                <ul class="text-xs text-stone-600 space-y-1">
                  <li>• View organization jobs</li>
                  <li>• Basic organization access</li>
                  <li>• Cannot manage jobs or team members</li>
                </ul>
              </div>
            </div>
          </div>

          <!-- Form Actions -->
          <div class="flex items-center justify-end space-x-3 pt-6 border-t border-stone-200">
            <%= link_to "Cancel", super_admin_admin_organization_membership_path(@organization_membership), 
                class: "px-4 py-2 border border-stone-300 rounded-md text-sm font-medium text-stone-700 bg-white hover:bg-stone-50" %>
            <%= form.submit "Update Role", 
                class: "px-4 py-2 bg-blue-600 text-white rounded-md text-sm font-medium hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2" %>
          </div>
        </div>

        <!-- Right Column: Read-only Context -->
        <div class="space-y-6">
          <!-- User Details -->
          <div>
            <h3 class="text-lg font-medium text-stone-900 mb-4">User Details</h3>
            <div class="bg-stone-50 rounded-lg p-4">
              <div class="space-y-3">
                <div class="flex items-center space-x-3">
                  <% if @organization_membership.user.avatar.attached? %>
                    <%= image_tag @organization_membership.user.avatar, class: "h-10 w-10 rounded-full object-cover" %>
                  <% else %>
                    <div class="h-10 w-10 rounded-full bg-stone-300 flex items-center justify-center">
                      <span class="text-stone-600 font-medium">
                        <%= @organization_membership.user.first_name&.first&.upcase %>
                      </span>
                    </div>
                  <% end %>
                  <div>
                    <h4 class="font-medium text-stone-900"><%= @organization_membership.user.name.full %></h4>
                    <p class="text-sm text-stone-600"><%= @organization_membership.user.email %></p>
                  </div>
                </div>
                
                <div class="space-y-2 text-sm">
                  <div class="flex justify-between">
                    <span class="text-stone-600">User ID:</span>
                    <span class="text-stone-900 font-mono"><%= @organization_membership.user.id %></span>
                  </div>
                  
                  <div class="flex justify-between">
                    <span class="text-stone-600">Account Created:</span>
                    <span class="text-stone-900"><%= @organization_membership.user.created_at.strftime('%B %d, %Y') %></span>
                  </div>
                  
                  <div class="flex justify-between">
                    <span class="text-stone-600">Verified:</span>
                    <span class="text-stone-900">
                      <% if @organization_membership.user.verified? %>
                        <span class="text-green-600">✓ Verified</span>
                      <% else %>
                        <span class="text-red-600">✗ Not Verified</span>
                      <% end %>
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Organization Details -->
          <div>
            <h3 class="text-lg font-medium text-stone-900 mb-4">Organization Details</h3>
            <div class="bg-stone-50 rounded-lg p-4">
              <div class="space-y-3">
                <div>
                  <h4 class="font-medium text-stone-900"><%= @organization_membership.organization.name %></h4>
                  <p class="text-sm text-stone-600">Organization ID: <%= @organization_membership.organization.id %></p>
                </div>
                
                <div class="space-y-2 text-sm">
                  <div class="flex justify-between">
                    <span class="text-stone-600">Total Members:</span>
                    <span class="text-stone-900"><%= @organization_membership.organization.organization_memberships.count %></span>
                  </div>
                  
                  <div class="flex justify-between">
                    <span class="text-stone-600">Total Jobs:</span>
                    <span class="text-stone-900"><%= @organization_membership.organization.jobs.count %></span>
                  </div>
                  
                  <div class="flex justify-between">
                    <span class="text-stone-600">Created:</span>
                    <span class="text-stone-900"><%= @organization_membership.organization.created_at.strftime('%B %d, %Y') %></span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Membership Timeline -->
          <div>
            <h3 class="text-lg font-medium text-stone-900 mb-4">Membership Timeline</h3>
            <div class="bg-stone-50 rounded-lg p-4">
              <div class="space-y-3">
                <div class="flex items-center space-x-3">
                  <div class="flex-shrink-0 w-2 h-2 bg-blue-500 rounded-full"></div>
                  <div class="text-sm">
                    <span class="font-medium text-stone-900">Joined Organization</span>
                    <span class="text-stone-500 ml-2"><%= @organization_membership.created_at.strftime('%B %d, %Y at %I:%M %p') %></span>
                  </div>
                </div>
                
                <% if @organization_membership.updated_at != @organization_membership.created_at %>
                  <div class="flex items-center space-x-3">
                    <div class="flex-shrink-0 w-2 h-2 bg-green-500 rounded-full"></div>
                    <div class="text-sm">
                      <span class="font-medium text-stone-900">Last Role Update</span>
                      <span class="text-stone-500 ml-2"><%= @organization_membership.updated_at.strftime('%B %d, %Y at %I:%M %p') %></span>
                    </div>
                  </div>
                <% end %>
              </div>
            </div>
          </div>
        </div>
      </div>
    <% end %>
  </div>
</div>
