<% content_for :title, "Organization Memberships" %>

<div class="bg-white rounded-lg shadow">
  <!-- Header -->
  <div class="px-6 py-4 border-b border-stone-200">
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-xl font-semibold text-stone-900">Organization Memberships</h1>
        <p class="text-sm text-stone-500 mt-1">Manage user-organization relationships and roles</p>
      </div>
      <div class="flex items-center space-x-3">
        <%= link_to "Export CSV", super_admin_admin_organization_memberships_path(format: :csv, **request.query_parameters), 
            class: "inline-flex items-center px-3 py-2 border border-stone-300 rounded-md text-sm font-medium text-stone-700 bg-white hover:bg-stone-50" %>
      </div>
    </div>
  </div>

  <!-- Stats Cards -->
  <div class="px-6 py-4 border-b border-stone-200 bg-stone-50">
    <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
      <div class="bg-white p-4 rounded-lg border border-stone-200">
        <div class="text-2xl font-bold text-stone-900"><%= @stats[:total] %></div>
        <div class="text-sm text-stone-500">Total Memberships</div>
      </div>
      <div class="bg-white p-4 rounded-lg border border-stone-200">
        <div class="text-2xl font-bold text-purple-600"><%= @stats[:owners] %></div>
        <div class="text-sm text-stone-500">Owners</div>
      </div>
      <div class="bg-white p-4 rounded-lg border border-stone-200">
        <div class="text-2xl font-bold text-blue-600"><%= @stats[:admins] %></div>
        <div class="text-sm text-stone-500">Admins</div>
      </div>
      <div class="bg-white p-4 rounded-lg border border-stone-200">
        <div class="text-2xl font-bold text-green-600"><%= @stats[:members] %></div>
        <div class="text-sm text-stone-500">Members</div>
      </div>
      <div class="bg-white p-4 rounded-lg border border-stone-200">
        <div class="text-2xl font-bold text-orange-600"><%= @stats[:organizations_with_multiple_members] %></div>
        <div class="text-sm text-stone-500">Multi-Member Orgs</div>
      </div>
    </div>
  </div>

  <!-- Filters -->
  <div class="px-6 py-4 border-b border-stone-200">
    <%= form_with url: super_admin_admin_organization_memberships_path, method: :get, local: true, class: "space-y-4" do |form| %>
      <!-- Search and Actions Row -->
      <div class="flex flex-col sm:flex-row gap-4">
        <div class="flex-1">
          <%= form.text_field :search, placeholder: "Search by user name, email, or organization name...", 
              value: params[:search], 
              class: "w-full px-3 py-2 border border-stone-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" %>
        </div>
        <div class="flex gap-2">
          <%= form.submit "Filter", class: "px-4 py-2 bg-blue-600 text-white rounded-md text-sm font-medium hover:bg-blue-700" %>
          <%= link_to "Clear", super_admin_admin_organization_memberships_path, class: "px-4 py-2 border border-stone-300 text-stone-700 rounded-md text-sm font-medium hover:bg-stone-50" %>
        </div>
      </div>

      <!-- Filter Dropdowns Row -->
      <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
        <div>
          <%= form.select :org_role, options_for_select([['All Roles', 'all']] + @available_roles, params[:org_role]), 
              {}, { class: "w-full px-3 py-2 border border-stone-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500" } %>
        </div>
        <div>
          <%= form.select :organization_id, options_from_collection_for_select([OpenStruct.new(id: 'all', name: 'All Organizations')] + @available_organizations, :id, :name, params[:organization_id]), 
              {}, { class: "w-full px-3 py-2 border border-stone-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500" } %>
        </div>
        <div>
          <%= form.select :date_range, options_for_select([
                ['All Time', 'all'],
                ['Today', 'today'],
                ['This Week', 'week'],
                ['This Month', 'month']
              ], params[:date_range]), 
              {}, { class: "w-full px-3 py-2 border border-stone-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500" } %>
        </div>
      </div>
    <% end %>
  </div>

  <!-- Bulk Actions -->
  <%= form_with url: bulk_update_super_admin_admin_organization_memberships_path, method: :post, local: true, id: "bulk-form" do |form| %>
    <div class="px-6 py-3 border-b border-stone-200 bg-stone-50">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
          <input type="checkbox" id="select-all" class="rounded border-stone-300 text-blue-600 focus:ring-blue-500">
          <label for="select-all" class="text-sm text-stone-700">Select All</label>
        </div>
        <div class="flex items-center space-x-2">
          <%= form.select :bulk_action, options_for_select([
                ['Promote to Owner', 'promote_to_owner'],
                ['Promote to Admin', 'promote_to_admin'],
                ['Demote to Member', 'demote_to_member'],
                ['Delete Selected', 'delete']
              ]), 
              { prompt: 'Bulk Actions' }, 
              { class: "px-3 py-2 border border-stone-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500" } %>
          <%= form.submit "Apply", class: "px-4 py-2 bg-blue-600 text-white rounded-md text-sm font-medium hover:bg-blue-700", 
              onclick: "return confirm('Are you sure you want to apply this action to selected memberships?')" %>
        </div>
      </div>
    </div>

    <!-- Table -->
    <div class="overflow-x-auto">
      <% if @organization_memberships.any? %>
        <table class="min-w-full divide-y divide-stone-200">
          <thead class="bg-stone-50">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">
                <input type="checkbox" class="rounded border-stone-300 text-blue-600 focus:ring-blue-500">
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">User</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">Organization</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">Role</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">Joined</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">Actions</th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-stone-200">
            <% @organization_memberships.each do |membership| %>
              <tr class="hover:bg-stone-50">
                <td class="px-6 py-4 whitespace-nowrap">
                  <%= check_box_tag "organization_membership_ids[]", membership.id, false, 
                      class: "membership-checkbox rounded border-stone-300 text-blue-600 focus:ring-blue-500" %>
                </td>
                <td class="px-6 py-4">
                  <div class="flex items-center">
                    <% if membership.user.avatar.attached? %>
                      <%= image_tag membership.user.avatar, class: "h-8 w-8 rounded-full object-cover mr-3" %>
                    <% else %>
                      <div class="h-8 w-8 rounded-full bg-stone-300 flex items-center justify-center mr-3">
                        <span class="text-stone-600 text-xs font-medium">
                          <%= membership.user.first_name&.first&.upcase %>
                        </span>
                      </div>
                    <% end %>
                    <div>
                      <div class="text-sm font-medium text-stone-900"><%= membership.user.name.full %></div>
                      <div class="text-sm text-stone-500"><%= membership.user.email %></div>
                    </div>
                  </div>
                </td>
                <td class="px-6 py-4">
                  <div class="text-sm font-medium text-stone-900"><%= membership.organization.name %></div>
                  <div class="text-sm text-stone-500">
                    <%= pluralize(membership.organization.organization_memberships.count, 'member') %>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <% case membership.org_role %>
                  <% when 'owner' %>
                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-purple-100 text-purple-800">Owner</span>
                  <% when 'admin' %>
                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">Admin</span>
                  <% when 'member' %>
                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">Member</span>
                  <% end %>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-stone-500">
                  <%= membership.created_at.strftime('%b %d, %Y') %>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                  <%= link_to "View", super_admin_admin_organization_membership_path(membership), 
                      class: "text-blue-600 hover:text-blue-900" %>
                  <%= link_to "Edit", edit_super_admin_admin_organization_membership_path(membership), 
                      class: "text-indigo-600 hover:text-indigo-900" %>
                  <%= link_to "Delete", super_admin_admin_organization_membership_path(membership), 
                      method: :delete, 
                      confirm: "Are you sure you want to remove #{membership.user.name.full} from #{membership.organization.name}?", 
                      class: "text-red-600 hover:text-red-900" %>
                </td>
              </tr>
            <% end %>
          </tbody>
        </table>
      <% else %>
        <div class="text-center py-12">
          <svg class="mx-auto h-12 w-12 text-stone-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
          </svg>
          <h3 class="mt-2 text-sm font-medium text-stone-900">No organization memberships found</h3>
          <p class="mt-1 text-sm text-stone-500">No memberships match your current filters.</p>
        </div>
      <% end %>
    </div>
  <% end %>

  <!-- Pagination -->
  <% if @organization_memberships.any? %>
    <div class="px-6 py-4 border-t border-stone-200 bg-stone-50">
      <%== pagy_nav(@pagy) if @pagy.pages > 1 %>
    </div>
  <% end %>
</div>

<script>
  // Bulk selection functionality
  document.addEventListener('DOMContentLoaded', function() {
    const selectAllCheckbox = document.getElementById('select-all');
    const membershipCheckboxes = document.querySelectorAll('.membership-checkbox');
    
    selectAllCheckbox.addEventListener('change', function() {
      membershipCheckboxes.forEach(checkbox => {
        checkbox.checked = this.checked;
      });
    });
    
    membershipCheckboxes.forEach(checkbox => {
      checkbox.addEventListener('change', function() {
        const allChecked = Array.from(membershipCheckboxes).every(cb => cb.checked);
        const noneChecked = Array.from(membershipCheckboxes).every(cb => !cb.checked);
        
        selectAllCheckbox.checked = allChecked;
        selectAllCheckbox.indeterminate = !allChecked && !noneChecked;
      });
    });
  });
</script>
