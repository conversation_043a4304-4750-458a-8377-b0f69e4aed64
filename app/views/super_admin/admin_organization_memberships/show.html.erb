<% content_for :title, "Organization Membership ##{@organization_membership.id}" %>

<div class="bg-white rounded-lg shadow">
  <!-- Header -->
  <div class="px-6 py-4 border-b border-stone-200">
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-xl font-semibold text-stone-900">Organization Membership #<%= @organization_membership.id %></h1>
        <p class="text-sm text-stone-500 mt-1">View membership details</p>
      </div>
      <div class="flex items-center space-x-3">
        <%= link_to "Edit", edit_super_admin_admin_organization_membership_path(@organization_membership), 
            class: "inline-flex items-center px-3 py-2 border border-stone-300 rounded-md text-sm font-medium text-stone-700 bg-white hover:bg-stone-50" %>
        <%= link_to "Back to List", super_admin_admin_organization_memberships_path, 
            class: "inline-flex items-center px-3 py-2 border border-stone-300 rounded-md text-sm font-medium text-stone-700 bg-white hover:bg-stone-50" %>
      </div>
    </div>
  </div>

  <div class="p-6">
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
      <!-- Left Column: Membership Details -->
      <div class="space-y-6">
        <!-- Role Information -->
        <div>
          <h3 class="text-lg font-medium text-stone-900 mb-4">Role Information</h3>
          <div class="bg-stone-50 rounded-lg p-4">
            <div class="flex items-center justify-between mb-3">
              <span class="text-sm font-medium text-stone-700">Current Role:</span>
              <% case @organization_membership.org_role %>
              <% when 'owner' %>
                <span class="inline-flex px-3 py-1 text-sm font-semibold rounded-full bg-purple-100 text-purple-800">Owner</span>
              <% when 'admin' %>
                <span class="inline-flex px-3 py-1 text-sm font-semibold rounded-full bg-blue-100 text-blue-800">Admin</span>
              <% when 'member' %>
                <span class="inline-flex px-3 py-1 text-sm font-semibold rounded-full bg-green-100 text-green-800">Member</span>
              <% end %>
            </div>
            
            <div class="space-y-2 text-sm">
              <div class="flex justify-between">
                <span class="text-stone-600">Joined Organization:</span>
                <span class="text-stone-900"><%= @organization_membership.created_at.strftime('%B %d, %Y at %I:%M %p') %></span>
              </div>
              
              <div class="flex justify-between">
                <span class="text-stone-600">Last Updated:</span>
                <span class="text-stone-900"><%= @organization_membership.updated_at.strftime('%B %d, %Y at %I:%M %p') %></span>
              </div>
            </div>
          </div>
        </div>

        <!-- Role Permissions -->
        <div>
          <h3 class="text-lg font-medium text-stone-900 mb-4">Role Permissions</h3>
          <div class="bg-stone-50 rounded-lg p-4">
            <% case @organization_membership.org_role %>
            <% when 'owner' %>
              <div class="space-y-2">
                <div class="flex items-center text-sm">
                  <svg class="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                  </svg>
                  <span class="text-stone-700">Full organization management</span>
                </div>
                <div class="flex items-center text-sm">
                  <svg class="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                  </svg>
                  <span class="text-stone-700">Manage all jobs and applications</span>
                </div>
                <div class="flex items-center text-sm">
                  <svg class="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                  </svg>
                  <span class="text-stone-700">Add/remove team members</span>
                </div>
                <div class="flex items-center text-sm">
                  <svg class="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                  </svg>
                  <span class="text-stone-700">Billing and subscription management</span>
                </div>
              </div>
            <% when 'admin' %>
              <div class="space-y-2">
                <div class="flex items-center text-sm">
                  <svg class="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                  </svg>
                  <span class="text-stone-700">Manage jobs and applications</span>
                </div>
                <div class="flex items-center text-sm">
                  <svg class="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                  </svg>
                  <span class="text-stone-700">View team member activity</span>
                </div>
                <div class="flex items-center text-sm">
                  <svg class="w-4 h-4 text-red-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                  </svg>
                  <span class="text-stone-700">Cannot manage billing or remove owner</span>
                </div>
              </div>
            <% when 'member' %>
              <div class="space-y-2">
                <div class="flex items-center text-sm">
                  <svg class="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                  </svg>
                  <span class="text-stone-700">View organization jobs</span>
                </div>
                <div class="flex items-center text-sm">
                  <svg class="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                  </svg>
                  <span class="text-stone-700">Basic organization access</span>
                </div>
                <div class="flex items-center text-sm">
                  <svg class="w-4 h-4 text-red-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                  </svg>
                  <span class="text-stone-700">Cannot manage jobs or team members</span>
                </div>
              </div>
            <% end %>
          </div>
        </div>
      </div>

      <!-- Right Column: User and Organization Details -->
      <div class="space-y-6">
        <!-- User Details -->
        <div>
          <h3 class="text-lg font-medium text-stone-900 mb-4">User Details</h3>
          <div class="bg-stone-50 rounded-lg p-4">
            <div class="space-y-3">
              <div class="flex items-center space-x-3">
                <% if @organization_membership.user.avatar.attached? %>
                  <%= image_tag @organization_membership.user.avatar, class: "h-12 w-12 rounded-full object-cover" %>
                <% else %>
                  <div class="h-12 w-12 rounded-full bg-stone-300 flex items-center justify-center">
                    <span class="text-stone-600 font-medium text-lg">
                      <%= @organization_membership.user.first_name&.first&.upcase %>
                    </span>
                  </div>
                <% end %>
                <div>
                  <h4 class="font-medium text-stone-900"><%= @organization_membership.user.name.full %></h4>
                  <p class="text-sm text-stone-600"><%= @organization_membership.user.email %></p>
                </div>
              </div>
              
              <div class="space-y-2 text-sm">
                <div class="flex justify-between">
                  <span class="text-stone-600">User ID:</span>
                  <span class="text-stone-900 font-mono"><%= @organization_membership.user.id %></span>
                </div>
                
                <div class="flex justify-between">
                  <span class="text-stone-600">Account Created:</span>
                  <span class="text-stone-900"><%= @organization_membership.user.created_at.strftime('%B %d, %Y') %></span>
                </div>
                
                <div class="flex justify-between">
                  <span class="text-stone-600">Verified:</span>
                  <span class="text-stone-900">
                    <% if @organization_membership.user.verified? %>
                      <span class="text-green-600">✓ Verified</span>
                    <% else %>
                      <span class="text-red-600">✗ Not Verified</span>
                    <% end %>
                  </span>
                </div>
              </div>
              
              <div class="mt-3">
                <%= link_to "View User", super_admin_admin_user_path(@organization_membership.user), 
                    class: "inline-flex items-center px-3 py-1 border border-stone-300 rounded-md text-sm font-medium text-stone-700 bg-white hover:bg-stone-50" %>
              </div>
            </div>
          </div>
        </div>

        <!-- Organization Details -->
        <div>
          <h3 class="text-lg font-medium text-stone-900 mb-4">Organization Details</h3>
          <div class="bg-stone-50 rounded-lg p-4">
            <div class="space-y-3">
              <div>
                <h4 class="font-medium text-stone-900"><%= @organization_membership.organization.name %></h4>
                <p class="text-sm text-stone-600">Organization ID: <%= @organization_membership.organization.id %></p>
              </div>
              
              <div class="space-y-2 text-sm">
                <div class="flex justify-between">
                  <span class="text-stone-600">Total Members:</span>
                  <span class="text-stone-900"><%= @organization_membership.organization.organization_memberships.count %></span>
                </div>
                
                <div class="flex justify-between">
                  <span class="text-stone-600">Total Jobs:</span>
                  <span class="text-stone-900"><%= @organization_membership.organization.jobs.count %></span>
                </div>
                
                <div class="flex justify-between">
                  <span class="text-stone-600">Created:</span>
                  <span class="text-stone-900"><%= @organization_membership.organization.created_at.strftime('%B %d, %Y') %></span>
                </div>
              </div>
              
              <div class="mt-3">
                <%= link_to "View Organization", super_admin_admin_organization_path(@organization_membership.organization), 
                    class: "inline-flex items-center px-3 py-1 border border-stone-300 rounded-md text-sm font-medium text-stone-700 bg-white hover:bg-stone-50" %>
              </div>
            </div>
          </div>
        </div>

        <!-- Other Team Members -->
        <div>
          <h3 class="text-lg font-medium text-stone-900 mb-4">Other Team Members</h3>
          <div class="bg-stone-50 rounded-lg p-4">
            <% other_members = @organization_membership.organization.organization_memberships.where.not(id: @organization_membership.id).includes(:user).limit(5) %>
            <% if other_members.any? %>
              <div class="space-y-3">
                <% other_members.each do |member| %>
                  <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-2">
                      <% if member.user.avatar.attached? %>
                        <%= image_tag member.user.avatar, class: "h-6 w-6 rounded-full object-cover" %>
                      <% else %>
                        <div class="h-6 w-6 rounded-full bg-stone-300 flex items-center justify-center">
                          <span class="text-stone-600 text-xs">
                            <%= member.user.first_name&.first&.upcase %>
                          </span>
                        </div>
                      <% end %>
                      <span class="text-sm text-stone-900"><%= member.user.name.full %></span>
                    </div>
                    <% case member.org_role %>
                    <% when 'owner' %>
                      <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-purple-100 text-purple-800">Owner</span>
                    <% when 'admin' %>
                      <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">Admin</span>
                    <% when 'member' %>
                      <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">Member</span>
                    <% end %>
                  </div>
                <% end %>
                
                <% if @organization_membership.organization.organization_memberships.count > 6 %>
                  <div class="text-sm text-stone-500 text-center pt-2 border-t border-stone-200">
                    +<%= @organization_membership.organization.organization_memberships.count - 6 %> more members
                  </div>
                <% end %>
              </div>
            <% else %>
              <p class="text-sm text-stone-500 italic">This user is the only member of this organization.</p>
            <% end %>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
