<% content_for :title, "Saved Searches" %>

<div class="max-w-7xl mx-auto">
  <div class="mb-8">
    <h1 class="text-3xl font-bold text-stone-900">Saved Searches</h1>
    <p class="mt-2 text-stone-600">Manage your saved search filters and queries</p>
  </div>

  <% if @searches_by_resource.any? %>
    <div class="space-y-8">
      <% @searches_by_resource.each do |resource_type, searches| %>
        <div class="bg-white shadow rounded-lg">
          <div class="px-4 py-5 sm:p-6">
            <h3 class="text-lg font-medium text-stone-900 mb-4">
              <%= resource_type.gsub('Admin', '').pluralize %>
              <span class="text-sm font-normal text-stone-500">(<%= searches.count %> saved searches)</span>
            </h3>
            
            <div class="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
              <% searches.each do |search| %>
                <div class="border border-stone-200 rounded-lg p-4 hover:border-stone-300 transition-colors">
                  <div class="flex items-start justify-between">
                    <div class="flex-1 min-w-0">
                      <h4 class="text-sm font-medium text-stone-900 truncate">
                        <%= search.name %>
                      </h4>
                      <p class="mt-1 text-xs text-stone-500 line-clamp-2">
                        <%= search.description %>
                      </p>
                      <p class="mt-2 text-xs text-stone-400">
                        Saved <%= time_ago_in_words(search.created_at) %> ago
                      </p>
                    </div>
                    
                    <div class="flex items-center space-x-2 ml-4">
                      <%= link_to super_admin_saved_search_path(search),
                          class: "inline-flex items-center p-1 border border-transparent text-xs font-medium rounded text-stone-600 hover:text-stone-900",
                          title: "Apply search" do %>
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                      <% end %>
                      
                      <%= link_to super_admin_saved_search_path(search),
                          method: :delete,
                          data: { 
                            confirm: "Are you sure you want to delete the saved search '#{search.name}'?",
                            turbo_method: :delete
                          },
                          class: "inline-flex items-center p-1 border border-transparent text-xs font-medium rounded text-red-600 hover:text-red-900",
                          title: "Delete search" do %>
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                        </svg>
                      <% end %>
                    </div>
                  </div>
                </div>
              <% end %>
            </div>
          </div>
        </div>
      <% end %>
    </div>
  <% else %>
    <div class="text-center py-12">
      <svg class="mx-auto h-12 w-12 text-stone-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
      </svg>
      <h3 class="mt-2 text-sm font-medium text-stone-900">No saved searches</h3>
      <p class="mt-1 text-sm text-stone-500">
        You haven't saved any searches yet. Use the advanced search feature on any admin page to save your search criteria.
      </p>
      <div class="mt-6">
        <%= link_to "Browse Users", super_admin_admin_users_path,
            class: "inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-stone-600 hover:bg-stone-700" %>
      </div>
    </div>
  <% end %>
</div>

<style>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
