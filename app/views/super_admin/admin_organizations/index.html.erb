<% content_for :title, "Organizations" %>

<div class="bg-white rounded-lg shadow">
  <div class="px-4 py-5 sm:p-6">
    <div class="sm:flex sm:items-center">
      <div class="sm:flex-auto">
        <h1 class="text-xl font-semibold text-stone-900">Organizations</h1>
        <p class="mt-2 text-sm text-stone-700">
          Manage company organizations and their settings
        </p>
      </div>
      <div class="mt-4 sm:mt-0 sm:ml-16 sm:flex-none">
        <%= link_to request.params.merge(format: :csv),
            class: "inline-flex items-center justify-center rounded-md border border-stone-300  px-4 py-2 text-sm font-medium text-stone-800 shadow-sm hover:bg-stone-100 focus:outline-none focus:ring-2 focus:ring-stone-500 focus:ring-offset-2" do %>
          <svg class="w-4 h-4 mr-2 -ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
          </svg>
          Export CSV
        <% end %>
      </div>
    </div>

    <!-- Search and Filters -->
    <div class="pt-6 mt-8 border-t border-stone-200">
      <%= form_with url: super_admin_admin_organizations_path, method: :get, local: true, class: "space-y-4" do |form| %>
        <!-- Search and Action Buttons Row -->
        <div class="flex items-end gap-4">
          <!-- Search Field (takes most space) -->
          <div class="flex-1">
            <%= form.label :search, "Search", class: "block text-sm font-medium text-stone-700" %>
            <%= form.text_field :search, value: params[:search], placeholder: "Search by organization name...",
                class: "mt-1 block w-full rounded-md border-stone-300 shadow-sm focus:border-stone-500 focus:ring-stone-500 sm:text-sm" %>
          </div>

          <!-- Filter and Clear Buttons -->
          <div class="flex items-center space-x-4">
            <%= form.submit "Filter", class: "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-stone-800 hover:bg-stone-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-stone-500" %>
            <% if params[:search].present? || params[:with_jobs].present? || params[:size].present? %>
              <%= link_to "Clear", super_admin_admin_organizations_path, class: "text-stone-600 hover:text-stone-900" %>
            <% end %>
          </div>
        </div>

        <!-- Filter Dropdowns Row -->
        <div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
          <!-- Job Status Filter -->
          <div>
            <%= form.label :with_jobs, "Job Status", class: "block text-sm font-medium text-stone-700" %>
            <%= form.select :with_jobs,
                options_for_select([['All', '']] + @filter_options[:with_jobs], params[:with_jobs]),
                {},
                { class: "mt-1 block w-full rounded-md border-stone-300 shadow-sm focus:border-stone-500 focus:ring-stone-500 sm:text-sm" } %>
          </div>

          <!-- Company Size Filter -->
          <div>
            <%= form.label :size, "Company Size", class: "block text-sm font-medium text-stone-700" %>
            <%= form.select :size,
                options_for_select([['All', '']] + @filter_options[:size], params[:size]),
                {},
                { class: "mt-1 block w-full rounded-md border-stone-300 shadow-sm focus:border-stone-500 focus:ring-stone-500 sm:text-sm" } %>
          </div>
        </div>
      <% end %>
    </div>

    <!-- Table Section -->
    <div class="pt-6 mt-8 border-t border-stone-100">
      <!-- Organizations Table -->
      <div class="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
        <table class="min-w-full divide-y divide-stone-300">
          <thead class="bg-stone-50">
            <tr>
              <th scope="col" class="px-6 py-3 text-xs font-medium tracking-wider text-left uppercase text-stone-500">
                ID
              </th>
              <th scope="col" class="px-6 py-3 text-xs font-medium tracking-wider text-left uppercase text-stone-500">
                Organization
              </th>
              <th scope="col" class="px-6 py-3 text-xs font-medium tracking-wider text-left uppercase text-stone-500">
                Size
              </th>
              <th scope="col" class="px-6 py-3 text-xs font-medium tracking-wider text-left uppercase text-stone-500">
                Members
              </th>
              <th scope="col" class="px-6 py-3 text-xs font-medium tracking-wider text-left uppercase text-stone-500">
                Jobs
              </th>
              <th scope="col" class="px-6 py-3 text-xs font-medium tracking-wider text-left uppercase text-stone-500">
                Created
              </th>
              <th scope="col" class="relative px-6 py-3">
                <span class="sr-only">Actions</span>
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-stone-200">
            <% if @organizations.any? %>
              <% @organizations.each do |organization| %>
                <tr class="hover:bg-stone-50">
                  <td class="px-6 py-4 text-sm text-stone-900 whitespace-nowrap">
                    <%= organization.id %>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm font-medium text-stone-900"><%= organization.name %></div>
                    <% if organization.operating_timezone.present? %>
                      <div class="text-sm text-stone-500">Timezone: <%= organization.operating_timezone %></div>
                    <% end %>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <% if organization.size.present? %>
                      <% size_class = case organization.size
                                     when 'startup' then 'bg-blue-100 text-blue-800'
                                     when 'small' then 'bg-green-100 text-green-800'
                                     when 'medium' then 'bg-yellow-100 text-yellow-800'
                                     when 'large' then 'bg-orange-100 text-orange-800'
                                     when 'enterprise' then 'bg-purple-100 text-purple-800'
                                     else 'bg-stone-100 text-stone-800'
                                     end %>
                      <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <%= size_class %>">
                        <%= organization.size.humanize %>
                      </span>
                    <% else %>
                      <span class="text-sm italic text-stone-500">Not specified</span>
                    <% end %>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <% members_count = organization.organization_memberships.size %>
                    <% owners_count = organization.organization_memberships.count { |m| m.org_role == 'owner' } %>
                    <% admins_count = organization.organization_memberships.count { |m| m.org_role == 'admin' } %>
                    <div class="text-sm font-medium text-stone-900"><%= members_count %> total</div>
                    <div class="text-sm text-stone-500"><%= owners_count %> owners, <%= admins_count %> admins</div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <% jobs_count = organization.jobs.size %>
                    <% published_count = organization.jobs.count { |j| j.status == 'published' } %>
                    <div class="text-sm font-medium text-stone-900"><%= jobs_count %> total</div>
                    <% if jobs_count > 0 %>
                      <div class="text-sm text-stone-500"><%= published_count %> published</div>
                    <% end %>
                  </td>
                  <td class="px-6 py-4 text-sm text-stone-900 whitespace-nowrap">
                    <div class="text-sm text-stone-900"><%= organization.created_at.strftime('%b %d, %Y') %></div>
                    <div class="text-xs text-stone-500"><%= organization.created_at.strftime('%I:%M %p') %></div>
                  </td>
                  <td class="relative px-6 py-4 text-sm font-medium text-right whitespace-nowrap">
                    <div class="flex items-center justify-end space-x-2">
                      <%= link_to "View", super_admin_admin_organization_path(organization),
                          class: "inline-flex items-center px-3 py-1.5 text-xs font-medium rounded-md text-stone-700 bg-white border border-stone-300 hover:bg-stone-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-stone-500 transition-colors" %>
                      <%= link_to "Edit", edit_super_admin_admin_organization_path(organization),
                          class: "inline-flex items-center px-3 py-1.5 text-xs font-medium rounded-md text-stone-700 bg-white border border-stone-300 hover:bg-stone-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-stone-500 transition-colors" %>
                    </div>
                  </td>
                </tr>
              <% end %>
            <% else %>
              <tr>
                <td colspan="6" class="px-6 py-12 text-sm text-center text-stone-900">
                  <div class="text-stone-500">
                    <svg class="w-12 h-12 mx-auto mb-4 text-stone-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                    </svg>
                    <p class="text-sm font-medium">No organizations found</p>
                    <p class="text-xs">Try adjusting your search or filter criteria</p>
                  </div>
                </td>
              </tr>
            <% end %>
          </tbody>
        </table>

        <!-- Table Footer -->
        <div class="px-6 py-2 border-t bg-stone-50 border-stone-200">
          <div class="flex items-center justify-between">
            <div class="text-sm text-stone-700">
              Showing <%= @pagy.from %> to <%= @pagy.to %> of <%= @pagy.count %> organizations
            </div>
            <div class="flex items-center space-x-2">
              <%= render 'shared/admin/pagination', collection: @pagy %>
            </div>
          </div>
        </div>
      </div>
  </div>
</div>
