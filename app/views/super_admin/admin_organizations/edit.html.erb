<% content_for :title, "Edit #{@organization.name}" %>

<div class="max-w-4xl mx-auto">
  <!-- Header -->
  <div class="mb-8">
    <nav class="flex" aria-label="Breadcrumb">
      <ol class="flex items-center space-x-4">
        <li>
          <%= link_to "Organizations", super_admin_admin_organizations_path, class: "text-stone-500 hover:text-stone-700" %>
        </li>
        <li>
          <div class="flex items-center">
            <svg class="flex-shrink-0 h-5 w-5 text-stone-400" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
            </svg>
            <%= link_to @organization.name, super_admin_admin_organization_path(@organization), class: "ml-4 text-stone-500 hover:text-stone-700" %>
          </div>
        </li>
        <li>
          <div class="flex items-center">
            <svg class="flex-shrink-0 h-5 w-5 text-stone-400" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
            </svg>
            <span class="ml-4 text-sm font-medium text-stone-900">Edit</span>
          </div>
        </li>
      </ol>
    </nav>
    <h1 class="mt-2 text-3xl font-bold text-stone-900">Edit Organization</h1>
    <p class="mt-1 text-stone-600">Update organization details and settings</p>
  </div>

  <!-- Form -->
  <div class="bg-white shadow rounded-lg">
    <div class="px-6 py-4 border-b border-stone-200">
      <h2 class="text-lg font-medium text-stone-900">Organization Information</h2>
    </div>
    
    <%= form_with model: @organization, url: super_admin_admin_organization_path(@organization), method: :patch, local: true, class: "px-6 py-4" do |form| %>
      <% if @organization.errors.any? %>
        <div class="mb-6 bg-red-50 border border-red-200 rounded-md p-4">
          <div class="flex">
            <div class="flex-shrink-0">
              <svg class="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
              </svg>
            </div>
            <div class="ml-3">
              <h3 class="text-sm font-medium text-red-800">
                There were <%= pluralize(@organization.errors.count, "error") %> with your submission:
              </h3>
              <div class="mt-2 text-sm text-red-700">
                <ul class="list-disc pl-5 space-y-1">
                  <% @organization.errors.full_messages.each do |message| %>
                    <li><%= message %></li>
                  <% end %>
                </ul>
              </div>
            </div>
          </div>
        </div>
      <% end %>

      <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
        <!-- Organization Name -->
        <div class="sm:col-span-2">
          <%= form.label :name, class: "block text-sm font-medium text-stone-700" %>
          <%= form.text_field :name, 
              class: "mt-1 block w-full border-stone-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm",
              placeholder: "Enter organization name" %>
        </div>

        <!-- Company Size -->
        <div>
          <%= form.label :size, "Company Size", class: "block text-sm font-medium text-stone-700" %>
          <%= form.select :size, 
              options_for_select([
                ['Select company size', ''],
                ['Startup (1-10)', 'startup'],
                ['Small (11-50)', 'small'],
                ['Medium (51-200)', 'medium'],
                ['Large (201-1000)', 'large'],
                ['Enterprise (1000+)', 'enterprise']
              ], @organization.size),
              {},
              { class: "mt-1 block w-full border-stone-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm" } %>
        </div>

        <!-- Operating Timezone -->
        <div>
          <%= form.label :operating_timezone, "Operating Timezone", class: "block text-sm font-medium text-stone-700" %>
          <%= form.select :operating_timezone,
              options_for_select([['Select timezone', '']] + ActiveSupport::TimeZone.all.map { |tz| [tz.to_s, tz.name] }, @organization.operating_timezone),
              {},
              { class: "mt-1 block w-full border-stone-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm" } %>
        </div>
      </div>

      <!-- Form Actions -->
      <div class="mt-8 flex items-center justify-between">
        <div>
          <%= link_to "Cancel", super_admin_admin_organization_path(@organization), 
              class: "bg-white py-2 px-4 border border-stone-300 rounded-md shadow-sm text-sm font-medium text-stone-700 hover:bg-stone-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500" %>
        </div>
        <div class="flex space-x-3">
          <%= link_to "View Organization", super_admin_admin_organization_path(@organization), 
              class: "bg-white py-2 px-4 border border-stone-300 rounded-md shadow-sm text-sm font-medium text-stone-700 hover:bg-stone-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500" %>
          <%= form.submit "Update Organization", 
              class: "inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500" %>
        </div>
      </div>
    <% end %>
  </div>
</div>
