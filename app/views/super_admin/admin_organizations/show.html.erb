<% content_for :title, @organization.name %>

<div class="max-w-7xl mx-auto">
  <!-- Header -->
  <div class="mb-8">
    <div class="flex items-center justify-between">
      <div>
        <nav class="flex" aria-label="Breadcrumb">
          <ol class="flex items-center space-x-4">
            <li>
              <%= link_to "Organizations", super_admin_admin_organizations_path, class: "text-stone-500 hover:text-stone-700" %>
            </li>
            <li>
              <div class="flex items-center">
                <svg class="flex-shrink-0 h-5 w-5 text-stone-400" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                </svg>
                <span class="ml-4 text-sm font-medium text-stone-900"><%= @organization.name %></span>
              </div>
            </li>
          </ol>
        </nav>
        <h1 class="mt-2 text-3xl font-bold text-stone-900"><%= @organization.name %></h1>
        <p class="mt-1 text-stone-600">Organization details and management</p>
      </div>
      <div>
        <%= link_to "Edit Organization", edit_super_admin_admin_organization_path(@organization), 
            class: "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500" %>
      </div>
    </div>
  </div>

  <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
    <!-- Main Content -->
    <div class="lg:col-span-2 space-y-8">
      <!-- Organization Details -->
      <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-stone-200">
          <h2 class="text-lg font-medium text-stone-900">Organization Details</h2>
        </div>
        <div class="px-6 py-4">
          <dl class="grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2">
            <div>
              <dt class="text-sm font-medium text-stone-500">Organization ID</dt>
              <dd class="mt-1 text-sm text-stone-900"><%= @organization.id %></dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-stone-500">Name</dt>
              <dd class="mt-1 text-sm text-stone-900"><%= @organization.name %></dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-stone-500">Company Size</dt>
              <dd class="mt-1">
                <% if @organization.size.present? %>
                  <% size_class = case @organization.size
                                  when 'startup' then 'bg-blue-100 text-blue-800'
                                  when 'small' then 'bg-green-100 text-green-800'
                                  when 'medium' then 'bg-yellow-100 text-yellow-800'
                                  when 'large' then 'bg-orange-100 text-orange-800'
                                  when 'enterprise' then 'bg-purple-100 text-purple-800'
                                  else 'bg-stone-100 text-stone-800'
                                  end %>
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <%= size_class %>">
                    <%= @organization.size.humanize %>
                  </span>
                <% else %>
                  <span class="text-sm text-stone-500 italic">Not specified</span>
                <% end %>
              </dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-stone-500">Operating Timezone</dt>
              <dd class="mt-1 text-sm text-stone-900">
                <%= @organization.operating_timezone.present? ? @organization.operating_timezone : "Not specified" %>
              </dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-stone-500">Created At</dt>
              <dd class="mt-1 text-sm text-stone-900"><%= @organization.created_at.strftime('%B %d, %Y at %I:%M %p') %></dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-stone-500">Last Updated</dt>
              <dd class="mt-1 text-sm text-stone-900"><%= @organization.updated_at.strftime('%B %d, %Y at %I:%M %p') %></dd>
            </div>
          </dl>
        </div>
      </div>

      <!-- Members -->
      <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-stone-200">
          <h2 class="text-lg font-medium text-stone-900">Organization Members</h2>
        </div>
        <div class="overflow-hidden">
          <% if @members.any? %>
            <table class="min-w-full divide-y divide-stone-200">
              <thead class="bg-stone-50">
                <tr>
                  <th class="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">Member</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">Role</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">Joined</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">Actions</th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-stone-200">
                <% @members.each do |membership| %>
                  <tr>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="flex items-center">
                        <div class="flex-shrink-0 h-8 w-8">
                          <% if membership.user.avatar.attached? %>
                            <%= image_tag membership.user.avatar, class: "h-8 w-8 rounded-full object-cover" %>
                          <% else %>
                            <div class="h-8 w-8 rounded-full bg-stone-300 flex items-center justify-center">
                              <span class="text-xs font-medium text-stone-700"><%= membership.user.initials %></span>
                            </div>
                          <% end %>
                        </div>
                        <div class="ml-4">
                          <div class="text-sm font-medium text-stone-900"><%= membership.user.name %></div>
                          <div class="text-sm text-stone-500"><%= membership.user.email %></div>
                        </div>
                      </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <% role_class = case membership.org_role
                                      when 'owner' then 'bg-purple-100 text-purple-800'
                                      when 'admin' then 'bg-blue-100 text-blue-800'
                                      when 'member' then 'bg-green-100 text-green-800'
                                      else 'bg-stone-100 text-stone-800'
                                      end %>
                      <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <%= role_class %>">
                        <%= membership.org_role.humanize %>
                      </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-stone-500">
                      <%= time_ago_in_words(membership.created_at) %> ago
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <%= link_to "View Profile", super_admin_admin_user_path(membership.user), 
                          class: "text-blue-600 hover:text-blue-900" %>
                    </td>
                  </tr>
                <% end %>
              </tbody>
            </table>
          <% else %>
            <div class="px-6 py-8 text-center">
              <p class="text-stone-500 italic">No members found</p>
            </div>
          <% end %>
        </div>
      </div>

      <!-- Recent Jobs -->
      <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-stone-200">
          <h2 class="text-lg font-medium text-stone-900">Recent Jobs</h2>
        </div>
        <div class="overflow-hidden">
          <% if @recent_jobs.any? %>
            <table class="min-w-full divide-y divide-stone-200">
              <thead class="bg-stone-50">
                <tr>
                  <th class="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">Job</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">Status</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">Created</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">Actions</th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-stone-200">
                <% @recent_jobs.each do |job| %>
                  <tr>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm font-medium text-stone-900"><%= truncate(job.title, length: 50) %></div>
                      <div class="text-sm text-stone-500"><%= job.job_category.humanize if job.job_category %></div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <% status_class = case job.status
                                        when 'published' then 'bg-green-100 text-green-800'
                                        when 'draft' then 'bg-yellow-100 text-yellow-800'
                                        when 'expired' then 'bg-red-100 text-red-800'
                                        else 'bg-stone-100 text-stone-800'
                                        end %>
                      <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <%= status_class %>">
                        <%= job.status.humanize %>
                      </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-stone-500">
                      <%= time_ago_in_words(job.created_at) %> ago
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <%= link_to "View Job", super_admin_admin_job_path(job), 
                          class: "text-blue-600 hover:text-blue-900" %>
                    </td>
                  </tr>
                <% end %>
              </tbody>
            </table>
          <% else %>
            <div class="px-6 py-8 text-center">
              <p class="text-stone-500 italic">No jobs found</p>
            </div>
          <% end %>
        </div>
      </div>
    </div>

    <!-- Sidebar -->
    <div class="space-y-8">
      <!-- Statistics -->
      <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-stone-200">
          <h2 class="text-lg font-medium text-stone-900">Statistics</h2>
        </div>
        <div class="px-6 py-4">
          <dl class="space-y-4">
            <div>
              <dt class="text-sm font-medium text-stone-500">Total Jobs</dt>
              <dd class="mt-1 text-2xl font-semibold text-stone-900"><%= @stats[:total_jobs] %></dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-stone-500">Published Jobs</dt>
              <dd class="mt-1 text-2xl font-semibold text-green-600"><%= @stats[:published_jobs] %></dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-stone-500">Draft Jobs</dt>
              <dd class="mt-1 text-2xl font-semibold text-yellow-600"><%= @stats[:draft_jobs] %></dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-stone-500">Total Members</dt>
              <dd class="mt-1 text-2xl font-semibold text-stone-900"><%= @stats[:members_count] %></dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-stone-500">Owners</dt>
              <dd class="mt-1 text-2xl font-semibold text-purple-600"><%= @stats[:owners_count] %></dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-stone-500">Admins</dt>
              <dd class="mt-1 text-2xl font-semibold text-blue-600"><%= @stats[:admins_count] %></dd>
            </div>
          </dl>
        </div>
      </div>
    </div>
  </div>
</div>
