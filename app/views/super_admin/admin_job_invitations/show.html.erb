<% content_for :title, "Job Invitation ##{@job_invitation.id}" %>

<div class="bg-white rounded-lg shadow">
  <!-- Header -->
  <div class="px-6 py-4 border-b border-stone-200">
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-xl font-semibold text-stone-900">Job Invitation #<%= @job_invitation.id %></h1>
        <p class="text-sm text-stone-500 mt-1">View job invitation details</p>
      </div>
      <div class="flex items-center space-x-3">
        <%= link_to "Edit", edit_super_admin_admin_job_invitation_path(@job_invitation), 
            class: "inline-flex items-center px-3 py-2 border border-stone-300 rounded-md text-sm font-medium text-stone-700 bg-white hover:bg-stone-50" %>
        <%= link_to "Back to List", super_admin_admin_job_invitations_path, 
            class: "inline-flex items-center px-3 py-2 border border-stone-300 rounded-md text-sm font-medium text-stone-700 bg-white hover:bg-stone-50" %>
      </div>
    </div>
  </div>

  <div class="p-6">
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
      <!-- Left Column: Invitation Details -->
      <div class="space-y-6">
        <!-- Status -->
        <div>
          <h3 class="text-lg font-medium text-stone-900 mb-4">Invitation Status</h3>
          <div class="bg-stone-50 rounded-lg p-4">
            <div class="flex items-center justify-between mb-3">
              <span class="text-sm font-medium text-stone-700">Current Status:</span>
              <% case @job_invitation.status %>
              <% when 'pending' %>
                <span class="inline-flex px-3 py-1 text-sm font-semibold rounded-full bg-amber-100 text-amber-800">Pending</span>
              <% when 'accepted' %>
                <span class="inline-flex px-3 py-1 text-sm font-semibold rounded-full bg-green-100 text-green-800">Accepted</span>
              <% when 'ignored' %>
                <span class="inline-flex px-3 py-1 text-sm font-semibold rounded-full bg-red-100 text-red-800">Ignored</span>
              <% end %>
            </div>
            
            <div class="space-y-2 text-sm">
              <div class="flex justify-between">
                <span class="text-stone-600">Invited At:</span>
                <span class="text-stone-900"><%= @job_invitation.invited_at&.strftime('%B %d, %Y at %I:%M %p') %></span>
              </div>
              
              <% if @job_invitation.accepted_at %>
                <div class="flex justify-between">
                  <span class="text-stone-600">Accepted At:</span>
                  <span class="text-green-700"><%= @job_invitation.accepted_at.strftime('%B %d, %Y at %I:%M %p') %></span>
                </div>
              <% end %>
              
              <% if @job_invitation.ignored_at %>
                <div class="flex justify-between">
                  <span class="text-stone-600">Ignored At:</span>
                  <span class="text-red-700"><%= @job_invitation.ignored_at.strftime('%B %d, %Y at %I:%M %p') %></span>
                </div>
              <% end %>
            </div>
          </div>
        </div>

        <!-- Invitation Letter -->
        <div>
          <h3 class="text-lg font-medium text-stone-900 mb-4">Invitation Message</h3>
          <div class="bg-stone-50 rounded-lg p-4">
            <% if @job_invitation.invitation_letter.present? %>
              <div class="prose prose-sm max-w-none">
                <%= simple_format(@job_invitation.invitation_letter) %>
              </div>
            <% else %>
              <p class="text-stone-500 italic">No invitation message provided</p>
            <% end %>
          </div>
        </div>

        <!-- Related Job Application -->
        <% if @job_application %>
          <div>
            <h3 class="text-lg font-medium text-stone-900 mb-4">Related Job Application</h3>
            <div class="bg-blue-50 rounded-lg p-4">
              <div class="flex items-center justify-between mb-3">
                <span class="text-sm font-medium text-blue-900">Application Status:</span>
                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                  <%= @job_application.status.humanize %>
                </span>
              </div>
              
              <div class="space-y-2 text-sm">
                <div class="flex justify-between">
                  <span class="text-blue-700">Applied At:</span>
                  <span class="text-blue-900"><%= @job_application.applied_at&.strftime('%B %d, %Y at %I:%M %p') %></span>
                </div>
                
                <div class="mt-3">
                  <%= link_to "View Application", super_admin_admin_job_application_path(@job_application), 
                      class: "inline-flex items-center px-3 py-1 border border-blue-300 rounded-md text-sm font-medium text-blue-700 bg-white hover:bg-blue-50" %>
                </div>
              </div>
            </div>
          </div>
        <% end %>
      </div>

      <!-- Right Column: Job and Talent Details -->
      <div class="space-y-6">
        <!-- Job Details -->
        <div>
          <h3 class="text-lg font-medium text-stone-900 mb-4">Job Details</h3>
          <div class="bg-stone-50 rounded-lg p-4">
            <div class="space-y-3">
              <div>
                <h4 class="font-medium text-stone-900"><%= @job_invitation.job.title %></h4>
                <p class="text-sm text-stone-600"><%= @job_invitation.job.organization.name %></p>
              </div>
              
              <div class="space-y-2 text-sm">
                <div class="flex justify-between">
                  <span class="text-stone-600">Category:</span>
                  <span class="text-stone-900"><%= @job_invitation.job.job_category&.humanize %></span>
                </div>
                
                <div class="flex justify-between">
                  <span class="text-stone-600">Budget Range:</span>
                  <span class="text-stone-900"><%= @job_invitation.job.budget_range&.humanize %></span>
                </div>
                
                <div class="flex justify-between">
                  <span class="text-stone-600">Status:</span>
                  <span class="text-stone-900"><%= @job_invitation.job.status&.humanize %></span>
                </div>
                
                <div class="flex justify-between">
                  <span class="text-stone-600">Published:</span>
                  <span class="text-stone-900"><%= @job_invitation.job.published_at&.strftime('%B %d, %Y') || 'Not published' %></span>
                </div>
              </div>
              
              <div class="mt-3">
                <%= link_to "View Job", super_admin_admin_job_path(@job_invitation.job), 
                    class: "inline-flex items-center px-3 py-1 border border-stone-300 rounded-md text-sm font-medium text-stone-700 bg-white hover:bg-stone-50" %>
              </div>
            </div>
          </div>
        </div>

        <!-- Talent Details -->
        <div>
          <h3 class="text-lg font-medium text-stone-900 mb-4">Talent Details</h3>
          <div class="bg-stone-50 rounded-lg p-4">
            <div class="space-y-3">
              <div class="flex items-center space-x-3">
                <% if @job_invitation.user.avatar.attached? %>
                  <%= image_tag @job_invitation.user.avatar, class: "h-12 w-12 rounded-full object-cover" %>
                <% else %>
                  <div class="h-12 w-12 rounded-full bg-stone-300 flex items-center justify-center">
                    <span class="text-stone-600 font-medium text-lg">
                      <%= @job_invitation.user.first_name&.first&.upcase %>
                    </span>
                  </div>
                <% end %>
                <div>
                  <h4 class="font-medium text-stone-900"><%= @job_invitation.user.name.full %></h4>
                  <p class="text-sm text-stone-600"><%= @job_invitation.user.email %></p>
                </div>
              </div>
              
              <% if @job_invitation.talent_profile %>
                <div class="space-y-2 text-sm">
                  <div class="flex justify-between">
                    <span class="text-stone-600">Availability:</span>
                    <span class="text-stone-900"><%= @job_invitation.talent_profile.availability_status&.humanize %></span>
                  </div>
                  
                  <% if @job_invitation.talent_profile.price_range_min && @job_invitation.talent_profile.price_range_max %>
                    <div class="flex justify-between">
                      <span class="text-stone-600">Price Range:</span>
                      <span class="text-stone-900">
                        $<%= number_with_delimiter(@job_invitation.talent_profile.price_range_min) %> - 
                        $<%= number_with_delimiter(@job_invitation.talent_profile.price_range_max) %>
                      </span>
                    </div>
                  <% end %>
                  
                  <% if @job_invitation.talent_profile.skills.any? %>
                    <div>
                      <span class="text-stone-600">Skills:</span>
                      <div class="mt-1 flex flex-wrap gap-1">
                        <% @job_invitation.talent_profile.skills.first(5).each do |skill| %>
                          <span class="inline-flex px-2 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800">
                            <%= skill %>
                          </span>
                        <% end %>
                        <% if @job_invitation.talent_profile.skills.count > 5 %>
                          <span class="inline-flex px-2 py-1 text-xs font-medium rounded-full bg-stone-100 text-stone-600">
                            +<%= @job_invitation.talent_profile.skills.count - 5 %> more
                          </span>
                        <% end %>
                      </div>
                    </div>
                  <% end %>
                </div>
              <% end %>
              
              <div class="mt-3 space-x-2">
                <%= link_to "View User", super_admin_admin_user_path(@job_invitation.user), 
                    class: "inline-flex items-center px-3 py-1 border border-stone-300 rounded-md text-sm font-medium text-stone-700 bg-white hover:bg-stone-50" %>
                <% if @job_invitation.talent_profile %>
                  <%= link_to "View Profile", super_admin_admin_talent_profile_path(@job_invitation.talent_profile), 
                      class: "inline-flex items-center px-3 py-1 border border-stone-300 rounded-md text-sm font-medium text-stone-700 bg-white hover:bg-stone-50" %>
                <% end %>
              </div>
            </div>
          </div>
        </div>

        <!-- Metadata -->
        <div>
          <h3 class="text-lg font-medium text-stone-900 mb-4">Metadata</h3>
          <div class="bg-stone-50 rounded-lg p-4">
            <div class="space-y-2 text-sm">
              <div class="flex justify-between">
                <span class="text-stone-600">Created:</span>
                <span class="text-stone-900"><%= @job_invitation.created_at.strftime('%B %d, %Y at %I:%M %p') %></span>
              </div>
              
              <div class="flex justify-between">
                <span class="text-stone-600">Last Updated:</span>
                <span class="text-stone-900"><%= @job_invitation.updated_at.strftime('%B %d, %Y at %I:%M %p') %></span>
              </div>
              
              <div class="flex justify-between">
                <span class="text-stone-600">Invitation ID:</span>
                <span class="text-stone-900 font-mono"><%= @job_invitation.id %></span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
