<% content_for :title, "Edit Job Invitation ##{@job_invitation.id}" %>

<div class="bg-white rounded-lg shadow">
  <!-- Header -->
  <div class="px-6 py-4 border-b border-stone-200">
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-xl font-semibold text-stone-900">Edit Job Invitation #<%= @job_invitation.id %></h1>
        <p class="text-sm text-stone-500 mt-1">Update job invitation details</p>
      </div>
      <div class="flex items-center space-x-3">
        <%= link_to "View", super_admin_admin_job_invitation_path(@job_invitation), 
            class: "inline-flex items-center px-3 py-2 border border-stone-300 rounded-md text-sm font-medium text-stone-700 bg-white hover:bg-stone-50" %>
        <%= link_to "Back to List", super_admin_admin_job_invitations_path, 
            class: "inline-flex items-center px-3 py-2 border border-stone-300 rounded-md text-sm font-medium text-stone-700 bg-white hover:bg-stone-50" %>
      </div>
    </div>
  </div>

  <div class="p-6">
    <%= form_with model: @job_invitation, url: super_admin_admin_job_invitation_path(@job_invitation), method: :patch, local: true, class: "space-y-6" do |form| %>
      <% if @job_invitation.errors.any? %>
        <div class="bg-red-50 border border-red-200 rounded-md p-4">
          <div class="flex">
            <div class="flex-shrink-0">
              <svg class="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
              </svg>
            </div>
            <div class="ml-3">
              <h3 class="text-sm font-medium text-red-800">There were errors with your submission:</h3>
              <div class="mt-2 text-sm text-red-700">
                <ul class="list-disc pl-5 space-y-1">
                  <% @job_invitation.errors.full_messages.each do |message| %>
                    <li><%= message %></li>
                  <% end %>
                </ul>
              </div>
            </div>
          </div>
        </div>
      <% end %>

      <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- Left Column: Editable Fields -->
        <div class="space-y-6">
          <!-- Status -->
          <div>
            <label class="block text-sm font-medium text-stone-700 mb-2">Status</label>
            <%= form.select :status, 
                options_for_select([
                  ['Pending', 'pending'],
                  ['Accepted', 'accepted'],
                  ['Ignored', 'ignored']
                ], @job_invitation.status), 
                {}, 
                { class: "w-full px-3 py-2 border border-stone-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" } %>
            <p class="mt-1 text-sm text-stone-500">Update the invitation status</p>
          </div>

          <!-- Invitation Letter -->
          <div>
            <label class="block text-sm font-medium text-stone-700 mb-2">Invitation Message</label>
            <%= form.text_area :invitation_letter, 
                rows: 8, 
                class: "w-full px-3 py-2 border border-stone-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",
                placeholder: "Enter the invitation message..." %>
            <p class="mt-1 text-sm text-stone-500">The message sent to the talent with this invitation</p>
          </div>

          <!-- Form Actions -->
          <div class="flex items-center justify-end space-x-3 pt-6 border-t border-stone-200">
            <%= link_to "Cancel", super_admin_admin_job_invitation_path(@job_invitation), 
                class: "px-4 py-2 border border-stone-300 rounded-md text-sm font-medium text-stone-700 bg-white hover:bg-stone-50" %>
            <%= form.submit "Update Invitation", 
                class: "px-4 py-2 bg-blue-600 text-white rounded-md text-sm font-medium hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2" %>
          </div>
        </div>

        <!-- Right Column: Read-only Context -->
        <div class="space-y-6">
          <!-- Job Details -->
          <div>
            <h3 class="text-lg font-medium text-stone-900 mb-4">Job Details</h3>
            <div class="bg-stone-50 rounded-lg p-4">
              <div class="space-y-3">
                <div>
                  <h4 class="font-medium text-stone-900"><%= @job_invitation.job.title %></h4>
                  <p class="text-sm text-stone-600"><%= @job_invitation.job.organization.name %></p>
                </div>
                
                <div class="space-y-2 text-sm">
                  <div class="flex justify-between">
                    <span class="text-stone-600">Category:</span>
                    <span class="text-stone-900"><%= @job_invitation.job.job_category&.humanize %></span>
                  </div>
                  
                  <div class="flex justify-between">
                    <span class="text-stone-600">Budget Range:</span>
                    <span class="text-stone-900"><%= @job_invitation.job.budget_range&.humanize %></span>
                  </div>
                  
                  <div class="flex justify-between">
                    <span class="text-stone-600">Status:</span>
                    <span class="text-stone-900"><%= @job_invitation.job.status&.humanize %></span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Talent Details -->
          <div>
            <h3 class="text-lg font-medium text-stone-900 mb-4">Talent Details</h3>
            <div class="bg-stone-50 rounded-lg p-4">
              <div class="space-y-3">
                <div class="flex items-center space-x-3">
                  <% if @job_invitation.user.avatar.attached? %>
                    <%= image_tag @job_invitation.user.avatar, class: "h-10 w-10 rounded-full object-cover" %>
                  <% else %>
                    <div class="h-10 w-10 rounded-full bg-stone-300 flex items-center justify-center">
                      <span class="text-stone-600 font-medium">
                        <%= @job_invitation.user.first_name&.first&.upcase %>
                      </span>
                    </div>
                  <% end %>
                  <div>
                    <h4 class="font-medium text-stone-900"><%= @job_invitation.user.name.full %></h4>
                    <p class="text-sm text-stone-600"><%= @job_invitation.user.email %></p>
                  </div>
                </div>
                
                <% if @job_invitation.talent_profile %>
                  <div class="space-y-2 text-sm">
                    <div class="flex justify-between">
                      <span class="text-stone-600">Availability:</span>
                      <span class="text-stone-900"><%= @job_invitation.talent_profile.availability_status&.humanize %></span>
                    </div>
                    
                    <% if @job_invitation.talent_profile.price_range_min && @job_invitation.talent_profile.price_range_max %>
                      <div class="flex justify-between">
                        <span class="text-stone-600">Price Range:</span>
                        <span class="text-stone-900">
                          $<%= number_with_delimiter(@job_invitation.talent_profile.price_range_min) %> - 
                          $<%= number_with_delimiter(@job_invitation.talent_profile.price_range_max) %>
                        </span>
                      </div>
                    <% end %>
                  </div>
                <% end %>
              </div>
            </div>
          </div>

          <!-- Timeline -->
          <div>
            <h3 class="text-lg font-medium text-stone-900 mb-4">Timeline</h3>
            <div class="bg-stone-50 rounded-lg p-4">
              <div class="space-y-3">
                <div class="flex items-center space-x-3">
                  <div class="flex-shrink-0 w-2 h-2 bg-blue-500 rounded-full"></div>
                  <div class="text-sm">
                    <span class="font-medium text-stone-900">Invitation Sent</span>
                    <span class="text-stone-500 ml-2"><%= @job_invitation.invited_at&.strftime('%B %d, %Y at %I:%M %p') %></span>
                  </div>
                </div>
                
                <% if @job_invitation.accepted_at %>
                  <div class="flex items-center space-x-3">
                    <div class="flex-shrink-0 w-2 h-2 bg-green-500 rounded-full"></div>
                    <div class="text-sm">
                      <span class="font-medium text-stone-900">Accepted</span>
                      <span class="text-stone-500 ml-2"><%= @job_invitation.accepted_at.strftime('%B %d, %Y at %I:%M %p') %></span>
                    </div>
                  </div>
                <% end %>
                
                <% if @job_invitation.ignored_at %>
                  <div class="flex items-center space-x-3">
                    <div class="flex-shrink-0 w-2 h-2 bg-red-500 rounded-full"></div>
                    <div class="text-sm">
                      <span class="font-medium text-stone-900">Ignored</span>
                      <span class="text-stone-500 ml-2"><%= @job_invitation.ignored_at.strftime('%B %d, %Y at %I:%M %p') %></span>
                    </div>
                  </div>
                <% end %>
              </div>
            </div>
          </div>
        </div>
      </div>
    <% end %>
  </div>
</div>
