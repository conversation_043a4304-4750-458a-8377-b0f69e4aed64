<% content_for :title, "Job Invitations" %>

<div class="bg-white rounded-lg shadow">
  <!-- Header -->
  <div class="px-6 py-4 border-b border-stone-200">
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-xl font-semibold text-stone-900">Job Invitations</h1>
        <p class="text-sm text-stone-500 mt-1">Manage job invitations sent by scouts to talent</p>
      </div>
      <div class="flex items-center space-x-3">
        <%= link_to "Export CSV", super_admin_admin_job_invitations_path(format: :csv, **request.query_parameters), 
            class: "inline-flex items-center px-3 py-2 border border-stone-300 rounded-md text-sm font-medium text-stone-700 bg-white hover:bg-stone-50" %>
      </div>
    </div>
  </div>

  <!-- Stats Cards -->
  <div class="px-6 py-4 border-b border-stone-200 bg-stone-50">
    <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
      <div class="bg-white p-4 rounded-lg border border-stone-200">
        <div class="text-2xl font-bold text-stone-900"><%= @stats[:total] %></div>
        <div class="text-sm text-stone-500">Total Invitations</div>
      </div>
      <div class="bg-white p-4 rounded-lg border border-stone-200">
        <div class="text-2xl font-bold text-amber-600"><%= @stats[:pending] %></div>
        <div class="text-sm text-stone-500">Pending</div>
      </div>
      <div class="bg-white p-4 rounded-lg border border-stone-200">
        <div class="text-2xl font-bold text-green-600"><%= @stats[:accepted] %></div>
        <div class="text-sm text-stone-500">Accepted</div>
      </div>
      <div class="bg-white p-4 rounded-lg border border-stone-200">
        <div class="text-2xl font-bold text-red-600"><%= @stats[:ignored] %></div>
        <div class="text-sm text-stone-500">Ignored</div>
      </div>
      <div class="bg-white p-4 rounded-lg border border-stone-200">
        <div class="text-2xl font-bold text-blue-600"><%= @stats[:acceptance_rate] %>%</div>
        <div class="text-sm text-stone-500">Acceptance Rate</div>
      </div>
    </div>
  </div>

  <!-- Filters -->
  <div class="px-6 py-4 border-b border-stone-200">
    <%= form_with url: super_admin_admin_job_invitations_path, method: :get, local: true, class: "space-y-4" do |form| %>
      <!-- Search and Actions Row -->
      <div class="flex flex-col sm:flex-row gap-4">
        <div class="flex-1">
          <%= form.text_field :search, placeholder: "Search by job title, talent name, email, or invitation content...", 
              value: params[:search], 
              class: "w-full px-3 py-2 border border-stone-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" %>
        </div>
        <div class="flex gap-2">
          <%= form.submit "Filter", class: "px-4 py-2 bg-blue-600 text-white rounded-md text-sm font-medium hover:bg-blue-700" %>
          <%= link_to "Clear", super_admin_admin_job_invitations_path, class: "px-4 py-2 border border-stone-300 text-stone-700 rounded-md text-sm font-medium hover:bg-stone-50" %>
        </div>
      </div>

      <!-- Filter Dropdowns Row -->
      <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        <div>
          <%= form.select :status, options_for_select([['All Statuses', 'all']] + @available_statuses, params[:status]), 
              {}, { class: "w-full px-3 py-2 border border-stone-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500" } %>
        </div>
        <div>
          <%= form.select :job_id, options_for_select([['All Jobs', 'all']] + @available_jobs.map { |job| [job.title, job.id] }, params[:job_id]),
              {}, { class: "w-full px-3 py-2 border border-stone-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500" } %>
        </div>
        <div>
          <%= form.select :organization_id, options_for_select([['All Organizations', 'all']] + @available_organizations.map { |org| [org.name, org.id] }, params[:organization_id]),
              {}, { class: "w-full px-3 py-2 border border-stone-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500" } %>
        </div>
        <div>
          <%= form.select :date_range, options_for_select([
                ['All Time', 'all'],
                ['Today', 'today'],
                ['This Week', 'week'],
                ['This Month', 'month']
              ], params[:date_range]), 
              {}, { class: "w-full px-3 py-2 border border-stone-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500" } %>
        </div>
      </div>
    <% end %>
  </div>

  <!-- Bulk Actions -->
  <%= form_with url: bulk_update_super_admin_admin_job_invitations_path, method: :post, local: true, id: "bulk-form" do |form| %>
    <div class="px-6 py-3 border-b border-stone-200 bg-stone-50">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
          <input type="checkbox" id="select-all" class="rounded border-stone-300 text-blue-600 focus:ring-blue-500">
          <label for="select-all" class="text-sm text-stone-700">Select All</label>
        </div>
        <div class="flex items-center space-x-2">
          <%= form.select :bulk_action, options_for_select([
                ['Mark as Pending', 'mark_pending'],
                ['Mark as Accepted', 'mark_accepted'],
                ['Mark as Ignored', 'mark_ignored'],
                ['Delete Selected', 'delete']
              ]), 
              { prompt: 'Bulk Actions' }, 
              { class: "px-3 py-2 border border-stone-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500" } %>
          <%= form.submit "Apply", class: "px-4 py-2 bg-blue-600 text-white rounded-md text-sm font-medium hover:bg-blue-700", 
              onclick: "return confirm('Are you sure you want to apply this action to selected invitations?')" %>
        </div>
      </div>
    </div>

    <!-- Table -->
    <div class="overflow-x-auto">
      <% if @job_invitations.any? %>
        <table class="min-w-full divide-y divide-stone-200">
          <thead class="bg-stone-50">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">
                <input type="checkbox" class="rounded border-stone-300 text-blue-600 focus:ring-blue-500">
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">Job & Organization</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">Talent</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">Status</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">Invited</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">Response</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">Actions</th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-stone-200">
            <% @job_invitations.each do |invitation| %>
              <tr class="hover:bg-stone-50">
                <td class="px-6 py-4 whitespace-nowrap">
                  <%= check_box_tag "job_invitation_ids[]", invitation.id, false, 
                      class: "invitation-checkbox rounded border-stone-300 text-blue-600 focus:ring-blue-500" %>
                </td>
                <td class="px-6 py-4">
                  <div class="text-sm font-medium text-stone-900"><%= invitation.job.title %></div>
                  <div class="text-sm text-stone-500"><%= invitation.job.organization.name %></div>
                </td>
                <td class="px-6 py-4">
                  <div class="text-sm font-medium text-stone-900"><%= invitation.user.name.full %></div>
                  <div class="text-sm text-stone-500"><%= invitation.user.email %></div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <% case invitation.status %>
                  <% when 'pending' %>
                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-amber-100 text-amber-800">Pending</span>
                  <% when 'accepted' %>
                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">Accepted</span>
                  <% when 'ignored' %>
                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">Ignored</span>
                  <% end %>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-stone-500">
                  <%= invitation.invited_at&.strftime('%b %d, %Y') %>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-stone-500">
                  <% if invitation.accepted_at %>
                    <%= invitation.accepted_at.strftime('%b %d, %Y') %>
                  <% elsif invitation.ignored_at %>
                    <%= invitation.ignored_at.strftime('%b %d, %Y') %>
                  <% else %>
                    -
                  <% end %>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                  <%= link_to "View", super_admin_admin_job_invitation_path(invitation), 
                      class: "text-blue-600 hover:text-blue-900" %>
                  <%= link_to "Edit", edit_super_admin_admin_job_invitation_path(invitation), 
                      class: "text-indigo-600 hover:text-indigo-900" %>
                  <%= link_to "Delete", super_admin_admin_job_invitation_path(invitation), 
                      method: :delete, 
                      confirm: "Are you sure?", 
                      class: "text-red-600 hover:text-red-900" %>
                </td>
              </tr>
            <% end %>
          </tbody>
        </table>
      <% else %>
        <div class="text-center py-12">
          <svg class="mx-auto h-12 w-12 text-stone-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2M4 13h2m13-8l-4 4m0 0l-4-4m4 4V3"></path>
          </svg>
          <h3 class="mt-2 text-sm font-medium text-stone-900">No job invitations found</h3>
          <p class="mt-1 text-sm text-stone-500">No job invitations match your current filters.</p>
        </div>
      <% end %>
    </div>
  <% end %>

  <!-- Pagination -->
  <% if @job_invitations.any? %>
    <div class="px-6 py-4 border-t border-stone-200 bg-stone-50">
      <%== pagy_nav(@pagy) if @pagy.pages > 1 %>
    </div>
  <% end %>
</div>

<script>
  // Bulk selection functionality
  document.addEventListener('DOMContentLoaded', function() {
    const selectAllCheckbox = document.getElementById('select-all');
    const invitationCheckboxes = document.querySelectorAll('.invitation-checkbox');
    
    selectAllCheckbox.addEventListener('change', function() {
      invitationCheckboxes.forEach(checkbox => {
        checkbox.checked = this.checked;
      });
    });
    
    invitationCheckboxes.forEach(checkbox => {
      checkbox.addEventListener('change', function() {
        const allChecked = Array.from(invitationCheckboxes).every(cb => cb.checked);
        const noneChecked = Array.from(invitationCheckboxes).every(cb => !cb.checked);
        
        selectAllCheckbox.checked = allChecked;
        selectAllCheckbox.indeterminate = !allChecked && !noneChecked;
      });
    });
  });
</script>
