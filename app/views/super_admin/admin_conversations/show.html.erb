<% content_for :title, "Conversation ##{@conversation.id}" %>

<div class="max-w-7xl mx-auto">
  <!-- Header -->
  <div class="mb-8">
    <div class="flex items-center justify-between">
      <div>
        <nav class="flex" aria-label="Breadcrumb">
          <ol class="flex items-center space-x-4">
            <li>
              <%= link_to "Conversations", super_admin_admin_conversations_path, class: "text-stone-500 hover:text-stone-700" %>
            </li>
            <li>
              <div class="flex items-center">
                <svg class="flex-shrink-0 h-5 w-5 text-stone-400" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                </svg>
                <span class="ml-4 text-sm font-medium text-stone-900">Conversation #<%= @conversation.id %></span>
              </div>
            </li>
          </ol>
        </nav>
        <h1 class="mt-2 text-3xl font-bold text-stone-900">Conversation #<%= @conversation.id %></h1>
        <p class="mt-1 text-stone-600">
          <%= @conversation.users.map(&:name).join(' & ') %>
        </p>
      </div>
    </div>
  </div>

  <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
    <!-- Main Content -->
    <div class="lg:col-span-2 space-y-8">
      <!-- Conversation Details -->
      <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-stone-200">
          <h2 class="text-lg font-medium text-stone-900">Conversation Details</h2>
        </div>
        <div class="px-6 py-4">
          <dl class="grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2">
            <div>
              <dt class="text-sm font-medium text-stone-500">Conversation ID</dt>
              <dd class="mt-1 text-sm text-stone-900"><%= @conversation.id %></dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-stone-500">Participants</dt>
              <dd class="mt-1 text-sm text-stone-900"><%= @stats[:participants_count] %> users</dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-stone-500">Total Messages</dt>
              <dd class="mt-1 text-sm text-stone-900"><%= @stats[:total_messages] %></dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-stone-500">Unread Messages</dt>
              <dd class="mt-1 text-sm text-stone-900"><%= @stats[:unread_messages] %></dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-stone-500">Created At</dt>
              <dd class="mt-1 text-sm text-stone-900"><%= @conversation.created_at.strftime('%B %d, %Y at %I:%M %p') %></dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-stone-500">Last Activity</dt>
              <dd class="mt-1 text-sm text-stone-900">
                <%= @stats[:last_activity].strftime('%B %d, %Y at %I:%M %p') %>
                <span class="text-stone-500">(<%= time_ago_in_words(@stats[:last_activity]) %> ago)</span>
              </dd>
            </div>
          </dl>
        </div>
      </div>

      <!-- Related Job -->
      <% if @conversation.job %>
        <div class="bg-white shadow rounded-lg">
          <div class="px-6 py-4 border-b border-stone-200">
            <h2 class="text-lg font-medium text-stone-900">Related Job</h2>
          </div>
          <div class="px-6 py-4">
            <div class="flex items-start">
              <div class="flex-1">
                <h3 class="text-lg font-medium text-stone-900"><%= @conversation.job.title %></h3>
                <p class="text-sm text-stone-600 mt-1"><%= @conversation.job.organization.name %></p>
                <div class="mt-2">
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <%= @conversation.job.status == 'published' ? 'bg-green-100 text-green-800' : @conversation.job.status == 'draft' ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800' %>">
                    <%= @conversation.job.status.humanize %>
                  </span>
                </div>
              </div>
              <div class="ml-4">
                <%= link_to "View Job", super_admin_admin_job_path(@conversation.job), 
                    class: "text-blue-600 hover:text-blue-900 text-sm" %>
              </div>
            </div>
          </div>
        </div>
      <% end %>

      <!-- Recent Messages -->
      <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-stone-200">
          <h2 class="text-lg font-medium text-stone-900">Recent Messages</h2>
          <p class="text-sm text-stone-500">Last 20 messages (newest first)</p>
        </div>
        <div class="overflow-hidden">
          <% if @recent_messages.any? %>
            <div class="divide-y divide-stone-200">
              <% @recent_messages.each do |message| %>
                <div class="px-6 py-4">
                  <div class="flex items-start space-x-3">
                    <div class="flex-shrink-0">
                      <% if message.user.avatar.attached? %>
                        <%= image_tag message.user.avatar, class: "h-8 w-8 rounded-full object-cover" %>
                      <% else %>
                        <div class="h-8 w-8 rounded-full bg-stone-300 flex items-center justify-center">
                          <span class="text-xs font-medium text-stone-700"><%= message.user.initials %></span>
                        </div>
                      <% end %>
                    </div>
                    <div class="flex-1 min-w-0">
                      <div class="flex items-center justify-between">
                        <p class="text-sm font-medium text-stone-900"><%= message.user.name %></p>
                        <div class="flex items-center space-x-2">
                          <% unless message.read_at %>
                            <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                              Unread
                            </span>
                          <% end %>
                          <p class="text-sm text-stone-500"><%= time_ago_in_words(message.created_at) %> ago</p>
                        </div>
                      </div>
                      <div class="mt-1">
                        <p class="text-sm text-stone-700"><%= simple_format(message.body) %></p>
                      </div>
                    </div>
                  </div>
                </div>
              <% end %>
            </div>
          <% else %>
            <div class="px-6 py-8 text-center">
              <p class="text-stone-500 italic">No messages in this conversation yet</p>
            </div>
          <% end %>
        </div>
      </div>
    </div>

    <!-- Sidebar -->
    <div class="space-y-8">
      <!-- Participants -->
      <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-stone-200">
          <h2 class="text-lg font-medium text-stone-900">Participants</h2>
        </div>
        <div class="px-6 py-4 space-y-4">
          <% @participants.each do |participant| %>
            <div class="flex items-center justify-between">
              <div class="flex items-center">
                <div class="flex-shrink-0 h-10 w-10">
                  <% if participant.user.avatar.attached? %>
                    <%= image_tag participant.user.avatar, class: "h-10 w-10 rounded-full object-cover" %>
                  <% else %>
                    <div class="h-10 w-10 rounded-full bg-stone-300 flex items-center justify-center">
                      <span class="text-sm font-medium text-stone-700"><%= participant.user.initials %></span>
                    </div>
                  <% end %>
                </div>
                <div class="ml-3">
                  <div class="text-sm font-medium text-stone-900"><%= participant.user.name %></div>
                  <div class="text-sm text-stone-500"><%= participant.user.email %></div>
                  <div class="flex items-center space-x-2 mt-1">
                    <% if participant.archived? %>
                      <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-red-100 text-red-800">
                        Archived
                      </span>
                    <% end %>
                    <% if participant.bookmarked? %>
                      <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-yellow-100 text-yellow-800">
                        Bookmarked
                      </span>
                    <% end %>
                  </div>
                </div>
              </div>
              <div>
                <%= link_to "View Profile", super_admin_admin_user_path(participant.user), 
                    class: "text-blue-600 hover:text-blue-900 text-sm" %>
              </div>
            </div>
          <% end %>
        </div>
      </div>

      <!-- Activity Stats -->
      <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-stone-200">
          <h2 class="text-lg font-medium text-stone-900">Activity Statistics</h2>
        </div>
        <div class="px-6 py-4">
          <dl class="space-y-4">
            <div>
              <dt class="text-sm font-medium text-stone-500">Total Messages</dt>
              <dd class="mt-1 text-2xl font-semibold text-stone-900"><%= @stats[:total_messages] %></dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-stone-500">Unread Messages</dt>
              <dd class="mt-1 text-2xl font-semibold text-stone-900"><%= @stats[:unread_messages] %></dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-stone-500">Participants</dt>
              <dd class="mt-1 text-2xl font-semibold text-stone-900"><%= @stats[:participants_count] %></dd>
            </div>
          </dl>
        </div>
      </div>
    </div>
  </div>
</div>
