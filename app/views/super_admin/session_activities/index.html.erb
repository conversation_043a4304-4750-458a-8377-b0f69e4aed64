<div class="space-y-6">
  <!-- Header -->
  <div class="flex justify-between items-center">
    <div>
      <h1 class="text-2xl font-bold text-stone-900">Session Activities</h1>
      <p class="text-stone-600 mt-1">Monitor admin session activities and track user behavior</p>
    </div>
  </div>

  <!-- Filters -->
  <div class="bg-white rounded-lg shadow-sm border border-stone-200 p-6">
    <%= form_with url: super_admin_session_activities_path, method: :get, local: true, class: "space-y-4" do |form| %>
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <!-- Activity Type Filter -->
        <div>
          <%= form.label :activity_type, "Activity Type", class: "block text-sm font-medium text-stone-700 mb-1" %>
          <%= form.select :activity_type, 
                          options_for_select([['All Types', 'all']] + @available_activity_types, params[:activity_type]),
                          {}, 
                          { class: "w-full rounded-md border-stone-300 shadow-sm focus:border-blue-500 focus:ring-blue-500" } %>
        </div>

        <!-- User Filter -->
        <div>
          <%= form.label :user_filter, "User", class: "block text-sm font-medium text-stone-700 mb-1" %>
          <%= form.select :user_filter,
                          options_for_select([['All Users', 'all']] + @available_users.map { |user| [user.email, user.id] }, params[:user_filter]),
                          {},
                          { class: "w-full rounded-md border-stone-300 shadow-sm focus:border-blue-500 focus:ring-blue-500" } %>
        </div>

        <!-- IP Address Filter -->
        <div>
          <%= form.label :ip_filter, "IP Address", class: "block text-sm font-medium text-stone-700 mb-1" %>
          <%= form.select :ip_filter, 
                          options_for_select([['All IPs', '']] + @available_ips.map { |ip| [ip, ip] }, params[:ip_filter]),
                          {}, 
                          { class: "w-full rounded-md border-stone-300 shadow-sm focus:border-blue-500 focus:ring-blue-500" } %>
        </div>

        <!-- Date Range -->
        <div>
          <%= form.label :date_from, "Date From", class: "block text-sm font-medium text-stone-700 mb-1" %>
          <%= form.date_field :date_from, 
                              value: params[:date_from],
                              class: "w-full rounded-md border-stone-300 shadow-sm focus:border-blue-500 focus:ring-blue-500" %>
        </div>
      </div>

      <div class="flex justify-between items-center">
        <div class="flex space-x-2">
          <%= form.submit "Apply Filters", class: "bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500" %>
          <%= link_to "Clear Filters", super_admin_session_activities_path, class: "bg-stone-200 text-stone-700 px-4 py-2 rounded-md hover:bg-stone-300 focus:outline-none focus:ring-2 focus:ring-stone-500" %>
        </div>
      </div>
    <% end %>
  </div>

  <!-- Activities Table -->
  <div class="bg-white rounded-lg shadow-sm border border-stone-200 overflow-hidden">
    <div class="px-6 py-4 border-b border-stone-200">
      <h2 class="text-lg font-semibold text-stone-900">
        Session Activities 
        <span class="text-sm font-normal text-stone-500">(<%= @pagy.count %> total)</span>
      </h2>
    </div>

    <div class="overflow-x-auto">
      <table class="min-w-full divide-y divide-stone-200">
        <thead class="bg-stone-50">
          <tr>
            <th class="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">User</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">Activity</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">Controller/Action</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">IP Address</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">Time</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">Actions</th>
          </tr>
        </thead>
        <tbody class="bg-white divide-y divide-stone-200">
          <% if @session_activities.any? %>
            <% @session_activities.each do |activity| %>
              <tr class="hover:bg-stone-50">
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="flex items-center">
                    <div>
                      <div class="text-sm font-medium text-stone-900"><%= activity.user_name %></div>
                      <div class="text-sm text-stone-500"><%= activity.user_email %></div>
                    </div>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                    <%= case activity.activity_type
                        when 'login' then 'bg-green-100 text-green-800'
                        when 'logout' then 'bg-red-100 text-red-800'
                        when 'admin_action' then 'bg-blue-100 text-blue-800'
                        when 'security_event' then 'bg-red-100 text-red-800'
                        else 'bg-stone-100 text-stone-800'
                        end %>">
                    <%= activity.activity_type.humanize %>
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-stone-900">
                  <%= "#{activity.controller}##{activity.action}" if activity.controller && activity.action %>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-stone-500">
                  <%= activity.ip_address %>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-stone-500">
                  <%= time_ago_in_words(activity.created_at) %> ago
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <%= link_to "View", super_admin_session_activity_path(activity),
                      class: "text-blue-600 hover:text-blue-900" %>
                </td>
              </tr>
            <% end %>
          <% else %>
            <tr>
              <td colspan="6" class="px-6 py-12 text-sm text-center text-stone-900">
                <div class="text-stone-500">
                  <svg class="w-12 h-12 mx-auto mb-4 text-stone-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                  </svg>
                  <p class="text-sm font-medium">No session activities found</p>
                  <p class="text-xs">Try adjusting your search or filter criteria</p>
                </div>
              </td>
            </tr>
          <% end %>
        </tbody>
      </table>
    </div>

    <!-- Pagination -->
    <div class="px-6 py-4 border-t border-stone-200">
      <%== pagy_nav(@pagy) if @pagy.pages > 1 %>
    </div>
  </div>
</div>
