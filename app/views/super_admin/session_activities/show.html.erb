<div class="space-y-6">
  <!-- Header -->
  <div class="flex justify-between items-center">
    <div>
      <h1 class="text-2xl font-bold text-stone-900">Session Activity Details</h1>
      <p class="text-stone-600 mt-1">Detailed information about this session activity</p>
    </div>
    <div class="flex space-x-3">
      <%= link_to "← Back to Activities", super_admin_session_activities_path, 
          class: "bg-stone-200 text-stone-700 px-4 py-2 rounded-md hover:bg-stone-300 focus:outline-none focus:ring-2 focus:ring-stone-500" %>
    </div>
  </div>

  <!-- Activity Overview -->
  <div class="bg-white rounded-lg shadow-sm border border-stone-200 p-6">
    <h2 class="text-lg font-semibold text-stone-900 mb-4">Activity Overview</h2>
    
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
      <div>
        <h3 class="text-sm font-medium text-stone-500 mb-2">Activity Type</h3>
        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-sm font-medium 
          <%= case @session_activity.activity_type
              when 'login' then 'bg-green-100 text-green-800'
              when 'logout' then 'bg-red-100 text-red-800'
              when 'admin_action' then 'bg-blue-100 text-blue-800'
              when 'security_event' then 'bg-red-100 text-red-800'
              else 'bg-stone-100 text-stone-800'
              end %>">
          <%= @session_activity.activity_type.humanize %>
        </span>
      </div>

      <div>
        <h3 class="text-sm font-medium text-stone-500 mb-2">Timestamp</h3>
        <p class="text-stone-900">
          <%= @session_activity.created_at.strftime("%B %d, %Y at %I:%M %p") %>
          <span class="text-stone-500">(<%= time_ago_in_words(@session_activity.created_at) %> ago)</span>
        </p>
      </div>

      <div>
        <h3 class="text-sm font-medium text-stone-500 mb-2">Controller/Action</h3>
        <p class="text-stone-900">
          <%= "#{@session_activity.controller}##{@session_activity.action}" if @session_activity.controller && @session_activity.action %>
        </p>
      </div>

      <div>
        <h3 class="text-sm font-medium text-stone-500 mb-2">Request Path</h3>
        <p class="text-stone-900 font-mono text-sm">
          <%= @session_activity.request_path %>
        </p>
      </div>

      <div>
        <h3 class="text-sm font-medium text-stone-500 mb-2">IP Address</h3>
        <p class="text-stone-900 font-mono">
          <%= @session_activity.ip_address %>
        </p>
      </div>

      <div>
        <h3 class="text-sm font-medium text-stone-500 mb-2">User Agent</h3>
        <p class="text-stone-900 text-sm break-all">
          <%= @session_activity.user_agent %>
        </p>
      </div>
    </div>

    <div class="mt-6">
      <h3 class="text-sm font-medium text-stone-500 mb-2">Description</h3>
      <p class="text-stone-900">
        <%= @session_activity.description %>
      </p>
    </div>

    <% if @session_activity.suspicious? %>
      <div class="mt-6 p-4 bg-red-50 border border-red-200 rounded-md">
        <div class="flex">
          <div class="flex-shrink-0">
            <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
            </svg>
          </div>
          <div class="ml-3">
            <h3 class="text-sm font-medium text-red-800">Suspicious Activity Detected</h3>
            <p class="text-sm text-red-700 mt-1">This activity has been flagged as potentially suspicious.</p>
          </div>
        </div>
      </div>
    <% end %>
  </div>

  <!-- User Information -->
  <div class="bg-white rounded-lg shadow-sm border border-stone-200 p-6">
    <h2 class="text-lg font-semibold text-stone-900 mb-4">User Information</h2>
    
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
      <div>
        <h3 class="text-sm font-medium text-stone-500 mb-2">Name</h3>
        <p class="text-stone-900"><%= @user.full_name %></p>
      </div>

      <div>
        <h3 class="text-sm font-medium text-stone-500 mb-2">Email</h3>
        <p class="text-stone-900"><%= @user.email %></p>
      </div>

      <div>
        <h3 class="text-sm font-medium text-stone-500 mb-2">User ID</h3>
        <p class="text-stone-900 font-mono"><%= @user.id %></p>
      </div>

      <div>
        <h3 class="text-sm font-medium text-stone-500 mb-2">Account Created</h3>
        <p class="text-stone-900"><%= @user.created_at.strftime("%B %d, %Y") %></p>
      </div>
    </div>

    <div class="mt-4">
      <%= link_to "View User Details", super_admin_admin_user_path(@user), 
          class: "text-blue-600 hover:text-blue-900 text-sm font-medium" %>
    </div>
  </div>

  <!-- Session Information -->
  <div class="bg-white rounded-lg shadow-sm border border-stone-200 p-6">
    <h2 class="text-lg font-semibold text-stone-900 mb-4">Session Information</h2>
    
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
      <div>
        <h3 class="text-sm font-medium text-stone-500 mb-2">Session ID</h3>
        <p class="text-stone-900 font-mono text-sm"><%= @session.id %></p>
      </div>

      <div>
        <h3 class="text-sm font-medium text-stone-500 mb-2">Session Created</h3>
        <p class="text-stone-900"><%= @session.created_at.strftime("%B %d, %Y at %I:%M %p") %></p>
      </div>

      <div>
        <h3 class="text-sm font-medium text-stone-500 mb-2">Last Activity</h3>
        <p class="text-stone-900">
          <%= @session.last_activity_at&.strftime("%B %d, %Y at %I:%M %p") || 'Never' %>
        </p>
      </div>

      <div>
        <h3 class="text-sm font-medium text-stone-500 mb-2">Security Warnings</h3>
        <p class="text-stone-900"><%= @session.security_warnings_count %></p>
      </div>
    </div>

    <div class="mt-4">
      <%= link_to "View Session Details", super_admin_admin_session_path(@session), 
          class: "text-blue-600 hover:text-blue-900 text-sm font-medium" %>
    </div>
  </div>

  <!-- Metadata -->
  <% if @session_activity.metadata.present? %>
    <div class="bg-white rounded-lg shadow-sm border border-stone-200 p-6">
      <h2 class="text-lg font-semibold text-stone-900 mb-4">Additional Metadata</h2>
      
      <div class="bg-stone-50 rounded-md p-4">
        <pre class="text-sm text-stone-700 whitespace-pre-wrap"><%= JSON.pretty_generate(@session_activity.metadata) %></pre>
      </div>
    </div>
  <% end %>
</div>
