<% content_for :title, "Badge Assignments for #{@user.name}" %>

<div class="container px-4 mx-auto my-8">
  <div class="flex items-center justify-between mb-4">
    <h1 class="text-2xl font-bold">Badge Assignments for <%= @user.name %></h1>
    <%= link_to 'Assign New Badge', new_super_admin_user_badge_assignment_path(@user), class: "btn-primary" %>
  </div>

  <div class="p-8 bg-white rounded-lg shadow">
    <% if @badge_assignments.any? %>
      <table class="w-full">
        <thead>
          <tr class="border-b">
            <th class="p-4 text-left">Badge</th>
            <th class="p-4 text-left">Assigned At</th>
            <th class="p-4 text-left">Expires At</th>
            <th class="p-4 text-left">Assigned By</th>
            <th class="p-4 text-left">Actions</th>
          </tr>
        </thead>
        <tbody>
          <% @badge_assignments.each do |assignment| %>
            <tr class="border-b">
              <td class="p-4">
                <span class="inline-flex items-center px-3 py-1 text-sm font-medium rounded-md" style="background-color: <%= assignment.badge_type.background_color %>; color: <%= assignment.badge_type.text_color %>;">
                  <%= heroicon(assignment.badge_type.icon, class: "w-4 h-4 mr-1.5") %>
                  <%= assignment.badge_type.name %>
                </span>
              </td>
              <td class="p-4"><%= time_ago_in_words(assignment.assigned_at) %> ago</td>
              <td class="p-4"><%= assignment.expires_at ? time_ago_in_words(assignment.expires_at) : 'Never' %></td>
              <td class="p-4"><%= assignment.admin.name %></td>
              <td class="p-4">
                <%= link_to 'Revoke', super_admin_badge_assignment_path(assignment), data: { "turbo-method": :delete, "turbo-confirm": "Are you sure you want to revoke this badge?" }, class: "text-red-600 hover:text-red-800" %>
              </td>
            </tr>
          <% end %>
        </tbody>
      </table>
    <% else %>
      <p class="text-center text-gray-500">This user has no badges assigned.</p>
    <% end %>
  </div>
</div>