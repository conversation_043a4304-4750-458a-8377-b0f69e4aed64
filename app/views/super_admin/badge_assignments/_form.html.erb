<%= form_with(model: [:super_admin, @user, badge_assignment], class: "space-y-6") do |form| %>
  <% if badge_assignment.errors.any? %>
    <div class="p-4 text-red-700 bg-red-100 border-l-4 border-red-500">
      <h2 class="font-bold"><%= pluralize(badge_assignment.errors.count, "error") %> prohibited this badge assignment from being saved:</h2>

      <ul class="mt-2 ml-4 list-disc">
        <% badge_assignment.errors.each do |error| %>
          <li><%= error.full_message %></li>
        <% end %>
      </ul>
    </div>
  <% end %>

  <%= form.hidden_field :user_id %>

  <div class="field">
    <%= form.label :badge_type_id, class: "label" %>
    <%= form.collection_select :badge_type_id, @available_badges, :id, :name, { prompt: "Select a badge" }, { class: "input" } %>
  </div>

  <div class="field">
    <%= form.label :expires_at, class: "label" %>
    <%= form.datetime_select :expires_at, { include_blank: true, start_year: Date.current.year }, { class: "input" } %>
    <p class="mt-1 text-sm text-gray-500">Leave blank for no expiration.</p>
  </div>

  <div class="field">
    <%= form.label :notes, class: "label" %>
    <%= form.text_area :notes, rows: 4, class: "input" %>
  </div>

  <div class="actions">
    <%= form.submit "Assign Badge", class: "btn-primary" %>
  </div>
<% end %>