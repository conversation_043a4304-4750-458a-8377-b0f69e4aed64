<% content_for :title, "User Management" %>

<div class="bg-white rounded-lg shadow" data-controller="impersonation">
  <div class="px-4 py-5 sm:p-6">
    <div class="sm:flex sm:items-center">
      <div class="sm:flex-auto">
        <h1 class="text-xl font-semibold text-stone-900">User Management</h1>
        <p class="mt-2 text-sm text-stone-700">
          Search, filter, and manage all users in the system
        </p>
      </div>
      <div class="mt-4 sm:mt-0 sm:ml-16 sm:flex-none">
        <%= link_to super_admin_users_path(format: :csv, **request.query_parameters),
            class: "inline-flex items-center justify-center rounded-md border border-transparent bg-stone-800 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-stone-700 focus:outline-none focus:ring-2 focus:ring-stone-500 focus:ring-offset-2" do %>
          <svg class="w-4 h-4 mr-2 -ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
          </svg>
          Export CSV
        <% end %>
      </div>
    </div>

    <!-- Search and Filters -->
    <div class="pt-6 mt-8 border-t border-stone-200">
      <%= form_with url: super_admin_users_path, method: :get, local: true, class: "space-y-4" do |form| %>
        <!-- Search and Action Buttons Row -->
        <div class="flex items-end gap-4">
          <!-- Search Field (takes most space) -->
          <div class="flex-1">
            <%= form.label :q, "Search", class: "block text-sm font-medium text-stone-700" %>
            <%= form.text_field :q, value: @q, placeholder: "Search by name, email, or ID...",
                class: "mt-1 block w-full rounded-md border-stone-300 shadow-sm focus:border-stone-500 focus:ring-stone-500 sm:text-sm" %>
          </div>

          <!-- Filter and Clear Buttons -->
          <div class="flex items-center space-x-4">
            <%= form.submit "Filter", class: "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-stone-800 hover:bg-stone-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-stone-500" %>
            <% if @q.present? || (@role_filter.present? && @role_filter != 'all') || (@status_filter.present? && @status_filter != 'all') %>
              <%= link_to "Clear", super_admin_users_path, class: "text-stone-600 hover:text-stone-900" %>
            <% end %>
          </div>
        </div>

        <!-- Filter Dropdowns Row -->
        <div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
          <!-- Role Filter -->
          <div>
            <%= form.label :role_filter, "Role", class: "block text-sm font-medium text-stone-700" %>
            <%= form.select :role_filter,
                options_for_select([
                  ['All Roles', 'all'],
                  ['No Role', 'no_role']
                ] + @available_roles.map { |role| [role.titleize, role] }, @role_filter),
                {},
                { class: "mt-1 block w-full rounded-md border-stone-300 shadow-sm focus:border-stone-500 focus:ring-stone-500 sm:text-sm" } %>
          </div>

          <!-- Status Filter -->
          <div>
            <%= form.label :status_filter, "Status", class: "block text-sm font-medium text-stone-700" %>
            <%= form.select :status_filter,
                options_for_select([
                  ['All Statuses', 'all'],
                  ['Verified', 'verified'],
                  ['Unverified', 'unverified'],
                  ['Onboarding Complete', 'onboarding_complete'],
                  ['Onboarding Incomplete', 'onboarding_incomplete']
                ], @status_filter),
                {},
                { class: "mt-1 block w-full rounded-md border-stone-300 shadow-sm focus:border-stone-500 focus:ring-stone-500 sm:text-sm" } %>
          </div>
        </div>
      <% end %>
    </div>

    <!-- Table Section -->
    <div class="pt-6 mt-8 border-t border-stone-100">
      <!-- Users Table -->
      <div class="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
        <table class="min-w-full divide-y divide-stone-300">
          <thead class="bg-stone-50">
            <tr>
              <th scope="col" class="px-6 py-3 text-xs font-medium tracking-wider text-left uppercase text-stone-500">
                User
              </th>
              <th scope="col" class="px-6 py-3 text-xs font-medium tracking-wider text-left uppercase text-stone-500">
                Email
              </th>
              <th scope="col" class="px-6 py-3 text-xs font-medium tracking-wider text-left uppercase text-stone-500">
                Roles
              </th>
              <th scope="col" class="px-6 py-3 text-xs font-medium tracking-wider text-left uppercase text-stone-500">
                Organizations
              </th>
              <th scope="col" class="px-6 py-3 text-xs font-medium tracking-wider text-left uppercase text-stone-500">
                Status
              </th>
              <th scope="col" class="px-6 py-3 text-xs font-medium tracking-wider text-left uppercase text-stone-500">
                Joined
              </th>
              <th scope="col" class="relative px-6 py-3">
                <span class="sr-only">Actions</span>
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-stone-200">
            <% @users.each do |user| %>
              <tr class="hover:bg-stone-50">
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="flex items-center">
                    <div class="flex-shrink-0 w-10 h-10">
                      <% if user.avatar.attached? && user.avatar.representable? %>
                        <%= image_tag url_for(user.avatar), class: "h-10 w-10 rounded-full object-cover", alt: user.name %>
                      <% else %>
                        <div class="flex items-center justify-center w-10 h-10 rounded-full bg-stone-300">
                          <span class="text-sm font-medium text-stone-700"><%= user.initials %></span>
                        </div>
                      <% end %>
                    </div>
                    <div class="ml-4">
                      <div class="text-sm font-medium text-stone-900">
                        <%= user.name.presence || "No name" %>
                      </div>
                      <div class="text-sm text-stone-500">ID: <%= user.id %></div>
                    </div>
                  </div>
                </td>
                <td class="px-6 py-4 text-sm whitespace-nowrap text-stone-900">
                  <%= user.email %>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <% if user.roles.any? %>
                    <% user.roles.each do |role| %>
                      <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-stone-100 text-stone-800">
                        <%= role.name %>
                      </span>
                    <% end %>
                  <% else %>
                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-stone-100 text-stone-800">
                      user
                    </span>
                  <% end %>
                </td>
                <td class="px-6 py-4 text-sm whitespace-nowrap text-stone-900">
                  <% if user.organizations.any? %>
                    <%= user.organizations.pluck(:name).join(", ") %>
                  <% else %>
                    <span class="text-stone-500">None</span>
                  <% end %>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <% if user.verified? %>
                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-stone-100 text-stone-800">
                      Verified
                    </span>
                  <% else %>
                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-stone-100 text-stone-800">
                      Unverified
                    </span>
                  <% end %>
                </td>
                <td class="px-6 py-4 text-sm whitespace-nowrap text-stone-900">
                  <%= user.created_at.strftime("%b %d, %Y") %>
                </td>
                <td class="px-6 py-4 text-sm font-medium text-right whitespace-nowrap">
                  <% unless user.superadmin? %>
                    <button type="button"
                            data-action="click->impersonation#showConfirmation"
                            data-user-id="<%= user.id %>"
                            data-user-name="<%= user.name %>"
                            data-user-email="<%= user.email %>"
                            class="text-stone-600 hover:text-stone-900">
                      Impersonate
                    </button>
                  <% else %>
                    <span class="text-stone-400">Super Admin</span>
                  <% end %>
                </td>
              </tr>
            <% end %>
          </tbody>
        </table>

        <!-- Table Footer with Results and Pagination -->
        <div class="px-6 py-2 border-t bg-stone-50 border-stone-300">
        <div class="flex items-center justify-between">
          <div class="flex justify-between flex-1 sm:hidden">
            <!-- Mobile pagination -->
            <% if @pagy.pages > 1 %>
              <% if @pagy.prev %>
                <%= link_to "Previous", pagy_url_for(@pagy, @pagy.prev),
                    class: "relative inline-flex items-center px-4 py-2 border border-stone-300 text-xs font-medium rounded-md text-stone-500 bg-white hover:bg-stone-50 uppercase tracking-wider" %>
              <% else %>
                <span class="relative inline-flex items-center px-4 py-2 text-xs font-medium tracking-wider uppercase border rounded-md cursor-not-allowed border-stone-300 text-stone-400 bg-stone-50">
                  Previous
                </span>
              <% end %>
              <% if @pagy.next %>
                <%= link_to "Next", pagy_url_for(@pagy, @pagy.next),
                    class: "ml-3 relative inline-flex items-center px-4 py-2 border border-stone-300 text-xs font-medium rounded-md text-stone-500 bg-white hover:bg-stone-50 uppercase tracking-wider" %>
              <% else %>
                <span class="relative inline-flex items-center px-4 py-2 ml-3 text-xs font-medium tracking-wider uppercase border rounded-md cursor-not-allowed border-stone-300 text-stone-400 bg-stone-50">
                  Next
                </span>
              <% end %>
            <% end %>
          </div>
          <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
            <div>
              <p class="text-xs font-medium tracking-wider uppercase text-stone-500">
                Showing <span class="font-medium"><%= @pagy.from %></span> to <span class="font-medium"><%= @pagy.to %></span> of <span class="font-medium"><%= @pagy.count %></span> results
              </p>
            </div>
            <div>
              <% if @pagy.pages > 1 %>
                <nav class="relative z-0 inline-flex -space-x-px rounded-md shadow-sm" aria-label="Pagination">
                  <!-- Previous button -->
                  <% if @pagy.prev %>
                    <%= link_to pagy_url_for(@pagy, @pagy.prev),
                        class: "relative inline-flex items-center px-2 py-2 rounded-l-md border border-stone-300 bg-white text-xs font-medium text-stone-500 hover:bg-stone-50 uppercase tracking-wider" do %>
                      <span class="sr-only">Previous</span>
                      <svg class="w-5 h-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                        <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
                      </svg>
                    <% end %>
                  <% else %>
                    <span class="relative inline-flex items-center px-2 py-2 text-xs font-medium tracking-wider uppercase border cursor-not-allowed rounded-l-md border-stone-300 bg-stone-50 text-stone-300">
                      <span class="sr-only">Previous</span>
                      <svg class="w-5 h-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                        <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
                      </svg>
                    </span>
                  <% end %>

                  <!-- Page numbers -->
                  <% @pagy.series.each do |item| %>
                    <% if item.is_a?(Integer) %>
                      <% if item == @pagy.page %>
                        <span aria-current="page" class="relative z-10 inline-flex items-center px-4 py-2 text-xs font-medium tracking-wider uppercase border bg-stone-50 border-stone-500 text-stone-600">
                          <%= item %>
                        </span>
                      <% else %>
                        <%= link_to item, pagy_url_for(@pagy, item),
                            class: "bg-white border-stone-300 text-stone-500 hover:bg-stone-50 relative inline-flex items-center px-4 py-2 border text-xs font-medium uppercase tracking-wider" %>
                      <% end %>
                    <% elsif item == :gap %>
                      <span class="relative inline-flex items-center px-4 py-2 text-xs font-medium tracking-wider uppercase bg-white border border-stone-300 text-stone-500">
                        ...
                      </span>
                    <% end %>
                  <% end %>

                  <!-- Next button -->
                  <% if @pagy.next %>
                    <%= link_to pagy_url_for(@pagy, @pagy.next),
                        class: "relative inline-flex items-center px-2 py-2 rounded-r-md border border-stone-300 bg-white text-xs font-medium text-stone-500 hover:bg-stone-50 uppercase tracking-wider" do %>
                      <span class="sr-only">Next</span>
                      <svg class="w-5 h-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                        <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                      </svg>
                    <% end %>
                  <% else %>
                    <span class="relative inline-flex items-center px-2 py-2 text-xs font-medium tracking-wider uppercase border cursor-not-allowed rounded-r-md border-stone-300 bg-stone-50 text-stone-300">
                      <span class="sr-only">Next</span>
                      <svg class="w-5 h-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                        <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                      </svg>
                    </span>
                  <% end %>
                </nav>
              <% end %>
            </div>
          </div>
        </div>
      </div>

    </div>
  </div>
</div>

<!-- Impersonation Confirmation Modal -->
<%= render "shared/impersonation_confirmation_modal" %>
