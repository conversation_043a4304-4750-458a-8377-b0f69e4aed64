<% content_for :title, "Audit Log Details" %>

<div class="bg-white shadow rounded-lg">
  <div class="px-4 py-5 sm:p-6">
    <div class="sm:flex sm:items-center sm:justify-between">
      <div class="sm:flex-auto">
        <h1 class="text-xl font-semibold text-stone-900">Audit Log Details</h1>
        <p class="mt-2 text-sm text-stone-700">
          Detailed information about this administrative action.
        </p>
      </div>
      <div class="mt-4 sm:mt-0 sm:ml-16 sm:flex-none">
        <%= link_to "← Back to Audit Logs", super_admin_admin_audit_logs_path, 
            class: "inline-flex items-center px-4 py-2 border border-stone-300 text-sm font-medium rounded-md text-stone-700 bg-white hover:bg-stone-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-stone-500" %>
      </div>
    </div>

    <div class="mt-8">
      <dl class="grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2">
        <!-- Basic Information -->
        <div class="sm:col-span-2">
          <h3 class="text-lg font-medium text-stone-900 mb-4">Basic Information</h3>
        </div>

        <div>
          <dt class="text-sm font-medium text-stone-500">Date & Time</dt>
          <dd class="mt-1 text-sm text-stone-900">
            <%= @audit_log.created_at.strftime('%B %d, %Y at %I:%M %p %Z') %>
          </dd>
        </div>

        <div>
          <dt class="text-sm font-medium text-stone-500">Action</dt>
          <dd class="mt-1">
            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-stone-100 text-stone-800">
              <%= @audit_log.action.humanize %>
            </span>
          </dd>
        </div>

        <div>
          <dt class="text-sm font-medium text-stone-500">Admin User</dt>
          <dd class="mt-1 text-sm text-stone-900">
            <div class="font-medium"><%= @audit_log.admin_name %></div>
            <div class="text-stone-500"><%= @audit_log.admin_email %></div>
          </dd>
        </div>

        <div>
          <dt class="text-sm font-medium text-stone-500">Controller</dt>
          <dd class="mt-1 text-sm text-stone-900">
            <code class="bg-stone-100 px-2 py-1 rounded text-xs"><%= @audit_log.controller %></code>
          </dd>
        </div>

        <!-- Resource Information -->
        <% if @audit_log.resource %>
          <div class="sm:col-span-2 mt-6">
            <h3 class="text-lg font-medium text-stone-900 mb-4">Resource Information</h3>
          </div>

          <div>
            <dt class="text-sm font-medium text-stone-500">Resource Type</dt>
            <dd class="mt-1 text-sm text-stone-900"><%= @audit_log.resource_type %></dd>
          </div>

          <div>
            <dt class="text-sm font-medium text-stone-500">Resource ID</dt>
            <dd class="mt-1 text-sm text-stone-900"><%= @audit_log.resource_id %></dd>
          </div>

          <div class="sm:col-span-2">
            <dt class="text-sm font-medium text-stone-500">Resource Identifier</dt>
            <dd class="mt-1 text-sm text-stone-900"><%= @audit_log.resource_identifier %></dd>
          </div>
        <% end %>

        <!-- Description -->
        <div class="sm:col-span-2 mt-6">
          <dt class="text-sm font-medium text-stone-500">Description</dt>
          <dd class="mt-1 text-sm text-stone-900">
            <%= @audit_log.description %>
          </dd>
        </div>

        <!-- Changes -->
        <% if @changes.present? %>
          <div class="sm:col-span-2 mt-6">
            <h3 class="text-lg font-medium text-stone-900 mb-4">Changes Made</h3>
            <div class="bg-stone-50 rounded-lg p-4">
              <% @changes.each do |field, (old_value, new_value)| %>
                <div class="mb-4 last:mb-0">
                  <div class="text-sm font-medium text-stone-700 mb-2">
                    <%= field.humanize %>
                  </div>
                  <div class="grid grid-cols-1 gap-2 sm:grid-cols-2">
                    <div>
                      <div class="text-xs text-stone-500 mb-1">Before</div>
                      <div class="bg-red-50 border border-red-200 rounded p-2 text-sm">
                        <% if old_value.nil? %>
                          <span class="text-stone-400 italic">None</span>
                        <% elsif old_value.is_a?(Array) %>
                          <% if old_value.empty? %>
                            <span class="text-stone-400 italic">Empty</span>
                          <% else %>
                            <%= old_value.join(', ') %>
                          <% end %>
                        <% else %>
                          <%= old_value %>
                        <% end %>
                      </div>
                    </div>
                    <div>
                      <div class="text-xs text-stone-500 mb-1">After</div>
                      <div class="bg-green-50 border border-green-200 rounded p-2 text-sm">
                        <% if new_value.nil? %>
                          <span class="text-stone-400 italic">None</span>
                        <% elsif new_value.is_a?(Array) %>
                          <% if new_value.empty? %>
                            <span class="text-stone-400 italic">Empty</span>
                          <% else %>
                            <%= new_value.join(', ') %>
                          <% end %>
                        <% else %>
                          <%= new_value %>
                        <% end %>
                      </div>
                    </div>
                  </div>
                </div>
              <% end %>
            </div>
          </div>
        <% end %>

        <!-- Technical Details -->
        <div class="sm:col-span-2 mt-6">
          <h3 class="text-lg font-medium text-stone-900 mb-4">Technical Details</h3>
        </div>

        <div>
          <dt class="text-sm font-medium text-stone-500">IP Address</dt>
          <dd class="mt-1 text-sm text-stone-900">
            <% if @audit_log.ip_address.present? %>
              <code class="bg-stone-100 px-2 py-1 rounded text-xs"><%= @audit_log.ip_address %></code>
            <% else %>
              <span class="text-stone-400 italic">Not recorded</span>
            <% end %>
          </dd>
        </div>

        <div>
          <dt class="text-sm font-medium text-stone-500">User Agent</dt>
          <dd class="mt-1 text-sm text-stone-900">
            <% if @audit_log.user_agent.present? %>
              <div class="bg-stone-100 p-2 rounded text-xs font-mono break-all">
                <%= @audit_log.user_agent %>
              </div>
            <% else %>
              <span class="text-stone-400 italic">Not recorded</span>
            <% end %>
          </dd>
        </div>

        <!-- Raw Data -->
        <% if @audit_log.changes.present? %>
          <div class="sm:col-span-2 mt-6">
            <details class="group">
              <summary class="cursor-pointer text-sm font-medium text-stone-700 hover:text-stone-900">
                <span class="group-open:hidden">Show Raw JSON Data</span>
                <span class="hidden group-open:inline">Hide Raw JSON Data</span>
              </summary>
              <div class="mt-2 bg-stone-900 text-stone-100 p-4 rounded-lg overflow-x-auto">
                <pre class="text-xs"><%= JSON.pretty_generate(@audit_log.changes) %></pre>
              </div>
            </details>
          </div>
        <% end %>
      </dl>
    </div>
  </div>
</div>
