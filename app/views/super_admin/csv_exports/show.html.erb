<% content_for :title, "CSV Export Details" %>

<div class="flex h-screen bg-stone-50">
  <!-- Sidebar -->
  <div class="w-64 bg-white shadow-sm border-r border-stone-200">
    <%= render 'shared/admin_sidebar' %>
  </div>

  <!-- Main Content -->
  <div class="flex-1 overflow-auto">
    <div class="p-8">
      <!-- Header -->
      <div class="mb-8">
        <div class="flex items-center justify-between">
          <div>
            <h1 class="text-2xl font-bold text-stone-900">Export Details</h1>
            <p class="mt-1 text-sm text-stone-600">
              Detailed information about CSV export request
            </p>
          </div>
          <div class="flex space-x-3">
            <%= link_to "← Back to Exports", super_admin_csv_exports_path, 
                class: "inline-flex items-center px-4 py-2 border border-stone-300 rounded-md shadow-sm text-sm font-medium text-stone-700 bg-white hover:bg-stone-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500" %>
            <% if @csv_export.failed? %>
              <%= link_to "Retry Export", retry_super_admin_csv_export_path(@csv_export), 
                  method: :post,
                  class: "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500" %>
            <% end %>
          </div>
        </div>
      </div>

      <!-- Export Overview -->
      <div class="bg-white shadow rounded-lg mb-6">
        <div class="px-6 py-4 border-b border-stone-200">
          <h3 class="text-lg font-medium text-stone-900">Export Overview</h3>
        </div>
        <div class="px-6 py-4">
          <dl class="grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2">
            <div>
              <dt class="text-sm font-medium text-stone-500">Export ID</dt>
              <dd class="mt-1 text-sm text-stone-900 font-mono"><%= @csv_export.export_id %></dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-stone-500">Export Type</dt>
              <dd class="mt-1 text-sm text-stone-900"><%= @csv_export.display_name %></dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-stone-500">Status</dt>
              <dd class="mt-1">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-<%= @csv_export.status_color %>-100 text-<%= @csv_export.status_color %>-800">
                  <%= @csv_export.status_icon %> <%= @csv_export.status.humanize %>
                </span>
              </dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-stone-500">Progress</dt>
              <dd class="mt-1">
                <div class="w-full bg-stone-200 rounded-full h-2">
                  <div class="bg-<%= @csv_export.status_color %>-600 h-2 rounded-full" style="width: <%= @csv_export.progress_percentage %>%"></div>
                </div>
                <div class="text-xs text-stone-500 mt-1"><%= @csv_export.progress_percentage %>%</div>
              </dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-stone-500">Requested By</dt>
              <dd class="mt-1 text-sm text-stone-900">
                <%= @csv_export.admin_user.full_name %><br>
                <span class="text-stone-500"><%= @csv_export.admin_user.email %></span>
              </dd>
            </div>
            <% if @csv_export.record_count %>
              <div>
                <dt class="text-sm font-medium text-stone-500">Record Count</dt>
                <dd class="mt-1 text-sm text-stone-900"><%= number_with_delimiter(@csv_export.record_count) %> records</dd>
              </div>
            <% end %>
          </dl>
        </div>
      </div>

      <!-- Timing Information -->
      <div class="bg-white shadow rounded-lg mb-6">
        <div class="px-6 py-4 border-b border-stone-200">
          <h3 class="text-lg font-medium text-stone-900">Timing Information</h3>
        </div>
        <div class="px-6 py-4">
          <dl class="grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-3">
            <div>
              <dt class="text-sm font-medium text-stone-500">Created At</dt>
              <dd class="mt-1 text-sm text-stone-900"><%= @csv_export.created_at.strftime('%B %d, %Y at %I:%M %p') %></dd>
            </div>
            <% if @csv_export.started_at %>
              <div>
                <dt class="text-sm font-medium text-stone-500">Started At</dt>
                <dd class="mt-1 text-sm text-stone-900"><%= @csv_export.started_at.strftime('%B %d, %Y at %I:%M %p') %></dd>
              </div>
            <% end %>
            <% if @csv_export.completed_at %>
              <div>
                <dt class="text-sm font-medium text-stone-500">Completed At</dt>
                <dd class="mt-1 text-sm text-stone-900"><%= @csv_export.completed_at.strftime('%B %d, %Y at %I:%M %p') %></dd>
              </div>
            <% end %>
          </dl>
          
          <% if @csv_export.started_at && @csv_export.completed_at %>
            <div class="mt-4 pt-4 border-t border-stone-200">
              <dt class="text-sm font-medium text-stone-500">Duration</dt>
              <dd class="mt-1 text-sm text-stone-900">
                <%= time_ago_in_words(@csv_export.started_at, @csv_export.completed_at) %>
              </dd>
            </div>
          <% end %>
        </div>
      </div>

      <!-- Applied Filters -->
      <% if @filters.any? %>
        <div class="bg-white shadow rounded-lg mb-6">
          <div class="px-6 py-4 border-b border-stone-200">
            <h3 class="text-lg font-medium text-stone-900">Applied Filters</h3>
          </div>
          <div class="px-6 py-4">
            <dl class="grid grid-cols-1 gap-x-4 gap-y-4 sm:grid-cols-2">
              <% @filters.each do |key, value| %>
                <% next if value.blank? %>
                <div>
                  <dt class="text-sm font-medium text-stone-500"><%= key.humanize %></dt>
                  <dd class="mt-1 text-sm text-stone-900">
                    <% if value.is_a?(Array) %>
                      <%= value.join(', ') %>
                    <% else %>
                      <%= value %>
                    <% end %>
                  </dd>
                </div>
              <% end %>
            </dl>
          </div>
        </div>
      <% end %>

      <!-- Error Information -->
      <% if @csv_export.failed? && @csv_export.error_message.present? %>
        <div class="bg-white shadow rounded-lg mb-6">
          <div class="px-6 py-4 border-b border-stone-200">
            <h3 class="text-lg font-medium text-stone-900 text-red-600">Error Information</h3>
          </div>
          <div class="px-6 py-4">
            <div class="bg-red-50 border border-red-200 rounded-md p-4">
              <div class="flex">
                <div class="flex-shrink-0">
                  <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                  </svg>
                </div>
                <div class="ml-3">
                  <h3 class="text-sm font-medium text-red-800">Export Failed</h3>
                  <div class="mt-2 text-sm text-red-700">
                    <pre class="whitespace-pre-wrap font-mono text-xs"><%= @csv_export.error_message %></pre>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      <% end %>

      <!-- Export File Information -->
      <% if @csv_export.completed? %>
        <div class="bg-white shadow rounded-lg">
          <div class="px-6 py-4 border-b border-stone-200">
            <h3 class="text-lg font-medium text-stone-900">Export File</h3>
          </div>
          <div class="px-6 py-4">
            <div class="bg-green-50 border border-green-200 rounded-md p-4">
              <div class="flex">
                <div class="flex-shrink-0">
                  <svg class="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                  </svg>
                </div>
                <div class="ml-3">
                  <h3 class="text-sm font-medium text-green-800">Export Completed Successfully</h3>
                  <div class="mt-2 text-sm text-green-700">
                    <p>The CSV file has been generated and sent to <strong><%= @csv_export.admin_user.email %></strong>.</p>
                    <p class="mt-1">Filename: <code class="bg-green-100 px-1 rounded"><%= @csv_export.filename %></code></p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      <% end %>
    </div>
  </div>
</div>
