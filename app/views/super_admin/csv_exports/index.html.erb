<% content_for :title, "CSV Export History" %>

<div class="flex h-screen bg-stone-50">
  <!-- Sidebar -->
  <div class="w-64 bg-white shadow-sm border-r border-stone-200">
    <%= render 'shared/admin_sidebar' %>
  </div>

  <!-- Main Content -->
  <div class="flex-1 overflow-auto">
    <div class="p-8">
      <!-- Header -->
      <div class="mb-8">
        <div class="flex items-center justify-between">
          <div>
            <h1 class="text-2xl font-bold text-stone-900">CSV Export History</h1>
            <p class="mt-1 text-sm text-stone-600">
              View and manage CSV export requests and their status
            </p>
          </div>
        </div>
      </div>

      <!-- Search and Filters -->
      <div class="bg-white shadow rounded-lg mb-6">
        <%= form_with url: super_admin_csv_exports_path, method: :get, local: true, class: "p-6" do |form| %>
          <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <!-- Status Filter -->
            <div>
              <%= form.label :status, "Status", class: "block text-sm font-medium text-stone-700 mb-1" %>
              <%= form.select :status, 
                  options_for_select([['All Statuses', 'all']] + @available_statuses, params[:status]),
                  {},
                  { class: "block w-full border-stone-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm" } %>
            </div>

            <!-- Controller Filter -->
            <div>
              <%= form.label :controller_filter, "Export Type", class: "block text-sm font-medium text-stone-700 mb-1" %>
              <%= form.select :controller_filter, 
                  options_for_select([['All Types', 'all']] + @available_controllers, params[:controller_filter]),
                  {},
                  { class: "block w-full border-stone-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm" } %>
            </div>

            <!-- User Filter -->
            <div>
              <%= form.label :user_filter, "Requested By", class: "block text-sm font-medium text-stone-700 mb-1" %>
              <%= form.select :user_filter, 
                  options_for_select([['All Users', 'all']] + @available_users.map { |user| [user.full_name, user.id] }, params[:user_filter]),
                  {},
                  { class: "block w-full border-stone-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm" } %>
            </div>

            <!-- Actions -->
            <div class="flex items-end space-x-2">
              <%= form.submit "Filter", 
                  class: "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500" %>
              <%= link_to "Clear", super_admin_csv_exports_path, 
                  class: "inline-flex items-center px-4 py-2 border border-stone-300 rounded-md shadow-sm text-sm font-medium text-stone-700 bg-white hover:bg-stone-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500" %>
            </div>
          </div>
        <% end %>
      </div>

      <!-- Results Summary -->
      <div class="mb-4">
        <p class="text-sm text-stone-600">
          Showing <%= @csv_exports.count %> export requests
        </p>
      </div>

      <!-- Exports Table -->
      <div class="bg-white shadow rounded-lg overflow-hidden">
        <table class="min-w-full divide-y divide-stone-200">
          <thead class="bg-stone-50">
            <tr>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">
                Export Details
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">
                Status
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">
                Progress
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">
                Requested By
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">
                Timing
              </th>
              <th scope="col" class="relative px-6 py-3">
                <span class="sr-only">Actions</span>
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-stone-200">
            <% @csv_exports.each do |export| %>
              <tr class="hover:bg-stone-50">
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="flex items-center">
                    <div>
                      <div class="text-sm font-medium text-stone-900">
                        <%= export.display_name %>
                      </div>
                      <div class="text-sm text-stone-500">
                        ID: <%= export.export_id[0..7] %>...
                      </div>
                      <% if export.record_count %>
                        <div class="text-xs text-stone-400">
                          <%= number_with_delimiter(export.record_count) %> records
                        </div>
                      <% end %>
                    </div>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-<%= export.status_color %>-100 text-<%= export.status_color %>-800">
                    <%= export.status_icon %> <%= export.status.humanize %>
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="w-full bg-stone-200 rounded-full h-2">
                    <div class="bg-<%= export.status_color %>-600 h-2 rounded-full" style="width: <%= export.progress_percentage %>%"></div>
                  </div>
                  <div class="text-xs text-stone-500 mt-1"><%= export.progress_percentage %>%</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm text-stone-900"><%= export.admin_user.full_name %></div>
                  <div class="text-sm text-stone-500"><%= export.admin_user.email %></div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-stone-500">
                  <div>Created: <%= export.created_at.strftime('%m/%d/%Y %H:%M') %></div>
                  <% if export.started_at %>
                    <div>Started: <%= export.started_at.strftime('%m/%d/%Y %H:%M') %></div>
                  <% end %>
                  <% if export.completed_at %>
                    <div>Completed: <%= export.completed_at.strftime('%m/%d/%Y %H:%M') %></div>
                  <% end %>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                  <div class="flex space-x-2">
                    <%= link_to "View", super_admin_csv_export_path(export), 
                        class: "text-blue-600 hover:text-blue-900" %>
                    <% if export.failed? %>
                      <%= link_to "Retry", retry_super_admin_csv_export_path(export), 
                          method: :post,
                          class: "text-green-600 hover:text-green-900" %>
                    <% end %>
                    <% unless export.queued? || export.processing? %>
                      <%= link_to "Delete", super_admin_csv_export_path(export), 
                          method: :delete,
                          data: { confirm: "Are you sure you want to delete this export record?" },
                          class: "text-red-600 hover:text-red-900" %>
                    <% end %>
                  </div>
                </td>
              </tr>
            <% end %>
          </tbody>
        </table>
        
        <% if @csv_exports.empty? %>
          <div class="text-center py-12">
            <div class="text-stone-500">
              <svg class="mx-auto h-12 w-12 text-stone-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              <h3 class="mt-2 text-sm font-medium text-stone-900">No exports found</h3>
              <p class="mt-1 text-sm text-stone-500">No CSV exports match your current filters.</p>
            </div>
          </div>
        <% end %>
      </div>

      <!-- Pagination -->
      <div class="mt-6">
        <%== pagy_nav(@pagy) if @pagy.pages > 1 %>
      </div>
    </div>
  </div>
</div>
