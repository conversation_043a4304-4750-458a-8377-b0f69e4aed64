<!DOCTYPE html>
<html>
  <head>
    <title>Ghostwrote - Onboarding</title>
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <%= favicon_link_tag asset_path('favicon.ico') %>
    <%= csrf_meta_tags %>
    <%= csp_meta_tag %>
    <%= stylesheet_link_tag "application" %>
    <%= javascript_pack_tag "application" %>

<script>
    !function(t,e){var o,n,p,r;e.__SV||(window.posthog=e,e._i=[],e.init=function(i,s,a){function g(t,e){var o=e.split(".");2==o.length&&(t=t[o[0]],e=o[1]),t[e]=function(){t.push([e].concat(Array.prototype.slice.call(arguments,0)))}}(p=t.createElement("script")).type="text/javascript",p.crossOrigin="anonymous",p.async=!0,p.src=s.api_host.replace(".i.posthog.com","-assets.i.posthog.com")+"/static/array.js",(r=t.getElementsByTagName("script")[0]).parentNode.insertBefore(p,r);var u=e;for(void 0!==a?u=e[a]=[]:a="posthog",u.people=u.people||[],u.toString=function(t){var e="posthog";return"posthog"!==a&&(e+="."+a),t||(e+=" (stub)"),e},u.people.toString=function(){return u.toString(1)+".people (stub)"},o="init bs ws ge fs capture De Ai $s register register_once register_for_session unregister unregister_for_session Is getFeatureFlag getFeatureFlagPayload isFeatureEnabled reloadFeatureFlags updateEarlyAccessFeatureEnrollment getEarlyAccessFeatures on onFeatureFlags onSurveysLoaded onSessionId getSurveys getActiveMatchingSurveys renderSurvey canRenderSurvey canRenderSurveyAsync identify setPersonProperties group resetGroups setPersonPropertiesForFlags resetPersonPropertiesForFlags setGroupPropertiesForFlags resetGroupPropertiesForFlags reset get_distinct_id getGroups get_session_id get_session_replay_url alias set_config startSessionRecording stopSessionRecording sessionRecordingStarted captureException loadToolbar get_property getSessionProperty xs Ss createPersonProfile Es gs opt_in_capturing opt_out_capturing has_opted_in_capturing has_opted_out_capturing clear_opt_in_out_capturing ys debug ks getPageViewId captureTraceFeedback captureTraceMetric".split(" "),n=0;n<o.length;n++)g(u,o[n]);e._i.push([i,s,a])},e.__SV=1)}(document,window.posthog||[]);
    posthog.init('phc_vgVSNt8fZ4lCmxFuGhWK98EiWNgggHYrdlMQbp42JCm', {
        api_host: 'https://us.i.posthog.com',
        person_profiles: 'identified_only', // or 'always' to create profiles for anonymous users as well
    })
</script>

  </head>

  <body class="min-h-screen bg-stone-50">
    <div class="relative flex items-center justify-center min-h-screen bg-center bg-no-repeat bg-cover"
         style="background-image: url('<%= asset_path('office.png') %>');">
      <div class="absolute inset-0 bg-black bg-opacity-40 backdrop-blur-sm"></div>
      
      <div class="relative z-10 w-full max-w-2xl p-8 bg-white rounded shadow">
        <div class="flex items-center justify-between mb-6">
          <div class="text-xl font-bold">ghostwrote.</div>
          <div class="text-sm text-stone-500">
            <% if Current.user.onboarding_step == "personal" %>
              Step 1 of 2
            <% else %>
              Step 2 of 2
            <% end %>
          </div>
        </div>
        
        <%= yield %>
      </div>
    </div>
  </body>
</html>
