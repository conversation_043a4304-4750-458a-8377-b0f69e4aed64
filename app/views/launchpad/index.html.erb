<%# Set a title for the page %>
<% content_for :title, "Launchpad" %>

<div class="flex flex-col items-center justify-center min-h-screen bg-stone-50">

  <%# Top Left Logo %>
  <div class="absolute top-0 left-0 p-6">
    <%= link_to root_path, class: "flex items-center" do %> <%# Added flex for alignment %>
      <%# Text-based logo provided by user %>
      <span class="items-center w-auto h-8 text-2xl font-semibold text-stone-800 letter-spacing-title">ghostwrote</span> <%# Changed text color to match theme %>
      <span class="w-auto h-8 ml-1 text-xs font-medium text-indigo-600 items-bottom letter-spacing-title"> LAUNCHPAD</span> <%# Changed text color to match theme %>
    <% end %>
  </div>

  <%# Top Right Navigation %>
  <div class="absolute top-0 right-0 p-6 text-sm">
    <div class="flex items-center space-x-4">
      <%# Avatar (optional) %>
      <% if Current.user.avatar.attached? %>
        <%# Use image_tag directly with the attachment, letting Rails handle URL generation %>
        <%= image_tag Current.user.avatar, class: "w-8 h-8 rounded-full object-cover" %> <%# Added object-cover %>
      <% else %>
        <%# Placeholder or default avatar %>
        <span class="inline-block w-8 h-8 overflow-hidden bg-gray-100 rounded-full">
          <svg class="w-full h-full text-gray-300" fill="currentColor" viewBox="0 0 24 24">
            <path d="M24 20.993V24H0v-2.996A14.977 14.977 0 0112.004 15c4.904 0 9.26 2.354 11.996 5.993zM16.002 8.999a4 4 0 11-8 0 4 4 0 018 0z" />
          </svg>
        </span>
      <% end %>

      <%# Links %>
      <%= link_to "Edit Profile", edit_account_profile_path, class: "text-stone-600 hover:text-stone-900" %>
      <%= link_to "Devices & Sessions", sessions_path, class: "text-stone-600 hover:text-stone-900" %>
      <%= button_to "Log out", global_sign_out_path, method: :delete, class: "text-stone-600 hover:text-stone-900" %>
    </div>
  </div>

   <%# Main Content Area %>
   <div class="w-full max-w-lg p-8 mx-auto text-center">
     <%= render "shared/flash_messages" %> <%# Add flash messages here %>
 
     <%# Determine if we need to center a single card or show a grid %>
     <% available_areas = [Current.user.scout_signup_completed?, Current.user.talent_signup_completed?, Current.user.superadmin?].count(true) %>
     <% single_card = available_areas == 1 %>
     <% grid_class = case available_areas
                     when 1 then 'flex justify-center'
                     when 2 then 'grid grid-cols-2 gap-6 sm:grid-cols-2'
                     else 'grid grid-cols-1 gap-6 sm:grid-cols-3'
                     end %>

     <div class="<%= grid_class %>">

      <%# Scout Card %>
      <% if Current.user.scout_signup_completed? %>
        <%= link_to scout_root_path, class: "block p-6 transition duration-150 ease-in-out bg-white border rounded-lg shadow hover:shadow-md" do %>
          <div class="flex flex-col items-center">
      <%# Scout Launchpad Icon %>
      <img src="/images/scout_launch.svg" class="w-48 h-48 mb-3" alt="Scout Icon">
            <h2 class="text-lg font-medium bg-stone-800 text-slate-50 px-2 mb-1 py-0.5 rounded-md ">Scout Area</h2>
            <p class="mt-1 text-sm text-stone-500">Manage jobs & find talent</p>
          </div>
        <% end %>
      <% end %>

      <% if Current.user.talent_signup_completed? %>
        <%= link_to talent_root_path, class: "block p-6 transition duration-150 ease-in-out bg-white border rounded-lg shadow hover:shadow-md" do %>
          <div class="flex flex-col items-center">
      <%# Scout Launchpad Icon %>
      <img src="/images/talent_launch.svg" class="w-48 h-48 mb-3" alt="Scout Icon">
            <h2 class="text-lg font-medium bg-stone-800 text-slate-50 px-2 mb-1 py-0.5 rounded-md ">Talent Area</h2>
            <p class="mt-1 text-sm text-stone-500">Find jobs & manage profile</p>
          </div>
        <% end %>
      <% end %>

      <%# Super Admin Card %>
      <% if Current.user.superadmin? %>
        <%= link_to super_admin_root_path, class: "block p-6 transition duration-150 ease-in-out bg-white border rounded-lg shadow hover:shadow-md" do %>
          <div class="flex flex-col items-center">
            <%# Super Admin Icon - consistent with other cards %>
            <div class="w-48 h-48 mb-3 flex items-center justify-center">
              <svg class="w-32 h-32 text-stone-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
              </svg>
            </div>
            <h2 class="text-lg font-medium bg-stone-800 text-slate-50 px-2 mb-1 py-0.5 rounded-md">Super Admin</h2>
            <p class="mt-1 text-sm text-stone-500">System administration</p>
          </div>
        <% end %>
      <% end %>


    </div>

    <%# Message if no areas are accessible (shouldn't happen with proper signup flow) %>
    <% if !Current.user.scout_signup_completed? && !Current.user.talent_signup_completed? && !Current.user.superadmin? %>
      <p class="mt-8 text-stone-500">Complete your Scout or Talent profile setup to access application areas.</p>
    <% end %>

  </div>
</div>
