<p style="color: red"><%= alert %></p>

<h1>Forgot your password?</h1>

<%= form_with(url: identity_password_reset_path) do |form| %>
  <div>
    <%= form.label :email, style: "display: block" %>
    <%= form.email_field :email, required: true, autofocus: true %>
  </div>

  <div>
    <%= form.submit "Send password reset email" %>
  </div>
<% end %>

<%# Add the resend button conditionally based on the alert message %>
<% if alert&.include?("You can't reset your password until you verify your email") %>
  <div style="margin-top: 1em;">
    <%= button_to "Resend Verification Email", resend_identity_email_verification_path,
        method: :post,
        params: { email: params[:email] }, # Use email from URL params passed by controller
        disabled: params[:email].blank?, # Disable button if email isn't available in params
        class: "your-button-classes-here" # Add appropriate styling classes
    %>
    <p style="font-size: 0.8em; color: grey;">(You can request a new verification email every 3 minutes)</p>
  </div>
<% end %>
