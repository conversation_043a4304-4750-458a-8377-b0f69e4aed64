<h1>Reset your password</h1>

<%= form_with(url: identity_password_reset_path, method: :patch) do |form| %>
  <% if @user.errors.any? %>
    <div style="color: red">
      <h2><%= pluralize(@user.errors.count, "error") %> prohibited this user from being saved:</h2>

      <ul>
        <% @user.errors.each do |error| %>
          <li><%= error.full_message %></li>
        <% end %>
      </ul>
    </div>
  <% end %>

  <%= form.hidden_field :sid, value: params[:sid] %>

  <div>
    <%= form.label :password, "New password", style: "display: block" %>
    <%= form.password_field :password, required: true, autofocus: true, autocomplete: "new-password" %>
    <div>12 characters minimum.</div>
  </div>

  <div>
    <%= form.label :password_confirmation, "Confirm new password", style: "display: block" %>
    <%= form.password_field :password_confirmation, required: true, autocomplete: "new-password" %>
  </div>

  <div>
    <%= form.submit "Save changes" %>
  </div>
<% end %>
