<div class="container mx-auto px-4 py-8">
  <h1 class="text-2xl font-bold mb-6">My Messages</h1>

  <div class="space-y-4">
    <% @conversations.each do |conversation| %>
      <%= link_to conversation_path(conversation), class: "block p-4 border rounded-lg hover:bg-stone-50" do %>
        <div class="flex justify-between items-center">
          <div>
            <p class="font-semibold"><%= conversation.users.where.not(id: Current.user).first %></p>
            <% if conversation.job %>
              <p class="text-sm text-stone-500">Re: <%= conversation.job.title %></p>
            <% end %>
          </div>
          <div class="text-sm text-stone-500">
            <%= time_ago_in_words(conversation.messages.last&.created_at || conversation.created_at) %> ago
          </div>
        </div>
      <% end %>
    <% end %>
  </div>
</div>
