<div class="flex min-h-screen max-h-screen overflow-hidden bg-stone-50">
  <!-- Sidebar -->
  <%= render "scout/shared/sidenav" %>
  
  <!-- Main Content -->
  <main class="flex-1 p-1 overflow-y-auto scrollbar-gutter-stable">
    <div class="flex h-full p-8 px-24 mx-auto bg-white border border-stone-200 rounded shadow-sm">
      <!-- Conversations List -->
      <div class="w-1/3 pr-8 border-r border-stone-200 overflow-y-auto">
        <div class="pb-5 mb-4 border-b border-stone-200 sm:pb-0">
          <div class="flex items-center justify-between">
            <h3 class="text-xl font-semibold leading-6 text-stone-900">Your Messages</h3>
            <div class="relative">
              <%= form_with url: scout_conversations_path, method: :get, class: "relative" do |f| %>
                <%= f.search_field :query,
                      value: params[:query],
                      placeholder: "Search conversations...",
                      class: "w-64 px-4 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500",
                      data: { controller: "search", action: "input->search#submit" } %>
                <% if params[:query].present? %>
                  <%= link_to scout_conversations_path, class: "absolute inset-y-0 right-0 pr-3 flex items-center" do %>
                    <svg class="h-5 w-5 text-stone-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  <% end %>
                <% end %>
              <% end %>
            </div>
          </div>
          <div class="mt-6 sm:mt-4">
            <%= render "scout/conversations/tabbar" %>
          </div>
        </div>

        <div class="space-y-4">
          <% @conversations.each do |conversation| %>
            <%= link_to scout_conversation_path(conversation),
                class: "block p-4 border rounded-lg transition duration-150 ease-in-out #{'bg-blue-50 border-blue-200' if @conversation == conversation} #{'hover:bg-stone-50' unless @conversation == conversation}" do %>
              <div class="flex justify-between items-center gap-4">
                <div class="flex items-center gap-4">
                  <div class="w-12 h-12 rounded-full bg-stone-200 flex items-center justify-center">
                    <% if conversation.users.where.not(id: Current.user).first&.avatar&.attached? %>
                      <%= image_tag conversation.users.where.not(id: Current.user).first.avatar,
                          class: "w-12 h-12 rounded-full object-cover" %>
                    <% else %>
                      <span class="text-stone-500 text-xl">
                        <%= conversation.users.where.not(id: Current.user).first&.name.initials %>
                      </span>
                    <% end %>
                  </div>

                  <div>
                    <p class="font-semibold text-stone-900">
                      Chat with <%= conversation.users.where.not(id: Current.user.id).pluck(:first_name).join(', ') %>
                      <% if conversation.conversation_participants.find_by(user: Current.user)&.archived? %>
                        <span class="text-xs text-stone-500">(Archived)</span>
                      <% end %>
                    </p>
                    <% if conversation.job %>
                      <p class="text-sm text-stone-500">Re: <%= conversation.job.title %></p>
                    <% end %>
                    <p class="text-sm text-stone-500 mt-1 truncate max-w-md">
                      <%= conversation.messages.last&.body || "No messages yet" %>
                    </p>
                  </div>
                </div>

                <div class="text-right">
                  <div class="text-sm text-stone-500">
                    <%= time_ago_in_words(conversation.messages.last&.created_at || conversation.created_at) %> ago
                  </div>
                </div>
              </div>
            <% end %>
          <% end %>
        </div>
      </div>

      <!-- Chat Window -->
      <div class="w-2/3 pl-8">
        <div class="bg-white rounded-lg shadow-lg h-full flex flex-col" data-controller="chat">
          <div class="border-b px-4 py-3">
            <h1 class="text-xl font-semibold">
              Chat with <%= @conversation.users.where.not(id: Current.user.id).pluck(:first_name).join(', ') %>
            </h1>
            <% if @conversation.job %>
              <p class="text-sm text-stone-500">Re: <%= @conversation.job.title %></p>
            <% end %>

            <div class="flex justify-end mb-4">
              <% if @conversation.conversation_participants.find_by(user: Current.user).archived? %>
                <%= button_to "Unarchive", unarchive_scout_conversation_path(@conversation),
                            class: "px-4 py-2 text-sm text-stone-600 bg-stone-100 rounded hover:bg-stone-200" %>
              <% else %>
                <%= button_to "Archive", archive_scout_conversation_path(@conversation),
                            class: "px-4 py-2 text-sm text-stone-600 bg-stone-100 rounded hover:bg-stone-200" %>
              <% end %>
            </div>
          </div>

          <%= turbo_stream_from @conversation %>

          <div id="messages"
              data-controller="chat scroll"
              data-chat-target="messages"
              data-scroll-target="container"
              data-action="turbo:stream:received->chat#messageReceived"
              class="messages-container flex-1 overflow-y-auto">
            <%= render partial: 'scout/conversations/message', collection: @messages %>
          </div>

          <div class="border-t p-4">
            <%= form_with(model: [@conversation, @message],
                        class: "message-form",
                        data: {
                          controller: "form-reset",
                          action: "turbo:submit-end->form-reset#reset"
                        }) do |f| %>
              <div class="flex">
                <%= f.text_area :body,
                              class: "flex-1 border rounded-l-lg p-2",
                              placeholder: "Type your message...",
                              data: { form_reset_target: "input" } %>
                <%= f.submit "Send",
                            class: "bg-blue-500 text-white px-6 py-2 rounded-r-lg" %>
              </div>
            <% end %>
          </div>
        </div>
      </div>
    </div>
  </main>
</div>
