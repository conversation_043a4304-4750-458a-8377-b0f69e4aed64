<%= content_tag :div, data: { controller: "segmented" }, class: "flex flex-col gap-4" do %>
  <!-- Segmented buttons -->
  <div class="inline-flex border border-stone-200 rounded-full overflow-hidden">
    <% tabs.each_with_index do |tab, index| %>
      <% is_active = tab[:active].present? && tab[:active] == true %>
      <button
        type="button"
        data-segmented-target="button"
        data-segment-url="<%= tab[:url] %>"
        data-action="click->segmented#switchTab"
        class="<%= is_active ? 'px-4 py-2 text-sm font-medium bg-[#111827] text-white' : 'px-4 py-2 text-sm font-medium bg-white text-stone-600' %>"
      >
        <%= tab[:label] %>
      </button>
    <% end %>
  </div>

  <!-- Turbo Frame to load content -->
  <turbo-frame
    id="<%= frame_id %>"
    data-segmented-target="frame"
    class="block w-full"
  >
    <!-- Optionally, render initial content for the active tab,
         or leave empty if you want to load on first click. -->
  </turbo-frame>
<% end %>
