<div
  class="relative flex items-center justify-center min-h-screen bg-center bg-no-repeat bg-cover"
  style="background-image: url('<%= asset_path('office.png') %>');"
>
  <!-- Optional overlay to darken or tint the background -->
  <div class="absolute inset-0 bg-black bg-opacity-40 backdrop-blur-sm"></div>

  <!-- Main container -->
  <div class="relative z-10 w-full max-w-md p-16 bg-white rounded shadow">
    <!-- Heading -->
    <h1 class="mb-1 text-xl font-bold">Sign up</h1>
    <p class="mb-6 text-sm text-stone-600">Create your account to get started.</p>

    <!-- Flash messages (optional) -->
    <p class="text-green-500"><%= notice %></p>
    <p class="text-red-500"><%= alert %></p>

    <!-- Display any validation errors -->
    <% if @user.errors.any? %>
      <div class="mb-4 text-red-600">
        <h2>
          <%= pluralize(@user.errors.count, "error") %> prohibited this user from being saved:
        </h2>
        <ul class="list-disc list-inside">
          <% @user.errors.each do |error| %>
            <li><%= error.full_message %></li>
          <% end %>
        </ul>
      </div>
    <% end %>

    <!-- Form -->
    <%= form_with(url: sign_up_path, class: "space-y-4 mb-6") do |form| %>
      <div>
        <%= form.label :email, "Email", class: "block text-sm text-stone-700 font-medium mb-1" %>
        <%= form.email_field :email,
                             value: @user.email,
                             required: true,
                             autofocus: true,
                             autocomplete: "email",
                             class: "w-full border border-stone-500 rounded px-3 py-2 focus:outline-none focus:ring focus:ring-blue-200" %>
      </div>

      <div>
        <%= form.label :password, "Password", class: "block text-sm text-stone-700 font-medium mb-1" %>
        <%= form.password_field :password,
                                required: true,
                                autocomplete: "new-password",
                                class: "w-full border border-stone-500 rounded px-3 py-2 focus:outline-none focus:ring focus:ring-blue-200" %>
        <div class="mt-1 text-xs text-stone-500">12 characters minimum.</div>
      </div>

      <div>
        <%= form.label :password_confirmation, "Confirm Password", class: "block text-sm text-stone-700 font-medium mb-1" %>
        <%= form.password_field :password_confirmation,
                                required: true,
                                autocomplete: "new-password",
                                class: "w-full border border-stone-500 rounded px-3 py-2 focus:outline-none focus:ring focus:ring-blue-200" %>
      </div>

      <!-- User Type Selection -->
      <fieldset class="mt-4">
        <legend class="block mb-2 text-sm font-medium text-stone-700">I want to sign up as a:</legend>
        <div class="space-y-2">
          <div class="flex items-center">
            <%= radio_button_tag :user_type, 'scout', false, required: true, class: "h-4 w-4 text-black border-stone-300 focus:ring-black" %>
            <%= label_tag :user_type_scout, "Scout (Looking to hire talent)", class: "ml-3 block text-sm text-stone-700" %>
          </div>
          <div class="flex items-center">
            <%= radio_button_tag :user_type, 'talent', false, required: true, class: "h-4 w-4 text-black border-stone-300 focus:ring-black" %>
            <%= label_tag :user_type_talent, "Talent (Looking for work)", class: "ml-3 block text-sm text-stone-700" %>
          </div>
        </div>
      </fieldset>

      <!-- Submit button -->
      <%= form.submit "Sign up", class: "w-full bg-black text-white rounded py-2 px-4 hover:bg-stone-800 focus:outline-none focus:ring focus:ring-stone-500 mt-6" %>
    <% end %>

    <!-- Footer links -->
    <div class="text-sm text-center">
      <p class="mb-2">
        Already have an account?
        <%= link_to "Log in", sign_in_path, class: "underline" %>
      </p>
    </div>
  </div>
</div>
