<!-- Shadcn-inspired Sidebar -->
<div class="fixed top-0 left-0 z-40 h-full overflow-x-hidden overflow-y-auto transition-all duration-300 ease-in-out bg-stone-50 sidebar-expanded" data-sidebar-target="aside" data-sidebar-expanded="true">
  <div class="flex flex-col h-full max-h-screen gap-2 p-1">
    <!-- Header/Logo Section -->
    <div class="flex items-center px-4 border-b h-14 header-container">
      <a href="#" class="flex items-center gap-2 font-semibold">
        <%= phosphor_icon "goodreads-logo" , class: "w-8 h-8"%>
        <span class="sidebar-text">Ghostwrote</span>
      </a>
    </div>
    
    <!-- Navigation Section -->
    <div class="flex-1 py-2 overflow-auto">
      <nav class="flex flex-col items-start px-4 text-sm font-medium" data-sidebar-target="navigationItems"> <%# Added flex flex-col %>
        <!-- Main Navigation Section -->
        <div class="w-full mb-4 navigation-container"> <%# Added w-full %>
          <div class="mb-2 text-xs font-semibold text-stone-400 sidebar-text">Platform</div>
          
          <div data-controller="accordion">
            <div style="display: none;">
              <a href="#" class="flex items-center gap-3 px-3 py-2 pl-10 transition-all rounded-lg text-stone-500 hover:text-stone-900">
                History
              </a>
              <a href="#" class="flex items-center gap-3 px-3 py-2 pl-10 transition-all rounded-lg text-stone-500 hover:text-stone-900">
                Starred
              </a>
              <a href="#" class="flex items-center gap-3 px-3 py-2 pl-10 transition-all rounded-lg text-stone-500 hover:text-stone-900">
                Settings
              </a>
            </div>
          </div>
          
          
          <%= link_to talent_jobs_path, class: "flex items-center gap-3 rounded-lg px-3 py-2 text-stone-500 transition-all hover:text-stone-900  #{current_page?(talent_jobs_path) ? 'bg-stone-100 text-stone-900' : ''}" do %>
          <%= phosphor_icon "read-cv-logo", class: "w-6 h-6" %> <%# Increased size %>
            <span class="sidebar-text">Jobs</span>
          <% end %>
          
          <%= link_to talent_profile_path, class: "flex items-center gap-3 rounded-lg px-3 py-2 text-stone-500 transition-all hover:text-stone-900 #{current_page?(talent_profile_path) ? 'bg-stone-100 text-stone-900' : ''}" do %>
          
          <%= phosphor_icon "user", class: "w-6 h-6" %> <%# Increased size %>
            <span class="sidebar-text">Profile</span>
          <% end %>
          
          
          <%= link_to talent_conversations_path, class: "flex items-center gap-3 rounded-lg px-3 py-2 text-stone-500 transition-all hover:text-stone-900  #{current_page?(talent_conversations_path) ? 'bg-stone-100 text-stone-900' : ''}" do %>
          <%= phosphor_icon "chats", class: "w-6 h-6" %> <%# Increased size %>
            <span class="sidebar-text">Messages</span>
          <% end %>

          <%= link_to talent_chat_requests_path, class: "flex items-center gap-3 rounded-lg px-3 py-2 text-stone-500 transition-all hover:text-stone-900 #{current_page?(talent_chat_requests_path) ? 'bg-stone-100 text-stone-900' : ''}" do %>
          <%= phosphor_icon "chat-circle-dots", class: "w-6 h-6" %> <%# Increased size %>
            <span class="sidebar-text">Chat Requests</span>
          <% end %>

          <%= link_to talent_job_invitations_path, class: "flex items-center gap-3 rounded-lg px-3 py-2 text-stone-500 transition-all hover:text-stone-900 #{current_page?(talent_job_invitations_path) ? 'bg-stone-100 text-stone-900' : ''}" do %>
          <%= phosphor_icon "gift", class: "w-6 h-6" %> <%# Increased size %>
            <span class="sidebar-text">Invitations</span>
          <% end %>

          <%= link_to talent_job_applications_path, class: "flex items-center gap-3 rounded-lg px-3 py-2 text-stone-500 transition-all hover:text-stone-900 #{current_page?(talent_job_applications_path) ? 'bg-stone-100 text-stone-900' : ''}" do %>
          <%= phosphor_icon "stack", class: "w-6 h-6" %> <%# Increased size %>

            <span class="sidebar-text">Applications</span>
          <% end %>

        </div>
      </nav>
    </div>
    
    <!-- Sidebar Toggle Button (Above Footer) -->
    <div class="p-2">
      <button class="flex items-center justify-center w-full gap-2 px-3 py-2 text-sm font-medium transition-all rounded-lg text-stone-500 hover:bg-stone-100 hover:text-stone-900 sidebar-toggle-btn" data-action="click->talent-sidebar#toggle" data-sidebar-target="toggleButton"> <%# Corrected controller name %>
        <!-- Icon will be dynamically inserted by the sidebar controller -->
      </button>
    </div>
    
    <!-- Footer/User Section -->
    <div class="pt-2 mt-0 border-t border-stone-200 ">
      <div class="overflow-x-hidden" data-controller="dropdown">
        <button type="button" data-action="dropdown#toggle click@window->dropdown#hide" class="flex items-center justify-between w-full px-2 py-2 text-sm font-medium transition-all rounded-lg text-stone-500 hover:text-stone-900 user-avatar-button">
          <div class="flex items-center gap-2 user-details">
            <%# Display user avatar or placeholder %>
            <% if Current.user.avatar.attached? %>
              <%= image_tag Current.user.avatar, alt: "User Avatar", class: "w-8 h-8 rounded-md object-cover" %>
            <% else %>
              <%# Placeholder - adjust as needed (e.g., use an icon or initials) %>
              <div class="flex items-center justify-center w-8 h-8 bg-gray-200 rounded-md">
                <span class="text-xs text-gray-500"><%= Current.user.first_name.first %></span> <%# Example: Initials %>
              </div>
            <% end %>
            <%# Display Full Name and Email %>
            <div class="flex flex-col items-start sidebar-text">
              <span class="text-stone-700"><%= Current.user.name %></span> <%# Use .name provided by has_person_name %>
              <span class="text-xs text-stone-500"><%= Current.user.email %></span>
            </div>
          </div>
          <%= phosphor_icon "caret-up-down", class: "w-4 h-4 sidebar-text" %>
        </button>
        <div data-dropdown-target="menu" class="hidden overflow-hidden text-sm border rounded-lg bg-stone-900 dropdown-menu border-stone-800">
          <!-- User Info Header -->
          <div class="px-4 py-3 border-b border-stone-800">
            <div class="text-sm text-stone-400">You are logged in as:</div>
            <div class="text-white"><%= Current.user.email %></div>
          </div>
          
          <!-- Menu Items -->
          <div>
            <!-- Account Settings -->
            <%= link_to talent_settings_path, class: 'flex items-center gap-3 px-4 py-3 text-sm text-white hover:bg-stone-900 border-b hover:bg-stone-600 border-stone-800' do %>
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-5 h-5 text-stone-400">
                <circle cx="12" cy="12" r="10" />
                <circle cx="12" cy="10" r="3" />
                <path d="M7 20.662V19a2 2 0 0 1 2-2h6a2 2 0 0 1 2 2v1.662" />
              </svg>
              <span>My Account</span>
            <% end %>
            
            
            
            
            <!-- Logout Button -->
            <%= button_to global_sign_out_path, method: :delete, class: "flex w-full items-center gap-3 px-4 py-3 text-sm text-white hover:bg-stone-600" do %>
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-5 h-5 text-stone-400">
                <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4" />
                <polyline points="16 17 21 12 16 7" />
                <line x1="21" y1="12" x2="9" y2="12" />
              </svg>
              <span>Log out</span>
            <% end %>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
