<div class="py-4">
  <nav aria-label="Progress">
    <ol role="list" class="flex items-center">
      <% steps = ['Job Overview', 'Basic Info', 'Portfolio', 'Questions', 'Review'] %>
      
      <% steps.each_with_index do |step_name, index| %>
        <li class="relative <%= index > 0 ? 'pl-8 sm:pl-20' : '' %>">
          <% if index < current_step - 1 %>
            <!-- Completed Step -->
            <div class="absolute inset-0 flex items-center" aria-hidden="true">
              <div class="h-0.5 w-full bg-indigo-600"></div>
            </div>
            <div class="relative flex items-center justify-center w-8 h-8 bg-indigo-600 rounded-full">
              <svg class="w-5 h-5 text-white" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
              </svg>
            </div>
          <% elsif index == current_step - 1 %>
            <!-- Current Step -->
            <div class="absolute inset-0 flex items-center" aria-hidden="true">
              <div class="h-0.5 w-full bg-stone-200"></div>
            </div>
            <div class="relative flex items-center justify-center w-8 h-8 bg-white border-2 border-indigo-600 rounded-full">
              <span class="text-indigo-600"><%= index + 1 %></span>
            </div>
          <% else %>
            <!-- Upcoming Step -->
            <div class="absolute inset-0 flex items-center" aria-hidden="true">
              <div class="h-0.5 w-full bg-stone-200"></div>
            </div>
            <div class="relative flex items-center justify-center w-8 h-8 bg-white border-2 border-stone-300 rounded-full">
              <span class="text-stone-500"><%= index + 1 %></span>
            </div>
          <% end %>
          <span class="absolute top-10 text-sm font-medium <%= index == current_step - 1 ? 'text-indigo-600' : 'text-stone-500' %>">
            <%= step_name %>
          </span>
        </li>
      <% end %>
    </ol>
  </nav>
</div>