<div class="flex flex-col h-full bg-white border rounded-md border-stone-200">
  <div class="flex-1 w-full px-16 py-8 mx-auto max-w-7xl">
    <% if @invitations.any? %>

    <div class="mb-2 text-2xl font-semibold text-stone-900">
      Invitations
    </div>
    <div class="pb-2 mb-4 text-sm text-stone-600 ">
      Here is a list of job invitations sent to you by recruiters.
    </div>

    <div class="pb-4 mb-6 text-stone-600">
      <div class="inline-flex rounded-md shadow-sm" role="group">
        <% if @counts[:all] == 0 %>
          <div class="px-4 py-2 text-sm font-medium bg-white border rounded-l-lg cursor-not-allowed border-stone-200">
            <span class="opacity-50 text-stone-700">
              All <span class="ml-1 text-xs opacity-70">(<%= @counts[:all] %>)</span>
            </span>
          </div>
        <% else %>
          <%= link_to talent_job_invitations_path, 
            class: "px-4 py-2 text-sm font-medium #{params[:status].blank? ? 'bg-stone-900 text-white' : 'bg-white text-stone-700 hover:bg-stone-50'} border border-stone-200 rounded-l-lg" do %>
            All <span class="ml-1 text-xs opacity-70">(<%= @counts[:all] %>)</span>
          <% end %>
        <% end %>
        <% if @counts[:pending] == 0 %>
          <div class="px-4 py-2 text-sm font-medium bg-white border-t border-b cursor-not-allowed border-stone-200">
            <span class="opacity-50 text-stone-700">
              Pending <span class="ml-1 text-xs opacity-70">(<%= @counts[:pending] %>)</span>
            </span>
          </div>
        <% else %>
          <%= link_to talent_job_invitations_path(status: "pending"), 
            class: "px-4 py-2 text-sm font-medium #{params[:status] == 'pending' ? 'bg-stone-900 text-white' : 'bg-white text-stone-700 hover:bg-stone-50'} border-t border-r border-b border-stone-200" do %>
            Pending <span class="ml-1 text-xs opacity-70">(<%= @counts[:pending] %>)</span>
          <% end %>
        <% end %>
        <% if @counts[:accepted] == 0 %>
          <div class="px-4 py-2 text-sm font-medium border-t border-b cursor-not-allowed bg-stone-100 border-stone-200">
            <span class="opacity-50 text-stone-700">
              Accepted <span class="ml-1 text-xs opacity-70">(<%= @counts[:accepted] %>)</span>
            </span>
          </div>
        <% else %>
          <%= link_to talent_job_invitations_path(status: "accepted"), 
            class: "px-4 py-2 text-sm font-medium #{params[:status] == 'accepted' ? 'bg-stone-900 text-white' : 'bg-white text-stone-700 hover:bg-stone-50'} border-t border-r border-b border-stone-200" do %>
            Accepted <span class="ml-1 text-xs opacity-70">(<%= @counts[:accepted] %>)</span>
          <% end %>
        <% end %>
        <% if @counts[:ignored] == 0 %>
          <div class="px-4 py-2 text-sm font-medium border rounded-r-lg cursor-not-allowed bg-stone-100 border-stone-200">
            <span class="opacity-50 text-stone-700">
              Ignored <span class="ml-1 text-xs opacity-70">(<%= @counts[:ignored] %>)</span>
            </span>
          </div>
        <% else %>
          <%= link_to talent_job_invitations_path(status: "ignored"), 
            class: "px-4 py-2 text-sm font-medium #{params[:status] == 'ignored' ? 'bg-stone-900 text-white' : 'bg-white text-stone-700 hover:bg-stone-50'} border border-stone-200 rounded-r-lg" do %>
            Ignored <span class="ml-1 text-xs opacity-70">(<%= @counts[:ignored] %>)</span>
          <% end %>
        <% end %>
      </div>
    </div>
      <div class="border rounded-lg border-stone-200">
        <ul>
        <% @invitations.each do |invitation| %>
          <li class="border-b last:border-none border-stone-200">
            <div class="flex items-start justify-between p-6">
              <div class="flex-1">
                <div class="flex items-center justify-between">
                  <div class="flex items-center">
                    <%= link_to talent_job_path(invitation.job), class: "text-lg font-medium text-stone-900 hover:underline" do %>
                      <%= invitation.job.title %>
                    <% end %>
                    <span class="ml-2 px-2 py-0.5 text-xs font-medium rounded-md <%= invitation.accepted? ? 'bg-green-100 text-green-700' : invitation.ignored? ? 'bg-red-100 text-red-700' : 'bg-blue-100 text-blue-700' %>">
                      <%= invitation.status.capitalize %>
                    </span>
                  </div>
                  <span class="text-sm text-stone-600">
                    Invited <%= time_ago_in_words(invitation.created_at) %> ago
                  </span>
                </div>
                <p class="mt-1 text-sm text-stone-600">
                  <%= invitation.job.organization.name %>
                </p>
                
                <% if invitation.invitation_letter.present? %>
                  <div class="p-4 mt-4 border rounded-md bg-stone-100 border-stone-200">
                    <h4 class="mb-2 text-sm font-medium text-stone-700">Message from recruiter:</h4>
                    <p class="text-sm text-stone-600"><%= invitation.invitation_letter %></p>
                  </div>
                <% end %>
                
                <div class="flex items-center justify-between gap-3 mt-4">
                  <div>
                    <%= link_to talent_job_path(invitation.job), class: "inline-flex items-center px-4 py-2 border border-stone-300 text-sm font-medium rounded-md text-stone-700 bg-white hover:bg-stone-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-stone-500" do %>
                      View Job
                    <% end %>
                  </div>

                  <div class="flex">
                    <% if invitation.pending? %>
                      <%= button_to ignore_talent_job_invitation_path(invitation), method: :post, class: "mr-2 inline-flex items-center px-4 py-2 text-sm font-medium rounded-md text-stone-700 bg-white hover:text-red-600 underline focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-stone-500" do %>
                        Ignore
                      <% end %>
                    <% end %>
                    <% if invitation.accepted? %>
                      <%= link_to talent_job_application_path(invitation.job_application), class: "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-stone-900 hover:bg-stone-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-stone-500" do %>
                        View Application
                      <% end %>
                    <% else %>
                      <%= button_to accept_talent_job_invitation_path(invitation), method: :post, class: "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-stone-900 hover:bg-stone-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-stone-500" do %>
                        Accept Invitation
                      <% end %>
                    <% end %>
                  </div>
                </div>
              </div>
            </div>
          </li>
        <% end %>
        </ul>
      </div>
    <% else %>
      <div class="flex flex-col items-center justify-center h-[70vh]">
        <%= phosphor_icon "tray", style: "duotone", class: "w-16 h-16"%>
        <h3 class="mt-4 text-lg font-medium text-stone-900">No invitations</h3>
        <p class="mt-1 text-sm text-stone-500">
          You don't have any job invitations at the moment.
        </p>
        <div class="mt-8">
          <%= link_to talent_jobs_path, class: "inline-flex items-center px-6 py-3 text-base font-medium text-white bg-stone-900 border border-transparent rounded-md shadow-sm hover:bg-stone-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-stone-500" do %>
            Source Jobs
            <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5 ml-2 -mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6" />
            </svg>
          <% end %>
        </div>
      </div>
    <% end %>
  </div>
</div>
