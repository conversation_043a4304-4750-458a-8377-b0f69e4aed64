<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
  <%= render 'talent/shared/application_progress', current_step: 2 %>
  
  <div class="bg-white shadow sm:rounded-lg">
    <div class="px-4 py-5 sm:px-6">
      <h3 class="text-lg leading-6 font-medium text-stone-900">Basic Information</h3>
      <p class="mt-1 max-w-2xl text-sm text-stone-500">Tell us why you're interested in this position</p>
    </div>

    <div class="border-t border-stone-200">
      <%= form_with(model: @job_application, 
              url: talent_job_job_applications_path(@job), 
              method: :post,
              data: { controller: 'application-wizard' }) do |f| %>
    <%= hidden_field_tag :step, 3 %>
    <div class="px-4 py-5 sm:p-6">
          <div class="space-y-6">
            <div>
              <%= f.label :application_letter, "Cover Letter", class: "block text-sm font-medium text-stone-700" %>
              <div class="mt-1">
                <%= f.text_area :application_letter, rows: 8, required: true,
                    class: "shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-stone-300 rounded-md",
                    data: { action: "input->application-wizard#handleInput" } %>
              </div>
            </div>
          </div>
        </div>

        <div class="px-4 py-3 bg-stone-50 text-right sm:px-6">
          <%= link_to "Back", new_talent_job_job_application_path(@job, step: 1), 
          class: "inline-flex justify-center py-2 px-4 border border-stone-300 shadow-sm text-sm font-medium rounded-md text-stone-700 bg-white hover:bg-stone-50" %>
      <%= f.submit "Continue", class: "ml-3 inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700",
          data: { application_wizard_target: "submitButton" } %>
    </div>
  <% end %>
</div>
  </div>
</div>