<div class="px-4 py-8 mx-auto max-w-7xl sm:px-6 lg:px-8">
  <div class="mb-6">
    <%= link_to talent_job_path(@job), class: "text-indigo-600 hover:text-indigo-900" do %>
      ← Back to Job
    <% end %>
  </div>
  
  <div class="bg-white shadow sm:rounded-lg">
    <div class="px-4 py-5 sm:px-6">
      <h3 class="text-lg font-medium leading-6 text-stone-900">Apply for <%= @job.title %></h3>
      <p class="max-w-2xl mt-1 text-sm text-stone-500">Please fill out all required fields to apply for this position.</p>
      
      <% if @errors.present? %>
        <div class="p-4 mt-4 text-sm text-red-700 bg-red-100 rounded-lg">
          <h2><%= pluralize(@errors.count, "error") %> prohibited this application from being saved:</h2>
          <ul class="list-disc list-inside">
            <% @errors.each do |error| %>
              <li><%= error %></li>
            <% end %>
          </ul>
        </div>
      <% end %>
    </div>

    <div class="border-t border-stone-200">
      <%= form_with(model: @job_application, 
                  url: talent_job_job_applications_path(@job),
                  method: :post,
                  multipart: true, 
                  data: { controller: 'application-wizard' }) do |f| %>
        <%= hidden_field_tag :final_submission, true %>
        
        <div class="px-4 py-5 sm:p-6">
          <!-- Basic Information Section -->
          <div class="mb-8">
            <h4 class="mb-4 text-base font-medium text-stone-900">Basic Information</h4>
            <div class="space-y-6">
              <div>
                <%= f.label :application_letter, "Cover Letter", class: "block text-sm font-medium text-stone-700" %>
                <div class="mt-1">
                  <%= f.text_area :application_letter, rows: 6, required: true,
                      class: "shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-stone-300 rounded-md",
                      data: { action: "input->application-wizard#handleInput" } %>
                </div>
                <p class="mt-2 text-sm text-stone-500">Tell us why you're interested in this position</p>
              </div>
            </div>
          </div>

          <!-- Portfolio & Experience Section -->
          <div class="mb-8">
            <h4 class="mb-4 text-base font-medium text-stone-900">Portfolio & Experience</h4>
            <div class="space-y-6">
              <div>
                <%= f.label :resume, class: "block text-sm font-medium text-stone-700" %>
                <div class="mt-1">
                  <%= f.file_field :resume, required: true, accept: ".pdf,.doc,.docx",
                      class: "shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-stone-300",
                      data: { action: "change->application-wizard#handleInput" } %>
                </div>
                <p class="mt-2 text-sm text-stone-500">Upload your resume (PDF, DOC, or DOCX)</p>
              </div>

              <div>
                <%= f.label :documents, "Additional Documents", class: "block text-sm font-medium text-stone-700" %>
                <div class="mt-1">
                  <%= f.file_field :documents, multiple: true, accept: ".pdf,.doc,.docx",
                      class: "shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-stone-300" %>
                </div>
                <p class="mt-2 text-sm text-stone-500">Optional: Add portfolio samples or other relevant documents</p>
              </div>
            </div>
          </div>

          <!-- Additional Questions Section -->
          <div class="mb-8">
            <h4 class="mb-4 text-base font-medium text-stone-900">Additional Questions</h4>
            <div class="space-y-6">
              <div>
                <%= f.label :additional_info, "Additional Information", class: "block text-sm font-medium text-stone-700" %>
                <div class="mt-1">
                  <%= f.text_area :additional_info, rows: 4, required: true,
                      class: "shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-stone-300 rounded-md",
                      data: { action: "input->application-wizard#handleInput" } %>
                </div>
                <p class="mt-2 text-sm text-stone-500">Share any other relevant information about your experience and qualifications</p>
              </div>
            </div>
          </div>
        </div>

        <div class="px-4 py-3 text-right bg-stone-50 sm:px-6">
          <%= link_to "Cancel", talent_job_path(@job), class: "inline-flex justify-center py-2 px-4 border border-stone-300 shadow-sm text-sm font-medium rounded-md text-stone-700 bg-white hover:bg-stone-50" %>
          <%= f.submit "Submit Application", class: "ml-3 inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700",
              data: { application_wizard_target: "submitButton" } %>
        </div>
      <% end %>
    </div>
  </div>
</div>
