<%= form_with(model: [:talent, @job, @job_application], local: true, class: "space-y-6") do |f| %>
  <% if @job_application.errors.any? %>
    <div class="p-4 rounded-md bg-red-50">
      <div class="flex">
        <div class="ml-3">
          <h3 class="text-sm font-medium text-red-800">
            <%= pluralize(@job_application.errors.count, 'error') %> prohibited
            this application from being saved:
          </h3>
          <div class="mt-2 text-sm text-red-700">
            <ul class="pl-5 space-y-1 list-disc">
              <% @job_application.errors.full_messages.each do |msg| %>
                <li><%= msg %></li>
              <% end %>
            </ul>
          </div>
        </div>
      </div>
    </div>
  <% end %>
  <div>
    <%=
      f.label :application_letter,
              class: 'block text-sm font-medium text-stone-700'
    %>
    <div class="mt-1">
      <%=
        f.text_area :application_letter,
                    rows: 8,
                    class:
                      'shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-stone-300 rounded-md',
                    placeholder:
                      "Tell us why you're interested in this position..."
      %>
    </div>
  </div>
  <div>
    <%= f.label :resume, class: 'block text-sm font-medium text-stone-700' %>
    <div class="mt-1">
      <%=
        f.file_field :resume,
                     class:
                       'block w-full text-sm text-stone-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-indigo-50 file:text-indigo-700 hover:file:bg-indigo-100',
                     accept: '.pdf,.doc,.docx'
      %>
    </div>
    <p class="mt-2 text-sm text-stone-500">PDF, DOC, or DOCX up to 10MB</p>
  </div>
  <div>
    <%=
      f.label :documents,
              'Additional Documents',
              class: 'block text-sm font-medium text-stone-700'
    %>
    <div class="mt-1">
      <%=
        f.file_field :documents,
                     multiple: true,
                     class:
                       'block w-full text-sm text-stone-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-indigo-50 file:text-indigo-700 hover:file:bg-indigo-100',
                     accept: '.pdf,.doc,.docx'
      %>
    </div>
    <p class="mt-2 text-sm text-stone-500">
      Optional. Add any supporting documents (PDF, DOC, or DOCX up to 10MB each)
    </p>
  </div>
  <div class="flex justify-end">
    <%=
      f.submit class:
                 'ml-3 inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500'
    %>
  </div>
<% end %>
