<li>
  <%= link_to talent_job_application_path(application), class: "block hover:bg-stone-50" do %>
    <div class="px-4 py-4 sm:px-6">
      <div class="flex items-center justify-between">
        <div class="flex-1 min-w-0">
          <p class="text-sm font-medium text-indigo-600 truncate">
            <%= application.job.title %>
          </p>
          <p class="flex items-center mt-2 text-sm text-stone-500">
            <% if application.invited? && application.invited_at.present? %>
              Invited <%= time_ago_in_words(application.invited_at) %> ago
            <% elsif application.applied_at.present? %>
              Applied <%= time_ago_in_words(application.applied_at) %> ago
            <% else %>
              Application in progress
            <% end %>
          </p>
        </div>
        <div class="flex-shrink-0 ml-4">
          <% status_colors = {
            "pending" => "bg-yellow-100 text-yellow-800",
            "under_review" => "bg-blue-100 text-blue-800",
            "accepted" => "bg-green-100 text-green-800",
            "rejected" => "bg-red-100 text-red-800",
            "withdrawn" => "bg-stone-100 text-stone-800"
          } %>
          <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium capitalize <%= status_colors[application.status] %>">
            <%= application.status.humanize %>
          </span>
        </div>
      </div>
    </div>
  <% end %>
</li>
