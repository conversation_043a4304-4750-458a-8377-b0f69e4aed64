<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
  <%= render 'talent/shared/application_progress', current_step: 5 %>
  
  <% if @errors.present? %>
    <div class="bg-red-50 border-l-4 border-red-400 p-4 mb-4">
      <div class="flex">
        <div class="flex-shrink-0">
          <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
          </svg>
        </div>
        <div class="ml-3">
          <h3 class="text-sm font-medium text-red-800">There were errors with your submission:</h3>
          <div class="mt-2 text-sm text-red-700">
            <ul class="list-disc pl-5 space-y-1">
              <% @errors.each do |error| %>
                <li><%= error %></li>
              <% end %>
            </ul>
          </div>
        </div>
      </div>
    </div>
  <% end %>
  
  <div class="bg-white shadow sm:rounded-lg">
    <div class="px-4 py-5 sm:px-6">
      <h3 class="text-lg leading-6 font-medium text-stone-900">Review Your Application</h3>
      <p class="mt-1 max-w-2xl text-sm text-stone-500">Please review your application before submitting</p>
    </div>

    <div class="border-t border-stone-200">
      <dl class="divide-y divide-stone-200">
        <div class="px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
          <dt class="text-sm font-medium text-stone-500">Job Title</dt>
          <dd class="mt-1 text-sm text-stone-900 sm:mt-0 sm:col-span-2"><%= @job.title %></dd>
        </div>

        <div class="px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
          <dt class="text-sm font-medium text-stone-500">Cover Letter</dt>
          <dd class="mt-1 text-sm text-stone-900 sm:mt-0 sm:col-span-2"><%= simple_format @job_application.application_letter %></dd>
        </div>

        <div class="px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
          <dt class="text-sm font-medium text-stone-500">Resume</dt>
          <dd class="mt-1 text-sm text-stone-900 sm:mt-0 sm:col-span-2">
            <% if @job_application.resume.attached? %>
              <%= @job_application.resume.filename %>
            <% end %>
          </dd>
        </div>

        <div class="px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
          <dt class="text-sm font-medium text-stone-500">Additional Information</dt>
          <dd class="mt-1 text-sm text-stone-900 sm:mt-0 sm:col-span-2">
            <%= simple_format @job_application.additional_info %>
          </dd>
        </div>
      </dl>

      <div class="border-t border-stone-200">
        <%= form_with(model: @job_application, 
                  url: talent_job_job_applications_path(@job),
                  method: :post,
                  local: true) do |f| %>
          <%= hidden_field_tag :step, 5 %>
          <%= hidden_field_tag :final_submission, true %>
          
          <!-- Preserve all application data in hidden fields -->
          <%= f.hidden_field :application_letter %>
          <%= f.hidden_field :additional_info %>
          
          <!-- If resume is attached, we need to preserve it -->
          <% if @job_application.resume.attached? %>
            <%= f.hidden_field :resume, value: @job_application.resume.signed_id %>
          <% end %>
          
          <!-- If documents are attached, preserve them -->
          <% if @job_application.documents.attached? %>
            <% @job_application.documents.each do |document| %>
              <%= hidden_field_tag 'job_application[documents][]', document.signed_id %>
            <% end %>
          <% end %>
          
          <div class="px-4 py-3 bg-stone-50 text-right sm:px-6">
            <%= link_to "Back", new_talent_job_job_application_path(@job, step: 4), 
                class: "inline-flex justify-center py-2 px-4 border border-stone-300 shadow-sm text-sm font-medium rounded-md text-stone-700 bg-white hover:bg-stone-50" %>
            <%= f.submit "Submit Application", class: "ml-3 inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700" %>
          </div>
        <% end %>
      </div>
    </div>
  </div>
</div>