<div class="flex flex-col h-full bg-white border rounded-md border-stone-200">
  <div class="flex-1 w-full px-4 py-8 mx-auto max-w-7xl sm:px-6 lg:px-8">

    <div class="px-16 mb-8">
      <%= link_to talent_job_applications_path, class: "inline-flex items-center text-sm font-medium text-stone-600 hover:text-stone-900" do %>
        <%= phosphor_icon "arrow-left", class: "h-4 w-4 mr-1" %>
        Back to Applications
      <% end %>
    </div>

    <div class="flex items-center justify-between px-16 mb-6">
      <div>
        <h1 class="text-2xl font-bold text-stone-900"><%= @job_application.job.title %></h1>
        <p class="max-w-2xl mt-1 text-sm leading-6 text-stone-500">
          Application submitted <%= time_ago_in_words(@job_application.applied_at) %> ago
        </p>
      </div>
      <div class="flex items-center space-x-3">
        <% status_colors = {
          "pending" => "bg-yellow-100 text-yellow-800",
          "under_review" => "bg-blue-100 text-blue-800",
          "accepted" => "bg-green-100 text-green-800",
          "rejected" => "bg-red-100 text-red-800",
          "withdrawn" => "bg-stone-100 text-stone-800"
        } %>

        <%= link_to edit_talent_job_application_path(@job_application), class: "inline-flex items-center px-4 py-2 text-sm font-medium text-stone-50 bg-stone-900 rounded-md shadow-sm hover:bg-stone-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500" do %>
          Edit Application
        <% end %>
      </div>
    </div>

    <!-- Application Overview Section -->
    <div class="px-16">
      <div class="sm:px-0">
        <h3 class="text-base font-semibold leading-7 text-stone-900">Application Overview</h3>
        <p class="max-w-2xl mt-1 text-sm leading-6 text-stone-500">Your application details</p>
      </div>

      <div class="mt-6 border-t border-stone-100">
        <dl class="divide-y divide-stone-100">
          <div class="py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
            <dt class="text-sm font-medium leading-6 text-stone-900">Application Status</dt>
            <dd class="mt-1 text-sm leading-6 text-stone-700 sm:col-span-2 sm:mt-0">
              <% status_colors = {
                "applied" => "bg-yellow-100 text-yellow-800 border border-yellow-800/30 ",
                "reviewed" => "bg-blue-100 text-blue-800 border border-blue-800/30",
                "qualified" => "bg-purple-100 text-purple-800",
                "offered" => "bg-indigo-100 text-indigo-800",
                "accepted" => "bg-green-100 text-green-800",
                "withdrawn" => "bg-stone-100 text-stone-800"
              } %>
              <div class="max-w-md p-4 border rounded-md border-stone-200 bg-stone-50">
                <div class="flex items-center">
                  <span class="inline-flex items-center justify-center px-3 py-1 mr-3 text-sm font-medium rounded-md <%= status_colors[@job_application.status] %>">
                    <%= @job_application.status.humanize %>
                  </span>
                </div>
                
                <% case @job_application.status %>
                <% when "applied" %>
                  <p class="mt-2 text-sm text-stone-600">Your application is being reviewed by the employer.</p>
                <% when "reviewed" %>
                  <p class="mt-2 text-sm text-stone-600">Your application has been reviewed by the employer.</p>
                <% when "qualified" %>
                  <p class="mt-2 text-sm text-stone-600">Congratulations! You've been qualified for this position.</p>
                <% when "offered" %>
                  <p class="mt-2 text-sm text-stone-600">Congratulations! You've received an offer for this position.</p>
                <% when "accepted" %>
                  <p class="mt-2 text-sm text-stone-600">You've accepted this job offer. Congratulations!</p>
                <% when "withdrawn" %>
                  <p class="mt-2 text-sm text-stone-600">You've withdrawn this application.</p>
                <% end %>
              </div>
            </dd>
          </div>

          <div class="py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
            <dt class="text-sm font-medium leading-6 text-stone-900">Application Letter</dt>
            <dd class="mt-1 space-y-4 text-sm leading-6 text-stone-700 sm:col-span-2 sm:mt-0">
              <% if @job_application.application_letter.present? %>
                <%= simple_format @job_application.application_letter %>
              <% else %>
                <div class="flex items-center justify-center p-4 border border-dashed rounded-md text-stone-500 border-stone-300 bg-stone-50">
                  <%= phosphor_icon "file-text", class: "h-5 w-5 mr-2" %>
                  <span>No application letter provided</span>
                </div>
              <% end %>
            </dd>
          </div>
          
          <div class="py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
            <dt class="text-sm font-medium leading-6 text-stone-900">Resume</dt>
            <dd class="mt-1 text-sm leading-6 text-stone-700 sm:col-span-2 sm:mt-0">
              <% if @job_application.resume.attached? %>
                <div class="flex items-center justify-between p-4 border rounded-md border-stone-200 bg-stone-50">
                  <div class="flex items-center">
                    <%= phosphor_icon "file-pdf", class: "w-5 h-5 mr-2 text-stone-600" %>
                    <span class="text-sm font-medium text-stone-900"><%= @job_application.resume.filename %></span>
                  </div>
                  <%= link_to rails_blob_path(@job_application.resume, disposition: 'attachment'), class: "text-sm text-stone-600 hover:text-stone-900" do %>
                    <%= phosphor_icon "download", class: "w-4 h-4 inline mr-1" %>
                    Download
                  <% end %>
                </div>
              <% else %>
                <div class="flex items-center justify-center p-4 border border-dashed rounded-md text-stone-500 border-stone-300 bg-stone-50">
                  <%= phosphor_icon "file-x", class: "h-5 w-5 mr-2" %>
                  <span>No resume uploaded</span>
                </div>
              <% end %>
            </dd>
          </div>

          <div class="py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
            <dt class="text-sm font-medium leading-6 text-stone-900">Additional Documents</dt>
            <dd class="mt-1 text-sm leading-6 text-stone-700 sm:col-span-2 sm:mt-0">
              <% if @job_application.documents.attached? && @job_application.documents.any? %>
                <div class="space-y-3">
                  <% @job_application.documents.each do |document| %>
                    <div class="flex items-center justify-between p-4 border rounded-md border-stone-200 bg-stone-50">
                      <div class="flex items-center">
                        <%= phosphor_icon "file-pdf", class: "w-5 h-5 mr-2 text-stone-600" %>
                        <span class="text-sm font-medium text-stone-900"><%= document.filename %></span>
                      </div>
                      <%= link_to rails_blob_path(document, disposition: 'attachment'), class: "text-sm text-stone-600 hover:text-stone-900" do %>
                        <%= phosphor_icon "download", class: "w-4 h-4 inline mr-1" %>
                        Download
                      <% end %>
                    </div>
                  <% end %>
                </div>
              <% else %>
                <div class="flex items-center justify-center p-4 border border-dashed rounded-md text-stone-500 border-stone-300 bg-stone-50">
                  <%= phosphor_icon "files", class: "h-5 w-5 mr-2" %>
                  <span>No additional documents provided</span>
                </div>
              <% end %>
            </dd>
          </div>
        </dl>
      </div>
    </div>

    <!-- Actions Section -->
    <div class="px-16 pt-8">

      <div class="py-6 mt-6 border-t border-stone-100">
        <div class="flex justify-end">
          <%= button_to withdraw_talent_job_application_path(@job_application),
                      method: :post,
                      class: "inline-flex items-center px-4 py-2 border border-stone-300 shadow-sm text-sm font-medium rounded-md text-stone-700 bg-white hover:bg-stone-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500",
                      data: { confirm: "Are you sure you want to withdraw this application?" } do %>
            <%= phosphor_icon "x-circle", class: "h-4 w-4 mr-1" %>
            Withdraw Application
          <% end %>
        </div>
      </div>
    </div>

  </div>
</div>
