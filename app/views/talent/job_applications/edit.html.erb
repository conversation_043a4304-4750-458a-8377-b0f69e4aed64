<div class="flex flex-col h-full bg-white border rounded-md border-stone-200">
  <div class="flex-1 w-full px-4 py-8 mx-auto max-w-7xl sm:px-6 lg:px-8">
    <div class="px-16 mb-8">
      <%= link_to talent_job_application_path(@job_application), class: "inline-flex items-center text-sm font-medium text-stone-600 hover:text-stone-900" do %>
        <%= phosphor_icon "arrow-left", class: "h-4 w-4 mr-1" %>
        Back to Application
      <% end %>
    </div>
    
    <div class="flex flex-col px-16 mb-8">
      <h1 class="text-2xl font-bold text-stone-900">
        Edit Application
      </h1>
      <p class="mt-1 text-sm leading-6 text-stone-600">
        Update your application for <%= @job_application.job.title %>
      </p>
    </div>

    <%= form_with(model: @job_application, 
                url: talent_job_application_path(@job_application),
                method: :patch,
                class: "px-16",
                multipart: true, 
                data: { controller: 'application-wizard' }) do |form| %>
      <% if @errors.present? %>
        <div class="p-4 mb-6 rounded-md bg-red-50">
          <div class="flex">
            <div class="flex-shrink-0">
              <%= phosphor_icon "warning-circle", class: "w-5 h-5 text-red-400" %>
            </div>
            <div class="ml-3">
              <h3 class="text-sm font-medium text-red-800">
                Please fix the following issues:
              </h3>
              <div class="mt-2 text-sm text-red-700">
                <ul class="pl-5 space-y-1 list-disc">
                  <% @errors.each do |error| %>
                    <li><%= error %></li>
                  <% end %>
                </ul>
              </div>
            </div>
          </div>
        </div>
      <% end %>

      <div class="space-y-12">
        <!-- Cover Letter Section -->
        <div class="grid grid-cols-2 py-12 border-y gap-x-8 gap-y-10 border-stone-900/10 md:grid-cols-3">
          <div>
            <h2 class="text-base font-semibold leading-7 text-stone-900">Cover Letter</h2>
            <p class="mt-1 text-sm leading-6 text-stone-600">Share why you're a great fit for this position</p>
          </div>

          <div class="grid max-w-2xl grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-6 md:col-span-2">
            <div class="sm:col-span-6">
              <%= form.label :application_letter, class: "block text-sm font-medium leading-6 text-stone-900" %>
              <div class="mt-2">
                <%= form.text_area :application_letter, rows: 8, required: true,
                    class: "block w-full rounded-md border-0 py-1.5 text-stone-900 shadow-sm ring-1 ring-inset ring-stone-300 placeholder:text-stone-400 focus:ring-2 focus:ring-inset focus:ring-stone-600 sm:text-sm sm:leading-6",
                    data: { action: "input->application-wizard#handleInput" } %>
              </div>
              <p class="mt-3 text-sm leading-6 text-stone-600">Explain your interest and qualifications for this role.</p>
            </div>
          </div>
        </div>

        <!-- Resume Section -->
        <div class="grid grid-cols-2 py-12 border-b gap-x-8 gap-y-10 border-stone-900/10 md:grid-cols-3">
          <div>
            <h2 class="text-base font-semibold leading-7 text-stone-900">Resume</h2>
            <p class="mt-1 text-sm leading-6 text-stone-600">Update your resume for this application</p>
          </div>

          <div class="grid max-w-2xl grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-6 md:col-span-2">
            <div class="sm:col-span-6">
              <% if @job_application.resume.attached? %>
                <div class="p-4 mb-4 border rounded-md border-stone-200 bg-stone-50">
                  <div class="flex items-center justify-between">
                    <div class="flex items-center">
                      <%= phosphor_icon "file-pdf", class: "w-5 h-5 mr-2 text-stone-600" %>
                      <span class="text-sm font-medium text-stone-900"><%= @job_application.resume.filename %></span>
                    </div>
                    <%= link_to rails_blob_path(@job_application.resume, disposition: 'attachment'), class: "text-sm text-stone-600 hover:text-stone-900" do %>
                      <%= phosphor_icon "download", class: "w-4 h-4 inline mr-1" %>
                      Download
                    <% end %>
                  </div>
                </div>
              <% end %>
              
              <%= form.label :resume, "Upload New Resume", class: "block text-sm font-medium leading-6 text-stone-900" %>
              <div class="mt-2">
                <%= form.file_field :resume, accept: ".pdf,.doc,.docx",
                    class: "block w-full rounded-md border-0 py-1.5 text-stone-900 shadow-sm ring-1 ring-inset ring-stone-300 placeholder:text-stone-400 focus:ring-2 focus:ring-inset focus:ring-stone-600 sm:text-sm sm:leading-6",
                    data: { action: "change->application-wizard#handleInput" } %>
              </div>
              <p class="mt-3 text-sm leading-6 text-stone-600">
                <%= @job_application.resume.attached? ? "Upload a new file to replace your current resume" : "Upload your resume (PDF, DOC, or DOCX)" %>
              </p>
            </div>
          </div>
        </div>

        <!-- Additional Documents Section -->
        <div class="grid grid-cols-2 py-12 border-b gap-x-8 gap-y-10 border-stone-900/10 md:grid-cols-3">
          <div>
            <h2 class="text-base font-semibold leading-7 text-stone-900">Supporting Documents</h2>
            <p class="mt-1 text-sm leading-6 text-stone-600">Share additional portfolio items or work samples</p>
          </div>

          <div class="grid max-w-2xl grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-6 md:col-span-2">
            <div class="sm:col-span-6">
              <% if @job_application.documents.attached? && @job_application.documents.any? %>
                <div class="p-4 mb-4 border rounded-md border-stone-200 bg-stone-50">
                  <h3 class="mb-2 text-sm font-medium text-stone-900">Current documents:</h3>
                  <ul class="space-y-2">
                    <% @job_application.documents.each do |document| %>
                      <li class="flex items-center justify-between">
                        <div class="flex items-center">
                          <%= phosphor_icon "file-text", class: "w-4 h-4 mr-2 text-stone-600" %>
                          <span class="text-sm text-stone-900"><%= document.filename %></span>
                        </div>
                        <%= link_to rails_blob_path(document, disposition: 'attachment'), class: "text-sm text-stone-600 hover:text-stone-900" do %>
                          <%= phosphor_icon "download", class: "w-4 h-4 inline mr-1" %>
                          Download
                        <% end %>
                      </li>
                    <% end %>
                  </ul>
                </div>
              <% end %>
              
              <%= form.label :documents, "Upload Additional Documents", class: "block text-sm font-medium leading-6 text-stone-900" %>
              <div class="mt-2">
                <%= form.file_field :documents, multiple: true, accept: ".pdf,.doc,.docx",
                    class: "block w-full rounded-md border-0 py-1.5 text-stone-900 shadow-sm ring-1 ring-inset ring-stone-300 placeholder:text-stone-400 focus:ring-2 focus:ring-inset focus:ring-stone-600 sm:text-sm sm:leading-6" %>
              </div>
              <p class="mt-3 text-sm leading-6 text-stone-600">
                Upload any additional documents to support your application (PDF, DOC, or DOCX).
                New documents will be added to your current ones.
              </p>
            </div>
          </div>
        </div>

        <!-- Additional Information Section -->
        <div class="grid grid-cols-2 py-12 gap-x-8 gap-y-10 md:grid-cols-3">
          <div>
            <h2 class="text-base font-semibold leading-7 text-stone-900">Additional Information</h2>
            <p class="mt-1 text-sm leading-6 text-stone-600">Provide any extra details to strengthen your application</p>
          </div>

          <div class="grid max-w-2xl grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-6 md:col-span-2">
            <div class="sm:col-span-6">
              <%= form.label :additional_info, class: "block text-sm font-medium leading-6 text-stone-900" %>
              <div class="mt-2">
                <%= form.text_area :additional_info, rows: 4,
                    class: "block w-full rounded-md border-0 py-1.5 text-stone-900 shadow-sm ring-1 ring-inset ring-stone-300 placeholder:text-stone-400 focus:ring-2 focus:ring-inset focus:ring-stone-600 sm:text-sm sm:leading-6",
                    data: { action: "input->application-wizard#handleInput" } %>
              </div>
              <p class="mt-3 text-sm leading-6 text-stone-600">Share any other relevant information about your experience and qualifications.</p>
            </div>
          </div>
        </div>
      </div>

      <div class="flex items-center justify-end mt-6 gap-x-6">
        <%= link_to "Cancel", talent_job_application_path(@job_application), class: "text-sm font-semibold leading-6 text-stone-900" %>
        <%= form.submit "Update Application", class: "px-3 py-2 text-sm font-semibold text-stone-50 bg-stone-900 rounded-md shadow-sm hover:bg-stone-700 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-stone-600", 
            data: { application_wizard_target: "submitButton" } %>
      </div>
    <% end %>
  </div>
</div>
