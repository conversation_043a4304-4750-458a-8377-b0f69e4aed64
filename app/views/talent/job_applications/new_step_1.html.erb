<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
  <%= render 'talent/shared/application_progress', current_step: 1 %>
  
  <div class="bg-white shadow overflow-hidden sm:rounded-lg">
    <div class="px-4 py-5 sm:px-6">
      <h3 class="text-lg leading-6 font-medium text-stone-900">
        Job Overview
      </h3>
      <p class="mt-1 max-w-2xl text-sm text-stone-500">
        Review the job details before proceeding with your application
      </p>
    </div>

    <div class="border-t border-stone-200 px-4 py-5 sm:px-6">
      <dl class="grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2">
        <div class="sm:col-span-2">
          <dt class="text-sm font-medium text-stone-500">Job Title</dt>
          <dd class="mt-1 text-sm text-stone-900"><%= @job.title %></dd>
        </div>

        <div class="sm:col-span-2">
          <dt class="text-sm font-medium text-stone-500">Description</dt>
          <dd class="mt-1 text-sm text-stone-900"><%= @job.description %></dd>
        </div>
      </dl>
    </div>

    <div class="px-4 py-3 bg-stone-50 text-right sm:px-6">
      <%= form_tag talent_job_job_applications_path(@job), method: :post do %>
        <%= hidden_field_tag :step, 2 %>
        <%= submit_tag "Continue to Application", 
            class: "ml-3 inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" %>
      <% end %>
    </div>
  </div>
</div>