<div class="flex flex-col h-full bg-white border rounded-md border-stone-200">
  <div class="flex-1 w-full px-16 py-8 mx-auto max-w-7xl">
    <% if @job_applications.any? %>

    <div class="mb-2 text-2xl font-semibold text-stone-900">
    Applications
    </div>
    <div class="pb-2 mb-4 text-sm text-stone-600 ">
Here is a list of applications sent by you.
    </div >
      
    <!-- Segmented Control for Application Status -->
    <div class="pb-4 mb-6 border-stone-200 text-stone-600 ">
      <div class="inline-flex rounded-md shadow-sm" role="group">
        <% if @all_applications_count.to_i > 0 %>
          <%= link_to talent_job_applications_path, class: "px-4 py-2 text-sm font-medium #{params[:status].blank? ? 'text-white bg-stone-900' : 'text-stone-700 bg-white hover:bg-stone-50'} border border-stone-200 rounded-l-lg focus:z-10 focus:ring-2 focus:ring-[#6100FF] focus:text-[#6100FF]" do %>
            All <span class="ml-1 text-xs opacity-70">(<%= @all_applications_count || 0 %>)</span>
          <% end %>
        <% else %>
          <span class="px-4 py-2 text-sm font-medium border rounded-l-lg cursor-not-allowed text-stone-400 bg-stone-100 border-stone-200">
            All <span class="ml-1 text-xs opacity-70">(0)</span>
          </span>
        <% end %>
        
        <% if @applied_count.to_i > 0 %>
          <%= link_to talent_job_applications_path(status: "applied"), class: "px-4 py-2 text-sm font-medium #{params[:status] == 'applied' ? 'text-white bg-stone-900' : 'text-stone-700 bg-white hover:bg-stone-50'} border-t border-b border-r border-stone-200 focus:z-10 focus:ring-2 focus:ring-[#6100FF] focus:text-[#6100FF]" do %>
            Applied <span class="ml-1 text-xs opacity-70">(<%= @applied_count || 0 %>)</span>
          <% end %>
        <% else %>
          <span class="px-4 py-2 text-sm font-medium border-t border-b border-r cursor-not-allowed text-stone-400 bg-stone-100 border-stone-200">
            Applied <span class="ml-1 text-xs opacity-70">(0)</span>
          </span>
        <% end %>
        
        <% if @reviewed_count.to_i > 0 %>
          <%= link_to talent_job_applications_path(status: "reviewed"), class: "px-4 py-2 text-sm font-medium #{params[:status] == 'reviewed' ? 'text-white bg-stone-900' : 'text-stone-700 bg-white hover:bg-stone-50'} border-t border-b border-r border-stone-200 focus:z-10 focus:ring-2 focus:ring-[#6100FF] focus:text-[#6100FF]" do %>
            Reviewed <span class="ml-1 text-xs opacity-70">(<%= @reviewed_count || 0 %>)</span>
          <% end %>
        <% else %>
          <span class="px-4 py-2 text-sm font-medium border-t border-b border-r cursor-not-allowed text-stone-400 bg-stone-100 border-stone-200">
            Reviewed <span class="ml-1 text-xs opacity-70">(0)</span>
          </span>
        <% end %>
        
        <% if @qualified_count.to_i > 0 %>
          <%= link_to talent_job_applications_path(status: "qualified"), class: "px-4 py-2 text-sm font-medium #{params[:status] == 'qualified' ? 'text-white bg-stone-900' : 'text-stone-700 bg-white hover:bg-stone-50'} border-t border-b border-r border-stone-200 focus:z-10 focus:ring-2 focus:ring-[#6100FF] focus:text-[#6100FF]" do %>
            Qualified <span class="ml-1 text-xs opacity-70">(<%= @qualified_count || 0 %>)</span>
          <% end %>
        <% else %>
          <span class="px-4 py-2 text-sm font-medium border-t border-b border-r cursor-not-allowed text-stone-400 bg-stone-100 border-stone-200">
            Qualified <span class="ml-1 text-xs opacity-70">(0)</span>
          </span>
        <% end %>
        
        <% if @offered_count.to_i > 0 %>
          <%= link_to talent_job_applications_path(status: "offered"), class: "px-4 py-2 text-sm font-medium #{params[:status] == 'offered' ? 'text-white bg-stone-900' : 'text-stone-700 bg-white hover:bg-stone-50'} border-t border-b border-r border-stone-200 focus:z-10 focus:ring-2 focus:ring-[#6100FF] focus:text-[#6100FF]" do %>
            Offered <span class="ml-1 text-xs opacity-70">(<%= @offered_count || 0 %>)</span>
          <% end %>
        <% else %>
          <span class="px-4 py-2 text-sm font-medium border-t border-b border-r cursor-not-allowed text-stone-400 bg-stone-100 border-stone-200">
            Offered <span class="ml-1 text-xs opacity-70">(0)</span>
          </span>
        <% end %>
        
        <% if @accepted_count.to_i > 0 %>
          <%= link_to talent_job_applications_path(status: "accepted"), class: "px-4 py-2 text-sm font-medium #{params[:status] == 'accepted' ? 'text-white bg-stone-900' : 'text-stone-700 bg-white hover:bg-stone-50'} border-t border-b border-r border-stone-200 focus:z-10 focus:ring-2 focus:ring-[#6100FF] focus:text-[#6100FF]" do %>
            Accepted <span class="ml-1 text-xs opacity-70">(<%= @accepted_count || 0 %>)</span>
          <% end %>
        <% else %>
          <span class="px-4 py-2 text-sm font-medium border-t border-b border-r cursor-not-allowed text-stone-400 bg-stone-100 border-stone-200">
            Accepted <span class="ml-1 text-xs opacity-70">(0)</span>
          </span>
        <% end %>
        
        <% if @withdrawn_count.to_i > 0 %>
          <%= link_to talent_job_applications_path(status: "withdrawn"), class: "px-4 py-2 text-sm font-medium #{params[:status] == 'withdrawn' ? 'text-white bg-stone-900' : 'text-stone-700 bg-white hover:bg-stone-50'} border-t border-b border-r border-stone-200 rounded-r-lg focus:z-10 focus:ring-2 focus:ring-[#6100FF] focus:text-[#6100FF]" do %>
            Withdrawn <span class="ml-1 text-xs opacity-70">(<%= @withdrawn_count || 0 %>)</span>
          <% end %>
        <% else %>
          <span class="px-4 py-2 text-sm font-medium border-t border-b border-r rounded-r-lg cursor-not-allowed text-stone-400 bg-stone-100 border-stone-200">
            Withdrawn <span class="ml-1 text-xs opacity-70">(0)</span>
          </span>
        <% end %>
      </div>
    </div>
      <div class="border rounded-md border-stone-200">
        <% @job_applications.each do |application| %>
          <div class="p-6 border-b last:border-none border-stone-200">
            <div class="flex items-start justify-between">
              <div class="flex-1">

                  <div class="flex items-center ">
                  <%= link_to talent_job_path(application.job), class: " mr-4 text-lg font-medium text-stone-900 hover:underline" do %>
                   <%= application.job.title %> 
                  <% end %>

                  <p class="<%= status_color_class(application.status)%> text-xs font-medium px-2 py-0.5 rounded-md ">
                  <%= application.status.capitalize %>
                </p>
   
                  </div>
                <p class="mt-1 text-sm text-stone-600">
                  <%= application.job.organization.name %>
                </p>
               
                <% if application.application_letter.present? %>
                  <div class="p-4 mt-4 border rounded-md border-stone-200 bg-stone-100">
                    <h4 class="mb-2 text-sm font-medium text-stone-700">Information sent:</h4>
                    <p class="text-sm text-stone-600"><%= application.application_letter %></p>
                  </div>
                <% end %>
                
                <div class="flex items-center justify-between gap-3 mt-4">
                  <div>


                  </div>

                  <div class="flex">
                  <%= link_to talent_job_path(application.job), class: "mr-2 inline-flex items-center px-4 py-2 text-sm font-medium rounded-md text-stone-700 bg-white hover:text-stone-900 underline focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-stone-500" do %>
                   View Job 
                  <% end %>
                  <%= link_to talent_job_application_path(application), method: :post, class: "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-stone-900 hover:bg-stone-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-stone-500" do %>
                   Edit my Application 
                  <% end %>

                  </div>
                </div>
              </div>
            </div>
          </div>
        <% end %>
      </div>
    <% else %>
      <div class="flex flex-col items-center justify-center h-[70vh]">
        <%= phosphor_icon "tray", style: "duotone", class: "w-16 h-16"%>
        <h3 class="mt-4 text-lg font-medium text-stone-900">No applications</h3>
        <p class="mt-1 text-sm text-stone-500">
          You don't have any job applications at the moment.
        </p>
        <div class="mt-8">
          <%= link_to talent_jobs_path, class: "inline-flex items-center px-6 py-3 text-base font-medium text-white bg-stone-900 border border-transparent rounded-md shadow-sm hover:bg-stone-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-stone-500" do %>
            Source Jobs
            <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5 ml-2 -mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6" />
            </svg>
          <% end %>
        </div>
      </div>
    <% end %>
  </div>
</div>
