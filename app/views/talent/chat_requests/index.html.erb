<div class="max-w-4xl mx-auto p-6">
  <div class="mb-6">
    <h1 class="text-2xl font-bold text-stone-900">Chat Invitations</h1>
    <p class="text-stone-600 mt-2">Scouts who want to start a conversation with you</p>
  </div>

  <% if @pending_requests.any? %>
    <div class="space-y-4">
      <% @pending_requests.each do |chat_request| %>
        <div class="bg-white border border-stone-200 rounded-lg p-6 shadow-sm">
          <div class="flex items-start justify-between">
            <div class="flex items-start space-x-4">
              <!-- Scout Avatar -->
              <div class="flex-shrink-0">
                <% if chat_request.scout.avatar.attached? %>
                  <%= image_tag chat_request.scout.avatar, 
                      class: "h-12 w-12 rounded-full object-cover" %>
                <% else %>
                  <div class="h-12 w-12 rounded-full bg-stone-100 flex items-center justify-center">
                    <span class="text-stone-600 font-medium text-sm">
                      <%= chat_request.scout.initials %>
                    </span>
                  </div>
                <% end %>
              </div>
              
              <!-- Scout Info -->
              <div class="flex-1">
                <h3 class="text-lg font-semibold text-stone-900">
                  <%= chat_request.scout.name %>
                </h3>
                <p class="text-stone-600 text-sm">
                  Wants to start a conversation with you
                </p>
                <p class="text-stone-500 text-xs mt-1">
                  Requested <%= time_ago_in_words(chat_request.requested_at) %> ago
                </p>
              </div>
            </div>
            
            <!-- Action Buttons -->
            <div class="flex space-x-3">
              <%= button_to accept_talent_chat_request_path(chat_request), 
                  method: :post,
                  class: "px-4 py-2 bg-green-600 text-white text-sm font-medium rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2" do %>
                Accept
              <% end %>
              
              <%= button_to decline_talent_chat_request_path(chat_request), 
                  method: :post,
                  class: "px-4 py-2 bg-stone-200 text-stone-700 text-sm font-medium rounded-md hover:bg-stone-300 focus:outline-none focus:ring-2 focus:ring-stone-500 focus:ring-offset-2",
                  data: { confirm: "Are you sure you want to decline this chat request?" } do %>
                Decline
              <% end %>
            </div>
          </div>
        </div>
      <% end %>
    </div>
  <% else %>
    <div class="text-center py-12">
      <div class="mx-auto h-24 w-24 text-stone-400 mb-4">
        <%= phosphor_icon "chat-circle-dots", class: "h-24 w-24" %>
      </div>
      <h3 class="text-lg font-medium text-stone-900 mb-2">No chat invitations</h3>
      <p class="text-stone-600">
        When scouts want to chat with you, their requests will appear here.
      </p>
    </div>
  <% end %>
</div>
