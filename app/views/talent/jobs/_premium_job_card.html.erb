<!-- Premium Dark Sidebar Job Card -->
<div class="relative pb-8">
  <div class="flex overflow-hidden bg-white border rounded-lg shadow-lg border-stone-200">
    <!-- Premium Badge -->
    <div class="absolute z-10 -top-3 right-6">
      <span class="inline-flex items-center px-3 py-1 text-xs font-bold text-white rounded-md shadow-sm bg-amber-500 bg-gradient-to-r from-amber-500 to-yellow-400">
        <%= phosphor_icon "medal", class: "text-white w-4 h-4" %>
        PREMIUM
      </span>
    </div>
    <!-- Dark Sidebar -->
    <div class="flex flex-col items-center w-16 py-5 bg-stone-900">
      <% org_initial = job.organization&.name&.first || 'B' %>
      <div class="flex items-center justify-center w-10 h-10 mb-4 text-xl font-bold bg-white rounded-full text-stone-900">
        <%= org_initial %>
      </div>
      <div class="w-8 h-1 mb-4 bg-red-500 rounded-full"></div>
      <div class="flex flex-col items-center gap-3 mt-auto">
        <%= render "talent/jobs/save_button", job: job %>
        <%# Removed eye button link_to %>
      </div>
    </div>
    
    <div class="flex-1 p-6">
      <!-- Header Section -->
      <div class="pb-4 mb-5 border-b border-stone-200">
        <div class="flex items-start justify-between">
          <div>
            <div class="flex items-center gap-2 mb-1">
              <span class="px-2 py-0.5 text-xs font-medium rounded-full bg-<%= job.active? ? 'green-100 text-green-700' : 'red-100 text-red-700' %>">
                <%= job.active? ? "Active" : "Expired" %>
              </span>
              <span class="text-sm text-stone-500"><%= job.organization&.name || "Organization" %></span>
            </div>
            <h3 class="text-xl font-bold text-stone-900"><%= job.title %></h3>
          </div>
          <div class="flex items-center gap-2">
            <div class="flex items-center gap-1 text-sm text-stone-500">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-4 h-4">
                <circle cx="12" cy="12" r="10"></circle>
                <polyline points="12 6 12 12 16 14"></polyline>
              </svg>
              <span>Posted <%= time_ago_in_words(job.created_at) %> ago</span>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Application Deadline - Enhanced -->
      <div class="mb-6 bg-<%= job.active? ? 'blue-50 border-blue-100' : 'red-50 border-red-100' %> rounded-md p-4">
        <div class="flex items-center">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-6 w-6 mr-3 text-<%= job.active? ? 'blue-500' : 'red-500' %>">
            <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
            <line x1="16" y1="2" x2="16" y2="6"></line>
            <line x1="8" y1="2" x2="8" y2="6"></line>
            <line x1="3" y1="10" x2="21" y2="10"></line>
          </svg>
          <div>
            <div class="text-sm font-semibold text-<%= job.active? ? 'blue-800' : 'red-800' %>">APPLICATION DEADLINE</div>
            <div class="text-lg font-medium text-stone-900">
              <%= job.application_deadline&.strftime("%b %-d, %Y at %I:%M %p") || "No deadline specified" %>
            </div>
          </div>
          <div class="ml-auto">
            <span class="px-3 py-1 text-sm font-medium rounded-md bg-<%= job.active? ? 'blue-200 text-blue-800' : 'red-200 text-red-800' %>">
              <%= job.active? ? "Accepting Applications" : "Applications Closed" %>
            </span>
          </div>
        </div>
      </div>
      
      <!-- Info Cards Grid -->
      <div class="grid grid-cols-3 gap-4 mb-6">
        <div class="p-3 border rounded-lg border-stone-200 bg-stone-50">
          <div class="mb-1 text-xs text-stone-500">Category</div>
          <div class="flex items-center text-sm font-medium text-stone-800">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4 mr-1.5 text-blue-600">
              <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
              <polyline points="14 2 14 8 20 8"></polyline>
              <line x1="16" y1="13" x2="8" y2="13"></line>
              <line x1="16" y1="17" x2="8" y2="17"></line>
              <polyline points="10 9 9 9 8 9"></polyline>
            </svg>
            <span><%= job.job_category.titleize %></span>
          </div>
        </div>
        <div class="p-3 border rounded-lg border-stone-200 bg-stone-50">
          <div class="mb-1 text-xs text-stone-500">Platform</div>
          <div class="flex items-center text-sm font-medium text-stone-800">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4 mr-1.5 text-blue-500">
              <path d="M23 3a10.9 10.9 0 0 1-3.14 1.53 4.48 4.48 0 0 0-7.86 3v1A10.66 10.66 0 0 1 3 4s-4 9 5 13a11.64 11.64 0 0 1-7 2c9 5 20 0 20-11.5a4.5 4.5 0 0 0-.08-.83A7.72 7.72 0 0 0 23 3z"></path>
            </svg>
            <span><%= job.platform.titleize %></span>
          </div>
        </div>
        <div class="p-3 border rounded-lg border-stone-200 bg-stone-50">
          <div class="mb-1 text-xs text-stone-500">Outcome</div>
          <div class="flex items-center text-sm font-medium text-stone-800">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4 mr-1.5 text-amber-500">
              <polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"></polygon>
            </svg>
            <span><%= job.outcome&.first || "Brand awareness" %></span>
          </div>
        </div>
        <div class="p-3 border rounded-lg border-stone-200 bg-stone-50">
          <div class="mb-1 text-xs text-stone-500">Budget</div>
          <div class="flex items-center text-sm font-medium text-stone-800">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4 mr-1.5 text-green-600">
              <line x1="12" y1="1" x2="12" y2="23"></line>
              <path d="M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"></path>
            </svg>
            <span><%= job.salary_range %></span>
          </div>
        </div>
        <div class="p-3 border rounded-lg border-stone-200 bg-stone-50">
          <div class="mb-1 text-xs text-stone-500">Payment</div>
          <div class="flex items-center text-sm font-medium text-stone-800">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4 mr-1.5 text-purple-600">
              <path d="M23 4v6h-6"></path>
              <path d="M1 20v-6h6"></path>
              <path d="M3.51 9a9 9 0 0 1 14.85-3.36L23 10"></path>
              <path d="M1 14l4.64 4.36A9 9 0 0 0 20.49 15"></path>
            </svg>
            <span><%= job.payment_frequency&.titleize %></span> <%# Use safe navigation %>
          </div>
        </div>
        <div class="p-3 border rounded-lg border-stone-200 bg-stone-50">
          <div class="mb-1 text-xs text-stone-500">Per Client</div>
          <div class="flex items-center text-sm font-medium text-stone-800">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4 mr-1.5 text-stone-700">
              <rect x="1" y="4" width="22" height="16" rx="2" ry="2"></rect>
              <line x1="1" y1="10" x2="23" y2="10"></line>
            </svg>
            <span><%= job.charge_per_client || "$200-500" %></span>
          </div>
        </div>
      </div>
      
      <!-- Job Details -->
      <div class="p-4 mb-6 bg-white border rounded-lg border-stone-200">
        <h4 class="mb-3 text-sm font-semibold text-stone-900">JOB DETAILS</h4>
        
        <!-- Description -->
        <div class="mb-4">
          <h5 class="mb-2 text-xs font-medium text-stone-500">Description</h5>
          <p class="text-sm text-stone-700"><%= job.description.truncate(150) %></p>
        </div>
        
        <!-- Topics and Goals Section -->
        <div class="grid gap-5 md:grid-cols-2">
          <div>
            <h5 class="mb-2 text-xs font-medium text-stone-500">Topics</h5>
            <div class="flex flex-wrap gap-2">
              <% (job.topics || ['E-Commerce & Dropshipping', 'SEO', 'Web 3']).each do |topic| %>
                <span class="inline-flex items-center px-3 py-1 text-xs font-medium border rounded-md text-stone-800 bg-stone-100 border-stone-200">
                  <%= topic %>
                </span>
              <% end %>
            </div>
          </div>
          <div>
            <h5 class="mb-2 text-xs font-medium text-stone-500">Goals</h5>
            <div class="text-sm text-stone-700">
              <% if job.outcome.present? %>
                <% job.outcome.each do |goal| %>
                  <div class="flex items-start mb-2">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4 mr-1.5 text-green-500 flex-shrink-0 mt-0.5">
                      <polyline points="20 6 9 17 4 12"></polyline>
                  </svg>
                  <span><%= goal %></span>
                </div>
              <% end %>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Client Information -->
      <div class="p-4 mb-5 border rounded-lg border-stone-200 bg-stone-50">
        <h4 class="mb-3 text-sm font-semibold text-stone-900">CLIENT INFORMATION</h4>
        <div class="grid grid-cols-2 gap-4 mb-4">
          <div class="p-3 bg-white border rounded-md border-stone-200">
            <div class="flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-5 h-5 mr-2 text-blue-500">
                <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
                <circle cx="9" cy="7" r="4"></circle>
                <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
                <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
              </svg>
              <div>
                <div class="text-xs text-stone-500">Client Count</div>
                <div class="text-sm font-semibold"><%= job.client_count || "100+" %></div>
              </div>
            </div>
          </div>
          <div class="p-3 bg-white border rounded-md border-stone-200">
            <div class="flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-5 h-5 mr-2 text-blue-500">
                <rect x="1" y="4" width="22" height="16" rx="2" ry="2"></rect>
                <line x1="1" y1="10" x2="23" y2="10"></line>
              </svg>
              <div>
                <div class="text-xs text-stone-500">Per Client Rate</div>
                <div class="text-sm font-semibold"><%= job.charge_per_client || "$200-500" %></div>
              </div>
            </div>
          </div>
        </div>
        <div class="p-3 bg-white border rounded-md border-stone-200">
          <div class="text-xs text-stone-600">
            <div class="mb-2">
              <span class="font-semibold text-stone-700">Challenge: </span>
              <%= job.business_challenge || "Specific challenges the client is facing that need to be addressed." %>
            </div>
            <div>
              <span class="font-semibold text-stone-700">Additional Info: </span>
              <%= job.useful_info || "Additional information about the project and requirements." %>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Footer with action button -->
      <div class="flex justify-end">
        <% if job.active? %>
          <% if Current.user.job_applications.exists?(job: job) %>
            <span class="px-6 py-2.5 rounded-md text-sm font-medium bg-green-100 text-green-800">
              Application Submitted
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="inline-block w-4 h-4 ml-2">
                <polyline points="20 6 9 17 4 12"></polyline>
              </svg>
            </span>
          <% else %>
            <%= link_to new_talent_job_job_application_path(job), class: "px-6 py-2.5 rounded-md text-sm font-medium bg-indigo-600 text-white hover:bg-indigo-700", data: { turbo: false } do %>
              Apply Now
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="inline-block w-4 h-4 ml-2">
                <line x1="5" y1="12" x2="19" y2="12"></line>
                <polyline points="12 5 19 12 12 19"></polyline>
              </svg>
            <% end %>
          <% end %>
        <% else %>
          <button class="px-6 py-2.5 rounded-md text-sm font-medium bg-stone-200 text-stone-500 cursor-not-allowed">
            Applications Closed
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="inline-block w-4 h-4 ml-2">
              <line x1="5" y1="12" x2="19" y2="12"></line>
              <polyline points="12 5 19 12 12 19"></polyline>
            </svg>
          </button>
        <% end %>
      </div>
    </div>
  </div>
</div>
