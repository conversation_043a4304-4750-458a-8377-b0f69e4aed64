<div class="mb-6" data-controller="filter-panel">
  <div class="flex items-center gap-4 mb-2">
    <div class="relative flex-1">
      <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
        <svg class="w-5 h-5 text-stone-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
          <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd" />
        </svg>
      </div>
      <%= form.text_field :search,
        placeholder: "Search jobs...",
        class: "w-full pl-10 pr-4 py-2 border border-stone-300 rounded-lg text-stone-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500",
        data: {
          action: "input->job-search#search input->filter-panel#updateActiveFilters",
          job_search_target: "input"
        } %>
    </div>
    
    <button type="button"
      class="flex items-center px-6 py-2 text-white rounded-lg bg-stone-900 hover:bg-stone-800"
      data-filter-panel-target="toggleButton"
      data-action="filter-panel#toggle">
      <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
        <path fill-rule="evenodd" d="M3 3a1 1 0 011-1h12a1 1 0 011 1v3a1 1 0 01-.293.707L12 11.414V15a1 1 0 01-.293.707l-2 2A1 1 0 018 17v-5.586L3.293 6.707A1 1 0 013 6V3z" clip-rule="evenodd" />
      </svg>
      Filters
    </button>
  </div>
  
  <!-- Active Filters Display -->
  <div class="flex flex-wrap items-center gap-2 mt-2 mb-4" data-filter-panel-target="activeFilters">
    <!-- Will be populated by the controller -->
  </div>
  
  <!-- Filter Panel -->
  <div class="hidden mb-6 bg-white border rounded-lg shadow-sm border-stone-200" data-filter-panel-target="panel">
    <div class="p-4">
      <div class="grid grid-cols-1 gap-6 md:grid-cols-3">
        <!-- Column 1: Job Details Section -->
        <div>
          <h3 class="mb-4 text-sm font-bold text-stone-900">JOB DETAILS</h3>
          
          <div class="mb-4">
            <label class="block mb-2 text-sm font-medium text-stone-700">Category</label>
            <div class="columns-2 gap-x-4">
              <% Job.job_categories.keys.each do |category| %>
                <div class="flex items-center mb-1 break-inside-avoid">
                  <%= form.check_box "filter[job_category][]", { 
                    class: "h-4 w-4 text-indigo-600 border-stone-300 rounded", 
                    data: { 
                      action: "change->job-search#search change->filter-panel#updateActiveFilters" 
                    } 
                  }, category, nil %>
                  <label class="ml-2 text-sm text-stone-700"><%= category.titleize %></label>
                </div>
              <% end %>
            </div>
          </div>
          
          <div class="mb-4">
            <label class="block mb-2 text-sm font-medium text-stone-700">Platform</label>
            <div class="columns-2 gap-x-4">
              <% Job.platforms.keys.each do |platform| %>
                <div class="flex items-center mb-1 break-inside-avoid">
                  <%= form.check_box "filter[platform][]", { 
                    class: "h-4 w-4 text-indigo-600 border-stone-300 rounded", 
                    data: { 
                      action: "change->job-search#search change->filter-panel#updateActiveFilters" 
                    } 
                  }, platform, nil %>
                  <label class="ml-2 text-sm text-stone-700"><%= platform.titleize %></label>
                </div>
              <% end %>
            </div>
          </div>
        </div>
        
        <!-- Column 2: Financial Details Section -->
        <div>
          <h3 class="mb-4 text-sm font-bold text-stone-900">FINANCIAL DETAILS</h3>
          
          <div class="mb-4">
            <label class="block mb-2 text-sm font-medium text-stone-700">Budget Range</label>
            <div class="space-y-2">
              <div class="flex items-center">
                <%= form.radio_button "filter[budget_range]", "", { 
                  class: "h-4 w-4 text-indigo-600 border-stone-300", 
                  data: { 
                    action: "change->job-search#search change->filter-panel#updateActiveFilters" 
                  }, 
                  checked: true 
                } %>
                <label class="ml-2 text-sm text-stone-700">Any</label>
              </div>
              <% Job.budget_ranges.keys.each do |range| %>
                <div class="flex items-center">
                  <%= form.radio_button "filter[budget_range]", range, { 
                    class: "h-4 w-4 text-indigo-600 border-stone-300", 
                    data: { 
                      action: "change->job-search#search change->filter-panel#updateActiveFilters" 
                    } 
                  } %>
                  <label class="ml-2 text-sm text-stone-700"><%= Job.new(budget_range: range).salary_range %></label>
                </div>
              <% end %>
            </div>
          </div>
          
          <div class="mb-4">
            <label class="block mb-2 text-sm font-medium text-stone-700">Payment Frequency</label>
            <div class="columns-2 gap-x-4">
              <% Job.payment_frequencies.keys.each do |frequency| %>
                <div class="flex items-center mb-1 break-inside-avoid">
                  <%= form.check_box "filter[payment_frequency][]", { 
                    class: "h-4 w-4 text-indigo-600 border-stone-300 rounded", 
                    data: { 
                      action: "change->job-search#search change->filter-panel#updateActiveFilters" 
                    } 
                  }, frequency, nil %>
                  <label class="ml-2 text-sm text-stone-700"><%= frequency.titleize %></label>
                </div>
              <% end %>
            </div>
          </div>
        </div>
        
        <!-- Column 3: Status & Topics Section -->
        <div>
          <h3 class="mb-4 text-sm font-bold text-stone-900">STATUS & TOPICS</h3>
          
          <div class="mb-4">
            <label class="block mb-2 text-sm font-medium text-stone-700">Job Status</label>
            <div class="space-y-2">
              <div class="flex items-center">
                <%= form.radio_button "filter[status]", "", { 
                  class: "h-4 w-4 text-indigo-600 border-stone-300", 
                  data: { 
                    action: "change->job-search#search change->filter-panel#updateActiveFilters" 
                  }, 
                  checked: true 
                } %>
                <label class="ml-2 text-sm text-stone-700">All Jobs</label>
              </div>
              <div class="flex items-center">
                <%= form.radio_button "filter[status]", "published", { 
                  class: "h-4 w-4 text-indigo-600 border-stone-300", 
                  data: { 
                    action: "change->job-search#search change->filter-panel#updateActiveFilters" 
                  } 
                } %>
                <label class="ml-2 text-sm text-stone-700">Active Jobs Only</label>
              </div>
              <div class="flex items-center">
                <%= form.radio_button "filter[status]", "expired", { 
                  class: "h-4 w-4 text-indigo-600 border-stone-300", 
                  data: { 
                    action: "change->job-search#search change->filter-panel#updateActiveFilters" 
                  } 
                } %>
                <label class="ml-2 text-sm text-stone-700">Expired Jobs</label>
              </div>
            </div>
          </div>
          
          <div class="mb-4">
            <label class="block mb-2 text-sm font-medium text-stone-700">Topics</label>
            <div class="columns-2 gap-x-4">
              <% Job::AVAILABLE_TOPICS.each do |topic| %>
                <div class="flex items-center mb-1 break-inside-avoid">
                  <%= form.check_box "filter[topics][]", { 
                    class: "h-4 w-4 text-indigo-600 border-stone-300 rounded", 
                    data: { 
                      action: "change->job-search#search change->filter-panel#updateActiveFilters" 
                    } 
                  }, topic, nil %>
                  <label class="ml-2 text-sm text-stone-700"><%= topic %></label>
                </div>
              <% end %>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Action Buttons -->
      <div class="flex items-center justify-end pt-4 mt-6 border-t border-stone-200">
        <button type="button"
          class="mr-3 text-stone-600 hover:text-stone-900"
          data-action="filter-panel#clearFilters"
          data-filter-panel-target="clearButton">
          Clear All Filters
        </button>
        
        <%= form.submit "Apply Filters",
          class: "px-4 py-2 bg-stone-900 text-white rounded-md hover:bg-stone-800",
          data: { action: "filter-panel#submitForm" } %>
      </div>
    </div>
  </div>
</div>
