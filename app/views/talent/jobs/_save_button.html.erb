<%# Partial for rendering the save/unsave job button within its Turbo Frame %>
<%= turbo_frame_tag "save_job_#{job.id}" do %>
  <%= button_to (job.saved_by?(Current.user) ? unsave_talent_job_path(job) : save_talent_job_path(job)),
      method: (job.saved_by?(Current.user) ? :delete : :post),
      # Determine class based on card type if needed, or use a generic one
      # Assuming a generic style for now, adjust if premium/regular need different styles
      class: "p-2 rounded-full text-stone-400 hover:text-stone-600 hover:bg-stone-100",
      data: { turbo_method: (job.saved_by?(Current.user) ? :delete : :post) } do %>
    <%# SVG logic remains the same %>
    <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5" width="24" height="24" viewBox="0 0 24 24" fill="<%= job.saved_by?(Current.user) ? 'currentColor' : 'none' %>" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
      <path d="m19 21-7-4-7 4V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2v16z"></path>
      <% unless job.saved_by?(Current.user) %>
        <line x1="12" x2="12" y1="7" y2="13"></line>
        <line x1="15" x2="9" y1="10" y2="10"></line>
      <% end %>
    </svg>
  <% end %>
<% end %>
