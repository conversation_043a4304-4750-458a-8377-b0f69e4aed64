<%# Partial for rendering a single conversation card %>
<%# Container div for the card and the absolute positioned button %>
<div class="relative p-4 border rounded-md transition duration-150 ease-in-out #{'bg-blue-50 border-blue-200' if local_assigns[:selected_conversation] == conversation} #{'hover:bg-stone-50' unless local_assigns[:selected_conversation] == conversation}">

  <%# --- Bookmark But<PERSON> (Positioned top-right) --- %>
  <% participant = conversation.conversation_participants.find_by(user: Current.user) %>
  <div class="absolute z-10 top-2 right-2"> <%# z-index to ensure it's clickable over the link %>
    <% if participant&.bookmarked? %>
      <%= button_to unbookmark_talent_conversation_path(conversation, status: params[:status]),
                    method: :post,
                    class: "p-1 text-yellow-500 hover:text-yellow-600",
                    title: "Unbookmark",
                    data: { turbo: false } do %>
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-5 h-5">
          <path fill-rule="evenodd" d="M6.32 2.577a49.255 49.255 0 0 1 11.36 0c1.497.174 2.57 1.46 2.57 2.93V21a.75.75 0 0 1-1.085.67L12 18.089l-7.165 3.583A.75.75 0 0 1 3.75 21V5.507c0-1.47 1.073-2.756 2.57-2.93Z" clip-rule="evenodd" />
        </svg>
      <% end %>
    <% else %>
      <%= button_to bookmark_talent_conversation_path(conversation, status: params[:status]),
                    method: :post,
                    class: "p-1 text-stone-400 hover:text-yellow-500",
                    title: "Bookmark",
                    data: { turbo: false } do %>
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5">
          <path stroke-linecap="round" stroke-linejoin="round" d="M17.593 3.322c1.1.128 1.907 1.077 1.907 2.185V21L12 17.25 4.5 21V5.507c0-1.108.806-2.057 1.907-2.185a48.507 48.507 0 0 1 11.186 0Z" />
        </svg>
      <% end %>
    <% end %>
  </div>
  <%# --- End Bookmark Button --- %>

  <%# --- Main Card Content Link --- %>
  <%= link_to talent_conversation_path(conversation), class: "block" do %>
    <div class="flex items-center justify-between gap-4"> <%# Main flex container %>
      <div class="flex items-center flex-shrink-0 gap-4"> <%# Left side: Avatar %>
        <div class="flex items-center justify-center w-12 h-12 rounded-full bg-stone-200">
          <% if conversation.users.where.not(id: Current.user).first&.avatar&.attached? %>
            <%= image_tag conversation.users.where.not(id: Current.user).first.avatar, class: "w-12 h-12 rounded-full object-cover" %>
          <% else %>
            <span class="text-xl text-stone-500"><%= conversation.users.where.not(id: Current.user).first&.name&.initials %></span>
          <% end %>
        </div> <%# Correctly close avatar container here %>
      </div> <%# Correctly close left-side container here %>


      <div class="flex-grow min-w-0"> <%# Middle: Text content %>
        <% other_participants = conversation.users.where.not(id: Current.user.id) %>
        <p class="font-semibold truncate text-stone-900">
          <%= other_participants.map { |user| "#{user.first_name} #{user.last_name}".strip }.join(', ') %>
          <% if participant&.archived? %>
            <span class="ml-1 text-xs font-normal text-stone-500">(Archived)</span>
          <% end %>
        </p>
        <% if conversation.job %>
          <p class="text-sm font-medium truncate text-stone-600"><%= conversation.job.title %></p>
        <% end %>
        <p class="mt-1 text-sm truncate text-stone-500">
          <%= conversation.messages.last&.body || "No messages yet" %>
        </p>
      </div>

      <div class="flex-shrink-0 text-right"> <%# Right side: Timestamp %>
        <div class="text-sm text-stone-500 whitespace-nowrap">
          <%= time_ago_in_words(conversation.messages.last&.created_at || conversation.created_at) %> ago
        </div>
      </div>
    </div>
  <% end %>
  <%# --- End Main Card Content Link --- %>

</div> <%# End container div %>
