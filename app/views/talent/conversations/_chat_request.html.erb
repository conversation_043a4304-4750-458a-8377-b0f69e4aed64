<%# Partial for rendering a single chat request card %>
<div class="relative p-4 border rounded-md transition duration-150 ease-in-out hover:bg-stone-50 border-stone-200">
  
  <%# Main Card Content %>
  <div class="flex items-start justify-between gap-4">
    <%# Left side: Scout Avatar and Info %>
    <div class="flex items-start gap-4 flex-grow min-w-0">
      <%# Scout Avatar %>
      <div class="flex-shrink-0">
        <% if chat_request.scout.avatar.attached? %>
          <%= image_tag chat_request.scout.avatar, 
              class: "h-12 w-12 rounded-full object-cover" %>
        <% else %>
          <div class="h-12 w-12 rounded-full bg-stone-100 flex items-center justify-center">
            <span class="text-stone-600 font-medium text-sm">
              <%= chat_request.scout.initials %>
            </span>
          </div>
        <% end %>
      </div>
      
      <%# Scout Info and Request Details %>
      <div class="flex-grow min-w-0">
        <div class="flex items-center gap-2 mb-1">
          <h3 class="font-semibold text-stone-900 truncate">
            <%= chat_request.scout.name %>
          </h3>
          <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
            Chat Request
          </span>
        </div>
        
        <p class="text-sm text-stone-600 mb-2">
          Wants to start a conversation with you
        </p>
        
        <% if chat_request.pitch.present? %>
          <div class="mb-3 p-3 bg-stone-50 rounded-md">
            <p class="text-sm text-stone-700 italic">
              "<%= truncate(chat_request.pitch, length: 150) %>"
            </p>
          </div>
        <% end %>
        
        <p class="text-xs text-stone-500">
          Requested <%= time_ago_in_words(chat_request.requested_at) %> ago
        </p>
      </div>
    </div>
    
    <%# Right side: Action Buttons %>
    <div class="flex-shrink-0 flex flex-col gap-2 ml-4">
      <%= button_to accept_talent_chat_request_path(chat_request), 
          method: :post,
          class: "px-4 py-2 bg-green-600 text-white text-sm font-medium rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition-colors",
          data: { turbo: false } do %>
        Accept
      <% end %>
      
      <%= button_to decline_talent_chat_request_path(chat_request), 
          method: :post,
          class: "px-4 py-2 bg-stone-200 text-stone-700 text-sm font-medium rounded-md hover:bg-stone-300 focus:outline-none focus:ring-2 focus:ring-stone-500 focus:ring-offset-2 transition-colors",
          data: { 
            turbo: false,
            confirm: "Are you sure you want to decline this chat request?" 
          } do %>
        Decline
      <% end %>
    </div>
  </div>
</div>
