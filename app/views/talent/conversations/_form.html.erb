<%= form_with(model: [@conversation, message],
              url: talent_conversation_messages_path(@conversation),
              class: "message-form",
              data: {
                controller: "form-reset",
                action: "turbo:submit-end->form-reset#reset"
              },
              id: "new_message_form") do |f| %>
  <div class="flex">
    <%= f.text_area :body,
                  class: "flex-1 border rounded-l-lg p-2",
                  placeholder: "Type your message...",
                  data: { form_reset_target: "input" } %>
    <%= f.submit "Send",
                class: "bg-blue-500 text-white px-6 py-2 rounded-r-lg" %>
  </div>
<% end %>
