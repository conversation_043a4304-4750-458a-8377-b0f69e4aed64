<div>
  <!-- Main Content -->
  <main class="flex flex-col h-full bg-white border rounded-md border-stone-200">
    <div class="flex-1 w-full px-16 py-8 mx-auto max-w-7xl">
      <!-- Header Section -->
      <div class="pb-5 mb-8 sm:pb-0">
        <div class="flex flex-col">

      <div class="mb-2 text-2xl font-semibold text-stone-900">
        Messages 
      </div>
      <div class="pb-2 mb-4 text-sm text-stone-600 ">
        List of conversation you having with recruiters.
      </div>

        </div>
        <div class="">
          <%# Segmented control for filtering conversations %>
          <div class="inline-flex rounded-md shadow-sm" role="group">
            <%# Active Button %>
            <% active_filter = params[:status].blank? || params[:status] == 'active' %>
            <% active_count = @counts&.dig(:active) || 0 %>
            <% if active_count == 0 && !active_filter %>
              <div class="px-4 py-2 text-sm font-medium bg-white border rounded-l-lg cursor-not-allowed border-stone-200">
                <span class="opacity-50 text-stone-700">
                  Active <span class="ml-1 text-xs opacity-70">(<%= active_count %>)</span>
                </span>
              </div>
            <% else %>
              <%= link_to talent_conversations_path(status: 'active'),
                  class: "px-4 py-2 text-sm font-medium #{active_filter ? 'bg-stone-900 text-white' : 'bg-white text-stone-700 hover:bg-stone-50'} border border-stone-200 rounded-l-lg" do %>
                Active <span class="ml-1 text-xs opacity-70">(<%= active_count %>)</span>
              <% end %>
            <% end %>

            <%# Archived Button %>
            <% archived_filter = params[:status] == 'archived' %>
            <% archived_count = @counts&.dig(:archived) || 0 %>
            <% if archived_count == 0 && !archived_filter %>
              <div class="px-4 py-2 text-sm font-medium bg-white border-t border-b cursor-not-allowed border-stone-200">
                <span class="opacity-50 text-stone-700">
                  Archived <span class="ml-1 text-xs opacity-70">(<%= archived_count %>)</span>
                </span>
              </div>
            <% else %>
              <%= link_to talent_conversations_path(status: 'archived'),
                  class: "px-4 py-2 text-sm font-medium #{archived_filter ? 'bg-stone-900 text-white' : 'bg-white text-stone-700 hover:bg-stone-50'} border-t border-b border-stone-200" do %>
                Archived <span class="ml-1 text-xs opacity-70">(<%= archived_count %>)</span>
              <% end %>
            <% end %>

            <%# Chat Requests Button %>
            <% chat_requests_filter = params[:status] == 'chat_requests' %>
            <% chat_requests_count = @counts&.dig(:chat_requests) || 0 %>
            <% if chat_requests_count == 0 && !chat_requests_filter %>
              <div class="px-4 py-2 text-sm font-medium bg-white border-t border-b border-r rounded-r-lg cursor-not-allowed border-stone-200">
                <span class="opacity-50 text-stone-700">
                  Chat Requests <span class="ml-1 text-xs opacity-70">(<%= chat_requests_count %>)</span>
                </span>
              </div>
            <% else %>
              <%= link_to talent_conversations_path(status: 'chat_requests'),
                  class: "px-4 py-2 text-sm font-medium #{chat_requests_filter ? 'bg-stone-900 text-white' : 'bg-white text-stone-700 hover:bg-stone-50'} border-t border-b border-r border-stone-200 rounded-r-lg" do %>
                Chat Requests <span class="ml-1 text-xs opacity-70">(<%= chat_requests_count %>)</span>
              <% end %>
            <% end %>
          </div>
        </div>
      </div>

      <!-- Conversations List -->
      <div class="flex-1 space-y-4">
        <% if params[:status] == 'chat_requests' %>
          <!-- Chat Requests List -->
          <% if @chat_requests&.any? %>
            <%= render partial: 'talent/conversations/chat_request',
                       collection: @chat_requests,
                       as: :chat_request %>
          <% else %>
            <!-- Empty state for chat requests -->
            <div class="py-12 text-center">
              <div class="text-stone-400">
                <%= phosphor_icon "chat-circle-dots", class: "w-12 h-12 mx-auto" %>
              </div>
              <h3 class="mt-2 text-sm font-medium text-stone-900">No chat requests</h3>
              <p class="mt-1 text-sm text-stone-500">When scouts want to chat with you, their requests will appear here.</p>
            </div>
          <% end %>
        <% else %>
          <!-- Regular Conversations List -->
          <% if @conversations.any? %>
            <%# Render the collection using the partial %>
            <%= render partial: 'talent/conversations/conversation',
                       collection: @conversations,
                       locals: { selected_conversation: @selected_conversation } %>
          <% else %>
            <!-- Empty state -->
            <div class="py-12 text-center">
              <div class="text-stone-400">
                <svg class="w-12 h-12 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z" />
                </svg>
              </div>
              <h3 class="mt-2 text-sm font-medium text-stone-900">No conversations</h3>
              <p class="mt-1 text-sm text-stone-500">Get started by reaching out to someone.</p>
            </div>
          <% end %>
        <% end %>
      </div>
    </div>
  </main>
</div>
