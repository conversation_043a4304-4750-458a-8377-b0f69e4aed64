<div class="sm:hidden">
  <label for="current-tab" class="sr-only">Select a tab</label>
  <select id="current-tab" name="current-tab" class="block w-full py-2 pl-3 pr-10 text-base rounded-md border-stone-300 focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm">
    <option selected>Active</option>
    <option>Archived</option>
  </select>
</div>
<!-- Tabs at small breakpoint and up -->
<div class="hidden sm:block">
  <nav class="flex mt-6 -mb-px space-x-8">
    <!-- Current: "border-indigo-500 text-indigo-600", Default: "border-transparent text-stone-500 hover:border-stone-300 hover:text-stone-700" -->


  <%= link_to "Active", talent_conversations_path, 
              class: " #{current_page?(talent_conversations_path) ? 'px-1 pb-4 text-sm font-medium text-indigo-600 border-b-2 border-indigo-500 whitespace-nowrap' : 'px-1 pb-4 text-sm font-medium text-stone-500 border-b-2 border-transparent whitespace-nowrap hover:border-stone-300 hover:text-stone-700'}" %>

  <%= link_to "Archived", talent_archives_path, 
              class: " #{current_page?(talent_archives_path) ? 'px-1 pb-4 text-sm font-medium text-indigo-600 border-b-2 border-indigo-500 whitespace-nowrap' : 'px-1 pb-4 text-sm font-medium text-stone-500 border-b-2 border-transparent whitespace-nowrap hover:border-stone-300 hover:text-stone-700'}" %>

  </nav>
</div>
