<%# Container div with styling moved from the shell %>
<div class="h-full px-16 py-8 bg-white border rounded shadow-sm border-stone-200">
  <%= render "talent/settings/shell" %> <%# Render shell inside container %>

  <%= form_with(url: talent_settings_passwords_path, method: :patch, autocomplete: "off", class: "max-w-2xl pt-6") do |form| %> <%# Added pt-6 for spacing, removed bg-white %>
    <% if @user.errors.any? %>
      <div style="color: red">
        <h2><%= pluralize(@user.errors.count, "error") %> prohibited this user from being saved:</h2>
        <ul>
          <% @user.errors.each do |error| %>
            <li><%= error.full_message %></li>
          <% end %>
        </ul>
      </div>
    <% end %>
    <div class="pb-12 border-b border-stone-900/10">
      <h2 class="text-base font-semibold leading-7 text-stone-900">Change your passwords</h2>
      <p class="mt-1 text-sm leading-6 text-stone-600">Use your current password to confirm your identity.</p>
      <p style="color: red"><%= alert %></p>
      <div class="grid grid-cols-1 mt-10 gap-x-6 gap-y-8 sm:grid-cols-6">
        <div class="max-w-xs sm:col-span-4">
          <label for="current_password" class="block text-sm font-medium leading-6 text-stone-900">Current password</label>
          <div class="mt-2">
            <div>
              <%# Simplified classes, removed focus utilities. Will add custom CSS below. %>
              <%= form.password_field :password_challenge, required: true, autofocus: true, autocomplete: "gh-password", class: "block w-full rounded-md py-1.5 text-stone-900 shadow-sm ring-1 ring-inset ring-stone-300 placeholder:text-stone-400 sm:text-sm sm:leading-6" %>
            </div>
          </div>
        </div>
        <div class="max-w-xs sm:col-span-4 ">
          <label for="new_password" class="block text-sm font-medium leading-6 text-stone-900">New password</label>
          <div class="mt-2">
             <%# Simplified classes, removed focus utilities. Will add custom CSS below. %>
            <%= form.password_field :password, required: true, autocomplete: "gh-new-password", class: "block w-full rounded-md py-1.5 text-stone-900 shadow-sm ring-1 ring-inset ring-stone-300 placeholder:text-stone-400 sm:text-sm sm:leading-6" %>
            <div class="text-sm leading-6 text-stone-600">12 characters minimum.</div>
          </div>
        </div>
        <div class="max-w-xs sm:col-span-4 ">
          <%= form.label :password_confirmation, "Confirm new password", class: "block text-sm font-medium leading-6 text-stone-900" %>
          <div class="mt-2">
             <%# Simplified classes, removed focus utilities. Will add custom CSS below. %>
            <%= form.password_field :password_confirmation, required: true, autocomplete: "gh-new-password", class: "block w-full rounded-md py-1.5 text-stone-900 shadow-sm ring-1 ring-inset ring-stone-300 placeholder:text-stone-400 sm:text-sm sm:leading-6" %>
          </div>
        </div>
      </div>
    </div>
    <div class="flex items-center justify-end py-4 mt-6 gap-x-6">
      <%= link_to "Cancel", root_path, class: "text-sm font-semibold leading-6 text-stone-900" %>
      <%= form.submit "Save changes", class: "rounded-md bg-stone-900 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-stone-700 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-stone-900" %>
    </div>
  <% end %>
</div> <%# Close the container div %>

<%# Custom CSS to override @tailwindcss/forms focus style with higher specificity %>
<style>
  .max-w-2xl input[type=password]:focus {
    border-color: #1c1917 !important; /* stone-900 */
    box-shadow: 0 0 0 1px #1c1917 !important; /* stone-900 */
    outline: none !important;
  }
</style>
