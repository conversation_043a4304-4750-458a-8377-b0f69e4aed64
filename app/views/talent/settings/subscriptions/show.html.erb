<%# Render the settings shell which includes the tab bar %>
<%= render 'talent/settings/shell' do %>
  <div class="mt-10 divide-y divide-stone-200">
    <div class="space-y-1">
      <h2 class="text-lg font-medium leading-6 text-stone-900">Subscription</h2>
      <p class="max-w-2xl text-sm text-stone-500">Manage your subscription details.</p>
    </div>
    <div class="pt-6">
      <% if @subscription %>
        <h3 class="text-base font-medium text-stone-900">Current Plan</h3>
        <dl class="mt-2 border-t border-b divide-y divide-stone-200 border-stone-200">
          <div class="flex justify-between py-3 text-sm font-medium">
            <dt class="text-stone-500">Plan ID</dt> <%# Changed label to Plan ID %>
            <dd class="text-stone-900"><%= @subscription.processor_plan %></dd> <%# Display processor_plan (Stripe Price ID) %>
          </div>
          <div class="flex justify-between py-3 text-sm font-medium">
            <dt class="text-stone-500">Status</dt>
            <dd class="capitalize text-stone-900"><%= @subscription.status %></dd>
          </div>
          <% if @subscription.trial_ends_at %>
            <div class="flex justify-between py-3 text-sm font-medium">
              <dt class="text-stone-500">Trial Ends</dt>
              <dd class="text-stone-900"><%= l @subscription.trial_ends_at.to_date, format: :long %></dd>
            </div>
          <% end %>
          <% if @subscription.ends_at %>
            <div class="flex justify-between py-3 text-sm font-medium">
              <dt class="text-stone-500">Subscription Ends</dt>
              <dd class="text-stone-900"><%= l @subscription.ends_at.to_date, format: :long %></dd>
            </div>
          <% else %>
             <div class="flex justify-between py-3 text-sm font-medium">
              <dt class="text-stone-500">Next Billing Date</dt>
              <%# Note: Pay gem doesn't directly store next billing date, Stripe portal is best %>
              <dd class="text-stone-900">See Billing Portal</dd>
            </div>
          <% end %>
        </dl>

        <% if @billing_portal_url %>
          <div class="flex items-center mt-6 space-x-4">
            <%# General Manage button %>
            <%= link_to "Manage Subscription", @billing_portal_url,
                        class: "inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-gray-600 border border-transparent rounded-md shadow-sm hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2",
                        data: { turbo: false } %>

            <%# Specific Upgrade button if on Standard plan %>
            <% if @subscription.processor_plan == "price_1R9Q55DYYVPVcCCrWQOwsKmT" %>
              <%= link_to "Upgrade to Premium", @billing_portal_url,
                          class: "inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-indigo-600 border border-transparent rounded-md shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2",
                          data: { turbo: false } %>
            <% end %>
          </div>
           <p class="mt-2 text-sm text-stone-500">Manage your plan, payment methods, and view invoices via the Stripe Billing Portal.</p>
        <% else %>
           <div class="p-4 mt-6 rounded-md bg-yellow-50">
            <div class="flex">
              <div class="flex-shrink-0">
                <%= phosphor_icon "warning-circle", class: "h-5 w-5 text-yellow-400", "aria-hidden": true %> <%# Use phosphor_icon helper %>
              </div>
              <div class="ml-3">
                <h3 class="text-sm font-medium text-yellow-800">Cannot Access Billing Portal</h3>
                <div class="mt-2 text-sm text-yellow-700">
                  <p>We encountered an issue accessing the subscription management portal. Please try again later or contact support if the problem persists.</p>
                </div>
              </div>
            </div>
          </div>
        <% end %>

      <% else %>
        <p class="mb-4">You do not have an active subscription.</p>
        <div class="flex space-x-4">
          <%= button_to "Upgrade to Standard", talent_subscription_path,
                        method: :post,
                        params: { plan: "price_1R9Q55DYYVPVcCCrWQOwsKmT" },
                        form: { data: { turbo: false } }, # Disable Turbo for this form
                        class: "inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-gray-600 border border-transparent rounded-md shadow-sm hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2" %>

          <%= button_to "Upgrade to Premium", talent_subscription_path,
                        method: :post,
                        params: { plan: "price_1R9Q66DYYVPVcCCrnqiXNafF" },
                        form: { data: { turbo: false } }, # Disable Turbo for this form
                        class: "inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-indigo-600 border border-transparent rounded-md shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2" %>
        </div>
      <% end %>
    </div>
  </div>
<% end %>
