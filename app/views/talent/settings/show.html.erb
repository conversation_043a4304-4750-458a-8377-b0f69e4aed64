<%# Container div with styling moved from the shell %>
<div class="h-full px-16 py-8 bg-white border rounded shadow-sm border-stone-200">
  <%= render "talent/settings/shell" %> <%# Render shell inside container %>

  <%= form_with model: @user, url: talent_settings_path, method: :patch, data: { turbo: false }, class: "max-w-2xl pt-6" do |f| %> <%# Added pt-6 for spacing, removed bg-white %>

    <%# Personal Information Section - Following Password Page Structure %>
    <div class="pb-12 border-b border-stone-900/10">
      <h2 class="text-base font-semibold leading-7 text-stone-900">Personal Information</h2>
      <p class="mt-1 text-sm leading-6 text-stone-600">Manage your name, email address, and preferred timezone.</p>

      <div class="grid grid-cols-1 mt-10 gap-x-6 gap-y-8 sm:grid-cols-6">
        <%# First Name %>
        <div class="sm:col-span-3">
          <%= f.label :first_name, class: "block text-sm font-medium leading-6 text-stone-900" %>
          <div class="mt-2">
             <%# Simplified classes, removed focus utilities. Will add custom CSS below. %>
            <%= f.text_field :first_name, autocomplete: "given-name", class: "block w-full rounded-md py-1.5 text-stone-900 shadow-sm ring-1 ring-inset ring-stone-300 placeholder:text-stone-400 sm:text-sm sm:leading-6" %>
          </div>
        </div>

        <%# Last Name %>
        <div class="sm:col-span-3">
          <%= f.label :last_name, class: "block text-sm font-medium leading-6 text-stone-900" %>
          <div class="mt-2">
             <%# Simplified classes, removed focus utilities. Will add custom CSS below. %>
            <%= f.text_field :last_name, autocomplete: "family-name", class: "block w-full rounded-md py-1.5 text-stone-900 shadow-sm ring-1 ring-inset ring-stone-300 placeholder:text-stone-400 sm:text-sm sm:leading-6" %>
          </div>
        </div>

        <%# Email %>
        <div class="sm:col-span-4">
          <%= f.label :email, class: "block text-sm font-medium leading-6 text-stone-900" %>
          <div class="mt-2">
             <%# Simplified classes, removed focus utilities. Will add custom CSS below. %>
            <%= f.email_field :email, autocomplete: "email", class: "block w-full rounded-md py-1.5 text-stone-900 shadow-sm ring-1 ring-inset ring-stone-300 placeholder:text-stone-400 sm:text-sm sm:leading-6" %>
          </div>
        </div>

        <%# Timezone %>
        <div class="sm:col-span-4">
          <%= f.label :time_zone, class: "block text-sm font-medium leading-6 text-stone-900" %>
          <div class="mt-2">
             <%# Simplified classes, removed focus utilities. Will add custom CSS below. %>
            <%= f.time_zone_select :time_zone, nil, {}, { class: "block w-full rounded-md py-1.5 text-stone-900 shadow-sm ring-1 ring-inset ring-stone-300 sm:text-sm sm:leading-6" } %>
          </div>
        </div>
      </div>
    </div>

    <%# Avatar Section - Following Password Page Structure %>
    <div class="pb-12 mt-10 border-b border-stone-900/10">
       <h2 class="text-base font-semibold leading-7 text-stone-900">Profile Photo</h2>
       <p class="mt-1 text-sm leading-6 text-stone-600">Update your avatar.</p>

       <div class="grid grid-cols-1 mt-10 gap-x-6 gap-y-8 sm:grid-cols-6">
         <div class="sm:col-span-4">
           <%= f.label :avatar, "Avatar", class: "block text-sm font-medium leading-6 text-stone-900" %>
           <div class="flex items-center mt-2 gap-x-3">
              <%# Display current avatar or placeholder %>
              <% if @user.avatar.attached? %>
                <%# Removed .variant(:thumb) - display original avatar %>
                <%= image_tag @user.avatar, id: "avatar-preview", class: "h-12 w-12 rounded-full object-cover" %>
              <% else %>
                <div id="avatar-preview" class="flex items-center justify-center w-12 h-12 bg-gray-200 rounded-full">
                   <span class="text-xs text-gray-500">No Photo</span>
                </div>
              <% end %>
              <%# Styled label acting as button, hiding the actual input %>
              <%= f.label :avatar, "Choose File", class: "cursor-pointer rounded-md bg-white px-2.5 py-1.5 text-sm font-semibold text-stone-900 shadow-sm ring-1 ring-inset ring-stone-300 hover:bg-gray-50" %>
              <%= f.file_field :avatar, id: "user_avatar", class: "sr-only", onchange: "previewImage(event, 'avatar-preview')" %>
           </div>
           <p class="mt-2 text-xs leading-5 text-gray-600">JPG, GIF or PNG. 1MB max.</p>
         </div>
       </div>
    </div>

    <%# Action Buttons - Following Password Page Structure %>
    <div class="flex items-center justify-end py-4 mt-6 gap-x-6">
      <%# Optional: Add a Cancel button %>
      <%# link_to "Cancel", talent_dashboard_path, class: "text-sm font-semibold leading-6 text-stone-900" %>
      <%= f.submit "Save Changes", class: "rounded-md bg-stone-900 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-stone-700 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-stone-900" %>
    </div>
  <% end %>

  <%# JavaScript for Avatar Preview (ensure it works with the new structure) %>
  <script>
    function previewImage(event, previewId) {
      const input = event.target;
      const output = document.getElementById(previewId);

      if (input.files && input.files[0]) {
        const reader = new FileReader();
        reader.onload = function(e) {
          // If the placeholder was a div, replace it with an img tag
          if (output.tagName === 'DIV') {
            const img = document.createElement('img');
            img.id = previewId;
            img.src = e.target.result;
            img.className = 'h-12 w-12 rounded-full'; // Match styling
            output.parentNode.replaceChild(img, output);
          } else {
            // If it's already an img, just update the src
            output.src = e.target.result;
          }
        };
        reader.readAsDataURL(input.files[0]);
      }
    }
  </script>
</div> <%# Close the container div %>

<%# Custom CSS to override @tailwindcss/forms focus style with higher specificity %>
<style>
  .max-w-2xl input[type=text]:focus,
  .max-w-2xl input[type=email]:focus,
  .max-w-2xl select:focus {
    border-color: #1c1917 !important; /* stone-900 */
    box-shadow: 0 0 0 1px #1c1917 !important; /* stone-900 */
    outline: none !important;
  }
</style>
