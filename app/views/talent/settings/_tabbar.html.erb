<div class="sm:hidden">
  <label for="current-tab" class="sr-only">Select a tab</label>
  <select id="current-tab" name="current-tab" class="block w-full py-2 pl-3 pr-10 text-base rounded-md border-stone-300 focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm">
    <option selected>General</option>
    <option>Password</option>
    <option >Notifications</option>
    <option>Privacy</option>
  </select>
</div>
<!-- Tabs at small breakpoint and up -->
<div class="hidden sm:block">
  <nav class="flex mt-6 -mb-px space-x-8">
    <!-- Current: "border-stone-900 text-stone-900", Default: "border-transparent text-stone-500 hover:border-stone-300 hover:text-stone-700" -->


  <%= link_to "General", talent_settings_path,
              class: " #{current_page?(talent_settings_path) ? 'px-1 pb-4 text-sm font-medium text-stone-900 border-b-2 border-stone-900 whitespace-nowrap' : 'px-1 pb-4 text-sm font-medium text-stone-500 border-b-2 border-transparent whitespace-nowrap hover:border-stone-300 hover:text-stone-700'}" %>
  <%= link_to "Password", talent_settings_passwords_path,
              class: " #{current_page?(talent_settings_passwords_path) ? 'px-1 pb-4 text-sm font-medium text-stone-900 border-b-2 border-stone-900 whitespace-nowrap' : 'px-1 pb-4 text-sm font-medium text-stone-500 border-b-2 border-transparent whitespace-nowrap hover:border-stone-300 hover:text-stone-700'}" %>
  <%= link_to "Subscription", talent_settings_subscription_path,
              class: " #{current_page?(talent_settings_subscription_path) ? 'px-1 pb-4 text-sm font-medium text-stone-900 border-b-2 border-stone-900 whitespace-nowrap' : 'px-1 pb-4 text-sm font-medium text-stone-500 border-b-2 border-transparent whitespace-nowrap hover:border-stone-300 hover:text-stone-700'}" %>

  </nav>
</div>
