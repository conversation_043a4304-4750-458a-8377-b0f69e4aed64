<div
  class="fixed inset-0 z-50 hidden"
  data-impersonation-target="modal"
  data-action="click->impersonation#closeOnOverlay"
>
  <!-- Overlay -->
  <div class="fixed inset-0 transition-opacity bg-black bg-opacity-50"></div>

  <!-- Modal -->
  <div class="fixed inset-0 z-10 overflow-y-auto">
    <div
      class="flex items-end justify-center min-h-full p-4 text-center sm:items-center sm:p-0"
    >
      <div
        class="relative px-4 pt-5 pb-4 overflow-hidden text-left transition-all transform bg-white rounded-lg shadow-xl sm:my-8 sm:w-full sm:max-w-lg sm:p-6"
      >
        <!-- Warning Icon -->
        <div
          class="flex items-center justify-center w-12 h-12 mx-auto rounded-full bg-amber-100"
        >
          <svg
            class="w-6 h-6 text-amber-600"
            fill="none"
            viewBox="0 0 24 24"
            stroke-width="1.5"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              d="M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z"
            />
          </svg>
        </div>

        <!-- Content -->
        <div class="mt-3 text-center sm:mt-5">
          <h3 class="text-lg font-semibold leading-6 text-stone-900">
            Confirm User Impersonation
          </h3>
          <div class="mt-2">
            <p class="text-sm text-stone-500">
              You are about to impersonate the following user. This action will
              be logged for security purposes.
            </p>
            <div class="p-4 mt-4 rounded-md bg-stone-50">
              <div class="text-sm">
                <div class="font-medium text-stone-900">
                  <span data-impersonation-target="userName"></span>
                </div>
                <div class="text-stone-600">
                  <span data-impersonation-target="userEmail"></span>
                </div>
              </div>
            </div>
            <div class="mt-4 text-left">
              <h4 class="mb-2 text-sm font-medium text-stone-900">
                Important Notes:
              </h4>
              <ul class="space-y-1 text-xs text-stone-600">
                <li>
                  • Your session will be logged with IP address and timestamp
                </li>
                <li>
                  • Certain sensitive actions will be restricted during
                  impersonation
                </li>
                <li>• The session will automatically expire after 2 hours</li>
                <li>
                  • The user will see an impersonation banner while you're
                  logged in
                </li>
              </ul>
            </div>
          </div>
        </div>

        <!-- Form -->
        <form data-impersonation-target="form">
          <!-- Actions -->
          <div
            class="mt-5 sm:mt-6 sm:grid sm:grid-flow-row-dense sm:grid-cols-2 sm:gap-3"
          >
            <button
              type="submit"
              data-action="click->impersonation#confirm"
              class="inline-flex justify-center w-full px-3 py-2 text-sm font-semibold text-white bg-stone-800 rounded-md shadow-sm hover:bg-stone-900 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-stone-600 sm:col-start-2"
            >
              Start Impersonation
            </button>
            <button
              type="button"
              data-action="click->impersonation#cancel"
              class="inline-flex justify-center w-full px-3 py-2 mt-3 text-sm font-semibold bg-white rounded-md shadow-sm text-stone-900 ring-1 ring-inset ring-stone-300 hover:bg-stone-50 sm:col-start-1 sm:mt-0"
            >
              Cancel
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>
