<% user = local_assigns.fetch(:user) %>
<% size = local_assigns.fetch(:size, "12") %>
<% classes = local_assigns.fetch(:classes, "") %>

<div class="<%= "w-#{size} h-#{size} rounded-full flex items-center justify-center #{classes}" %>">
  <% if user.avatar.attached? %>
    <%= image_tag user.avatar, 
        class: "w-full h-full rounded-full object-cover",
        alt: "#{user.name.full_name}'s avatar" %>
  <% else %>
    <% bg_color, text_color = user.avatar_color %>
    <div class="w-full h-full rounded-full <%= bg_color %> flex items-center justify-center">
      <span class="<%= text_color %> font-medium" style="font-size: <%= size.to_i * 0.4 %>px">
        <%= user.name.initials %>
      </span>
    </div>
  <% end %>
</div>
