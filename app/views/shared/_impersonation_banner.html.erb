<% if Current.impersonating? %>
  <div class="bg-amber-100 border-b border-amber-200 px-4 py-3 shadow-sm">
    <div class="max-w-7xl mx-auto flex items-center justify-between">
      <div class="flex items-center space-x-3">
        <!-- Warning Icon -->
        <div class="flex-shrink-0">
          <svg class="h-5 w-5 text-amber-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 19.5c-.77.833.192 2.5 1.732 2.5z" />
          </svg>
        </div>
        
        <!-- Banner Text -->
        <div class="flex-1">
          <p class="text-sm font-medium text-amber-800">
            <span class="font-semibold">Impersonating:</span> 
            <%= Current.user.name %> (<%= Current.user.email %>)
          </p>
          <% if Current.impersonator %>
            <p class="text-xs text-amber-700 mt-1">
              Logged in as: <%= Current.impersonator.name %>
            </p>
          <% end %>
        </div>
      </div>
      
      <!-- Exit Impersonation Button -->
      <div class="flex-shrink-0">
        <%= button_to "Exit Impersonation",
            super_admin_masquerades_path,
            method: :delete,
            class: "inline-flex items-center px-3 py-1.5 border border-amber-300 text-xs font-medium rounded-md text-amber-800 bg-amber-50 hover:bg-amber-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-amber-500 transition-colors duration-200",
            data: { 
              confirm: "Are you sure you want to exit impersonation and return to the super admin panel?" 
            } %>
      </div>
    </div>
  </div>
<% end %>
