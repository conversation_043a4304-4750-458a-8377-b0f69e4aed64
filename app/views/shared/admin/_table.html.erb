<%#
  Shared admin table component

  Usage:
  <%= render 'shared/admin/table',
      collection: @users,
      columns: [
        { key: 'id', label: 'ID', sortable: true },
        { key: 'email', label: 'Email', sortable: true },
        { key: 'created_at', label: 'Created', sortable: true }
      ],
      actions: true,
      show_path: ->(record) { super_admin_admin_user_path(record) },
      edit_path: ->(record) { edit_super_admin_admin_user_path(record) },
      bulk_operations: true,
      bulk_operations_config: @bulk_operations
  %>

<%
  columns ||= []
  actions ||= false
  show_path ||= nil
  edit_path ||= nil
  delete_path ||= nil
  bulk_operations ||= false
  bulk_operations_config ||= []
%>

<% if bulk_operations && bulk_operations_config.any? %>
  <!-- Bulk Operations Toolbar -->
  <div id="bulk-toolbar" class="hidden px-6 py-3 border-b bg-stone-50 border-stone-200">
    <div class="flex items-center justify-between">
      <div class="flex items-center space-x-4">
        <span id="selected-count" class="text-sm font-medium text-stone-900">0 selected</span>
        <button type="button" id="clear-selection" class="text-sm rounded text-stone-600 hover:text-stone-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-stone-500">
          Clear selection
        </button>
      </div>
      <div class="flex items-center space-x-2">
        <% bulk_operations_config.each do |operation| %>
          <button type="button"
                  data-bulk-action="<%= operation[:action] %>"
                  data-operation-key="<%= operation[:key] %>"
                  data-confirm="<%= operation[:confirm] %>"
                  data-confirm-message="<%= operation[:confirm_message] %>"
                  class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white <%= operation[:class] || 'bg-stone-600 hover:bg-stone-700' %> focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-stone-500 transition-colors">
            <%= operation[:label] %>
          </button>
        <% end %>
      </div>
    </div>
  </div>
<% end %>

<div class="overflow-hidden bg-white border rounded-lg shadow border-stone-200"
     data-controller="<%= bulk_operations ? 'bulk-operations' : '' %>"
     data-bulk-operations-base-url-value="<%= request.path %>">
  <div class="overflow-x-auto">
    <table class="min-w-full divide-y divide-stone-200">
      <thead class="bg-stone-50">
        <tr>
          <% if bulk_operations %>
            <th class="w-12 px-6 py-3 text-xs font-medium tracking-wider text-left uppercase text-stone-500">
              <input type="checkbox"
                     id="select-all"
                     data-bulk-operations-target="selectAll"
                     class="w-4 h-4 text-blue-600 rounded border-stone-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
            </th>
          <% end %>
          <% columns.each_with_index do |column, index| %>
            <%
              # Determine column width class based on column key
              width_class = case column[:key].to_s
                           when 'id' then 'w-16'
                           when 'title' then 'w-64'
                           when 'job_category', 'category' then 'w-32'
                           when 'status' then 'w-24'
                           when 'budget_range', 'budget' then 'w-32'
                           when 'applications' then 'w-28'
                           when 'is_premium', 'premium' then 'w-24'
                           when 'created_at', 'application_deadline', 'deadline' then 'w-32'
                           else 'w-auto'
                           end
            %>
            <th class="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider <%= width_class %>">
              <% if column[:sortable] && @sort_by %>
                <%= link_to column[:label],
                    request.params.merge(
                      sort_by: column[:key],
                      sort_direction: (@sort_by == column[:key].to_s && @sort_direction == 'asc') ? 'desc' : 'asc'
                    ),
                    class: "group inline-flex items-center space-x-1 text-stone-500 hover:text-stone-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded transition-colors" do %>
                  <span><%= column[:label] %></span>
                  <% if @sort_by == column[:key].to_s %>
                    <% if @sort_direction == 'asc' %>
                      <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7"></path>
                      </svg>
                    <% else %>
                      <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                      </svg>
                    <% end %>
                  <% else %>
                    <svg class="w-4 h-4 transition-opacity opacity-0 group-hover:opacity-100" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 9l4-4 4 4m0 6l-4 4-4-4"></path>
                    </svg>
                  <% end %>
                <% end %>
              <% else %>
                <%= column[:label] %>
              <% end %>
            </th>
          <% end %>
          <% if actions %>
            <th class="w-32 px-6 py-3 text-xs font-medium tracking-wider text-right uppercase text-stone-500">
              Actions
            </th>
          <% end %>
        </tr>
      </thead>
      <tbody class="bg-white divide-y divide-stone-200">
        <% if collection.any? %>
          <% collection.each do |record| %>
            <tr class="transition-colors duration-150 hover:bg-stone-50">
              <% if bulk_operations %>
                <td class="px-6 py-4 text-sm whitespace-nowrap text-stone-900">
                  <input type="checkbox"
                         data-bulk-operations-target="recordCheckbox"
                         data-record-id="<%= record.id %>"
                         class="w-4 h-4 text-blue-600 rounded border-stone-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                </td>
              <% end %>
              <% columns.each_with_index do |column, index| %>
                <%
                  # Determine column width class based on column key
                  width_class = case column[:key].to_s
                               when 'id' then 'w-16'
                               when 'title' then 'w-64'
                               when 'job_category', 'category' then 'w-32'
                               when 'status' then 'w-24'
                               when 'budget_range', 'budget' then 'w-32'
                               when 'applications' then 'w-28'
                               when 'is_premium', 'premium' then 'w-24'
                               when 'created_at', 'application_deadline', 'deadline' then 'w-32'
                               # User table specific columns
                               when 'name', 'full_name' then 'w-48'
                               when 'email' then 'w-56'
                               when 'roles' then 'w-32'
                               when 'verified' then 'w-20'
                               when 'onboarding_completed' then 'w-28'
                               else 'w-auto'
                               end

                  # Add truncation for title columns
                  truncate_class = column[:key].to_s == 'title' ? 'truncate' : ''
                %>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-stone-900 <%= width_class %> <%= truncate_class %>">
                  <% if column[:render] %>
                    <%= raw column[:render].call(record) %>
                  <% else %>
                    <% value = record.send(column[:key]) %>
                    <% if value.is_a?(Time) || value.is_a?(DateTime) %>
                      <span class="text-stone-900"><%= value.strftime('%b %d, %Y') %></span>
                      <span class="block text-xs text-stone-500"><%= value.strftime('%I:%M %p') %></span>
                    <% elsif value.is_a?(Date) %>
                      <%= value.strftime('%b %d, %Y') %>
                    <% elsif value.is_a?(TrueClass) || value.is_a?(FalseClass) %>
                      <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <%= value ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' %>">
                        <%= value ? 'Yes' : 'No' %>
                      </span>
                    <% else %>
                      <%= truncate(value.to_s, length: 50) %>
                    <% end %>
                  <% end %>
                </td>
              <% end %>
              <% if actions %>
                <td class="w-32 px-6 py-4 text-sm text-right whitespace-nowrap text-stone-900">
                  <div class="flex items-center justify-end space-x-2">
                    <% if show_path %>
                      <%= link_to "View", show_path.call(record),
                          class: "inline-flex items-center px-3 py-1.5 text-xs font-medium rounded-md text-stone-700 bg-white border border-stone-300 hover:bg-stone-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-stone-500 transition-colors" %>
                    <% end %>
                    <% if edit_path %>
                      <%= link_to "Edit", edit_path.call(record),
                          class: "inline-flex items-center px-3 py-1.5 text-xs font-medium rounded-md text-stone-700 bg-white border border-stone-300 hover:bg-stone-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-stone-500 transition-colors" %>
                    <% end %>
                    <% if delete_path %>
                      <%= link_to "Delete", delete_path.call(record),
                          method: :delete,
                          data: { confirm: "Are you sure you want to delete this record?" },
                          class: "inline-flex items-center px-3 py-1.5 text-xs font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors" %>
                    <% end %>
                  </div>
                </td>
              <% end %>
            </tr>
          <% end %>
        <% else %>
          <tr>
            <td colspan="<%= columns.length + (actions ? 1 : 0) + (bulk_operations ? 1 : 0) %>" class="px-6 py-12 text-sm text-center text-stone-900">
              <div class="text-stone-500">
                <svg class="w-12 h-12 mx-auto mb-4 text-stone-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 009.586 13H7"></path>
                </svg>
                <p class="text-sm font-medium">No records found</p>
                <p class="text-xs">Try adjusting your search or filter criteria</p>
              </div>
            </td>
          </tr>
        <% end %>
      </tbody>
    </table>
  </div>
</div>
