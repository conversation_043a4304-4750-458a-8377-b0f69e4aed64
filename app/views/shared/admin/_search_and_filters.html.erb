<%#
  Shared admin search and filters component

  Usage:
  <%= render 'shared/admin/search_and_filters',
      search_placeholder: "Search users...",
      filters: [
        { key: 'status', label: 'Status', options: [['Active', 'active'], ['Inactive', 'inactive']] }
      ]
  %>

<% 
  search_placeholder ||= "Search..."
  filters ||= []
%>

<div class="bg-white p-6 rounded-lg shadow-sm mb-6 border-t border-stone-200">
  <%= form_with url: request.path, method: :get, local: true, class: "space-y-4" do |form| %>
    <!-- Search and Action Buttons Row -->
    <div class="flex items-end gap-4">
      <!-- Search Field (takes most space) -->
      <div class="flex-1">
        <%= form.label :search, "Search", class: "block text-sm font-medium text-stone-700" %>
        <%= form.text_field :search,
            value: params[:search],
            placeholder: search_placeholder,
            class: "mt-1 block w-full rounded-md border-stone-300 shadow-sm focus:border-stone-500 focus:ring-stone-500 sm:text-sm" %>
      </div>

      <!-- Filter and Clear Buttons -->
      <div class="flex items-center space-x-4">
        <%= form.submit "Filter", class: "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-stone-600 hover:bg-stone-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-stone-500" %>
        <% if params[:search].present? || filters.any? { |f| params[f[:key]].present? } %>
          <%= link_to "Clear", request.path, class: "text-stone-600 hover:text-stone-900" %>
        <% end %>
      </div>
    </div>

    <!-- Filter Dropdowns Row -->
    <% if filters.any? %>
      <div class="grid grid-cols-1 gap-4 sm:grid-cols-<%= [filters.length + 1, 4].min %>">
        <!-- Filters -->
        <% filters.each do |filter| %>
          <div>
            <%= form.label filter[:key], filter[:label], class: "block text-sm font-medium text-stone-700" %>
            <%= form.select filter[:key],
                options_for_select([['All', '']] + filter[:options], params[filter[:key]]),
                {},
                { class: "mt-1 block w-full rounded-md border-stone-300 shadow-sm focus:border-stone-500 focus:ring-stone-500 sm:text-sm" } %>
          </div>
        <% end %>

        <!-- Page Size -->
        <div>
          <%= form.label :page_size, "Per Page", class: "block text-sm font-medium text-stone-700" %>
          <%= form.select :page_size,
              options_for_select([['25', 25], ['50', 50], ['100', 100]], params[:page_size] || 25),
              {},
              { class: "mt-1 block w-full rounded-md border-stone-300 shadow-sm focus:border-stone-500 focus:ring-stone-500 sm:text-sm" } %>
        </div>
      </div>
    <% end %>

    <!-- Preserve sort parameters -->
    <%= form.hidden_field :sort_by, value: params[:sort_by] if params[:sort_by].present? %>
    <%= form.hidden_field :sort_direction, value: params[:sort_direction] if params[:sort_direction].present? %>
  <% end %>
</div>
