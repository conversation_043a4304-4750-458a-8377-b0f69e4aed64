<div data-controller="modal chat-request-modal" class="hidden" data-modal-target="container">
  <!-- Overlay (background) that closes the modal when clicked -->
  <div class="fixed inset-0 transition-opacity bg-opacity-75 bg-stone-500" data-modal-target="overlay" data-action="click->modal#closeOnOverlayClick"></div>

  <div class="fixed inset-0 z-10 overflow-y-auto">
    <div class="flex items-end justify-center min-h-full p-4 text-center sm:items-center sm:p-0">
      <div class="relative px-4 pt-5 pb-4 overflow-hidden text-left transition-all transform bg-white rounded-lg shadow-xl sm:my-8 sm:w-full sm:max-w-3xl sm:p-6" data-action="click->modal#stopPropagation">
        <div class="absolute top-0 right-0 hidden pt-4 pr-4 sm:block">
          <button type="button" class="bg-white rounded-md text-stone-400 hover:text-stone-500" data-action="click->modal#close">
            <span class="sr-only">Close</span>
            <%= phosphor_icon "x", class: "h-6 w-6" %>
          </button>
        </div>
        
        <div data-modal-target="content">
          <%= turbo_frame_tag "message-modal-content" %>
        </div>
        
        <!-- This div is used to insert actions via Turbo Stream -->
        <div id="modal_actions"></div>
      </div>
    </div>
  </div>
</div>
