<div data-controller="segmented" class="<%= local_assigns[:class_name] %>">
  <!-- The segmented control buttons -->
  <div class="inline-flex rounded-full overflow-hidden border border-stone-200 p-1 bg-stone-100">
    <% tabs.each do |tab| %>
      <button
        type="button"
        data-action="click->segmented#switchTab"
        data-segmented-target="button"
        data-segment-id="<%= tab[:id] %>"
        <% if tab[:url].present? %>data-url="<%= tab[:url] %>"<% end %>
        class="px-4 py-2 text-base font-medium rounded-full transition-all duration-200 <%= tab[:active] ? 'bg-[#111827] text-white' : 'bg-transparent text-stone-600 hover:underline' %>"
      >
        <%= tab[:label] %>
      </button>
    <% end %>
  </div>

  <% if local_assigns[:frame_id].present? %>
    <!-- Turbo Frame for remote content -->
    <turbo-frame id="<%= frame_id %>" data-segmented-target="frame" class="mt-4">
      <%= render partial: "scout/talent/#{tabs.find { |tab| tab[:active] }[:id]}_content" %>
    </turbo-frame>
  <% end %>

  <% if content_for?(:segmented_panels) %>
    <%= content_for(:segmented_panels) %>
  <% end %>
</div>
