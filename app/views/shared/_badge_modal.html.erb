<%# Badge Detail Modal Component %>
<%# This modal displays detailed information about a badge when clicked %>

<div 
  id="badge-modal" 
  class="fixed inset-0 z-50 hidden overflow-y-auto"
  data-controller="badge-modal"
  data-badge-modal-target="container"
  role="dialog"
  aria-modal="true"
  aria-labelledby="badge-modal-title"
  aria-describedby="badge-modal-description"
>
  <!-- Backdrop with blur effect -->
  <div 
    class="fixed inset-0 transition-opacity duration-300 ease-out bg-black/60 backdrop-blur-sm"
    data-badge-modal-target="backdrop"
    data-action="click->badge-modal#close"
    aria-hidden="true"
  ></div>

  <!-- Modal container with view transition support -->
  <div class="flex items-end justify-center min-h-full p-2 sm:items-center sm:p-4 md:p-6 lg:p-8">
    <div
      class="relative w-full max-w-sm overflow-hidden transition-all duration-300 ease-out transform bg-white shadow-2xl sm:max-w-md md:max-w-lg rounded-t-2xl sm:rounded-2xl"
      data-badge-modal-target="content"
      data-action="click->badge-modal#stopPropagation"
      style="view-transition-name: badge-modal-content"
    >
      <!-- Close button -->
      <button
        type="button"
        class="absolute z-10 p-2 transition-colors duration-200 rounded-full right-4 top-4 bg-white/80 text-stone-400 hover:bg-white hover:text-stone-600 focus:outline-none focus:ring-2 focus:ring-stone-500 focus:ring-offset-2"
        data-action="click->badge-modal#close"
        data-badge-modal-target="closeButton"
        aria-label="Close badge details modal"
        aria-describedby="badge-modal-title"
      >
        <span class="sr-only">Close</span>
        <%= phosphor_icon "x", class: "h-5 w-5", "aria-hidden": "true" %>
      </button>

      <!-- Modal content -->
      <div class="relative">
        <!-- Holographic card background with gradient -->
        <div class="absolute inset-0 rounded-2xl bg-gradient-to-br from-stone-50 via-white to-stone-100 opacity-90"></div>
        
        <!-- Holographic overlay effects -->
        <div class="absolute inset-0 rounded-2xl bg-gradient-to-br from-transparent via-white/20 to-transparent opacity-30 mix-blend-mode-overlay"></div>
        
        <!-- Content container -->
        <div class="relative p-4 pt-8 sm:p-6 md:p-8 sm:pt-10 md:pt-12">
          <!-- Badge display section -->
          <div class="mb-6 text-center sm:mb-8">
            <!-- Large badge display with enhanced holographic effects -->
            <div
              class="inline-flex mx-auto mb-4 transition-transform duration-500 ease-out rounded-2xl sm:mb-6 transform-gpu hover:scale-105"
              data-badge-modal-target="badgeDisplay"
              data-controller="badge"
              data-badge-target="badge"
              data-badge-holographic-intensity-value="0.8"
              data-badge-rotation-factor-value="15"
              data-badge-glow-intensity-value="1.2"
              data-badge-prismatic-effect-value="true"
              data-badge-depth-effect-value="true"
              style="view-transition-name: badge-hero"
            >
              <!-- Badge content will be dynamically inserted here -->
            </div>

            <!-- Badge title -->
            <h2
              id="badge-modal-title"
              class="mb-2 text-xl font-bold sm:text-2xl text-stone-900"
              data-badge-modal-target="title"
            >
              <!-- Badge name will be inserted here -->
            </h2>

            <!-- Badge subtitle/category -->
            <p class="text-xs font-medium tracking-wide uppercase sm:text-sm text-stone-500">
              Achievement Badge
            </p>
          </div>

          <!-- Badge description section -->
          <div class="mb-6 sm:mb-8">
            <h3 class="mb-2 text-base font-semibold sm:text-lg text-stone-900 sm:mb-3">About This Badge</h3>
            <p
              id="badge-modal-description"
              class="text-sm leading-relaxed sm:text-base text-stone-700"
              data-badge-modal-target="description"
            >
              <!-- Badge description will be inserted here -->
            </p>
          </div>

          <!-- Badge criteria section -->
          <div class="mb-6 sm:mb-8">
            <h3 class="mb-2 text-base font-semibold sm:text-lg text-stone-900 sm:mb-3">Why It's Special</h3>
            <div
              class="p-3 border rounded-lg bg-stone-50 sm:p-4 border-stone-200"
              data-badge-modal-target="criteria"
            >
              <p class="text-xs leading-relaxed text-stone-700 sm:text-sm">
                This badge represents exceptional achievement and is awarded to recognize outstanding contributions to the platform.
              </p>
            </div>
          </div>

          <!-- Badge metadata section -->
          <div class="pt-6 border-t border-stone-200">
            <h3 class="sr-only">Badge Information</h3>
            <dl class="grid grid-cols-2 gap-4 text-sm">
              <div>
                <dt class="font-medium text-stone-500">Badge Type</dt>
                <dd class="mt-1 text-stone-900">Achievement</dd>
              </div>
              <div>
                <dt class="font-medium text-stone-500">Rarity</dt>
                <dd class="mt-1 text-stone-900">
                  <span
                    class="inline-flex items-center rounded-full bg-amber-100 px-2.5 py-0.5 text-xs font-medium text-amber-800"
                    role="status"
                    aria-label="Badge rarity: Special"
                  >
                    Special
                  </span>
                </dd>
              </div>
            </dl>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- CSS for enhanced modal effects -->
<style>
  /* View transition support for modern browsers */
  @supports (view-transition-name: none) {
    #badge-modal {
      view-transition-name: badge-modal;
    }
  }

  /* Enhanced backdrop blur for better focus */
  .backdrop-blur-sm {
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
  }

  /* Smooth modal entrance animation */
  @keyframes modalEnter {
    from {
      opacity: 0;
      transform: scale(0.95) translateY(20px);
    }
    to {
      opacity: 1;
      transform: scale(1) translateY(0);
    }
  }

  /* Modal content animation */
  .modal-enter {
    animation: modalEnter 0.3s ease-out forwards;
  }

  /* Holographic card effects for modal */
  .holographic-card {
    position: relative;
    background: linear-gradient(135deg, 
      rgba(255,255,255,0.9) 0%, 
      rgba(255,255,255,0.7) 50%, 
      rgba(255,255,255,0.9) 100%);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255,255,255,0.3);
  }

  .holographic-card::before {
    content: '';
    position: absolute;
    inset: 0;
    border-radius: inherit;
    background: linear-gradient(45deg, 
      transparent 30%, 
      rgba(255,255,255,0.3) 50%, 
      transparent 70%);
    opacity: 0.6;
    mix-blend-mode: overlay;
    pointer-events: none;
  }

  /* Accessibility: Respect reduced motion preferences */
  @media (prefers-reduced-motion: reduce) {
    #badge-modal * {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
    }
  }

  /* High contrast mode support */
  @media (prefers-contrast: high) {
    #badge-modal {
      backdrop-filter: none;
    }

    .holographic-card {
      background: white;
      border: 2px solid black;
    }

    .holographic-card::before {
      display: none;
    }
  }

  /* Mobile-specific styles */
  @media (max-width: 640px) {
    /* Mobile modal positioning */
    #badge-modal .flex {
      align-items: flex-end;
    }

    /* Mobile modal content */
    #badge-modal [data-badge-modal-target="content"] {
      border-radius: 1rem 1rem 0 0;
      max-height: 90vh;
      overflow-y: auto;
    }

    /* Touch-friendly close button */
    #badge-modal button[aria-label="Close modal"] {
      padding: 0.75rem;
      top: 0.75rem;
      right: 0.75rem;
    }

    /* Improved touch targets */
    .badge-clickable {
      min-height: 44px;
      min-width: 44px;
    }
  }

  /* Tablet styles */
  @media (min-width: 641px) and (max-width: 1024px) {
    #badge-modal [data-badge-modal-target="content"] {
      max-width: 28rem;
    }
  }

  /* Touch device optimizations */
  @media (hover: none) and (pointer: coarse) {
    .badge-clickable:hover {
      transform: none;
    }

    .badge-clickable:active {
      transform: scale(0.95);
      transition: transform 0.1s ease-out;
    }
  }
</style>
