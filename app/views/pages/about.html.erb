    <div class="">

      <h1 class="mb-12 text-4xl font-semibold title flex">Good morning, Dakota <span class="text-purple-300">.</span></h1>

      <%= render "shared/stats_grid" %>

        <!-- Features Grid -->
        <div class="grid grid-cols-1 gap-4 mb-12 md:grid-cols-2 lg:grid-cols-3">
            <!-- Source Talent -->
            <div class="p-4 bg-white border rounded card border-black/10">
                <div class="flex gap-8 mb-8">
                    <div class="flex items-center justify-center rounded-full w-14 h-14 bg-purple-600/14">
                        <i data-lucide="users" class="w-8 h-8 text-purple-600"></i>
                    </div>
                    <div>
                        <h3 class="mb-8 text-3xl font-semibold text-black/90">Source talent</h3>
                        <p class="text-black/60">Unique filter help cut through the noise, using our filters and suggestions</p>
                    </div>
                </div>
                <button class="w-full py-3 font-semibold border rounded-lg border-black/45 text-black/90">Source talent</button>
            </div>

            <!-- Track Applications -->
            <div class="p-4 bg-white border rounded card border-black/10">
                <div class="flex gap-8 mb-8">
                    <div class="flex items-center justify-center rounded-full w-14 h-14 bg-blue-500/5">
                        <i data-lucide="clipboard-list" class="w-8 h-8 text-blue-600"></i>
                    </div>
                    <div>
                        <h3 class="mb-8 text-3xl font-semibold text-black/90">Track applicants</h3>
                        <p class="text-black/60">Unique filter help cut through the noise, using our filters and suggestions</p>
                    </div>
                </div>
                <button class="w-full py-3 font-semibold border rounded-lg border-black/45 text-black/90">Track applications</button>
            </div>

            <!-- Managing Listings -->
            <div class="p-4 bg-white border rounded card border-black/10">
                <div class="flex gap-8 mb-8">
                    <div class="flex items-center justify-center rounded-full w-14 h-14 bg-amber-500/5">
                        <i data-lucide="file-text" class="w-8 h-8 text-amber-700"></i>
                    </div>
                    <div>
                        <h3 class="mb-8 text-3xl font-semibold text-black/90">Managing listings</h3>
                        <p class="text-black/60">Add, remove and edit job listing through our easy to use editor.</p>
                    </div>
                </div>
                <button class="w-full py-3 font-semibold border rounded-lg border-black/45 text-black/90">Manage listings</button>
            </div>
        </div>

        <!-- Activity Feed -->
        <div class="space-y-6">
            <div>
                <h2 class="mb-3 text-4xl font-semibold title">Activity Feed</h2>
                <p class="text-xl">Here's a reminder of what you and your team have been up to.</p>
            </div>

            <div class="space-y-4">
                <div class="p-4 border rounded border-black/10">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center gap-3">
                            <div class="flex items-center justify-center w-8 h-8 text-sm font-semibold rounded-full bg-black/5">AD</div>
                            <span>Adam D. posted a new job listing</span>
                        </div>
                        <span class="text-black/60">About 23 hours ago</span>
                    </div>
                </div>

                <div class="p-4 border rounded border-black/10">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center gap-3">
                            <div class="flex items-center justify-center w-8 h-8 text-sm font-semibold rounded-full bg-black/5">AD</div>
                            <span>Adam D. posted a message to Jak</span>
                        </div>
                        <span class="text-black/60">About 23 hours ago</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

</body>
</html>

