<div class="flex min-h-screen bg-stone-50">
  <!-- Sidebar -->
  <aside class="flex flex-col p-8 w-80">
    <div class="mb-12 text-2xl font-semibold letter-spacing-tight">
      Messages <span class="text-purple-400">.</span>
    </div>
  </aside>
  <!-- Main Content -->
  <main class="flex-1 p-2">
    <div
      class="flex-1 px-16 py-16 mx-auto bg-white border rounded shadow-sm border-stone-200"
    >
      <header class="w-full px-8 py-8 bg-white">
        <div class="relative pb-5 border-b border-stone-200 sm:pb-0">
          <div class="md:flex md:items-center md:justify-between">
            <h3 class="text-2xl font-semibold leading-6 text-stone-900">
              New Job Listing
            </h3>
            <div class="flex mt-3 md:absolute md:right-0 md:top-3 md:mt-0">
              <button
                type="button"
                class="inline-flex items-center px-3 py-2 text-sm font-semibold bg-white shadow-sm text-stone-900 rounded-m ring-1 ring-inset ring-stone-500 hover:bg-stone-50"
              >
                Back
              </button>
              <button
                type="button"
                class="inline-flex items-center px-3 py-2 ml-3 text-sm font-semibold text-white rounded-sm shadow-sm bg-stone-900 hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
              >
                Save
              </button>
            </div>
          </div>
          <div class="mt-4">
            <!-- Dropdown menu on small screens -->
            <div class="sm:hidden">
              <label for="current-tab" class="sr-only">Select a tab</label>
              <select
                id="current-tab"
                name="current-tab"
                class="block w-full rounded-md border-0 py-1.5 pl-3 pr-10 ring-1 ring-inset ring-stone-300 focus:ring-2 focus:ring-inset focus:ring-indigo-600"
              >
                <option>Overview</option>
                <option selected>Job Details</option>
                <option>Application Question</option>
              </select>
            </div>
            <!-- Tabs at small breakpoint and up -->
            <div class="hidden sm:block">
              <nav class="flex -mb-px space-x-8">
                <!-- Current: "border-indigo-500 text-indigo-600", Default: "border-transparent text-stone-500 hover:border-stone-300 hover:text-stone-700" -->
                <a
                  href="#"
                  class="px-1 pb-4 text-sm font-medium border-b-2 border-transparent text-stone-500 whitespace-nowrap hover:border-stone-300 hover:text-stone-700"
                  >Edit listing</a
                >
                <a
                  href="#"
                  class="px-1 pb-4 text-sm font-medium border-b-2 border-transparent text-stone-500 whitespace-nowrap hover:border-stone-300 hover:text-stone-700"
                  >Review</a
                >
              </nav>
            </div>
          </div>
        </div>
        <div class="max-w-[1103px] mx-auto"></div>
      </header>
      <div class="px-6 py-6">
        <div class="space-y-8">
          <form class="max-w-xl">
            <div class="space-y-12">
              <div class="pb-12 border-b border-stone-900/10"></div>
            </div>
          </form>
        </div>
      </div>
    </div>
  </main>
</div>
