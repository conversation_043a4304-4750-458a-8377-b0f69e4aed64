<% content_for :title, "Devices & Sessions" %>

<div class="container px-4 py-8 mx-auto">
  <%# Back Link %>
  <div class="mb-4">
    <%= link_to root_path, class: "text-sm text-stone-600 hover:text-stone-900 inline-flex items-center" do %>
      <span class="mr-1">←</span>
      Back
    <% end %>
  </div>

  <h1 class="mb-6 text-2xl font-semibold text-stone-800">Devices & Sessions</h1>

  <%= render "shared/flash_messages" %>

  <div id="sessions" class="space-y-4">
    <% @sessions.each do |session| %>
      <div id="<%= dom_id session %>" class="p-4 bg-white border rounded-lg shadow-sm">
        <div class="mb-2">
          <strong class="font-medium text-stone-700">User Agent:</strong>
          <span class="text-sm text-stone-600"><%= session.user_agent %></span>
        </div>

        <div class="mb-2">
          <strong class="font-medium text-stone-700">IP Address:</strong>
          <span class="text-sm text-stone-600"><%= session.ip_address %></span>
        </div>

        <div class="mb-3">
          <strong class="font-medium text-stone-700">Created at:</strong>
          <span class="text-sm text-stone-600"><%= session.created_at.strftime("%Y-%m-%d %H:%M:%S %Z") %></span> <%# Format time %>
        </div>

        <div>
          <%= button_to "Log out", session_path(session), method: :delete, class: "inline-block px-3 py-1 text-sm font-medium text-white bg-red-600 rounded cursor-pointer hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500", form_class: "inline-block" %>
        </div>
      </div>
    <% end %>
  </div>

</div>
