<h1 class="text-2xl font-bold mb-6">Set up your organization</h1>

<% if @organization.errors.any? %>
  <div class="mb-4 p-4 bg-red-50 border border-red-200 rounded text-red-700">
    <h2 class="font-semibold mb-2">Please fix the following errors:</h2>
    <ul class="list-disc pl-5">
      <% @organization.errors.full_messages.each do |message| %>
        <li><%= message %></li>
      <% end %>
    </ul>
  </div>
<% end %>

<%= form_with(model: @organization, url: onboarding_update_organization_path, method: :patch, class: "space-y-4 mb-6") do |form| %>
  <div>
    <%= form.label :name, "Organization Name", class: "block text-sm text-stone-700 font-medium mb-1" %>
    <%= form.text_field :name, 
                        required: true, 
                        autofocus: true,
                        class: "w-full border border-stone-500 rounded px-3 py-2 focus:outline-none focus:ring focus:ring-blue-200" %>
  </div>
  
  
  <div>
    <%= form.label :operating_timezone, "Operating Timezone", class: "block text-sm text-stone-700 font-medium mb-1" %>
    <%= form.time_zone_select :operating_timezone, 
                             ActiveSupport::TimeZone.all.sort,
                             { prompt: "Select timezone" },
                             { required: true, class: "w-full border border-stone-500 rounded px-3 py-2 focus:outline-none focus:ring focus:ring-blue-200" } %>
  </div>
  
  <div>
    <%= form.label :size, "Organization Size", class: "block text-sm text-stone-700 font-medium mb-1" %>
    <%= form.select :size, 
                   [
                     ["1-10 employees", "small"],
                     ["11-50 employees", "medium"],
                     ["51-200 employees", "large"],
                     ["201-1000 employees", "xlarge"],
                     ["1000+ employees", "enterprise"]
                   ],
                   { prompt: "Select organization size" },
                   { required: true, class: "w-full border border-stone-500 rounded px-3 py-2 focus:outline-none focus:ring focus:ring-blue-200" } %>
  </div>
  
  <div class="pt-4">
    <%= form.submit "Complete Setup", class: "w-full bg-black text-white rounded py-2 px-4 hover:bg-stone-800 focus:outline-none focus:ring focus:ring-stone-500" %>
  </div>
<% end %>