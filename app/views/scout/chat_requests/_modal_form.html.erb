<div class="w-full max-w-lg mx-auto">
  <!-- <PERSON><PERSON> -->
  <div class="mb-6">
    <h3 class="text-lg font-semibold text-stone-900">
      Request to <PERSON><PERSON> with <span data-chat-request-modal-target="talentName"><%= talent.name %></span>
    </h3>
    <p class="mt-1 text-sm text-stone-600">
      Send a chat request to start a conversation with this ghostwriter.
    </p>
  </div>

  <!-- Server-side Error Messages -->
  <%= render partial: "scout/chat_requests/form_errors", locals: { chat_request: chat_request } %>

  <!-- Client-side Error Message Container -->
  <div id="chat-request-error"
       class="hidden p-3 mb-4 text-sm text-red-700 bg-red-100 border border-red-200 rounded-md"
       data-chat-request-modal-target="errorMessage">
  </div>

  <!-- Form -->
  <%= form_with model: chat_request,
                url: scout_create_chat_request_path,
                method: :post,
                data: {
                  turbo: true,
                  chat_request_modal_target: "form"
                },
                class: "space-y-6" do |f| %>
    
    <%= hidden_field_tag :talent_user_id, talent.id %>
    
    <!-- Pitch Section -->
    <div>
      <label for="chat_request_pitch" class="block mb-2 text-sm font-medium text-stone-700">
        Compose Your Pitch <span class="text-stone-500">(Optional)</span>
      </label>
      <p class="mb-3 text-xs text-stone-500">
        Write a personalized message to encourage the ghostwriter to accept your chat request.
      </p>
      <%= f.text_area :pitch, 
                       placeholder: "Hi! I'm interested in working with you on...",
                       rows: 4,
                       class: "w-full px-3 py-2 border border-stone-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-stone-500 focus:border-stone-500 text-sm",
                       data: { chat_request_modal_target: "pitchTextarea" } %>
    </div>

    <!-- Action Buttons -->
    <div class="flex flex-col gap-3 pt-4">
      <!-- Send Request with Note Button -->
      <%= f.submit "Send Request with Note", 
                   class: "w-full px-4 py-2.5 bg-stone-900 text-white text-sm font-medium rounded-md hover:bg-stone-800 focus:outline-none focus:ring-2 focus:ring-stone-500 focus:ring-offset-2 transition-colors",
                   data: { 
                     action: "send-with-note",
                     chat_request_modal_target: "sendWithNoteButton"
                   } %>
      
      <!-- Send Request Button (without note) -->
      <%= button_tag "Send Request", 
                     type: "submit",
                     name: "chat_request[pitch]",
                     value: "",
                     class: "w-full px-4 py-2.5 bg-stone-100 text-stone-700 text-sm font-medium rounded-md hover:bg-stone-200 focus:outline-none focus:ring-2 focus:ring-stone-500 focus:ring-offset-2 transition-colors border border-stone-300" %>
    </div>
  <% end %>

  <!-- Help Text -->
  <div class="p-3 mt-4 rounded-md bg-stone-50">
    <p class="text-xs text-stone-600">
      <strong>Tip:</strong> A personalized message increases your chances of getting a response. 
      Mention specific details about your project or why you're interested in working with this ghostwriter.
    </p>
  </div>
</div>
