<div class="p-4 bg-white rounded-md shadow-sm">
  <div class="flex justify-between items-start">
    <div>
      <div class="prose prose-sm max-w-none">
        <%= note.content %>
      </div>
      <div class="mt-2 flex items-center text-sm text-stone-500">
        <% if note.category_general? %>
          <span class="inline-flex items-center rounded-full bg-stone-100 px-2.5 py-0.5 text-xs font-medium text-stone-800">
            General
          </span>
        <% elsif note.category_interview_feedback? %>
          <span class="inline-flex items-center rounded-full bg-blue-100 px-2.5 py-0.5 text-xs font-medium text-blue-800">
            Interview Feedback
          </span>
        <% elsif note.category_follow_up? %>
          <span class="inline-flex items-center rounded-full bg-green-100 px-2.5 py-0.5 text-xs font-medium text-green-800">
            Follow Up
          </span>
        <% elsif note.category_skill_assessment? %>
          <span class="inline-flex items-center rounded-full bg-yellow-100 px-2.5 py-0.5 text-xs font-medium text-yellow-800">
            Skill Assessment
          </span>
        <% elsif note.category_reference_check? %>
          <span class="inline-flex items-center rounded-full bg-purple-100 px-2.5 py-0.5 text-xs font-medium text-purple-800">
            Reference Check
          </span>
        <% elsif note.category_hiring_decision? %>
          <span class="inline-flex items-center rounded-full bg-red-100 px-2.5 py-0.5 text-xs font-medium text-red-800">
            Hiring Decision
          </span>
        <% end %>
        
        <% if note.pinned? %>
          <span class="ml-2 inline-flex items-center text-yellow-500">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="w-4 h-4">
              <path fill-rule="evenodd" d="M10.868 2.884c-.321-.772-1.415-.772-1.736 0l-1.83 4.401-4.753.381c-.833.067-1.171 1.107-.536 1.651l3.62 3.102-1.106 4.637c-.194.813.691 1.456 1.405 1.02L10 15.591l4.069 2.485c.713.436 1.598-.207 1.404-1.02l-1.106-4.637 3.62-3.102c.635-.544.297-1.584-.536-1.65l-4.752-.382-1.831-4.401z" clip-rule="evenodd" />
            </svg>
          </span>
        <% end %>
      </div>
    </div>
    <div class="text-sm text-stone-500">
      <div><%= note.user.name.full %></div>
      <div><%= time_ago_in_words(note.created_at) %> ago</div>
    </div>
  </div>
</div>