<%= form_with model: [talent_profile, note], url: scout_talent_notes_path(talent_profile), id: "new_note_form", class: "mt-2" do |form| %>
  <div class="space-y-4 p-4 bg-stone-50 rounded-md">
    <div>
      <%= form.label :content, "Add a note", class: "block text-sm font-medium text-stone-700" %>
      <%= form.rich_text_area :content, placeholder: "Write your note here...", class: "mt-1 block w-full rounded-md border-stone-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm" %>
    </div>
    
    <div>
      <%= form.label :category, class: "block text-sm font-medium text-stone-700" %>
      <%= form.select :category, TalentNote.categories.keys.map { |k| [k.humanize, k] }, {}, class: "mt-1 block w-full rounded-md border-stone-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm" %>
    </div>
    
    <div class="flex items-center">
      <%= form.check_box :pinned, class: "h-4 w-4 rounded border-stone-300 text-indigo-600 focus:ring-indigo-500" %>
      <%= form.label :pinned, "Pin this note", class: "ml-2 block text-sm text-stone-900" %>
    </div>
    
    <div class="flex justify-end space-x-2">
      <%= link_to "Cancel", "#", class: "inline-flex justify-center rounded-md border border-stone-300 bg-white py-2 px-4 text-sm font-medium text-stone-700 shadow-sm hover:bg-stone-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2", data: { action: "click->notes#hideForm" } %>
      <%= form.submit "Done", class: "inline-flex justify-center rounded-md border border-transparent bg-indigo-600 py-2 px-4 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2" %>
    </div>
  </div>
<% end %>