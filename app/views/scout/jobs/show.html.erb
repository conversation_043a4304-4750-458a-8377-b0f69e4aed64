<div class="flex flex-col h-full bg-white border rounded-md border-stone-200">
  <div class="flex-1 w-full px-4 py-8 mx-auto max-w-7xl sm:px-6 lg:px-8">

    <div class="px-16 mb-8">
      <%= link_to scout_jobs_path, class: "inline-flex items-center text-sm font-medium text-stone-600 hover:text-stone-900" do %>
        <%= phosphor_icon "arrow-left", class: "h-4 w-4 mr-1" %>
        Back to Jobs
      <% end %>
    </div>

    <div class="flex items-center justify-between px-16 mb-6">
      <div>
        <h1 class="text-2xl font-bold text-stone-900"><%= @job.title %></h1>
        <p class="max-w-2xl mt-1 text-sm leading-6 text-stone-500">Job posting details</p>
        <%# Removed the separate premium badge here, will add it near status %>
      </div>
      <div class="flex items-center space-x-3">
        <%# Payment Buttons based on status %>
        <% if @job.draft? || @job.expired? %>
          <%= button_to "Publish Job",
                scout_job_payment_path(@job),
                method: :post,
                params: { plan: 'price_1R9Q75DYYVPVcCCrJyRB0SP1' }, # Basic Job Price ID
                class: "btn btn-success",
                data: { turbo: false } %>
        <% elsif @job.published? && !@job.is_premium? %>
          <%= button_to "Upgrade to Premium",
                scout_job_payment_path(@job),
                method: :post,
                params: { plan: 'price_1R9Q7QDYYVPVcCCrcK2WYqcV', type: "premium_upgrade" }, # Premium Job Price ID
                class: "btn btn-warning",
                data: { turbo: false } %>
        <% end %>

        <%# Edit Button - always show? Or only for draft/expired? Adjust logic if needed %>
        <%= link_to "Edit Job", edit_scout_job_path(@job), class: "btn btn-secondary" %>
      </div>
    </div>

    <!-- Job Overview Section -->
    <div class="px-16">
      <div class="sm:px-0">
        <h3 class="text-base font-semibold leading-7 text-stone-900">Job Overview</h3>
        <p class="max-w-2xl mt-1 text-sm leading-6 text-stone-500">Basic job information</p>
      </div>

      <div class="mt-6 border-t border-stone-100">
        <dl class="divide-y divide-stone-100">
          <%# Updated Status Display %>
          <div class="py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
            <dt class="text-sm font-medium leading-6 text-stone-900">Status</dt>
            <dd class="mt-1 text-sm leading-6 text-stone-700 sm:col-span-2 sm:mt-0">
              <% status_class = case @job.status.to_sym
                                when :published
                                  'text-green-800 bg-green-100'
                                when :draft
                                  'text-yellow-800 bg-yellow-100'
                                when :expired
                                  'text-red-800 bg-red-100'
                                else
                                  'text-gray-800 bg-gray-100'
                                end %>
              <span class="px-2 py-1 text-xs font-medium rounded-full <%= status_class %>">
                <%= @job.status.humanize %>
              </span>
            </dd>
          </div>

          <%# Display Listing Type %>
          <div class="py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
            <dt class="text-sm font-medium leading-6 text-stone-900">Listing Type</dt>
            <dd class="mt-1 text-sm leading-6 text-stone-700 sm:col-span-2 sm:mt-0">
              <% if @job.is_premium? %>
                <span class="inline-flex items-center px-2 py-1 text-xs font-medium text-purple-700 rounded-md bg-purple-50 ring-1 ring-inset ring-purple-700/10">
                  <%= phosphor_icon "star", class: "w-3 h-3 mr-1" %>
                  Premium Listing
                </span>
              <% else %>
                <span class="inline-flex items-center px-2 py-1 text-xs font-medium text-gray-600 rounded-md bg-gray-50 ring-1 ring-inset ring-gray-500/10">
                  Standard Listing
                </span>
              <% end %>
            </dd>
          </div>

          <%# Display Published and Expiry Dates if applicable %>
          <% if @job.published_at.present? %>
            <div class="py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
              <dt class="text-sm font-medium leading-6 text-stone-900">Published On</dt>
              <dd class="mt-1 text-sm leading-6 text-stone-700 sm:col-span-2 sm:mt-0">
                <%= @job.published_at.strftime("%B %d, %Y") %>
              </dd>
            </div>
          <% end %>
          <% if @job.expires_at.present? %>
            <div class="py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
              <dt class="text-sm font-medium leading-6 text-stone-900">Expires On</dt>
              <dd class="mt-1 text-sm leading-6 text-stone-700 sm:col-span-2 sm:mt-0">
                <%= @job.expires_at.strftime("%B %d, %Y") %>
              </dd>
            </div>
          <% end %>

          <div class="py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
            <dt class="text-sm font-medium leading-6 text-stone-900">Job Category</dt>
            <dd class="mt-1 text-sm leading-6 text-stone-700 sm:col-span-2 sm:mt-0">
              <%= @job.job_category.present? ? @job.job_category.to_s.humanize : 'Not specified' %>
            </dd>
          </div>
          <div class="py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
            <dt class="text-sm font-medium leading-6 text-stone-900">Budget Range</dt>
            <dd class="mt-1 text-sm leading-6 text-stone-700 sm:col-span-2 sm:mt-0">
              <%= @job.salary_range %>
            </dd>
          </div>
          <div class="py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
            <dt class="text-sm font-medium leading-6 text-stone-900">Payment Frequency</dt>
            <dd class="mt-1 text-sm leading-6 text-stone-700 sm:col-span-2 sm:mt-0">
              <%= @job.payment_frequency.present? ? @job.payment_frequency.to_s.humanize : 'Not specified' %>
            </dd>
          </div>
          <div class="py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
            <dt class="text-sm font-medium leading-6 text-stone-900">Application Deadline</dt>
            <dd class="mt-1 text-sm leading-6 text-stone-700 sm:col-span-2 sm:mt-0">
              <%= @job.application_deadline %>
            </dd>
          </div>
        </dl>
      </div>
    </div>

    <!-- Job Details Section -->
    <div class="px-16 pt-8">
      <div class="sm:px-0">
        <h3 class="text-base font-semibold leading-7 text-stone-900">Job Details</h3>
        <p class="max-w-2xl mt-1 text-sm leading-6 text-stone-500">Detailed job specifications</p>
      </div>

      <div class="mt-6 border-t border-stone-100">
        <dl class="divide-y divide-stone-100">
          <div class="py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
            <dt class="text-sm font-medium leading-6 text-stone-900">Description</dt>
            <dd class="mt-1 text-sm leading-6 text-stone-700 sm:col-span-2 sm:mt-0">
              <%= @job.description %>
            </dd>
          </div>
          <% if @job.requirements.present? %>
            <div class="py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
              <dt class="text-sm font-medium leading-6 text-stone-900">Requirements</dt>
              <dd class="mt-1 text-sm leading-6 text-stone-700 sm:col-span-2 sm:mt-0">
                <%= @job.requirements %>
              </dd>
            </div>
          <% end %>
          <div class="py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
            <dt class="text-sm font-medium leading-6 text-stone-900">Work Duration</dt>
            <dd class="mt-1 text-sm leading-6 text-stone-700 sm:col-span-2 sm:mt-0">
              <%= @job.work_duration.present? ? @job.work_duration.humanize : 'Not specified' %>
            </dd>
          </div>
          <div class="py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
            <dt class="text-sm font-medium leading-6 text-stone-900">Desired Outcome</dt>
            <dd class="mt-1 text-sm leading-6 text-stone-700 sm:col-span-2 sm:mt-0">
              <%= @job.outcome.present? ? @job.outcome.to_s.humanize : 'Not specified' %>
            </dd>
          </div>
          <div class="py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
            <dt class="text-sm font-medium leading-6 text-stone-900">Involvement Level</dt>
            <dd class="mt-1 text-sm leading-6 text-stone-700 sm:col-span-2 sm:mt-0">
              <%= @job.involvement_level.present? ? @job.involvement_level.humanize : 'Not specified' %>
            </dd>
          </div>
          <div class="py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
            <dt class="text-sm font-medium leading-6 text-stone-900">Topics</dt>
            <dd class="mt-1 text-sm leading-6 text-stone-700 sm:col-span-2 sm:mt-0">
              <% if @job.topics.present? && @job.topics.any? %>
                <div class="flex flex-wrap gap-2">
                  <% @job.topics.each do |topic| %>
                    <span class="px-3 py-1 text-sm font-medium text-blue-800 bg-blue-100 rounded-full"><%= topic %></span>
                  <% end %>
                </div>
              <% else %>
                <span class="text-stone-500">Not specified</span>
              <% end %>
            </dd>
          </div>
        </dl>
      </div>
    </div>

    <!-- Category-Specific Details Section -->
    <div class="px-16 pt-8">
      <div class="sm:px-0">
        <h3 class="text-base font-semibold leading-7 text-stone-900">Category-Specific Details</h3>
        <p class="max-w-2xl mt-1 text-sm leading-6 text-stone-500">Details specific to the <%= @job.job_category.present? ? @job.job_category.humanize.downcase : 'selected' %> category</p>
      </div>

      <div class="mt-6 border-t border-stone-100">
        <dl class="divide-y divide-stone-100">
          <% if @job.social_media? %>
            <!-- Social Media Specific Fields -->
            <div class="py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
              <dt class="text-sm font-medium leading-6 text-stone-900">Platform</dt>
              <dd class="mt-1 text-sm leading-6 text-stone-700 sm:col-span-2 sm:mt-0">
                <%= @job.platform.present? ? @job.platform.to_s.humanize : 'Not specified' %>
              </dd>
            </div>

            <% if @job.leads? || @job.booked_calls? %>
              <div class="py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                <dt class="text-sm font-medium leading-6 text-stone-900">Social Media Goal Type</dt>
                <dd class="mt-1 text-sm leading-6 text-stone-700 sm:col-span-2 sm:mt-0">
                  <%= @job.social_media_goal_type.present? ? @job.social_media_goal_type.humanize : 'Not specified' %>
                </dd>
              </div>

              <div class="py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                <dt class="text-sm font-medium leading-6 text-stone-900">Risk Acknowledgment</dt>
                <dd class="mt-1 text-sm leading-6 text-stone-700 sm:col-span-2 sm:mt-0">
                  <% if @job.social_media_understands_risk_acknowledged %>
                    <span class="inline-flex items-center px-2 py-1 text-xs font-medium text-green-700 bg-green-100 rounded-md">
                      <%= phosphor_icon "check", class: "w-3 h-3 mr-1" %>
                      Acknowledged
                    </span>
                  <% else %>
                    <span class="text-stone-500">Not acknowledged</span>
                  <% end %>
                </dd>
              </div>
            <% end %>

          <% elsif @job.newsletter? %>
            <!-- Newsletter Specific Fields -->
            <div class="py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
              <dt class="text-sm font-medium leading-6 text-stone-900">Newsletter Frequency</dt>
              <dd class="mt-1 text-sm leading-6 text-stone-700 sm:col-span-2 sm:mt-0">
                <%= @job.newsletter_frequency.present? ? @job.newsletter_frequency.humanize : 'Not specified' %>
              </dd>
            </div>

            <div class="py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
              <dt class="text-sm font-medium leading-6 text-stone-900">Newsletter Length</dt>
              <dd class="mt-1 text-sm leading-6 text-stone-700 sm:col-span-2 sm:mt-0">
                <%= @job.newsletter_length.present? ? @job.newsletter_length.humanize : 'Not specified' %>
              </dd>
            </div>

          <% elsif @job.lead_magnet? %>
            <!-- Lead Magnet Specific Fields -->
            <div class="py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
              <dt class="text-sm font-medium leading-6 text-stone-900">Lead Magnet Type</dt>
              <dd class="mt-1 text-sm leading-6 text-stone-700 sm:col-span-2 sm:mt-0">
                <%= @job.lead_magnet_type.present? ? @job.lead_magnet_type.humanize : 'Not specified' %>
              </dd>
            </div>

          <% else %>
            <!-- Fallback for unknown or missing category -->
            <div class="py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
              <dt class="text-sm font-medium leading-6 text-stone-900">Category Details</dt>
              <dd class="mt-1 text-sm leading-6 text-stone-500 sm:col-span-2 sm:mt-0">
                No category-specific details available
              </dd>
            </div>
          <% end %>
        </dl>
      </div>
    </div>

    <!-- Client Information Section -->
    <div class="px-16 pt-8">
      <div class="sm:px-0">
        <h3 class="text-base font-semibold leading-7 text-stone-900">Client Information</h3>
        <p class="max-w-2xl mt-1 text-sm leading-6 text-stone-500">Background information about the client</p>
      </div>

      <div class="mt-6 border-t border-stone-100">
        <dl class="divide-y divide-stone-100">
          <div class="py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
            <dt class="text-sm font-medium leading-6 text-stone-900">Current Client Count</dt>
            <dd class="mt-1 text-sm leading-6 text-stone-700 sm:col-span-2 sm:mt-0">
              <%= @job.client_count.present? ? @job.client_count : 'Not specified' %>
            </dd>
          </div>
          <div class="py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
            <dt class="text-sm font-medium leading-6 text-stone-900">Average Charge Per Client</dt>
            <dd class="mt-1 text-sm leading-6 text-stone-700 sm:col-span-2 sm:mt-0">
              <%= @job.charge_per_client.present? ? @job.charge_per_client : 'Not specified' %>
            </dd>
          </div>
          <div class="py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
            <dt class="text-sm font-medium leading-6 text-stone-900">Current Business Challenge</dt>
            <dd class="mt-1 text-sm leading-6 text-stone-700 sm:col-span-2 sm:mt-0">
              <%= @job.business_challenge.present? ? @job.business_challenge : 'Not specified' %>
            </dd>
          </div>
          <div class="py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
            <dt class="text-sm font-medium leading-6 text-stone-900">Current Offer & Target Audience</dt>
            <dd class="mt-1 text-sm leading-6 text-stone-700 sm:col-span-2 sm:mt-0">
              <%= @job.offer_summary.present? ? @job.offer_summary : 'Not specified' %>
            </dd>
          </div>
          <% if @job.target_audience_description.present? %>
            <div class="py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
              <dt class="text-sm font-medium leading-6 text-stone-900">Target Audience</dt>
              <dd class="mt-1 text-sm leading-6 text-stone-700 sm:col-span-2 sm:mt-0">
                <%= @job.target_audience_description %>
              </dd>
            </div>
          <% end %>
          <% if @job.emulated_brands_description.present? %>
            <div class="py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
              <dt class="text-sm font-medium leading-6 text-stone-900">Brands to Emulate</dt>
              <dd class="mt-1 text-sm leading-6 text-stone-700 sm:col-span-2 sm:mt-0">
                <%= @job.emulated_brands_description %>
              </dd>
            </div>
          <% end %>
          <div class="py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
            <dt class="text-sm font-medium leading-6 text-stone-900">Additional Information</dt>
            <dd class="mt-1 text-sm leading-6 text-stone-700 sm:col-span-2 sm:mt-0">
              <%= @job.useful_info.present? ? @job.useful_info : 'Not specified' %>
            </dd>
          </div>
        </dl>
      </div>
    </div>

  </div>
</div>
