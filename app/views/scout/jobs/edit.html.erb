<div class="flex flex-col h-full bg-white border rounded-md border-stone-200">
  <div class="flex-1 w-full px-4 py-8 mx-auto max-w-7xl sm:px-6 lg:px-8">
    <div class="px-16 mb-8">
      <%= link_to scout_job_path(@job), class: "inline-flex items-center text-sm font-medium text-stone-600 hover:text-stone-900" do %>
        <%= phosphor_icon "arrow-left", class: "h-4 w-4 mr-1" %>
        Back to Job Details
      <% end %>
    </div>
    
    <div class="flex flex-col px-16 mb-8">
      <h1 class="text-2xl font-bold text-stone-900">
        Edit Job Posting
      </h1>
      <p class="mt-1 text-sm leading-6 text-stone-600">
        Update information for your job posting.
      </p>
    </div>

    <%= form_with(model: @job, url: scout_job_path(@job), method: :put, class: "px-16", data: { controller: "job-form" }) do |form| %>
      <% if @job.errors.any? %>
        <div class="p-4 mb-6 rounded-md bg-red-50">
          <div class="flex">
            <div class="flex-shrink-0">
              <svg
                class="w-5 h-5 text-red-400"
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 20 20"
                fill="currentColor"
                aria-hidden="true"
              >
                <path
                  fill-rule="evenodd"
                  d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.28 7.22a.75.75 0 00-1.06 1.06L8.94 10l-1.72 1.72a.75.75 0 101.06 1.06L10 11.06l1.72 1.72a.75.75 0 101.06-1.06L11.06 10l1.72-1.72a.75.75 0 00-1.06-1.06L10 8.94 8.28 7.22z"
                  clip-rule="evenodd"
                />
              </svg>
            </div>
            <div class="ml-3">
              <h3 class="text-sm font-medium text-red-800">
                Please fix the following issues:
              </h3>
              <div class="mt-2 text-sm text-red-700">
                <ul class="pl-5 space-y-1 list-disc">
                  <% @job.errors.messages.each do |field, messages| %>
                    <li>
                      <strong><%= field.to_s.humanize %>:</strong>
                      <%= messages.join(', ') %>
                    </li>
                  <% end %>
                </ul>
              </div>
            </div>
          </div>
        </div>
      <% end %>

      <div class="space-y-12">
        <!-- Basic Job Information -->
        <div class="grid grid-cols-2 py-12 border-y gap-x-8 gap-y-10 border-stone-900/10 md:grid-cols-3">
          <div>
            <h2 class="text-base font-semibold leading-7 text-stone-900">Basic Information</h2>
            <p class="mt-1 text-sm leading-6 text-stone-600">This information will be displayed to potential applicants.</p>
          </div>

          <div class="grid max-w-2xl grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-6 md:col-span-2">
            <div class="sm:col-span-4">
              <%= form.label :title, class: "block text-sm font-medium leading-6 text-stone-900" %>
              <div class="mt-2">
                <%= form.text_field :title, class: "block w-full rounded-md border-0 py-1.5 text-stone-900 shadow-sm ring-1 ring-inset ring-stone-300 placeholder:text-stone-400 focus:ring-2 focus:ring-inset focus:ring-stone-600 sm:text-sm sm:leading-6" %>
              </div>
            </div>

            <div class="col-span-5">
              <%= form.label :description, class: "block text-sm font-medium leading-6 text-stone-900" %>
              <div class="mt-2">
                <%= form.text_area :description, rows: 5, class: "block w-full rounded-md border-0 py-1.5 text-stone-900 shadow-sm ring-1 ring-inset ring-stone-300 placeholder:text-stone-400 focus:ring-2 focus:ring-inset focus:ring-stone-600 sm:text-sm sm:leading-6" %>
              </div>
              <p class="mt-3 text-sm leading-6 text-stone-600">Describe the job in detail.</p>
            </div>

            <div class="sm:col-span-3">
              <%= form.label :job_category, class: "block text-sm font-medium leading-6 text-stone-900" %>
              <div class="mt-2">
                <%= form.select :job_category, Job.job_categories.keys.map { |key| [key.titleize, key] }, { prompt: "Select Category" }, class: "block w-full rounded-md border-0 py-1.5 text-stone-900 shadow-sm ring-1 ring-inset ring-stone-300 focus:ring-2 focus:ring-inset focus:ring-stone-600 sm:text-sm sm:leading-6" %>
              </div>
            </div>

            <div class="sm:col-span-3">
              <%= form.label :status, class: "block text-sm font-medium leading-6 text-stone-900" %>
              <div class="mt-2">
                <%= form.select :status, Job.statuses.keys.map { |key| [key.titleize, key] }, { prompt: "Select Status" }, class: "block w-full rounded-md border-0 py-1.5 text-stone-900 shadow-sm ring-1 ring-inset ring-stone-300 focus:ring-2 focus:ring-inset focus:ring-stone-600 sm:text-sm sm:leading-6" %>
              </div>
            </div>

            <div class="sm:col-span-3">
              <%= form.label :application_deadline, class: "block text-sm font-medium leading-6 text-stone-900" %>
              <div class="mt-2">
                <%= form.datetime_field :application_deadline, class: "block w-full rounded-md border-0 py-1.5 text-stone-900 shadow-sm ring-1 ring-inset ring-stone-300 focus:ring-2 focus:ring-inset focus:ring-stone-600 sm:text-sm sm:leading-6" %>
              </div>
            </div>
          </div>
        </div>

        <!-- Job Specifications -->
        <div class="grid grid-cols-2 pb-12 border-b gap-x-8 gap-y-10 border-stone-900/10 md:grid-cols-3">
          <div>
            <h2 class="text-base font-semibold leading-7 text-stone-900">Job Specifications</h2>
            <p class="mt-1 text-sm leading-6 text-stone-600">Detailed specifications about the job.</p>
          </div>

          <div class="grid max-w-2xl grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-6 md:col-span-2">
            <div class="sm:col-span-3">
              <%= form.label :platform, class: "block text-sm font-medium leading-6 text-stone-900" %>
              <div class="mt-2">
                <%= form.select :platform, Job.platforms.keys.map { |key| [key.titleize, key] }, { prompt: "Select Platform" }, class: "block w-full rounded-md border-0 py-1.5 text-stone-900 shadow-sm ring-1 ring-inset ring-stone-300 focus:ring-2 focus:ring-inset focus:ring-stone-600 sm:text-sm sm:leading-6" %>
              </div>
            </div>

            <div class="sm:col-span-3">
              <%= form.label :outcome, class: "block text-sm font-medium leading-6 text-stone-900" %>
              <div class="mt-2">
                <%= form.select :outcome, Job.outcomes.keys.map { |key| [key.titleize, key] }, { prompt: "Select Outcome" }, class: "block w-full rounded-md border-0 py-1.5 text-stone-900 shadow-sm ring-1 ring-inset ring-stone-300 focus:ring-2 focus:ring-inset focus:ring-stone-600 sm:text-sm sm:leading-6" %>
              </div>
            </div>

            <div class="sm:col-span-6">
              <h3 class="text-sm font-medium leading-6 text-stone-900">Content Topics</h3>
              <p class="mt-1 text-sm leading-6 text-stone-500">Select content topics relevant to this job</p>
              <div class="grid grid-cols-1 gap-4 mt-4 sm:grid-cols-2">
                <% 
                  # Get custom topics (those not in the predefined list)
                  custom_topics = @job.topics.present? ? @job.topics.reject { |t| Job::AVAILABLE_TOPICS.include?(t) } : []
                %>
                
                <% Job::AVAILABLE_TOPICS.each do |topic| %>
                  <div class="flex items-center">
                    <%= check_box_tag "job[topics][]", topic, @job.topics&.include?(topic), id: "job_topics_#{topic.parameterize}", class: "h-4 w-4 rounded border-stone-300 text-stone-600 focus:ring-stone-600" %>
                    <label for="job_topics_<%= topic.parameterize %>" class="block ml-3 text-sm font-medium leading-6 text-stone-900">
                      <%= topic %>
                    </label>
                  </div>
                <% end %>
                
                <% if custom_topics.any? %>
                  <div class="pt-4 mt-4 border-t sm:col-span-2">
                    <h4 class="mb-2 text-sm font-medium text-stone-900">Custom Topics:</h4>
                    <% custom_topics.each do |topic| %>
                      <div class="flex items-center mb-2">
                        <%= check_box_tag "job[topics][]", topic, true, id: "job_topics_#{topic.parameterize}", class: "h-4 w-4 rounded border-stone-300 text-stone-600 focus:ring-stone-600" %>
                        <label for="job_topics_<%= topic.parameterize %>" class="block ml-3 text-sm font-medium leading-6 text-stone-900">
                          <%= topic %>
                        </label>
                      </div>
                    <% end %>
                  </div>
                <% end %>
                
                <div class="flex items-center mt-2 sm:col-span-2">
                  <%= check_box_tag "other_topic", "1", false, id: "job_topics_other", class: "h-4 w-4 rounded border-stone-300 text-stone-600 focus:ring-stone-600", data: { action: "click->job-form#toggleOtherTopic" } %>
                  <label for="job_topics_other" class="block ml-3 text-sm font-medium leading-6 text-stone-900">
                    Add Another Topic:
                  </label>
                  <%= text_field_tag "job[other_topic]", nil, class: "ml-3 block w-full rounded-md border-0 py-1.5 text-stone-900 shadow-sm ring-1 ring-inset ring-stone-300 placeholder:text-stone-400 focus:ring-2 focus:ring-inset focus:ring-stone-600 sm:text-sm sm:leading-6", placeholder: "Enter custom topic", data: { job_form_target: "otherTopicField" }, style: "display: none;" %>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- Budget & Payment -->
        <div class="grid grid-cols-2 pb-12 border-b gap-x-8 gap-y-10 border-stone-900/10 md:grid-cols-3">
          <div>
            <h2 class="text-base font-semibold leading-7 text-stone-900">Budget & Payment</h2>
            <p class="mt-1 text-sm leading-6 text-stone-600">Information about the job's budget and payment details.</p>
          </div>

          <div class="grid max-w-2xl grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-6 md:col-span-2">
            <div class="sm:col-span-3">
              <%= form.label :payment_frequency, class: "block text-sm font-medium leading-6 text-stone-900" %>
              <div class="mt-2">
                <%= form.select :payment_frequency, Job.payment_frequencies.keys.map { |key| [key.titleize, key] }, { prompt: "Select Payment Frequency" }, class: "block w-full rounded-md border-0 py-1.5 text-stone-900 shadow-sm ring-1 ring-inset ring-stone-300 focus:ring-2 focus:ring-inset focus:ring-stone-600 sm:text-sm sm:leading-6" %>
              </div>
            </div>
            
            <div class="sm:col-span-6">
              <%= form.label :budget_range, "Budget Range", class: "block text-sm font-medium leading-6 text-stone-900" %>
              <div class="mt-4 space-y-4">
                <div class="flex items-center">
                  <%= form.radio_button :budget_range, :under_1000, class: "h-4 w-4 border-stone-300 text-stone-600 focus:ring-stone-600" %>
                  <label for="job_budget_range_under_1000" class="block ml-3 text-sm font-medium leading-6 text-stone-900">
                    Under $1,000 USD (ghostwriters who haven't worked with clients)
                  </label>
                </div>
                
                <div class="flex items-center">
                  <%= form.radio_button :budget_range, :range_1000_2000, class: "h-4 w-4 border-stone-300 text-stone-600 focus:ring-stone-600" %>
                  <label for="job_budget_range_range_1000_2000" class="block ml-3 text-sm font-medium leading-6 text-stone-900">
                    $1,000-$2,000 USD (ghostwriters with little experience)
                  </label>
                </div>
                
                <div class="flex items-center">
                  <%= form.radio_button :budget_range, :range_2000_4000, class: "h-4 w-4 border-stone-300 text-stone-600 focus:ring-stone-600" %>
                  <label for="job_budget_range_range_2000_4000" class="block ml-3 text-sm font-medium leading-6 text-stone-900">
                    $2,000-$4,000 USD (ghostwriters with solid experience)
                  </label>
                </div>
                
                <div class="flex items-center">
                  <%= form.radio_button :budget_range, :range_4000_7000, class: "h-4 w-4 border-stone-300 text-stone-600 focus:ring-stone-600" %>
                  <label for="job_budget_range_range_4000_7000" class="block ml-3 text-sm font-medium leading-6 text-stone-900">
                    $4,000-$7,000 USD (ghostwriters with high experience)
                  </label>
                </div>
                
                <div class="flex items-center">
                  <%= form.radio_button :budget_range, :above_7000, class: "h-4 w-4 border-stone-300 text-stone-600 focus:ring-stone-600" %>
                  <label for="job_budget_range_above_7000" class="block ml-3 text-sm font-medium leading-6 text-stone-900">
                    $7,000+ USD (typically involves an offer that promises a big specific result)
                  </label>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Additional Questions -->
        <div class="grid grid-cols-2 pb-12 border-b gap-x-8 gap-y-10 border-stone-900/10 md:grid-cols-3">
          <div>
            <h2 class="text-base font-semibold leading-7 text-stone-900">Additional Information</h2>
            <p class="mt-1 text-sm leading-6 text-stone-600">Extra details to help ghostwriters understand your needs.</p>
          </div>

          <div class="grid max-w-2xl grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-6 md:col-span-2">
            <div class="sm:col-span-6">
              <%= form.label :client_count, "How many clients do you currently have?", class: "block text-sm font-medium leading-6 text-stone-900" %>
              <div class="mt-2">
                <%= form.text_field :client_count, class: "block w-full rounded-md border-0 py-1.5 text-stone-900 shadow-sm ring-1 ring-inset ring-stone-300 placeholder:text-stone-400 focus:ring-2 focus:ring-inset focus:ring-stone-600 sm:text-sm sm:leading-6" %>
              </div>
            </div>

            <div class="sm:col-span-6">
              <%= form.label :charge_per_client, "How much do you charge per client, on average?", class: "block text-sm font-medium leading-6 text-stone-900" %>
              <div class="mt-2">
                <%= form.text_field :charge_per_client, class: "block w-full rounded-md border-0 py-1.5 text-stone-900 shadow-sm ring-1 ring-inset ring-stone-300 placeholder:text-stone-400 focus:ring-2 focus:ring-inset focus:ring-stone-600 sm:text-sm sm:leading-6" %>
              </div>
            </div>

            <div class="sm:col-span-6">
              <%= form.label :business_challenge, "What is your biggest business challenge right now?", class: "block text-sm font-medium leading-6 text-stone-900" %>
              <div class="mt-2">
                <%= form.text_area :business_challenge, rows: 3, class: "block w-full rounded-md border-0 py-1.5 text-stone-900 shadow-sm ring-1 ring-inset ring-stone-300 placeholder:text-stone-400 focus:ring-2 focus:ring-inset focus:ring-stone-600 sm:text-sm sm:leading-6" %>
              </div>
            </div>

            <div class="sm:col-span-6">
              <%= form.label :useful_info, "Is there's anything that would be useful for me or the ghostwriter to know, please share it here.", class: "block text-sm font-medium leading-6 text-stone-900" %>
              <div class="mt-2">
                <%= form.text_area :useful_info, rows: 3, class: "block w-full rounded-md border-0 py-1.5 text-stone-900 shadow-sm ring-1 ring-inset ring-stone-300 placeholder:text-stone-400 focus:ring-2 focus:ring-inset focus:ring-stone-600 sm:text-sm sm:leading-6" %>
              </div>
            </div>

            <div class="sm:col-span-6">
              <%= form.label :offer_summary, "Can you please give me a 3-5 sentence summary of your current offer and target audience?", class: "block text-sm font-medium leading-6 text-stone-900" %>
              <div class="mt-2">
                <%= form.text_area :offer_summary, rows: 3, class: "block w-full rounded-md border-0 py-1.5 text-stone-900 shadow-sm ring-1 ring-inset ring-stone-300 placeholder:text-stone-400 focus:ring-2 focus:ring-inset focus:ring-stone-600 sm:text-sm sm:leading-6" %>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="flex items-center justify-end mt-6 gap-x-6">
        <%= link_to "Cancel", scout_job_path(@job), class: "text-sm font-semibold leading-6 text-stone-900" %>
        <%= form.submit "Update Job", class: "px-3 py-2 text-sm font-semibold text-white bg-stone-600 rounded-md shadow-sm hover:bg-stone-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-stone-600" %>
      </div>
    <% end %>
  </div>
</div>
