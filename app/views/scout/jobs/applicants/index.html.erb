<div data-controller="scout-sidebar" class="flex flex-col lg:flex-row">
  <!-- Sidebar -->
  <div class="flex flex-col w-1/5 lg:h-full">
    <div class="flex px-5 py-4">
      <span class="text-3xl font-semibold tracking-[-0.4px]">Track Applicants</span>
      <span class="text-3xl font-semibold text-[#6100FF] tracking-[-0.4px]">.</span>
    </div>

      <!-- Search -->
      <div class="px-4 mb-2">
        <%= form_with url: scout_job_applicants_path(params[:job_id]), method: :get, data: { controller: "search", action: "input->search#submit" } do |f| %>
          <%= f.search_field :query,
                value: params[:query],
                placeholder: "Search by name, skills, or experience...",
                class: "w-full h-[42px] bg-white border border-[#e5e7eb] rounded-lg px-4 text-[16px] text-[#6b7280]" %>
        <% end %>
      </div>

    <div class="px-5 py-4">
      <% if @jobs.present? %>
        <ul class="space-y-2">
          <% @jobs.each do |job| %>
            <% active = (params[:job_id].to_i == job.id) %>
            <li class="p-2 rounded <%= active ? 'bg-slate-200 font-bold' : 'hover:bg-stone-100' %>">
              <%= link_to job.title, scout_job_applicants_path(job), class: "block w-full" %>
            </li>
          <% end %>
        </ul>
      <% else %>
        <p class="text-stone-500">No jobs available</p>
      <% end %>
    </div>

  </div>
  
  <!-- Main Content -->
  <div class="flex w-4/5 p-4 m-2 bg-white border rounded-md">
    <main data-sidebar-target="main" class="w-8/12 p-4">

            <h3 class="mb-4 text-sm font-semibold text-stone-900">Application Stage</h3>
      <!-- Create a status count object for the current job -->
      <% 
        # Create a hash to store counts for all statuses
        status_counts = {}
        
        # Initialize all possible statuses with zero count
        JobApplication.statuses.keys.each do |status|
          status_counts[status] = 0
        end
        
        # Fill in actual counts from grouped applications
        @grouped_applications.each do |status, applications|
          status_counts[status] = applications.count
        end
      %>
      
<!-- Before the status buttons -->
<% 
  # Calculate total counts for all statuses before any filtering
  total_counts = if params[:job_id].present?
    JobApplication.where(job_id: params[:job_id]).group(:status).count
  else
    JobApplication.where(job_id: @job_ids).group(:status).count
  end
  
  # Get total applications count
  total_applications = total_counts.values.sum
%>

<!-- Status Buttons -->
<div class="grid grid-cols-6 gap-4 mb-6 text-sm">
  <!-- All Candidates button -->
  <% 
    is_all_selected = !params[:status].present?
    all_button_class = is_all_selected ? 
      "bg-stone-900 rounded-lg px-4 py-2" : 
      "bg-transparent shadow border-stone-200 border rounded-lg px-4 py-2"
    all_text_class = is_all_selected ? "text-stone-50" : "text-stone-900"
    all_label_class = is_all_selected ? "text-stone-200" : "text-stone-600"
  %>
  
  <%= link_to scout_job_applicants_path(params[:job_id]),
      class: all_button_class,
      data: {
        controller: "application-filter",
        status: "all"
      } do %>
    <div class="flex items-center justify-between">
      <span class="<%= all_text_class %> text-lg">
        <%= total_applications %>
      </span>
    </div>
    <div class="mt-1">
      <span class="<%= all_label_class %>">Total</span>
    </div>
  <% end %>

  <!-- Status buttons -->
  <% JobApplication.statuses.keys.reject { |s| s == "withdrawn" }.each do |status| %>
    <% count = total_counts[status] || 0 %>
    <% is_selected = params[:status] == status %>
    <% 
      # Determine button styling based on selection state and count
      if is_selected
        # Applied button styling (selected)
        button_class = "bg-stone-900 rounded-lg px-4 py-2"
        text_class = "text-stone-50"
        label_class = "text-stone-200"
      elsif count == 0
        # Offered button styling (empty stage)
        button_class = "bg-transparent border-stone-200 border-dashed border rounded-lg px-4 py-2"
        text_class = "text-stone-400"
        label_class = "text-stone-500"
      else
        # Reviewed button styling (has applicants)
        button_class = "bg-transparent shadow border-stone-200 border rounded-lg px-4 py-2"
        text_class = "text-stone-900"
        label_class = "text-stone-600"
      end
    %>

    <% if count > 0 %>
      <%= link_to scout_job_applicants_path(params[:job_id], status: is_selected ? nil : status),
          class: button_class,
          data: {
            controller: "application-filter",
            status: status
          } do %>
        <div class="flex items-center justify-between">
          <span class="<%= text_class %> text-lg">
            <%= count %>
          </span>
        </div>
        <div class="mt-1">
          <span class="<%= label_class %>"><%= status.titleize %></span>
        </div>
      <% end %>
    <% else %>
      <span class="<%= button_class %>" style="pointer-events: none;">
        <div class="flex items-center justify-between">
          <span class="<%= text_class %> text-lg">
            <%= count %>
          </span>
        </div>
        <div class="mt-1">
          <span class="<%= label_class %>"><%= status.titleize %></span>
        </div>
      </span>
    <% end %>
  <% end %>
</div>

<!-- Results header -->
<div class="flex items-center justify-between mb-6">
  <div class="text-[16px] font-semibold text-[#1f2937]">
    <%= @applications.count %> Candidates Found
  </div>
  <div class="flex items-center gap-2">
    <!-- Add the show sidebar button (hidden by default) -->
    <button id="show-sidebar-button" class=" rotate-180 h-7 bg-white border border-[#e5e7eb] rounded px-2 flex items-center justify-center cursor-pointer" data-action="click->scout-sidebar#toggle" data-sidebar-target="toggleButton">
      <%= phosphor_icon "sidebar-simple",style: 'duotone', class: "h-4 w-4 text-stone-600" %>
    </button>
  </div>
</div>


      <!-- Applicants List -->
      <div id="applicants-list" class="border rounded-md border-stone-200">
        <% @grouped_applications.each do |status, applications| %>
          <% applications.each do |application| %>
            <div class="p-6 border-b cursor-pointer last:rounded-b first:rounded-t border-stone-200" 
                 data-controller="candidate-selection" 
                 data-action="click->candidate-selection#showDetails"
                 data-candidate-selection-id-value="<%= application.id %>"
                 data-candidate-selection-name-value="<%= application.user.name %>"
                 data-candidate-selection-job-title-value="<%= application.job.title %>"
                 data-candidate-selection-status-value="<%= application.status %>"
                 data-candidate-selection-avatar-url-value="<%= application.user.avatar.attached? ? url_for(application.user.avatar) : '' %>">
              <div class="flex items-start justify-between">
                <div class="flex-1">
                  <!-- Profile Header -->
                  <div class="flex items-center gap-4 mb-4">
                    <div class="flex items-center justify-center w-12 h-12 rounded-full bg-stone-200">
                      <% if application.user.avatar.attached? %>
                        <%= image_tag application.user.avatar, class: "w-full h-full object-cover rounded-full" %>
                      <% else %>
                        <%= application.user.name.initials %>
                      <% end %>
                    </div>
                    <div class="flex-1">
                      <div class="flex items-center justify-between">
                        <div>
                          <h3 class="font-semibold text-[#1f2937]"><%= application.user.name %></h3>
                          <div class="flex items-center gap-2 text-sm text-stone-600">
                            <span><%= application.job.title %></span>
                            <span class="text-stone-400">•</span>
                            <span class="px-2 py-0.5 text-xs rounded-full <%= status_color_class(application.status) %>">
                              <%= application.status.titleize %>
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- Application Letter Preview -->
                  <% if application.application_letter.present? %>
                    <div class="mb-4">
                      <p class="text-[14px] text-stone-700 leading-relaxed">
                        <%= truncate(application.application_letter, length: 150) %>
                      </p>
                    </div>
                  <% end %>

                  <!-- Skills Section (if available) -->
                  <% if application.user.talent_profile&.skills&.any? %>
                    <div class="flex flex-wrap gap-2">
                      <% application.user.talent_profile.skills.first(5).each do |skill| %>
                        <span class="px-3 py-1 text-sm rounded-md text-stone-600 bg-stone-100">
                          <%= skill %>
                        </span>
                      <% end %>
                    </div>
                  <% end %>

                  <!-- Action Buttons -->
                  <div class="flex items-center justify-end mt-6">
                    <div class="flex items-center gap-3">

                      <button class="flex items-center gap-2 px-4 py-2 text-sm rounded-md text-stone-50 bg-stone-900 hover:bg-stone-800">
                        Message
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          <% end %>
        <% end %>
      </div>
    </main>
<aside data-sidebar-target="aside" class="w-4/12 p-4 mx-auto ml-2 overflow-hidden transition-transform duration-300 ease-in-out border-l candidate-details-container border-stone-100">
  <div data-controller="candidate-details">
    <%= turbo_frame_tag "candidate-details-container" do %>
      <%= render "placeholder" %>
    <% end %>
  </div>
</aside>
  </div>


</div>
<%= render "shared/modal" %>
