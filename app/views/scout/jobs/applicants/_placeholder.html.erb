<turbo-frame id="candidate-details-container">
  <!-- Header -->
  <div class="mb-8">
    <div class="flex items-center gap-2 cursor-pointer">
    <h2 class="mb-4 text-base font-medium text-stone-900">Select a Candidate</h2>
    </div>
      <p class="flex-1 text-sm leading-relaxed text-stone-600">
      Click on the application from a list to view the details and actions.
    </p>
  </div>

  <!-- Stats Section -->
  <div class="flex flex-col gap-6 mb-8">
    <!-- Stat Card: Total Candidates -->
    <div class="bg-stone-100 rounded-md p-4 relative h-[84px]">
      <div>
        <p class="mb-1 text-sm text-stone-600">Total Candidates</p>
        <p class="m-0 text-2xl text-stone-900">10</p>
      </div>
      <div class="absolute text-stone-500 -translate-y-1/2 right-4 top-1/2">
        <%= phosphor_icon "users", class: "w-6 h-6" %>
      </div>
    </div>
    <!-- Stat Card: Active Applications -->
    <div class="bg-stone-100 rounded-md p-4 relative h-[84px]">
      <div>
        <p class="mb-1 text-sm text-stone-600">Active Applications</p>
        <p class="m-0 text-2xl text-stone-900">20</p>
      </div>
      <div class="absolute text-stone-500 -translate-y-1/2 right-4 top-1/2">
        <%= phosphor_icon "file-text", class: "w-6 h-6" %>
      </div>
    </div>
  </div>

  <!-- Tips Section -->
  <div class="mb-8">
    <h2 class="mb-4 text-base font-medium text-stone-900">Quick Tips</h2>
    <div class="flex items-start mb-4">
      <div class="mt-1 mr-3 text-black">
        <%= phosphor_icon "check-circle", style: 'fill', class: "w-5 h-5" %>
      </div>
      <p class="flex-1 text-sm leading-relaxed text-stone-600">
        Use filters to narrow down candidates by skills.
      </p>
    </div>
    <div class="flex items-start mb-4">
      <div class="mt-1 mr-3 text-black">
        <%= phosphor_icon "check-circle", style: 'fill', class: "w-5 h-5" %>
      </div>
      <p class="flex-1 text-sm leading-relaxed text-stone-600">
        Message candidates directly to schedule interviews or request additional information.
      </p>
    </div>
    <div class="flex items-start">
      <div class="mt-1 mr-3 text-black">
        <%= phosphor_icon "check-circle", style: 'fill', class: "w-5 h-5" %>
      </div>
      <p class="flex-1 text-sm leading-relaxed text-stone-600">
        Save promising candidates to review them later or share with the team.
      </p>
    </div>
  </div>
</turbo-frame>