<div
  data-controller="vertical-progress"
  data-vertical-progress-current-step-value="0"
  class="flex flex-col h-full"
>
  <!-- Header matching the Manage Jobs style -->
  <div class="flex px-5 py-4">
    <span class="text-3xl font-semibold tracking-[-0.4px]"
      >New Job Listing</span
    >
    <span class="text-3xl font-semibold text-[#6100FF] tracking-[-0.4px]"
      >.</span
    >
  </div>

  <!-- Progress Steps -->
  <div class="px-5 py-2">
    <nav aria-label="Progress">
      <ol role="list" class="space-y-6">
        <!-- Step 1: Category Selection -->
        <li
          data-vertical-progress-target="step"
          data-step-key="category_selection"
        >
          <div class="flex items-center group">
            <span
              data-vertical-progress-target="stepIndicator"
              class="relative flex items-center justify-center flex-shrink-0 w-5 h-5"
            >
              <!-- Checkmark icon (hidden by default) -->
              <svg
                data-vertical-progress-target="checkIcon"
                class="hidden w-full h-full group-hover:text-[#6100FF]"
                style="color: #6100ff"
                viewBox="0 0 20 20"
                fill="currentColor"
                aria-hidden="true"
              >
                <path
                  fill-rule="evenodd"
                  d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z"
                  clip-rule="evenodd"
                />
              </svg>
              <!-- Current step indicator (hidden by default) -->
              <span
                data-vertical-progress-target="currentIndicator"
                class="absolute hidden w-4 h-4 rounded-full"
                style="background-color: rgba(97, 0, 255, 0.2)"
              ></span>
              <span
                data-vertical-progress-target="currentDot"
                class="relative hidden w-2 h-2 rounded-full"
                style="background-color: #6100ff"
              ></span>
              <!-- Future step dot (visible by default) -->
              <div
                data-vertical-progress-target="futureDot"
                class="w-2 h-2 rounded-full bg-stone-300 group-hover:bg-stone-400"
              ></div>
            </span>
            <span
              data-vertical-progress-target="stepTitle"
              class="ml-3 text-xs font-medium text-gray-500 group-hover:text-gray-900"
            >
              Category Selection
            </span>
          </div>
        </li>

        <!-- Step 2: Topics Selection -->
        <li
          data-vertical-progress-target="step"
          data-step-key="topics_selection"
        >
          <div class="flex items-center group">
            <span
              data-vertical-progress-target="stepIndicator"
              class="relative flex items-center justify-center flex-shrink-0 w-5 h-5"
            >
              <!-- Checkmark icon (hidden by default) -->
              <svg
                data-vertical-progress-target="checkIcon"
                class="hidden w-full h-full group-hover:text-[#6100FF]"
                style="color: #6100ff"
                viewBox="0 0 20 20"
                fill="currentColor"
                aria-hidden="true"
              >
                <path
                  fill-rule="evenodd"
                  d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z"
                  clip-rule="evenodd"
                />
              </svg>
              <!-- Current step indicator (hidden by default) -->
              <span
                data-vertical-progress-target="currentIndicator"
                class="absolute hidden w-4 h-4 rounded-full"
                style="background-color: rgba(97, 0, 255, 0.2)"
              ></span>
              <span
                data-vertical-progress-target="currentDot"
                class="relative hidden w-2 h-2 rounded-full"
                style="background-color: #6100ff"
              ></span>
              <!-- Future step dot (visible by default) -->
              <div
                data-vertical-progress-target="futureDot"
                class="w-2 h-2 rounded-full bg-stone-300 group-hover:bg-stone-400"
              ></div>
            </span>
            <span
              data-vertical-progress-target="stepTitle"
              class="ml-3 text-xs font-medium text-gray-500 group-hover:text-gray-900"
            >
              Topics Selection
            </span>
          </div>
        </li>

        <!-- Dynamic Category Steps (will be populated by JavaScript) -->
        <div data-vertical-progress-target="categorySteps"></div>

        <!-- Step: Job Details -->
        <li data-vertical-progress-target="step" data-step-key="job_details">
          <div class="flex items-center group">
            <span
              data-vertical-progress-target="stepIndicator"
              class="relative flex items-center justify-center flex-shrink-0 w-5 h-5"
            >
              <!-- Checkmark icon (hidden by default) -->
              <svg
                data-vertical-progress-target="checkIcon"
                class="hidden w-full h-full group-hover:text-[#6100FF]"
                style="color: #6100ff"
                viewBox="0 0 20 20"
                fill="currentColor"
                aria-hidden="true"
              >
                <path
                  fill-rule="evenodd"
                  d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z"
                  clip-rule="evenodd"
                />
              </svg>
              <!-- Current step indicator (hidden by default) -->
              <span
                data-vertical-progress-target="currentIndicator"
                class="absolute hidden w-4 h-4 rounded-full"
                style="background-color: rgba(97, 0, 255, 0.2)"
              ></span>
              <span
                data-vertical-progress-target="currentDot"
                class="relative hidden w-2 h-2 rounded-full"
                style="background-color: #6100ff"
              ></span>
              <!-- Future step dot (visible by default) -->
              <div
                data-vertical-progress-target="futureDot"
                class="w-2 h-2 rounded-full bg-stone-300 group-hover:bg-stone-400"
              ></div>
            </span>
            <span
              data-vertical-progress-target="stepTitle"
              class="ml-3 text-xs font-medium text-gray-500 group-hover:text-gray-900"
            >
              Job Details
            </span>
          </div>
        </li>

        <!-- Step: Personal Brands -->
        <li
          data-vertical-progress-target="step"
          data-step-key="personal_brands"
        >
          <div class="flex items-center group">
            <span
              data-vertical-progress-target="stepIndicator"
              class="relative flex items-center justify-center flex-shrink-0 w-5 h-5"
            >
              <!-- Checkmark icon (hidden by default) -->
              <svg
                data-vertical-progress-target="checkIcon"
                class="hidden w-full h-full group-hover:text-[#6100FF]"
                style="color: #6100ff"
                viewBox="0 0 20 20"
                fill="currentColor"
                aria-hidden="true"
              >
                <path
                  fill-rule="evenodd"
                  d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z"
                  clip-rule="evenodd"
                />
              </svg>
              <!-- Current step indicator (hidden by default) -->
              <span
                data-vertical-progress-target="currentIndicator"
                class="absolute hidden w-4 h-4 rounded-full"
                style="background-color: rgba(97, 0, 255, 0.2)"
              ></span>
              <span
                data-vertical-progress-target="currentDot"
                class="relative hidden w-2 h-2 rounded-full"
                style="background-color: #6100ff"
              ></span>
              <!-- Future step dot (visible by default) -->
              <div
                data-vertical-progress-target="futureDot"
                class="w-2 h-2 rounded-full bg-stone-300 group-hover:bg-stone-400"
              ></div>
            </span>
            <span
              data-vertical-progress-target="stepTitle"
              class="ml-3 text-xs font-medium text-gray-500 group-hover:text-gray-900"
            >
              Personal Brands
            </span>
          </div>
        </li>

        <!-- Step: Target Audience -->
        <li
          data-vertical-progress-target="step"
          data-step-key="target_audience"
        >
          <div class="flex items-center group">
            <span
              data-vertical-progress-target="stepIndicator"
              class="relative flex items-center justify-center flex-shrink-0 w-5 h-5"
            >
              <!-- Checkmark icon (hidden by default) -->
              <svg
                data-vertical-progress-target="checkIcon"
                class="hidden w-full h-full group-hover:text-[#6100FF]"
                style="color: #6100ff"
                viewBox="0 0 20 20"
                fill="currentColor"
                aria-hidden="true"
              >
                <path
                  fill-rule="evenodd"
                  d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z"
                  clip-rule="evenodd"
                />
              </svg>
              <!-- Current step indicator (hidden by default) -->
              <span
                data-vertical-progress-target="currentIndicator"
                class="absolute hidden w-4 h-4 rounded-full"
                style="background-color: rgba(97, 0, 255, 0.2)"
              ></span>
              <span
                data-vertical-progress-target="currentDot"
                class="relative hidden w-2 h-2 rounded-full"
                style="background-color: #6100ff"
              ></span>
              <!-- Future step dot (visible by default) -->
              <div
                data-vertical-progress-target="futureDot"
                class="w-2 h-2 rounded-full bg-stone-300 group-hover:bg-stone-400"
              ></div>
            </span>
            <span
              data-vertical-progress-target="stepTitle"
              class="ml-3 text-xs font-medium text-gray-500 group-hover:text-gray-900"
            >
              Target Audience
            </span>
          </div>
        </li>

        <!-- Step: Budget -->
        <li data-vertical-progress-target="step" data-step-key="budget">
          <div class="flex items-center group">
            <span
              data-vertical-progress-target="stepIndicator"
              class="relative flex items-center justify-center flex-shrink-0 w-5 h-5"
            >
              <!-- Checkmark icon (hidden by default) -->
              <svg
                data-vertical-progress-target="checkIcon"
                class="hidden w-full h-full group-hover:text-[#6100FF]"
                style="color: #6100ff"
                viewBox="0 0 20 20"
                fill="currentColor"
                aria-hidden="true"
              >
                <path
                  fill-rule="evenodd"
                  d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z"
                  clip-rule="evenodd"
                />
              </svg>
              <!-- Current step indicator (hidden by default) -->
              <span
                data-vertical-progress-target="currentIndicator"
                class="absolute hidden w-4 h-4 rounded-full"
                style="background-color: rgba(97, 0, 255, 0.2)"
              ></span>
              <span
                data-vertical-progress-target="currentDot"
                class="relative hidden w-2 h-2 rounded-full"
                style="background-color: #6100ff"
              ></span>
              <!-- Future step dot (visible by default) -->
              <div
                data-vertical-progress-target="futureDot"
                class="w-2 h-2 rounded-full bg-stone-300 group-hover:bg-stone-400"
              ></div>
            </span>
            <span
              data-vertical-progress-target="stepTitle"
              class="ml-3 text-xs font-medium text-gray-500 group-hover:text-gray-900"
            >
              Budget Range
            </span>
          </div>
        </li>

        <!-- Step: Work Duration -->
        <li data-vertical-progress-target="step" data-step-key="work_duration">
          <div class="flex items-center group">
            <span
              data-vertical-progress-target="stepIndicator"
              class="relative flex items-center justify-center flex-shrink-0 w-5 h-5"
            >
              <!-- Checkmark icon (hidden by default) -->
              <svg
                data-vertical-progress-target="checkIcon"
                class="hidden w-full h-full group-hover:text-[#6100FF]"
                style="color: #6100ff"
                viewBox="0 0 20 20"
                fill="currentColor"
                aria-hidden="true"
              >
                <path
                  fill-rule="evenodd"
                  d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z"
                  clip-rule="evenodd"
                />
              </svg>
              <!-- Current step indicator (hidden by default) -->
              <span
                data-vertical-progress-target="currentIndicator"
                class="absolute hidden w-4 h-4 rounded-full"
                style="background-color: rgba(97, 0, 255, 0.2)"
              ></span>
              <span
                data-vertical-progress-target="currentDot"
                class="relative hidden w-2 h-2 rounded-full"
                style="background-color: #6100ff"
              ></span>
              <!-- Future step dot (visible by default) -->
              <div
                data-vertical-progress-target="futureDot"
                class="w-2 h-2 rounded-full bg-stone-300 group-hover:bg-stone-400"
              ></div>
            </span>
            <span
              data-vertical-progress-target="stepTitle"
              class="ml-3 text-xs font-medium text-gray-500 group-hover:text-gray-900"
            >
              Work Duration
            </span>
          </div>
        </li>

        <!-- Step: Client Info -->
        <li data-vertical-progress-target="step" data-step-key="client_info">
          <div class="flex items-center group">
            <span
              data-vertical-progress-target="stepIndicator"
              class="relative flex items-center justify-center flex-shrink-0 w-5 h-5"
            >
              <!-- Checkmark icon (hidden by default) -->
              <svg
                data-vertical-progress-target="checkIcon"
                class="hidden w-full h-full group-hover:text-[#6100FF]"
                style="color: #6100ff"
                viewBox="0 0 20 20"
                fill="currentColor"
                aria-hidden="true"
              >
                <path
                  fill-rule="evenodd"
                  d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z"
                  clip-rule="evenodd"
                />
              </svg>
              <!-- Current step indicator (hidden by default) -->
              <span
                data-vertical-progress-target="currentIndicator"
                class="absolute hidden w-4 h-4 rounded-full"
                style="background-color: rgba(97, 0, 255, 0.2)"
              ></span>
              <span
                data-vertical-progress-target="currentDot"
                class="relative hidden w-2 h-2 rounded-full"
                style="background-color: #6100ff"
              ></span>
              <!-- Future step dot (visible by default) -->
              <div
                data-vertical-progress-target="futureDot"
                class="w-2 h-2 rounded-full bg-stone-300 group-hover:bg-stone-400"
              ></div>
            </span>
            <span
              data-vertical-progress-target="stepTitle"
              class="ml-3 text-xs font-medium text-gray-500 group-hover:text-gray-900"
            >
              Additional Info
            </span>
          </div>
        </li>
      </ol>
    </nav>
  </div>
</div>
