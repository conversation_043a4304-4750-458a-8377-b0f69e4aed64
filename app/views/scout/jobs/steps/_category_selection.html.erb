<%# Category Selection Step - Split into two separate steps %>

<%# Step 1: Category Selection Only %>
<div data-step-name="category_selection" data-form-wizard-target="step" class="">
  <div class="mb-6">
    <h3 class="text-2xl font-semibold text-stone-900 mb-2">Tell us about the ghostwriter you need</h3>
    <p class="text-stone-600">Choose the category that best describes the ghostwriting services you're looking for.</p>
  </div>

  <div class="mb-6">
    <%= form.label :job_category, "What type of ghostwriter are you looking to hire?",
        class: "block text-sm font-medium text-stone-700 mb-4" %>

    <div class="grid grid-cols-1 gap-4 sm:grid-cols-3">
      <!-- Social Media Card -->
      <label class="relative flex cursor-pointer rounded-lg border border-stone-300 bg-white p-4 shadow-sm focus:outline-none hover:border-stone-400 transition-colors duration-200">
        <%= form.radio_button :job_category, "social_media",
            class: "sr-only",
            data: { action: "change->form-wizard#handleCategoryChange" } %>
        <span class="flex flex-1">
          <span class="flex flex-col">
            <span class="block text-sm font-medium text-stone-900">Social Media</span>
            <span class="mt-1 flex items-center text-sm text-stone-500">
              Create engaging posts and content for social platforms like Twitter, LinkedIn, and Instagram
            </span>
          </span>
        </span>
        <!-- Selected state indicator -->
        <svg class="h-5 w-5 text-[#6100FF] opacity-0 transition-opacity duration-200" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
          <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.236 4.53L7.53 10.53a.75.75 0 00-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z" clip-rule="evenodd" />
        </svg>
        <!-- Focus ring -->
        <span class="pointer-events-none absolute -inset-px rounded-lg border-2 border-transparent" aria-hidden="true"></span>
      </label>

      <!-- Lead Magnet Card -->
      <label class="relative flex cursor-pointer rounded-lg border border-stone-300 bg-white p-4 shadow-sm focus:outline-none hover:border-stone-400 transition-colors duration-200">
        <%= form.radio_button :job_category, "lead_magnet",
            class: "sr-only",
            data: { action: "change->form-wizard#handleCategoryChange" } %>
        <span class="flex flex-1">
          <span class="flex flex-col">
            <span class="block text-sm font-medium text-stone-900">Lead Magnet</span>
            <span class="mt-1 flex items-center text-sm text-stone-500">
              Develop valuable resources like eBooks, checklists, and templates to capture leads
            </span>
          </span>
        </span>
        <!-- Selected state indicator -->
        <svg class="h-5 w-5 text-[#6100FF] opacity-0 transition-opacity duration-200" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
          <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.236 4.53L7.53 10.53a.75.75 0 00-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z" clip-rule="evenodd" />
        </svg>
        <!-- Focus ring -->
        <span class="pointer-events-none absolute -inset-px rounded-lg border-2 border-transparent" aria-hidden="true"></span>
      </label>

      <!-- Newsletter Card -->
      <label class="relative flex cursor-pointer rounded-lg border border-stone-300 bg-white p-4 shadow-sm focus:outline-none hover:border-stone-400 transition-colors duration-200">
        <%= form.radio_button :job_category, "newsletter",
            class: "sr-only",
            data: { action: "change->form-wizard#handleCategoryChange" } %>
        <span class="flex flex-1">
          <span class="flex flex-col">
            <span class="block text-sm font-medium text-stone-900">Newsletter</span>
            <span class="mt-1 flex items-center text-sm text-stone-500">
              Write compelling email newsletters to engage and nurture your audience
            </span>
          </span>
        </span>
        <!-- Selected state indicator -->
        <svg class="h-5 w-5 text-[#6100FF] opacity-0 transition-opacity duration-200" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
          <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.236 4.53L7.53 10.53a.75.75 0 00-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z" clip-rule="evenodd" />
        </svg>
        <!-- Focus ring -->
        <span class="pointer-events-none absolute -inset-px rounded-lg border-2 border-transparent" aria-hidden="true"></span>
      </label>
    </div>

    <span class="error-message hidden text-red-500 text-sm mt-2">Please select a category</span>
  </div>

  <div class="step-error-message hidden mt-6 p-4 text-red-700 bg-red-50 border border-red-200 rounded-md text-sm">
    Please select a category before continuing.
  </div>
</div>

<%# Step 2: Topics Selection %>
<div data-step-name="topics_selection" data-form-wizard-target="step" class="hidden">
  <div class="mb-6">
    <h3 class="text-2xl font-semibold text-stone-900 mb-2">What topics should they write about?</h3>
    <p class="text-stone-600">Select the topics and areas of expertise that are most relevant to your content needs.</p>
  </div>

  <div class="mb-6">
    <%= form.label :topics, "Select all that apply",
        class: "block text-sm font-medium text-stone-700 mb-3" %>
    <div class="mt-3 grid grid-cols-2 gap-3 sm:grid-cols-3">
      <% Job::AVAILABLE_TOPICS.each do |topic| %>
        <label class="inline-flex items-center p-3 border border-stone-200 rounded-md hover:bg-stone-50 cursor-pointer">
          <%= form.check_box :topics,
              { multiple: true, checked: form.object.topics&.include?(topic), class: "focus:ring-stone-500 h-4 w-4 text-stone-600 border-stone-300 rounded" },
              topic,
              nil %>
          <span class="ml-3 text-sm text-stone-700"><%= topic %></span>
        </label>
      <% end %>
    </div>

    <div class="mt-6">
      <%= form.label :other_topic, "Other (please specify):",
          class: "block text-sm font-medium text-stone-700 mb-2" %>
      <%= form.text_field :other_topic,
          class: "mt-1 block w-full border border-stone-300 rounded-md shadow-sm py-3 px-3 focus:outline-none focus:ring-stone-500 focus:border-stone-500 sm:text-sm",
          placeholder: "Enter other topic..." %>
    </div>
  </div>
</div>
