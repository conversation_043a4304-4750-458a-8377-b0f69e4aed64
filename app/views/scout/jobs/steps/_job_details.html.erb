<%# Job Details Step %>
<div data-step-name="job_details" data-form-wizard-target="step" class="hidden">
  <div class="mb-6">
    <h3 class="mb-2 text-2xl font-semibold text-stone-900">Job Listing Details</h3>
    <p class="text-stone-600">Provide the essential details for your job posting to attract the right ghostwriters.</p>
  </div>

  <div class="mb-6">
    <%= form.label :title, "Write the title for your job post.",
        class: "block text-sm font-medium text-stone-700 mb-2" %>
    <%= form.text_field :title,
        class: "mt-1 block w-full border border-stone-300 rounded-md shadow-sm py-3 px-3 focus:outline-none focus:ring-stone-500 focus:border-stone-500 sm:text-sm",
        placeholder: "e.g., Newsletter Ghostwriter for Tech Startup" %>
    <span class="hidden mt-2 text-sm text-red-500 error-message">Please enter a job title</span>
  </div>

  <div class="mb-6">
    <h4 class="mb-4 text-lg font-medium text-stone-700">Text</h4>
    <div class="p-4 border rounded-md border-stone-300 bg-stone-50">
      <div class="mb-4">
        <%= form.text_area :headline,
            rows: 3,
            class: "w-full border border-stone-300 rounded-md shadow-sm py-3 px-3 focus:outline-none focus:ring-stone-500 focus:border-stone-500 sm:text-sm",
            placeholder: "Write the headline for your job listing (70 character limit)." %>
      </div>
      <div class="flex items-center">
        <%= form.check_box :show_headline_in_form,
            class: "h-4 w-4 text-blue-600 focus:ring-blue-500 border-stone-300 rounded" %>
        <%= form.label :show_headline_in_form, "Show this text in form",
            class: "ml-2 text-sm text-stone-700" %>
      </div>
    </div>

    <div class="mt-4">
      <h5 class="mb-3 text-base font-medium text-stone-700">Description</h5>
      <div class="p-4 border rounded-md border-stone-300 bg-stone-50">
        <p class="mb-3 text-sm text-stone-600">This is the first thing ghostwriting candidates see. Please make your title concise and to the point.</p>

        <div class="mb-3">
          <p class="mb-2 text-sm font-medium text-stone-700">Example titles:</p>
          <ul class="space-y-1 text-sm text-stone-600">
            <li>• Tech founder wanting to grow to 10K LinkedIn followers.</li>
            <li>• Need a newsletter ghostwriter to write daily fitness emails.</li>
            <li>• High-converting lead magnet needed to generate leads for high-ticket info product.</li>
          </ul>
        </div>
      </div>
    </div>
  </div>

  <div class="mb-6">
    <%= form.label :description, "Describe the role and what you're looking for.",
        class: "block text-sm font-medium text-stone-700 mb-2" %>
    <%= form.text_area :description,
        rows: 6,
        class: "mt-1 block w-full border border-stone-300 rounded-md shadow-sm py-3 px-3 focus:outline-none focus:ring-stone-500 focus:border-stone-500 sm:text-sm",
        placeholder: "Describe the type of content you need, your target audience, tone of voice, and any specific requirements..." %>
    <span class="hidden mt-2 text-sm text-red-500 error-message">Please provide a job description</span>
  </div>

  <div class="mb-6">
    <%= form.label :requirements, "What are your requirements for the ghostwriter?",
        class: "block text-sm font-medium text-stone-700 mb-2" %>
    <%= form.text_area :requirements,
        rows: 4,
        class: "mt-1 block w-full border border-stone-300 rounded-md shadow-sm py-3 px-3 focus:outline-none focus:ring-stone-500 focus:border-stone-500 sm:text-sm",
        placeholder: "e.g., 2+ years experience, portfolio examples, specific industry knowledge..." %>
    <p class="mt-2 text-sm text-stone-500">Optional: List any specific qualifications or experience you require</p>
  </div>

  <div class="hidden p-4 mt-6 text-sm text-red-700 border border-red-200 rounded-md step-error-message bg-red-50">
    Please complete all required fields before continuing.
  </div>

  <% if Rails.env.development? %>
    <div class="p-4 mt-8 border rounded-md bg-stone-100 border-stone-300">
      <h4 class="mb-3 text-sm font-medium text-stone-700">Development: Form Data Preview</h4>
      <div class="space-y-1 text-xs text-stone-600" data-form-wizard-target="devPreview">
        <div><strong>Category:</strong> <span data-dev-field="job_category">-</span></div>
        <div><strong>Platform:</strong> <span data-dev-field="platform">-</span></div>
        <div><strong>Outcome:</strong> <span data-dev-field="outcome">-</span> <span data-dev-field="outcome_status" class="text-red-500"></span></div>
        <div><strong>Topics:</strong> <span data-dev-field="topics">-</span></div>
        <div><strong>Newsletter Frequency:</strong> <span data-dev-field="newsletter_frequency">-</span></div>
        <div><strong>Newsletter Length:</strong> <span data-dev-field="newsletter_length">-</span></div>
        <div><strong>Lead Magnet Type:</strong> <span data-dev-field="lead_magnet_type">-</span></div>
        <div><strong>Budget Range:</strong> <span data-dev-field="budget_range">-</span></div>
        <div><strong>Work Duration:</strong> <span data-dev-field="work_duration">-</span></div>
        <div><strong>Title:</strong> <span data-dev-field="title">-</span></div>
        <div><strong>Headline:</strong> <span data-dev-field="headline">-</span></div>
        <div><strong>Show Headline in Form:</strong> <span data-dev-field="show_headline_in_form">-</span></div>
        <div><strong>Description:</strong> <span data-dev-field="description">-</span></div>
        <div><strong>Personal Brands:</strong> <span data-dev-field="emulated_brands_description">-</span></div>
        <div><strong>Target Audience:</strong> <span data-dev-field="target_audience_description">-</span></div>
        <div><strong>Social Media Goal Type:</strong> <span data-dev-field="social_media_goal_type">-</span></div>
        <div><strong>Risk Acknowledged:</strong> <span data-dev-field="social_media_understands_risk_acknowledged">-</span></div>
        <div class="pt-2 mt-2 border-t border-stone-300">
          <div><strong>Outcome Fields Status:</strong></div>
          <div class="ml-2 text-xs">
            <div>Social Media: <span data-dev-field="outcome_social_media_status">-</span></div>
            <div>Newsletter: <span data-dev-field="outcome_newsletter_status">-</span></div>
            <div>Lead Magnet: <span data-dev-field="outcome_lead_magnet_status">-</span></div>
          </div>
        </div>
      </div>
    </div>
  <% end %>
</div>

<%# Personal Brands Step %>
<div data-step-name="personal_brands" data-form-wizard-target="step" class="hidden">
  <div class="mb-6">
    <h3 class="mb-2 text-2xl font-semibold text-stone-900">Personal Brands to Emulate</h3>
    <p class="text-stone-600">Help ghostwriters understand the direction you want to take your brand by listing personal brands you admire.</p>
  </div>

  <div class="mb-6">
    <%= form.label :emulated_brands_description, "Are there any personal brands you want to emulate? List them below.",
        class: "block text-sm font-medium text-stone-700 mb-2" %>
    <%= form.text_area :emulated_brands_description,
        rows: 4,
        class: "mt-1 block w-full border border-stone-300 rounded-md shadow-sm py-3 px-3 focus:outline-none focus:ring-stone-500 focus:border-stone-500 sm:text-sm",
        placeholder: "e.g., I love Alex Hormozi's posts. Authoritative but approachable.\nI want to emulate Dan Koe's philosophical style.\nI'd like my brand to be a mix between Sahil Bloom and Justin Welsh." %>
    <div class="p-4 mt-3 border rounded-md bg-stone-50 border-stone-200">
      <p class="mb-2 text-sm font-medium text-stone-700">This helps ghostwriters understand the direction you want to take your brand.</p>
      <p class="mb-3 text-sm text-stone-600"><strong>Personal brand examples:</strong></p>
      <ul class="ml-4 space-y-1 text-sm list-disc text-stone-600">
        <li>"I love Alex Hormozi's posts. Authoritative but approachable."</li>
        <li>"I want to emulate Dan Koe's philosophical style."</li>
        <li>"I'd like my brand to be a mix between Sahil Bloom and Justin Welsh."</li>
      </ul>
    </div>
  </div>
</div>

<%# Target Audience Step %>
<div data-step-name="target_audience" data-form-wizard-target="step" class="hidden">
  <div class="mb-6">
    <h3 class="mb-2 text-2xl font-semibold text-stone-900">Target Audience</h3>
    <p class="text-stone-600">Help ghostwriters understand who your content is intended for by providing detailed information about your target audience.</p>
  </div>

  <div class="mb-6">
    <%= form.label :target_audience_description, "Which target audience is your content intended for?",
        class: "block text-sm font-medium text-stone-700 mb-2" %>
    <%= form.text_area :target_audience_description,
        rows: 4,
        class: "mt-1 block w-full border border-stone-300 rounded-md shadow-sm py-3 px-3 focus:outline-none focus:ring-stone-500 focus:border-stone-500 sm:text-sm",
        placeholder: "Describe your target audience in detail..." %>

    <div class="mt-4">
      <h5 class="mb-3 text-base font-medium text-stone-700">Description</h5>
      <div class="p-4 border rounded-md border-stone-300 bg-stone-50">
        <p class="mb-3 text-sm text-stone-600">The more detail you can provide ghostwriters about your target audience, the better.</p>

        <div class="mb-3">
          <p class="mb-2 text-sm font-medium text-stone-700">Target audience examples:</p>
          <ul class="space-y-1 text-sm text-stone-600">
            <li>• Angel investors in the tech space.</li>
            <li>• B2B businesses doing $200K/month in revenue.</li>
            <li>• Male entrepreneurs over 40 looking to lose fat and build muscle.</li>
            <li>• 9-to-5 workers who want to quit their job and start an online business.</li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</div>

<%# Topics Step %>
<div data-step-name="topics" data-form-wizard-target="step" class="hidden">
  <div class="mb-6">
    <h3 class="mb-2 text-2xl font-semibold text-stone-900">Topics & Expertise</h3>
    <p class="text-stone-600">
      Select the topics and areas of expertise that are most relevant to your content needs.
      This helps match you with ghostwriters who have experience in your industry.
    </p>
  </div>

  <div class="mb-6">
    <%= form.label :topics, "Relevant topics (select all that apply):",
        class: "block text-sm font-medium text-stone-700 mb-3" %>
    <div class="grid grid-cols-2 gap-3 mt-3 sm:grid-cols-3">
      <% Job::AVAILABLE_TOPICS.each do |topic| %>
        <label class="inline-flex items-center p-3 border rounded-md cursor-pointer border-stone-200 hover:bg-stone-50">
          <%= form.check_box :topics,
              { multiple: true, checked: form.object.topics&.include?(topic), class: "focus:ring-stone-500 h-4 w-4 text-stone-600 border-stone-300 rounded" },
              topic,
              nil %>
          <span class="ml-3 text-sm text-stone-700"><%= topic %></span>
        </label>
      <% end %>
    </div>

    <div class="mt-6">
      <%= form.label :other_topic, "Other topic not listed:",
          class: "block text-sm font-medium text-stone-700 mb-2" %>
      <%= form.text_field :other_topic,
          class: "mt-1 block w-full border border-stone-300 rounded-md shadow-sm py-3 px-3 focus:outline-none focus:ring-stone-500 focus:border-stone-500 sm:text-sm",
          placeholder: "Enter other topic..." %>
    </div>
  </div>
</div>

<%# Budget Step %>
<div data-step-name="budget" data-form-wizard-target="step" class="hidden">
  <div class="mb-6">
    <h3 class="mb-2 text-2xl font-semibold text-stone-900">Budget Range</h3>
    <p class="text-stone-600">Set your budget expectations to attract the right level of talent.</p>
  </div>

  <div class="p-4 mb-6 border border-blue-200 rounded-md bg-blue-50">
    <p class="mb-2 text-sm font-medium text-blue-800">Budget Guidelines:</p>
    <ul class="ml-4 space-y-1 text-sm text-blue-700 list-disc">
      <li>Higher budgets attract more experienced ghostwriters</li>
      <li>Consider the complexity and volume of content needed</li>
      <li>Budget ranges are monthly for ongoing work</li>
    </ul>
  </div>

  <div class="mb-6">
    <%= form.label :budget_range, "What's your budget range for this ghostwriter?",
        class: "block text-sm font-medium text-stone-700 mb-3" %>
    <%= form.select :budget_range,
        options_for_select([
          ['Under $1,000 USD per month', 'under_1000'],
          ['$1,000–$2,000 USD per month', 'range_1000_2000'],
          ['$2,000–$3,500 USD per month', 'range_2000_3500'],
          ['$3,500–$5,000 USD per month', 'range_3500_5000'],
          ['$5,000+ USD per month', 'above_5000']
        ], form.object.budget_range),
        { prompt: "Select budget range" },
        {
          class: "mt-1 block w-full pl-3 pr-10 py-3 text-base border-stone-300 focus:outline-none focus:ring-stone-500 focus:border-stone-500 sm:text-sm rounded-md shadow-sm"
        } %>
    <span class="hidden mt-2 text-sm text-red-500 error-message">Please select a budget range</span>
  </div>

  <div class="hidden p-4 mt-6 text-sm text-red-700 border border-red-200 rounded-md step-error-message bg-red-50">
    Please select a budget range before continuing.
  </div>
</div>

<%# Work Duration Step %>
<div data-step-name="work_duration" data-form-wizard-target="step" class="hidden">
  <div class="mb-6">
    <h3 class="mb-2 text-2xl font-semibold text-stone-900">Work Duration</h3>
    <p class="text-stone-600">Help ghostwriters understand the expected timeline for this engagement.</p>
  </div>

  <div class="mb-6">
    <%= form.label :work_duration, "How long do you expect this engagement to last?",
        class: "block text-sm font-medium text-stone-700 mb-4" %>
    <div class="mt-3 space-y-3">
      <% Job.work_durations.each do |key, value| %>
        <label class="inline-flex items-center p-3 border rounded-md cursor-pointer border-stone-200 hover:bg-stone-50">
          <%= form.radio_button :work_duration, key,
              class: "focus:ring-stone-500 h-4 w-4 text-stone-600 border-stone-300" %>
          <span class="ml-3 text-sm text-stone-700">
            <%= case key
                when 'one_time_project' then 'One-time project'
                when 'short_term' then 'Short-term (1-3 months)'
                when 'long_term' then 'Long-term (3+ months)'
                when 'not_sure' then 'Not sure yet'
                else key.humanize
                end %>
          </span>
        </label>
      <% end %>
    </div>
    <span class="hidden mt-2 text-sm text-red-500 error-message">Please select a work duration</span>
  </div>

  <div class="hidden p-4 mt-6 text-sm text-red-700 border border-red-200 rounded-md step-error-message bg-red-50">
    Please select a work duration before continuing.
  </div>
</div>

<%# Client Info Step %>
<div data-step-name="client_info" data-form-wizard-target="step" class="hidden">
  <div class="mb-6">
    <h3 class="mb-2 text-2xl font-semibold text-stone-900">Additional Information</h3>
    <p class="text-stone-600">Provide context that will help ghostwriters understand your needs and working style.</p>
  </div>

  <div class="mb-6">
    <%= form.label :business_challenge, "What's your biggest business challenge that this ghostwriter will help solve?",
        class: "block text-sm font-medium text-stone-700 mb-2" %>
    <%= form.text_area :business_challenge,
        rows: 3,
        class: "mt-1 block w-full border border-stone-300 rounded-md shadow-sm py-3 px-3 focus:outline-none focus:ring-stone-500 focus:border-stone-500 sm:text-sm",
        placeholder: "e.g., Need to establish thought leadership, increase brand awareness, generate more leads..." %>
  </div>

  <div class="mb-6">
    <%= form.label :useful_info, "Any other information that would be useful for ghostwriters to know?",
        class: "block text-sm font-medium text-stone-700 mb-2" %>
    <%= form.text_area :useful_info,
        rows: 3,
        class: "mt-1 block w-full border border-stone-300 rounded-md shadow-sm py-3 px-3 focus:outline-none focus:ring-stone-500 focus:border-stone-500 sm:text-sm",
        placeholder: "e.g., Company culture, preferred communication style, specific tools you use..." %>
  </div>

  <div class="mb-6">
    <%= form.label :involvement_level, "How involved do you want to be in the content creation process?",
        class: "block text-sm font-medium text-stone-700 mb-4" %>
    <div class="mt-3 space-y-3">
      <% Job.involvement_levels.each do |key, value| %>
        <label class="inline-flex items-center p-3 border rounded-md cursor-pointer border-stone-200 hover:bg-stone-50">
          <%= form.radio_button :involvement_level, key,
              class: "focus:ring-stone-500 h-4 w-4 text-stone-600 border-stone-300" %>
          <span class="ml-3 text-sm text-stone-700">
            <%= case key
                when 'hands_off' then 'Hands-off (minimal involvement, trust the ghostwriter)'
                when 'collaborative' then 'Collaborative (regular check-ins and feedback)'
                when 'hands_on' then 'Hands-on (close oversight and frequent revisions)'
                else key.humanize
                end %>
          </span>
        </label>
      <% end %>
    </div>
  </div>
</div>
