<%# Lead Magnet Category Steps %>

<!-- Lead Magnet Goal Step -->
<div data-step-name="lead_magnet_goal" data-form-wizard-target="step" class="hidden">
  <div class="mb-6">
    <h3 class="text-2xl font-semibold text-stone-900 mb-2">Lead Magnet Goal</h3>
    <p class="text-stone-600">Define the primary objective for your lead magnet content.</p>
  </div>

  <!-- Show selected topics -->
  <div class="mb-6 p-4 bg-stone-50 border border-stone-200 rounded-md">
    <p class="text-sm font-medium text-stone-700 mb-2">Selected topics:</p>
    <div class="text-sm text-stone-800" data-selected-topics>
      <!-- Topics will be populated by JavaScript -->
    </div>
  </div>

  <div class="mb-6">
    <%= form.label :outcome, "What's the main goal for your lead magnet?",
        class: "block text-sm font-medium text-stone-700 mb-4" %>

    <div class="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
      <!-- Grow Email List Card -->
      <label class="relative flex cursor-pointer rounded-lg border border-stone-300 bg-white p-4 shadow-sm focus:outline-none hover:border-stone-400 transition-colors duration-200">
        <%= form.radio_button :outcome, "grow_email_list",
            class: "sr-only",
            data: { action: "change->form-wizard#handleGoalChange" } %>
        <span class="flex flex-1">
          <span class="flex flex-col">
            <span class="block text-sm font-medium text-stone-900">Grow Email List</span>
            <span class="mt-1 flex items-center text-sm text-stone-500">
              Capture email addresses and build a valuable subscriber base for future marketing
            </span>
          </span>
        </span>
        <!-- Selected state indicator -->
        <svg class="h-5 w-5 text-[#6100FF] opacity-0 transition-opacity duration-200" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
          <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.236 4.53L7.53 10.53a.75.75 0 00-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z" clip-rule="evenodd" />
        </svg>
        <!-- Focus ring -->
        <span class="pointer-events-none absolute -inset-px rounded-lg border-2 border-transparent" aria-hidden="true"></span>
      </label>

      <!-- Book Sales Calls Card -->
      <label class="relative flex cursor-pointer rounded-lg border border-stone-300 bg-white p-4 shadow-sm focus:outline-none hover:border-stone-400 transition-colors duration-200">
        <%= form.radio_button :outcome, "book_sales_calls",
            class: "sr-only",
            data: { action: "change->form-wizard#handleGoalChange" } %>
        <span class="flex flex-1">
          <span class="flex flex-col">
            <span class="block text-sm font-medium text-stone-900">Book Sales Calls</span>
            <span class="mt-1 flex items-center text-sm text-stone-500">
              Generate qualified leads and schedule sales conversations with potential clients
            </span>
          </span>
        </span>
        <!-- Selected state indicator -->
        <svg class="h-5 w-5 text-[#6100FF] opacity-0 transition-opacity duration-200" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
          <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.236 4.53L7.53 10.53a.75.75 0 00-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z" clip-rule="evenodd" />
        </svg>
        <!-- Focus ring -->
        <span class="pointer-events-none absolute -inset-px rounded-lg border-2 border-transparent" aria-hidden="true"></span>
      </label>

      <!-- Drive Traffic to Website Card -->
      <label class="relative flex cursor-pointer rounded-lg border border-stone-300 bg-white p-4 shadow-sm focus:outline-none hover:border-stone-400 transition-colors duration-200">
        <%= form.radio_button :outcome, "drive_traffic_to_website",
            class: "sr-only",
            data: { action: "change->form-wizard#handleGoalChange" } %>
        <span class="flex flex-1">
          <span class="flex flex-col">
            <span class="block text-sm font-medium text-stone-900">Drive Traffic to Website</span>
            <span class="mt-1 flex items-center text-sm text-stone-500">
              Increase website visitors and direct traffic to your main business site
            </span>
          </span>
        </span>
        <!-- Selected state indicator -->
        <svg class="h-5 w-5 text-[#6100FF] opacity-0 transition-opacity duration-200" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
          <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.236 4.53L7.53 10.53a.75.75 0 00-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z" clip-rule="evenodd" />
        </svg>
        <!-- Focus ring -->
        <span class="pointer-events-none absolute -inset-px rounded-lg border-2 border-transparent" aria-hidden="true"></span>
      </label>
    </div>

    <span class="error-message hidden text-red-500 text-sm mt-2">Please select a goal</span>
  </div>
  <div class="step-error-message hidden mt-6 p-4 text-red-700 bg-red-50 border border-red-200 rounded-md text-sm">
    Please select a goal before continuing.
  </div>
</div>