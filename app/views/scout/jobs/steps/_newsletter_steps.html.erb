<%# Newsletter Category Steps %>

<!-- Newsletter Goal Step -->
<div data-step-name="newsletter_goal" data-form-wizard-target="step" class="hidden">
  <div class="mb-6">
    <h3 class="text-2xl font-semibold text-stone-900 mb-2">Newsletter Goal</h3>
    <p class="text-stone-600">Define the primary objective for your newsletter content.</p>
  </div>

  <!-- Show selected topics -->
  <div class="mb-6 p-4 bg-stone-50 border border-stone-200 rounded-md">
    <p class="text-sm font-medium text-stone-700 mb-2">Selected topics:</p>
    <div class="text-sm text-stone-800" data-selected-topics>
      <!-- Topics will be populated by JavaScript -->
    </div>
  </div>

  <div class="mb-6">
    <%= form.label :outcome, "What's the main goal for your newsletter?",
        class: "block text-sm font-medium text-stone-700 mb-4" %>

    <div class="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
      <!-- Build Brand Card -->
      <label class="relative flex cursor-pointer rounded-lg border border-stone-300 bg-white p-4 shadow-sm focus:outline-none hover:border-stone-400 transition-colors duration-200">
        <%= form.radio_button :outcome, "build_brand",
            class: "sr-only",
            data: { action: "change->form-wizard#handleGoalChange" } %>
        <span class="flex flex-1">
          <span class="flex flex-col">
            <span class="block text-sm font-medium text-stone-900">Build Brand</span>
            <span class="mt-1 flex items-center text-sm text-stone-500">
              Establish thought leadership and strengthen your brand presence through valuable content
            </span>
          </span>
        </span>
        <!-- Selected state indicator -->
        <svg class="h-5 w-5 text-[#6100FF] opacity-0 transition-opacity duration-200" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
          <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.236 4.53L7.53 10.53a.75.75 0 00-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z" clip-rule="evenodd" />
        </svg>
        <!-- Focus ring -->
        <span class="pointer-events-none absolute -inset-px rounded-lg border-2 border-transparent" aria-hidden="true"></span>
      </label>

      <!-- Drive Traffic to Offers Card -->
      <label class="relative flex cursor-pointer rounded-lg border border-stone-300 bg-white p-4 shadow-sm focus:outline-none hover:border-stone-400 transition-colors duration-200">
        <%= form.radio_button :outcome, "drive_traffic_to_offers",
            class: "sr-only",
            data: { action: "change->form-wizard#handleGoalChange" } %>
        <span class="flex flex-1">
          <span class="flex flex-col">
            <span class="block text-sm font-medium text-stone-900">Drive Traffic to My Offers</span>
            <span class="mt-1 flex items-center text-sm text-stone-500">
              Direct subscribers to your products, services, or special promotions
            </span>
          </span>
        </span>
        <!-- Selected state indicator -->
        <svg class="h-5 w-5 text-[#6100FF] opacity-0 transition-opacity duration-200" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
          <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.236 4.53L7.53 10.53a.75.75 0 00-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z" clip-rule="evenodd" />
        </svg>
        <!-- Focus ring -->
        <span class="pointer-events-none absolute -inset-px rounded-lg border-2 border-transparent" aria-hidden="true"></span>
      </label>

      <!-- Nurture Leads for Future Sales Card -->
      <label class="relative flex cursor-pointer rounded-lg border border-stone-300 bg-white p-4 shadow-sm focus:outline-none hover:border-stone-400 transition-colors duration-200">
        <%= form.radio_button :outcome, "nurture_leads_for_future_sales",
            class: "sr-only",
            data: { action: "change->form-wizard#handleGoalChange" } %>
        <span class="flex flex-1">
          <span class="flex flex-col">
            <span class="block text-sm font-medium text-stone-900">Nurture Leads for Future Sales</span>
            <span class="mt-1 flex items-center text-sm text-stone-500">
              Build relationships with prospects and guide them through your sales funnel
            </span>
          </span>
        </span>
        <!-- Selected state indicator -->
        <svg class="h-5 w-5 text-[#6100FF] opacity-0 transition-opacity duration-200" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
          <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.236 4.53L7.53 10.53a.75.75 0 00-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z" clip-rule="evenodd" />
        </svg>
        <!-- Focus ring -->
        <span class="pointer-events-none absolute -inset-px rounded-lg border-2 border-transparent" aria-hidden="true"></span>
      </label>
    </div>

    <span class="error-message hidden text-red-500 text-sm mt-2">Please select a goal</span>
  </div>
  <div class="step-error-message hidden mt-6 p-4 text-red-700 bg-red-50 border border-red-200 rounded-md text-sm">
    Please select a goal before continuing.
  </div>
</div>

<!-- Newsletter Frequency Step -->
<div data-step-name="newsletter_frequency" data-form-wizard-target="step" class="hidden">
  <div class="mb-6">
    <h3 class="text-2xl font-semibold text-stone-900 mb-2">Newsletter Frequency</h3>
    <p class="text-stone-600">How often do you plan to send your newsletter to subscribers?</p>
  </div>
  <div class="mb-6">
    <%= form.label :newsletter_frequency, "How often do you want to send your newsletter?",
        class: "block text-sm font-medium text-stone-700 mb-4" %>
    <div class="mt-3 space-y-3">
      <% Job.newsletter_frequencies.each do |key, value| %>
        <label class="inline-flex items-center p-3 border border-stone-200 rounded-md hover:bg-stone-50 cursor-pointer">
          <%= form.radio_button :newsletter_frequency, key,
              class: "focus:ring-stone-500 h-4 w-4 text-stone-600 border-stone-300" %>
          <span class="ml-3 text-sm text-stone-700"><%= key.humanize %></span>
        </label>
      <% end %>
    </div>
    <span class="error-message hidden text-red-500 text-sm mt-2">Please select a frequency</span>
  </div>
  <div class="step-error-message hidden mt-6 p-4 text-red-700 bg-red-50 border border-red-200 rounded-md text-sm">
    Please select a frequency before continuing.
  </div>
</div>

<!-- Newsletter Length Step -->
<div data-step-name="newsletter_length" data-form-wizard-target="step" class="hidden">
  <div class="mb-6">
    <h3 class="text-2xl font-semibold text-stone-900 mb-2">Newsletter Length</h3>
    <p class="text-stone-600">Choose the ideal length for each newsletter edition.</p>
  </div>
  <div class="mb-6">
    <%= form.label :newsletter_length, "How long should each newsletter be?",
        class: "block text-sm font-medium text-stone-700 mb-4" %>
    <div class="mt-3 space-y-3">
      <% Job.newsletter_lengths.each do |key, value| %>
        <label class="inline-flex items-center p-3 border border-stone-200 rounded-md hover:bg-stone-50 cursor-pointer">
          <%= form.radio_button :newsletter_length, key,
              class: "focus:ring-stone-500 h-4 w-4 text-stone-600 border-stone-300" %>
          <span class="ml-3 text-sm text-stone-700">
            <%= case key
                when 'under_300_words' then 'Under 300 words'
                when 'words_300_600' then '300-600 words'
                when 'words_600_1000_plus' then '600-1000+ words'
                when 'any_length_is_fine' then 'Any length is fine'
                else key.humanize
                end %>
          </span>
        </label>
      <% end %>
    </div>
    <span class="error-message hidden text-red-500 text-sm mt-2">Please select a length</span>
  </div>
  <div class="step-error-message hidden mt-6 p-4 text-red-700 bg-red-50 border border-red-200 rounded-md text-sm">
    Please select a length before continuing.
  </div>
</div>


