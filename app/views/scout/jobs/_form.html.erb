<%# Modular Form Wizard - Refactored for better maintainability %>
<%= form_with(model: [:scout, @job], class: "max-w-4xl", id: "new_job_multi_step", data: { controller: "form-wizard", "form-wizard-current-step-value": 0 }) do |form| %>
  <% if @job.errors.any? %>
    <div class="p-4 mb-6 text-sm text-red-700 border border-red-200 rounded-lg bg-red-50">
      <h2 class="font-medium"><%= pluralize(@job.errors.count, "error") %> prohibited this job from being saved:</h2>
      <ul class="mt-2 list-disc list-inside">
        <% @job.errors.full_messages.each do |message| %>
          <li><%= message %></li>
        <% end %>
      </ul>
    </div>
  <% end %>

  <!-- Mobile Progress Bar (visible only on small screens) -->
  <div class="mb-6 lg:hidden">
    <div class="h-2 rounded-full bg-stone-200">
      <div data-form-wizard-target="progressBar" class="h-2 transition-all duration-300 rounded-full bg-stone-900" style="width: 0%"></div>
    </div>
    <div class="mt-2 text-xs text-center text-stone-600">
      <span data-form-wizard-target="stepTitle">Getting Started</span>
    </div>
  </div>

  <!-- Step Title (visible on all screens) -->
  <div class="mb-8">
    <h2 class="text-xl font-semibold lg:text-2xl text-stone-900">
      <span class="lg:hidden" data-form-wizard-target="stepTitle">Getting Started</span>
      <span class="hidden lg:inline" data-form-wizard-target="stepTitle">Getting Started</span>
    </h2>
  </div>

  <!-- Step Components -->
  <%= render 'scout/jobs/steps/category_selection', form: form %>
  <%= render 'scout/jobs/steps/social_media_steps', form: form %>

  <%= render 'scout/jobs/steps/newsletter_steps', form: form %>
  <%= render 'scout/jobs/steps/lead_magnet_steps', form: form %>

  <%= render 'scout/jobs/steps/job_details', form: form %>

  <!-- Navigation Buttons -->
  <div class="flex justify-between pt-8 mt-12 border-t border-stone-200">
    <div>
      <button type="button"
              data-form-wizard-target="previousButton"
              data-action="click->form-wizard#previousStep"
              class="flex items-center hidden px-4 py-2 text-sm font-medium bg-white border rounded-md shadow-sm text-stone-700 border-stone-300 hover:bg-stone-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-stone-500">
        <%= phosphor_icon "arrow-left", class: "w-4 h-4 mr-2" %>
        Back
      </button>
    </div>

    <!-- Action Buttons -->
    <div class="flex items-center justify-end space-x-4">
      <%= form.submit "Save as Draft", name: "commit", class: "text-sm font-medium text-stone-700 underline hover:text-stone-900 focus:outline-none" %>
      <button type="button"
              data-form-wizard-target="nextButton"
              data-action="click->form-wizard#nextStep"
              class="inline-flex items-center px-6 py-2 text-sm font-medium text-white rounded-md shadow-sm bg-stone-900 hover:bg-stone-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-stone-500">
        Continue
        <%= phosphor_icon "arrow-right", class: "w-4 h-4 ml-2" %>
      </button>
      <%= form.submit "Post Job", name: "commit", data: { form_wizard_target: "submitButton" }, class: "inline-flex items-center px-6 py-2 text-sm font-medium text-white bg-stone-900 rounded-md shadow-sm hover:bg-stone-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-stone-500 hidden" %>
    </div>
  </div>
<% end %>