<%# app/views/scout/jobs/_progress_indicator.html.erb %>
<nav aria-label="Progress">
  <ol role="list" class="space-y-4">
    <% steps = ["Category", "Job Details", "Outcome/Platform", "Topics", "Budget", "Client Info", "Details"] %>
    <% steps.each_with_index do |step_name, index| %>
      <% step_number = index + 1 %>
      <li data-multi-step-form-target="progressStep"
          data-action="click->multi-step-form#goToStep"
          data-step="<%= step_number %>"
          class="relative flex items-center cursor-pointer group state-default"> <%# Controller manages state-active/completed/default %>

        <%# Icon Container %>
        <span class="flex items-center justify-center w-6 h-6 mr-3 shrink-0">
          <%# Default State: Light grey dot %>
          <span class="hidden group-[.state-default]:block w-2.5 h-2.5 rounded-full bg-stone-300"></span>

          <%# Active State: Purple outer, purple inner circle %>
          <span class="hidden group-[.state-active]:flex relative items-center justify-center w-5 h-5 rounded-full bg-[#6100FF]/20"> <%# Lighter custom purple for outer ring %>
            <span class="w-2.5 h-2.5 rounded-full bg-[#6100FF]"></span> <%# Custom purple for inner dot %>
          </span>
          
          <%# Completed State: Purple circle with checkmark %>
          <span class="hidden group-[.state-completed]:flex items-center justify-center w-5 h-5 rounded-full bg-[#6100FF]"> <%# Custom purple background for completed %>
            <svg class="w-3 h-3 text-white" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
              <path fill-rule="evenodd" d="M16.704 4.153a.75.75 0 01.143 1.052l-8 10.5a.75.75 0 01-1.127.075l-4.5-4.5a.75.75 0 011.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 011.05-.143z" clip-rule="evenodd" />
            </svg>
          </span>
        </span>

        <%# Step Name Text %>
        <span class="text-sm text-stone-500 group-[.state-active]:text-[#6100FF] group-[.state-active]:font-semibold group-[.state-completed]:text-stone-700"> <%# Custom purple for active text %>
          <%= step_name %>
        </span>
      </li>
    <% end %>
  </ol>
</nav>
