<turbo-frame id="candidate-details-container">
<div class="p-0 ">
  <!-- Candidate <PERSON><PERSON> with Close Button -->
  <div class="flex justify-between items-center mb-4">
    <h3 class="text-[16px] font-semibold text-[#1f2937]">Candidate Details</h3>
    <button data-action="click->candidate-details#close" class="text-stone-500 hover:text-stone-700">
      <%= phosphor_icon "x", class: "h-5 w-5" %>
    </button>
  </div>

  <!-- Candidate Profile -->
  <div class="flex items-center mb-6">
    <div class="w-24 h-24 rounded-full overflow-hidden mr-4 bg-stone-200 flex items-center justify-center">
      <% if application.user.avatar.attached? %>
        <%= image_tag application.user.avatar, class: "w-full h-full object-cover", alt: "#{application.user.name}'s avatar" %>
      <% else %>
        <div class="w-full h-full flex items-center justify-center bg-stone-200 text-stone-600 text-xl">
          <%= application.user.name.initials %>
        </div>
      <% end %>
    </div>
    <div class="flex-1">
      <h4 class="text-xl font-semibold text-stone-900 "><%= application.user.name %></h4>
      <p class=" text-stone-600 mb-2"><%= application.job.title %></p>
      <div class="inline-block mt-2">
                            <div class="text-sm rounded-md border px-2 py-1 <%= status_color_class(application.status) %>">
                              <%= application.status.titleize %>
                            </div>
      </div>
    </div>
  </div>

  <!-- Quick Actions -->
  <div class="mb-6">
    <h3 class="text-sm font-medium text-stone-700 mb-4">Quick Actions</h3>
    <div class="flex gap-2">
      <button class="flex-1 border border-stone-300 rounded py-2 text-sm">Move Stage</button>
      <button class="flex-1 bg-stone-900 text-white rounded py-2 text-sm">Message</button>
    </div>
  </div>

  <!-- Notes Section -->
  <div class="mb-6">
    <div class="flex justify-between">
    <h3 class="text-sm font-medium text-stone-700 mb-4">Notes</h3>

    <div id="add_note_button">
      <%= link_to new_scout_applicant_note_path(application), 
          class: "flex hover:bg-stone-100 px-2 py-1 rounded-md underline text-xs text-stone-700 items-center mb-4",
          data: { turbo_stream: true } do %>
        <%= phosphor_icon "plus", class: "h-3 w-3 mr-1 text-purple-600" %>
        <span class="text-xs">Add note</span>
      <% end %>
    </div>
  </div>
  
  <div id="new_note_container"></div>
  
  <div id="notes_list">
    <% if application.user.talent_profile&.talent_notes&.any? %>
      <% application.user.talent_profile.talent_notes.ordered.each do |note| %>
        <div class="bg-stone-100 p-3 border border-stone-200 flex flex-col rounded-md mb-2">
          <span class="text-xs"><%= note.content %></span>
          <div class="text-xs mt-2 flex justify-end text-stone-500"> - <%= note.user.name %></div>
        </div>
      <% end %>
    <% else %>
      <div class="bg-stone-100 p-3 border border-stone-200 flex flex-col rounded-md">
        <span class="text-xs">No notes for this talented individual.</span>
      </div>
    <% end %>
  </div>
</div>



</turbo-frame>
