<div id="bookmark_button_<%= talent_profile.id %>"
     data-controller="bookmark-button"
     data-bookmark-button-bookmarked-value="<%= Current.user.talent_bookmarks.exists?(talent_profile: talent_profile) %>"
     data-bookmark-button-talent-profile-id-value="<%= talent_profile.id %>"
     data-bookmark-button-create-url-value="<%= bookmark_scout_talent_path(talent_profile) %>"
     data-bookmark-button-destroy-url-value="<%= bookmark_scout_talent_path(talent_profile) %>">

  <button type="button"
          class="p-2 border rounded-md border-stone-200 hover:border-stone-300" <%# Base classes, text color classes will be managed by controller %>
          data-action="click->bookmark-button#toggle"
          data-bookmark-button-target="buttonElement">

    <span data-bookmark-button-target="iconDisplay">
      <%# This span will be populated by the controller initially via connect() -> updateIcon() %>
    </span>
  </button>

  <%# Templates for the icons, controller will use their innerHTML %>
  <template data-bookmark-button-target="bookmarkedIconTemplate">
    <%= phosphor_icon "bookmark-simple", class: "h-5 w-5", style: "fill" %>
  </template>
  <template data-bookmark-button-target="unbookmarkedIconTemplate">
    <%= phosphor_icon "bookmark-simple", class: "h-5 w-5" %>
  </template>
</div>
