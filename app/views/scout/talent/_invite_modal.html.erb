<div id="invite-modal" class="modal">
  <div class="modal-content">
    <span class="close-button">&times;</span>
    <h2>Invite to <PERSON><PERSON></h2>
    
    <%= form_with url: scout_invitations_path, method: :post, id: "invitation-form", data: { turbo: true } do |f| %>
      <%= hidden_field_tag :talent_profile_id, @talent_profile_id %>
      
      <div class="mb-4 field">
        <%= f.label :job_id, "Select Job", class: "block mb-2 font-medium" %>
        <%= f.select :job_id, 
          options_from_collection_for_select(@jobs, :id, :title), 
          { prompt: "Select a job" }, 
          class: "w-full p-2 border rounded" 
        %>
      </div>

      <div class="mb-4 field">
        <%= f.label :pitch, "Invitation Message", class: "block mb-2 font-medium" %>
        <%= f.text_area :pitch, 
          class: "w-full p-2 border rounded", 
          rows: 5, 
          placeholder: "Tell them why you're interested in chatting..." 
        %>
      </div>

      <div class="flex justify-end mt-6">
        <button type="button" class="px-4 py-2 mr-2 text-stone-600 close-button-link hover:text-stone-800">
          Cancel
        </button>
        <%= f.submit "Send Invitation", class: "px-4 py-2 text-white bg-stone-900 rounded hover:bg-stone-800" %>
      </div>
    <% end %>
  </div>
</div>

<style>
  /* Basic modal styling - you can customize this */
  .modal {
    display: block; /* Hidden by default */
    position: fixed; /* Stay in place */
    z-index: 1; /* Sit on top */
    left: 0;
    top: 0;
    width: 100%; /* Full width */
    height: 100%; /* Full height */
    overflow: auto; /* Enable scroll if needed */
    background-color: rgb(0, 0, 0); /* Fallback color */
    background-color: rgba(0, 0, 0, 0.4); /* Black w/ opacity */
  }

  .modal-content {
    background-color: #fefefe;
    margin: 15% auto; /* 15% from the top and centered */
    padding: 20px;
    border: 1px solid #888;
    width: 80%; /* Could be more or less, depending on screen size */
  }

  .close-button {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
  }

  .close-button:hover,
  .close-button:focus {
    color: black;
    text-decoration: none;
    cursor: pointer;
  }
</style>
