<% view_type = local_assigns.fetch(:view_type, :list) %>

<% if talent_profiles.blank? %>
  <div class="py-12 text-center">
    <p class="text-lg font-medium text-stone-600">No talent profiles match your current filters.</p>
    <p class="text-sm text-stone-500">Try adjusting your search or filter criteria.</p>
  </div>
<% elsif view_type == :gallery %>
  <div class="grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-3 xl:grid-cols-4">
    <% talent_profiles.each do |talent_profile| %>
      <%= render partial: "scout/talent/talent_gallery_card", locals: { talent_profile: talent_profile } %>
    <% end %>
  </div>
<% else %>
  <div id="talent_profiles_list" class="space-y-6">
    <% talent_profiles.each do |talent_profile| %>
      <%= render partial: "scout/talent_profile_card_wrapper", locals: { talent_profile: talent_profile } %>
    <% end %>
  </div>
<% end %>
