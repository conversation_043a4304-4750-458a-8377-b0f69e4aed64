<div class="flex flex-col h-[600px] w-full" data-controller="chat">
  <!-- Cha<PERSON> -->
  <div class="h-[76px] border-b border-[#e5e7eb] flex items-center justify-between px-4">
    <div class="flex items-center">
      <div class="w-10 h-10 rounded-full overflow-hidden bg-[#333]">
      <% other_user = @conversation.users.where.not(id: Current.user.id).first %>
      <% if other_user&.avatar&.attached? %>
        <%= image_tag other_user.avatar, class: "w-full h-full object-cover" %>
      <% else %>
        <div class="flex items-center justify-center w-full h-full text-sm font-bold text-white bg-purple-500">
          <%= other_user&.name&.initials %>
        </div>
      <% end %>
      </div>
      <div class="ml-3">
        <div class="flex items-center text-base font-semibold text-[#1f2937]">
          <%= other_user&.name %>
        </div>
        <div class="text-sm text-[#6b7280] mt-[2px]">
          <%= @conversation.job&.title %>
        </div>
      </div>
    </div>
  </div>

  <!-- Replace the user-specific stream with the original conversation stream -->
  <%= turbo_stream_from "conversation_#{@conversation.id}_user_#{Current.user.id}" %>

  <!-- Messages Container -->
  <div id="messages"
       class="flex-1 p-4 overflow-y-auto"
       data-controller="chat scroll"
       data-chat-target="messages"
       data-scroll-target="container"
       data-action="turbo:stream:received->chat#messageReceived">
    <%= render partial: "scout/messages/message", collection: @messages, as: :message %>
  </div>

  <!-- Message Form -->
  <div class="border-t border-[#e5e7eb] py-[17px] px-4">
    <%= form_with(model: [:scout, @conversation, @message],
                  class: "message-form flex items-center gap-4",
                  data: {
                    controller: "form-reset",
                    action: "turbo:submit-end->form-reset#reset"
                  }) do |f| %>
      <div class="flex-1">
        <%= f.text_field :body,
            class: "w-full h-[42px] rounded-lg border border-[#e5e7eb] px-4 text-base font-sans placeholder:text-[#acaeb6]",
            placeholder: "Type a message...",
            data: { form_reset_target: "input" } %>
      </div>
      <button type="submit" class="flex items-center justify-center w-10 h-10 border-0 rounded-lg cursor-pointer bg-stone-900">
        <%= phosphor_icon "paper-plane-right", class: "p-0.5 text-stone-50" %>
      </button>
    <% end %>
  </div>
</div>
