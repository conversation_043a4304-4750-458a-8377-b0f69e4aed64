    <div class="w-full lg:w-[325px] lg:h-[calc(100vh-64px)] flex flex-col">
      <div class="py-4 px-5">
        <span class="text-3xl font-semibold tracking-[-0.4px]">Messages</span>
        <span class="text-3xl font-semibold text-[#6100FF] tracking-[-0.4px]">.</span>
      </div>
      <div class="p-4 bg-transparent">
        <div class="w-full h-[42px] border border-[#e5e7eb] bg-white flex items-center px-3">
          <i data-lucide="search" class="text-stone-500"></i>
          <%= form_with url: scout_conversations_path, method: :get, class: "w-full" do |f| %>
            <%= f.search_field :query,
                  value: params[:query],
                  placeholder: "Search conversations...",
                  class: "border-0 outline-none w-full h-full text-[16px] text-[#1f2837] bg-transparent pl-4 placeholder:text-[#adb0bc]",
                  data: { controller: "search", action: "input->search#submit" } %>
          <% end %>
        </div>
      </div>
      <div class="flex px-4 py-2 gap-4">
        <%= link_to scout_conversations_path, class: "py-[6px] px-[8px] rounded-[4px] text-[14px] cursor-pointer #{'bg-blue-100 border border-blue-600 text-blue-600 font-semibold' unless params[:archived].present?} #{'bg-[rgba(0,0,0,0.04)] border border-[rgba(0,0,0,0.1)] text-[rgba(0,0,0,0.6)]' if params[:archived].present?}" do %>
          Active (<%= Conversation.active_for(Current.user).count %>)
        <% end %>
        <%= link_to scout_archives_path, class: "py-[6px] px-[8px] rounded-[4px] text-[14px] cursor-pointer #{'bg-blue-100 border border-blue-600 text-blue-600 font-semibold' if params[:archived].present?} #{'bg-[rgba(0,0,0,0.04)] border border-[rgba(0,0,0,0.1)] text-[rgba(0,0,0,0.6)]' unless params[:archived].present?}" do %>
          Archived (<%= Conversation.archived_for(Current.user).count %>)
        <% end %>
      </div>
      <div class="mt-4 px-4 overflow-y-auto flex-1">
        <% @conversations.each do |conversation| %>
          <%= link_to scout_conversation_path(conversation), class: "block" do %>
            <%= render "scout/conversations/conversation", 
                       conversation: conversation,
                       active: @conversation.id == conversation.id,
                       archived: conversation.conversation_participants.find_by(user: Current.user)&.archived? %>
          <% end %>
        <% end %>
      </div>
    </div>
