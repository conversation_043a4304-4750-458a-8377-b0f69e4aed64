<div class="flex-1 bg-white border border-[rgba(0,0,0,0.1)] rounded-[4px] h-[calc(100vh-400px)] lg:h-[calc(100vh-80px)] m-2">
  <div class="h-full flex flex-col" data-controller="chat">
    <!-- Chat Header -->
    <div class="h-[76px] border-b border-[#e5e7eb] flex items-center justify-between px-4">
      <div class="flex items-center">
        <div class="w-10 h-10 rounded-full overflow-hidden bg-[#333]">
        <% if @conversation.users.first.avatar.attached? %>
          <%= image_tag other_user.avatar, class: "w-full h-full object-cover" %>
        <% else %>
          <div class="flex items-center justify-center w-full h-full bg-purple-500 text-white text-sm font-bold">
            <%= @conversation.users.first.name.initials %>
          </div>
        <% end %>
        </div>
        <div class="ml-3">
          <div class="flex items-center text-base font-semibold text-[#1f2937]">
            <PERSON>
            <span class="ml-2 text-blue-500">
              <%= phosphor_icon "facebook-logo", weight: "duotone", class: "w-4 h-4" %>
            </span>
          </div>
          <div class="text-sm text-[#6b7280] mt-[2px]">
            Memoir Ghostwriting Position
          </div>
        </div>
      </div>
      <div class="flex gap-3">
        <button class="w-8 h-8 rounded-lg flex items-center justify-center cursor-pointer bg-transparent border-0">
          <%= phosphor_icon "phone", weight: "duotone", class: "text-stone-600" %>
        </button>
        <button class="w-8 h-8 rounded-lg flex items-center justify-center cursor-pointer bg-transparent border-0">
          <%= phosphor_icon "video-camera", weight: "duotone", class: "text-stone-600" %>
        </button>
      </div>
    </div>

    <% if @conversation.conversation_participants.find_by(user: Current.user).archived? %>
      <div class="bg-yellow-100 p-4 border-b border-yellow-200">
        <div class="flex items-center text-yellow-800">
          <%= phosphor_icon "archive", class: "w-6 h-6 mr-2" %>
          <span class="text-sm">This conversation is archived</span>
        </div>
      </div>
    <% end %>

    <div class="p-4">
      <div class="flex items-center mb-2">
        <%= phosphor_icon "push-pin", style: 'duotone', class: "text-blue-600 mr-2" %>
        <span class="text-sm font-medium text-[#1f2937]">Pinned Information</span>
      </div>
      <div class="bg-blue-50 rounded-lg p-3 mb-4">
        <div class="flex items-center mb-2 last:mb-0">
          <span class="text-sm mr-2 font-medium text-[#1f2937]">Initial rate:</span>
          <span class="text-sm mr-2 text-[#1f2937]">$0.50/word</span>
        </div>
        <div class="flex items-center mb-2 last:mb-0">
          <span class="text-sm font-medium mr-2 text-[#1f2937]">Project deadline:</span>
          <span class="text-sm mr-2 text-[#1f2937]">March 15, 2025</span>
        </div>
      </div>
      <div class="h-px bg-[#d9d9d9] my-2"></div>
    </div>

    <!-- Replace the user-specific stream with the original conversation stream -->
    <%= turbo_stream_from "conversation_#{@conversation.id}_user_#{Current.user.id}" %>

    <!-- Messages Container -->
    <div id="messages"
         class="flex-1 overflow-y-auto p-4"
         data-controller="chat scroll"
         data-chat-target="messages"
         data-scroll-target="container"
         data-action="turbo:stream:received->chat#messageReceived">
      <%= render partial: "scout/messages/message", collection: @messages, as: :message %>
    </div>

    <!-- Message Form -->
    <div class="border-t border-[#e5e7eb] py-[17px] px-4">
      <!-- Check the form submission path -->
      <%= form_with(model: [:scout, @conversation, @message],
                    class: "message-form flex items-center gap-4",
                    data: {
                      controller: "form-reset",
                      action: "turbo:submit-end->form-reset#reset"
                    }) do |f| %>
        <div class="flex gap-2">
          <button type="button" class="w-8 h-8 rounded-lg flex items-center justify-center cursor-pointer bg-transparent border-0">
            <%= phosphor_icon "image", class: "text-stone-600" %>
          </button>
          <button type="button" class="w-8 h-8 rounded-lg flex items-center justify-center cursor-pointer bg-transparent border-0">
            <%= phosphor_icon "paperclip", class: "text-stone-600" %>
          </button>
        </div>
        <div class="flex-1">
          <%= f.text_field :body,
              class: "w-full h-[42px] rounded-lg border border-[#e5e7eb] px-4 text-base font-sans placeholder:text-[#acaeb6]",
              placeholder: "Type a message...",
              data: { form_reset_target: "input" } %>
        </div>
        <button type="submit" class="w-10 h-10 rounded-lg bg-stone-900 flex items-center justify-center cursor-pointer border-0">
          <%= phosphor_icon "paper-plane-right", class: "p-0.5 text-stone-50" %>
        </button>
      <% end %>
    </div>
  </div>
</div>
