<% other_user = conversation.users.where.not(id: Current.user.id).first %>
<% other_participants_exist = other_user.present? %>
<% last_message = conversation.messages.last %>

<% if archived %>

  <%= link_to scout_archive_path(conversation),
    class: "block w-full p-4 mb-2 rounded #{active ? 'bg-white shadow' : 'border border-black/10'} hover:shadow-md transition" do %>
  <div class="flex items-start justify-between">
    <!-- Left side: Avatar + Name + Role -->
    <div class="flex items-start gap-2">
      <!-- Avatar -->
      <div class="w-10 h-10 overflow-hidden bg-stone-200 rounded-full">
        <% if other_participants_exist && other_user.avatar.attached? %>
          <%= image_tag other_user.avatar, class: "w-full h-full object-cover" %>
        <% elsif other_participants_exist %>
          <div class="flex items-center justify-center w-full h-full text-sm font-bold text-white bg-purple-500">
            <%= other_user.name.initials %>
          </div>
        <% else %>
          <div class="flex items-center justify-center w-full h-full text-sm font-bold text-white bg-stone-400">
            ?
          </div>
        <% end %>
      </div>
      
      <!-- Name and Role -->
      <div>
        <div class="text-sm font-semibold text-stone-900">
          <%= other_participants_exist ? other_user.name : "No participant" %>
        </div>
        <div class="text-xs text-stone-500">
          <%= conversation.job&.title || "Not Job-related" %> role
        </div>
      </div>
    </div>

    <!-- Right side: Timestamp or Archived badge -->
    <div class="text-xs">
      <% if local_assigns[:archived] %>
        <span class="px-2 py-1 text-stone-500 border rounded-md">Archived</span>
      <% else %>
        <span class="text-stone-400">
          <%= last_message&.created_at&.strftime("%-I:%M %p") %>
        </span>
      <% end %>
    </div>
  </div>

  <!-- Last message preview -->
  <div class="mt-2 text-sm text-stone-700 truncate">
    <%= last_message&.body.present? ? truncate(last_message.body, length: 50) : "No messages yet" %>
  </div>
<% end %>

<% else %>

<%= link_to scout_conversation_path(conversation),
    class: "block w-full p-4 mb-2 rounded #{active ? 'bg-white shadow' : 'border border-black/10'} hover:shadow-md transition" do %>
  <div class="flex items-start justify-between">
    <!-- Left side: Avatar + Name + Role -->
    <div class="flex items-start gap-2">
      <!-- Avatar -->
      <div class="w-10 h-10 overflow-hidden bg-stone-200 rounded-full">
        <% if other_participants_exist && other_user.avatar.attached? %>
          <%= image_tag other_user.avatar, class: "w-full h-full object-cover" %>
        <% elsif other_participants_exist %>
          <div class="flex items-center justify-center w-full h-full text-sm font-bold text-white bg-purple-500">
            <%= other_user.name.initials %>
          </div>
        <% else %>
          <div class="flex items-center justify-center w-full h-full text-sm font-bold text-white bg-stone-400">
            ?
          </div>
        <% end %>
      </div>
      
      <!-- Name and Role -->
      <div>
        <div class="text-sm font-semibold text-stone-900">
          <%= other_participants_exist ? other_user.name : "No participant" %>
        </div>
        <div class="text-xs text-stone-500">
          <%= conversation.job&.title || "Not Job-related" %> role
        </div>
      </div>
    </div>

    <!-- Right side: Timestamp or Archived badge -->
    <div class="text-xs">
      <% if local_assigns[:archived] %>
        <span class="px-2 py-1 text-stone-500 border rounded-md">Archived</span>
      <% else %>
        <span class="text-stone-400">
          <%= last_message&.created_at&.strftime("%-I:%M %p") %>
        </span>
      <% end %>
    </div>
  </div>

  <!-- Last message preview -->
  <div class="mt-2 text-sm text-stone-700 truncate">
    <%= last_message&.body.present? ? truncate(last_message.body, length: 50) : "No messages yet" %>
  </div>
<% end %>

<% end %>
