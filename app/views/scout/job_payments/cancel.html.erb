<div class="container px-4 py-8 mx-auto">
  <div class="px-8 pt-6 pb-8 mb-4 text-center bg-white rounded shadow-md">
    <h1 class="mb-2 text-3xl font-bold text-red-600">Payment Cancelled</h1>

    <% if @job %>
      <p class="mb-4 text-lg text-gray-700">
        You cancelled the payment process for the job posting "<%= @job.title %>".
      </p>
      <p class="mb-6 text-gray-600">The job has not been published.</p>
      <%= link_to "Return to Job Posting", scout_job_path(@job), class: "bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline mr-2" %>
      <%= link_to "Back to Dashboard", scout_root_path, class: "bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline" %>
    <% else %>
      <p class="mb-4 text-lg text-gray-700">
        You cancelled the payment process.
      </p>
      <p class="mb-6 text-gray-600">Your job posting has not been published.</p>
      <%= link_to "Back to Dashboard", scout_root_path, class: "bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline" %>
    <% end %>

    <%# Display flash messages if any %>
    <% flash.each do |type, msg| %>
      <div class="mt-4 p-4 rounded <%= type == 'notice' ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700' %>">
        <%= msg %>
      </div>
    <% end %>
  </div>
</div>
