<div class="container px-4 py-8 mx-auto">
  <div class="px-8 pt-6 pb-8 mb-4 text-center bg-white rounded shadow-md">

    <%# Placeholder for confetti JS trigger %>
    <div id="confetti-container" class="mb-4">
      <h1 class="mb-2 text-3xl font-bold text-green-600">Congratulations!</h1>
      <p class="text-lg text-gray-700">Your payment is processing.</p>
    </div>

    <% if @job %>
      <p class="mb-4 text-gray-600">
        Your job posting for "<%= @job.title %>" is pending confirmation and will be published shortly.
      </p>
      <%= link_to "View Job Posting", scout_job_path(@job), class: "bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline" %>
    <% else %>
      <p class="mb-4 text-gray-600">
        Your job posting is pending confirmation and will be published shortly. You will be notified once it's live.
      </p>
      <%= link_to "Back to Dashboard", scout_root_path, class: "bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline" %>
    <% end %>

    <%# Display flash messages if any %>
    <% flash.each do |type, msg| %>
      <div class="mt-4 p-4 rounded <%= type == 'notice' ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700' %>">
        <%= msg %>
      </div>
    <% end %>
  </div>
</div>

<%# Example of how you might trigger confetti (requires a library like canvas-confetti) %>
<%# content_for :javascript do %>
  <%#= javascript_include_tag 'https://cdn.jsdelivr.net/npm/canvas-confetti@1.5.1/dist/confetti.browser.min.js', defer: true %>
  <%# <script> %>
  <%# document.addEventListener('turbo:load', () => { %>
  <%#   const container = document.getElementById('confetti-container'); %>
  <%#   if (container && typeof confetti === 'function') { %>
  <%#     confetti({ %>
  <%#       particleCount: 150, %>
  <%#       spread: 180, %>
  <%#       origin: { y: 0.6 } %>
  <%#     }); %>
  <%#   } %>
  <%# }); %>
  <%# </script> %>
<%# end %>
