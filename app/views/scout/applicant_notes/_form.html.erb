<%= form_with model: [:scout, @application, @note], url: scout_applicant_notes_path(@application), id: "new_note_form", class: "mb-4" do |form| %>
  <div class="bg-white p-3 border border-stone-200 rounded-md">
    <div class="mb-2">
      <%= form.text_area :content, placeholder: "Add your note here...", rows: 3, class: "w-full text-xs border-stone-200 rounded-md focus:ring-purple-500 focus:border-purple-500" %>
    </div>
    
    <div class="mb-2">
      <%= form.select :category, TalentNote.categories.keys.map { |k| [k.humanize, k] }, 
          { prompt: "Select category" }, 
          { class: "text-xs w-full border-stone-200 rounded-md" } %>
    </div>
    
    <div class="flex items-center mb-2">
      <%= form.check_box :pinned, class: "h-3 w-3 rounded border-stone-300 text-purple-600 focus:ring-purple-500" %>
      <%= form.label :pinned, "Pin this note", class: "ml-2 text-xs text-stone-700" %>
    </div>
    
    <div class="flex justify-end space-x-2">
      <%= link_to "Cancel", "#", class: "text-xs text-stone-500 hover:text-stone-700", 
          data: { 
            turbo_stream: true,
            action: "click->turbo#clearTarget",
            turbo_target: "new_note_container"
          } %>
      <%= form.submit "Done", class: "text-xs bg-stone-900 text-white px-3 py-1 rounded-md" %>
    </div>
  </div>
<% end %>