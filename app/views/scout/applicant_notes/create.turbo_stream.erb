<%= turbo_stream.replace "add_note_button" do %>
    <div id="add_note_button">
      <%= link_to new_scout_applicant_note_path(@application), 
          class: "flex hover:bg-stone-100 px-2 py-1 rounded-md underline text-xs text-stone-700 items-center mb-4",
          data: { turbo_stream: true } do %>
        <%= phosphor_icon "plus", class: "h-3 w-3 mr-1 text-purple-600" %>
        <span class="text-xs">Add note</span>
      <% end %>
    </div>
<% end %>

<%= turbo_stream.replace "new_note_container" do %>
  <div id="new_note_container"></div>
<% end %>

<%= turbo_stream.prepend "notes_list" do %>
  <div class="bg-stone-100 p-3 border border-stone-200 flex flex-col rounded-md mb-2">
    <span class="text-xs"><%= simple_format(@note.content) %></span>
    <div class="text-xs mt-2 flex justify-end text-stone-500"> - <%= @note.user.name %></div>
  </div>
<% end %>