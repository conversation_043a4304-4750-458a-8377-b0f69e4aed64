<%= form_with url: scout_invitations_path, method: :post, id: "invitation-form", data: { turbo: true } do |f| %>
  <%= hidden_field_tag :talent_profile_id, talent_profile_id %>
  
  <div class="mb-4 field">
    <%= f.label :job_id, "Select Job", class: "block mb-2 font-medium" %>
    <%= f.select :job_id, 
      options_from_collection_for_select(jobs, :id, :title), 
      { prompt: "Select a job" }, 
      class: "w-full p-2 border rounded" 
    %>
  </div>

  <div class="mb-4 field">
    <%= f.label :pitch, "Invitation Message", class: "block mb-2 font-medium" %>
    <%= f.text_area :pitch, 
      class: "w-full p-2 border rounded", 
      rows: 5, 
      placeholder: "Tell them why you're interested in chatting..." 
    %>
  </div>

  <div class="flex justify-end mt-6">
    <button type="button" class="px-4 py-2 mr-2 text-stone-600 close-button-link hover:text-stone-800"
            onclick="window.history.back();">
      Cancel
    </button>
    <%= f.submit "Send Invitation", class: "px-4 py-2 text-white bg-stone-900 rounded hover:bg-stone-800" %>
  </div>
<% end %>
