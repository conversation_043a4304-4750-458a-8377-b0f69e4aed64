<%= turbo_stream.replace "modal" do %>
  <div id="modal" class="fixed inset-0 bg-stone-600 bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-white p-6 rounded-lg shadow-lg max-w-md w-full">
      <div class="flex justify-between items-center mb-4">
        <h2 class="text-xl font-bold">Invite Talent to Chat</h2>
        <%= link_to scout_talent_index_path,
                   class: "text-stone-500 hover:text-stone-700" do %>
          <%= phosphor_icon "x", class: "h-5 w-5" %>
        <% end %>
      </div>
      
      <%= form_with url: scout_invitations_path, method: :post, class: "space-y-4" do |f| %>
        <%= f.hidden_field :talent_profile_id, value: @talent_profile_id %>
        
        <div>
          <label class="block text-sm font-medium text-stone-700 mb-1">Select Job <span class="text-red-500">*</span></label>
          <%= f.select :job_id,
                      @jobs.map { |j| [j.title, j.id] },
                      { include_blank: "Select a job (required)" },
                      { class: "w-full p-2 border border-stone-300 rounded", required: true } %>
        </div>
        
        <div>
          <label class="block text-sm font-medium text-stone-700 mb-1">Pitch (Message to Talent)</label>
          <%= f.text_area :pitch,
                         class: "w-full p-2 border border-stone-300 rounded h-32",
                         placeholder: "Write a personalized message to the talent..." %>
        </div>
        
        <div class="flex justify-end space-x-2 mt-4">
          <%= link_to "Cancel",
                     scout_talent_index_path,
                     class: "px-4 py-2 text-sm border border-stone-300 rounded text-stone-700 hover:bg-stone-50" %>
          <%= f.submit "Send Invitation", class: "px-4 py-2 text-sm bg-blue-600 text-white rounded hover:bg-blue-700" %>
        </div>
      <% end %>
    </div>
  </div>
<% end %>