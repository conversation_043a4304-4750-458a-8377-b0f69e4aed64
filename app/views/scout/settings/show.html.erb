<%= render "scout/shared/shell"  do %>
  <h1 class="text-2xl font-semibold text-[#09090F] letter-spacing-tight">Account</h1>
  <div class="mt-6 space-y-8">
    <!-- Organization Name -->
    <div class="border-t border-[#09090F]/10 pt-6">
      <div class="flex items-start justify-between">
        <div>
          <h2 class="text-sm font-semibold text-[#09090F]">Organization Name</h2>
          <p class="text-sm text-[#71717A] mt-1">This will be displayed on your public profile.</p>
        </div>
        <input type="text" value="Catalyst" class="w-[432px] px-3 py-2 rounded-lg border border-[#09090F]/10 text-sm">
      </div>
    </div>
    <!-- Organization Bio -->
    <div class="border-t border-[#09090F]/5 pt-6">
      <div class="flex items-start justify-between">
        <div>
          <h2 class="text-sm font-semibold text-[#09090F]">Organization Bio</h2>
          <p class="text-sm text-[#71717A] mt-1">This will be displayed on your public profile.</p>
        </div>
        <textarea class="w-[432px] h-[60px] px-3 py-2 rounded-lg border border-[#09090F]/10 text-sm resize-none"></textarea>
      </div>
    </div>
    <!-- Organization Email -->
    <div class="border-t border-[#09090F]/5 pt-6">
      <div class="flex items-start justify-between">
        <div>
          <h2 class="text-sm font-semibold text-[#09090F]">Organization Email</h2>
          <p class="text-sm text-[#71717A] mt-1">This is how customers can contact you for support.</p>
        </div>
        <div class="space-y-4">
          <input type="email" value="<EMAIL>" class="w-[432px] px-3 py-2 rounded-lg border border-[#09090F]/10 text-sm">
          <div class="flex items-center">
            <input type="checkbox" id="showEmail" class="rounded text-[#09090F]">
            <label for="showEmail" class="ml-2 text-sm text-[#09090F]">Show email on public profile</label>
          </div>
        </div>
      </div>
    </div>
    <!-- Address -->
    <div class="border-t border-[#09090F]/5 pt-6">
      <div class="flex items-start justify-between">
        <div>
          <h2 class="text-sm font-semibold text-[#09090F]">Address</h2>
          <p class="text-sm text-[#71717A] mt-1">This is where your organization is registered.</p>
        </div>
        <div class="space-y-4 w-[432px]">
          <input type="text" value="147 Catalyst Ave" class="w-full px-3 py-2 rounded-lg border border-[#09090F]/10 text-sm">
          <input type="text" value="Toronto" class="w-full px-3 py-2 rounded-lg border border-[#09090F]/10 text-sm">
          <div class="flex gap-4">
            <select class="w-1/2 px-3 py-2 rounded-lg border border-[#09090F]/10 text-sm">
              <option>Ontario</option>
            </select>
            <input type="text" value="A1A 1A1" class="w-1/2 px-3 py-2 rounded-lg border border-[#09090F]/10 text-sm">
          </div>
          <select class="w-full px-3 py-2 rounded-lg border border-[#09090F]/10 text-sm">
            <option>Canada</option>
          </select>
        </div>
      </div>
    </div>
    <!-- Currency -->
    <div class="border-t border-[#09090F]/5 pt-6">
      <div class="flex items-start justify-between">
        <div>
          <h2 class="text-sm font-semibold text-[#09090F]">Currency</h2>
          <p class="text-sm text-[#71717A] mt-1">The currency that your organization will be collecting.</p>
        </div>
        <select class="w-[432px] px-3 py-2 rounded-lg border border-[#09090F]/10 text-sm">
          <option>CAD - Canadian Dollar</option>
        </select>
      </div>
    </div>
    <!-- Action Buttons -->
    <div class="border-t border-[#09090F]/5 pt-6 flex justify-end space-x-4">
      <button class="px-4 py-2 text-sm font-semibold text-[#09090F]">Reset</button>
      <button class="px-4 py-2 text-sm font-semibold text-white bg-[#09090F] rounded-lg">Save changes</button>
    </div>
  </div>
<% end %>
