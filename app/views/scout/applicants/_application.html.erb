<div class="flex items-start justify-between">
  <div class="flex-1">
    <!-- Profile Header -->
    <div class="flex items-center gap-4 mb-4">
      <div class="flex items-center justify-center w-12 h-12 bg-stone-200 rounded-full">
        <% if application.user.avatar.attached? %>
          <%= image_tag application.user.avatar, class: "w-full h-full object-cover rounded-full" %>
        <% else %>
          <%= application.user.name.initials %>
        <% end %>
      </div>
      <div class="flex-1">
        <div class="flex items-center justify-between">
          <div>
            <h3 class="font-semibold text-[#1f2937]"><%= application.user.name %></h3>
            <div class="flex items-center gap-2 text-sm text-stone-600">
              <span><%= application.job.title %></span>
              <span class="text-stone-400">•</span>
              <span class="px-2 py-0.5 text-xs rounded-full <%= status_color_class(application.status) %>">
                <%= application.status.titleize %>
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Application Letter Preview -->
    <% if application.application_letter.present? %>
      <div class="mb-4">
        <p class="text-[14px] text-stone-700 leading-relaxed">
          <%= truncate(application.application_letter, length: 150) %>
        </p>
      </div>
    <% end %>

    <!-- Skills Section -->
    <% if application.user.talent_profile&.skills&.any? %>
      <div class="flex flex-wrap gap-2">
        <% application.user.talent_profile.skills.first(5).each do |skill| %>
          <span class="px-3 py-1 text-sm text-stone-600 bg-stone-100 rounded-md">
            <%= skill %>
          </span>
        <% end %>
      </div>
    <% end %>

    <!-- Action Buttons -->
    <div class="flex items-center justify-end mt-6">
      <div class="flex items-center gap-3">
        <select
          class="px-3 py-1 text-sm border border-stone-300 rounded-md"
          data-controller="status-update"
          data-action="change->status-update#update"
          data-status-update-url-value="<%= scout_applicant_path(application) %>"
        >
          <% JobApplication.statuses.reject { |status_name, _| status_name == "withdrawn" }.each do |status_name, status_value| %>
            <option
              value="<%= status_name %>"
              <%= 'selected' if application.status == status_name %>
            >
              <%= status_name.titleize %>
            </option>
          <% end %>
        </select>

        <button class="flex items-center gap-2 px-4 py-2 text-sm text-white bg-stone-900 rounded-md hover:bg-stone-800">
          Message
        </button>
      </div>
    </div>
  </div>
</div>
