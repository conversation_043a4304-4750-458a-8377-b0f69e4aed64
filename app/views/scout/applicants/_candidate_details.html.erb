<turbo-frame id="candidate-details-container">
<div class="p-0 ">
  <!-- Candidate <PERSON><PERSON> with Close Button -->
  <div class="flex items-center justify-between mb-4">
    <h3 class="text-[16px] font-semibold text-[#1f2937]">Candidate Details</h3>
    <button data-action="click->candidate-details#close" class="text-stone-500 hover:text-stone-700">
      <%= phosphor_icon "x", class: "h-5 w-5" %>
    </button>
  </div>

  <!-- Candidate Profile -->
  <div class="flex items-center mb-6">
    <div class="flex items-center justify-center w-24 h-24 mr-4 overflow-hidden rounded-full bg-stone-200">
      <% if application.user.avatar.attached? %>
        <%= image_tag application.user.avatar, class: "w-full h-full object-cover", alt: "#{application.user.name}'s avatar" %>
      <% else %>
        <div class="flex items-center justify-center w-full h-full text-xl bg-stone-200 text-stone-600">
          <%= application.user.name.initials %>
        </div>
      <% end %>
    </div>
    <div class="flex-1">
      <h4 class="text-xl font-semibold text-stone-900 "><%= application.user.name %></h4>
      <p class="mb-2 text-stone-600"><%= application.job.title %></p>
      <div class="inline-block mt-2">
                            <div class="text-sm rounded-md border px-2 py-1 <%= status_color_class(application.status) %>">
                              <%= application.status.titleize %>
                            </div>
      </div>
    </div>
  </div>

  <!-- Quick Actions -->
  <div class="mb-6">
    <h3 class="mb-4 text-sm font-medium text-stone-700">Quick Actions</h3>
    <div class="flex gap-2">
      <button class="flex-1 py-2 text-sm border rounded border-stone-300"
             data-controller="stage-update"
             data-stage-update-id-value="<%= application.id %>"
             data-stage-update-status-value="<%= application.status %>"
             data-stage-update-url-value="<%= scout_applicant_path(application) %>"
             data-action="click->stage-update#openModal">
        Move Stage
      </button>
      <button 
        class="flex-1 py-2 text-sm text-white rounded bg-stone-900"
        data-action="click->candidate-details#openMessageModal"
        data-applicant-id="<%= application.user.id %>"
        data-job-id="<%= application.job.id %>">
        Message
      </button>
    </div>
  </div>

  <!-- Notes Section -->
  <div class="mb-6">
    <div class="flex justify-between">
    <h3 class="mb-4 text-sm font-medium text-stone-700">Notes</h3>

    <div id="add_note_button">
      <%= link_to new_scout_applicant_note_path(application), 
          class: "flex hover:bg-stone-100 px-2 py-1 rounded-md underline text-xs text-stone-700 items-center mb-4",
          data: { turbo_stream: true } do %>
        <%= phosphor_icon "plus", class: "h-3 w-3 mr-1 text-purple-600" %>
        <span class="text-xs">Add note</span>
      <% end %>
    </div>
  </div>
  
  <div id="new_note_container"></div>
  
  <div id="notes_list">
    <% if application.user.talent_profile&.talent_notes&.any? %>
      <% application.user.talent_profile.talent_notes.ordered.each do |note| %>
        <div class="flex flex-col p-3 mb-2 border rounded-md bg-stone-100 border-stone-200">
          <span class="text-xs"><%= note.content %></span>
          <div class="flex justify-end mt-2 text-xs text-stone-500"> - <%= note.user.name %></div>
        </div>
      <% end %>
    <% else %>
      <div class="flex flex-col p-3 border rounded-md bg-stone-100 border-stone-200">
        <span class="text-xs">No notes for this talented individual.</span>
      </div>
    <% end %>
  </div>
</div>



</turbo-frame>
