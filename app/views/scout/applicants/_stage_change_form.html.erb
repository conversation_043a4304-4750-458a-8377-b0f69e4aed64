<div class="p-4">
  <div class="flex items-center justify-between mb-6">
    <h2 class="text-xl font-bold text-stone-800">Change Application Stage</h2>
  </div>

  <div class="p-4 mb-6 text-sm rounded-md bg-stone-100">
    <p>Currently changing stage for <strong><%= application.user.name %></strong> on job <strong><%= application.job.title %></strong>.</p>
    <p class="flex items-center mt-2">
      <span class="mr-2 font-medium">Current stage:</span>
      <span class="px-2 py-0.5 text-xs font-medium rounded-full <%= status_color_class(application.status) %>">
        <%= application.status.titleize %>
      </span>
    </p>
  </div>

  <%= form_with(model: application, 
                url: scout_applicant_path(application), 
                method: :patch,
                data: { 
                  action: "submit->stage-update#updateStage",
                }) do |form| %>
    
    <div class="mb-6">
      <label class="block mb-2 text-sm font-medium text-stone-700">
        Move to Stage:
      </label>
      
      <div class="grid grid-cols-1 gap-3">
        <% JobApplication.statuses.keys.reject { |s| s == application.status || s == "withdrawn" }.each do |status| %>
          <div class="flex items-center">
            <%= form.radio_button :status, status, class: "h-4 w-4 text-[#6100FF] focus:ring-[#6100FF] border-stone-300 mr-2" %>
            <%= form.label :status, status.titleize, value: status, class: "text-sm font-medium text-stone-700" %>
          </div>
        <% end %>
      </div>
    </div>

    <div class="flex justify-end space-x-3">
      <button type="button" 
              class="px-4 py-2 text-sm font-medium bg-white border rounded-md shadow-sm text-stone-700 border-stone-300 hover:bg-stone-50"
              data-action="click->modal#close">
        Cancel
      </button>
      <%= form.submit "Update Stage",
                      class: "px-4 py-2 text-sm font-medium text-stone-50 bg-stone-900 hover:bg-stone-800 border border-transparent rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-stone-900" %>
    </div>
  <% end %>
</div>
