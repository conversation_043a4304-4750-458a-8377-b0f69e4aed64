<div data-controller="scout-sidebar" class="flex flex-col min-h-screen transition-opacity duration-100 ease-linear opacity-0 lg:flex-row">
  <!-- Sidebar -->
  <div class="flex flex-col w-1/5 lg:h-full">
    <div class="flex px-5 py-4">
      <span class="text-3xl font-semibold tracking-[-0.4px]">Track Applicants</span>
      <span class="text-3xl font-semibold text-[#6100FF] tracking-[-0.4px]">.</span>
    </div>
    <div class="px-5 py-2 text-xs text-stone-700">
      <ul class="text-xs list-disc list-inside text-stone-600">
        <li class="py-1">Message candidates directly.</li>
        <li class="py-1">Review candidate details and notes.</li>
        <li class="py-1">Filter applicants by application stage.</li>
        <li class="py-1">Manage the application pipeline per job.</li>
        <li class="py-1">View all applicants for each job postings.</li>
      </ul>
    </div>

  </div>
  
  <!-- Main Content -->
  <div class="flex w-4/5 p-4 m-2 bg-white border rounded-md min-h-screen">
    <main data-scout-sidebar-target="main" class="w-full p-4 overflow-y-auto">

      <h3 class="mb-4 text-sm font-semibold text-stone-900">Application Stage</h3>
      <!-- Create a status count object for the current job -->
      <% 
        # Create a hash to store counts for all statuses
        status_counts = {}
        
        # Initialize all possible statuses with zero count
        JobApplication.statuses.keys.each do |status|
          status_counts[status] = 0
        end
        
        # Fill in actual counts from applications grouped by status
        @applications.group_by(&:status).each do |status, applications|
          status_counts[status] = applications.count
        end
      %>
      
  <!-- Before the status buttons -->
  <% 
    # Calculate total counts for all statuses before any filtering
    # These are now set as instance variables for the partial
    @stats_total_counts = if params[:job_id].present?
      JobApplication.where(job_id: params[:job_id]).group(:status).count
    else
      JobApplication.where(job_id: @job_ids).group(:status).count # @job_ids should be set by controller
    end
    
    # Get total applications count
    @total_applications = @stats_total_counts.values.sum
  %>

<!-- Status Buttons -->
<%= render 'application_stats' %>

<!-- Results header -->
<div class="flex items-center justify-between ">

      <h3 class="mb-4 text-sm font-semibold text-stone-900">
        </h3>
  <div class="flex items-center gap-2">
    <!-- Add the show sidebar button (hidden by default) -->
    <button id="show-sidebar-button" class=" rotate-180 h-7 bg-white border border-[#e5e7eb] rounded px-2 flex items-center justify-center cursor-pointer" data-action="click->scout-sidebar#toggle" data-sidebar-target="toggleButton">
      <%= phosphor_icon "sidebar-simple",style: 'duotone', class: "h-4 w-4 text-stone-600" %>
    </button>
  </div>
</div>

      <!-- Applicants List -->
      <div id="applicants-list" class="mb-4">
        <%= render "applicant_results" %>
      </div>
    </main>
<aside data-scout-sidebar-target="aside" data-controller="sidebar-stats" class="hidden w-4/12 p-4 mx-auto ml-2 overflow-hidden border-l candidate-details-container border-stone-100">
  <div data-controller="candidate-details">
    <%= turbo_frame_tag "candidate-details-container" do %>
      <%= render "placeholder" %>
    <% end %>
  </div>
</aside>
  </div>


</div>
<%= render "shared/modal" %>
