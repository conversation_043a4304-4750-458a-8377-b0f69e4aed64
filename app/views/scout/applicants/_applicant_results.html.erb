<% @applications.group_by(&:job).each do |job, job_applications| %>
  <!-- Job Header -->
  <div class="flex items-center">
    <div class="py-4 font-semibold text-stone-800 ">
      <%= job&.title || "No Job Title" %>
    </div>

    <div class="px-1 py-0.5 ml-2 text-xs border-stone-600 border rounded-md bg-stone-100 text-stone-600 ">
      <%= pluralize(job_applications.count, "Applicant") %>
    </div>
  </div>

  <div class="border rounded-md border-stone-200 "> 
    <% job_applications.group_by(&:status).each do |status, applications| %>
      <% applications.each do |application| %>
        <div class="p-6 border-b cursor-pointer last:border-none border-stone-200" 
             data-controller="candidate-selection" 
             data-action="click->candidate-selection#showDetails"
             data-candidate-selection-id-value="<%= application.id %>"
             data-candidate-selection-name-value="<%= application.user.name %>"
             data-candidate-selection-job-title-value="<%= application.job.title %>"
             data-candidate-selection-status-value="<%= application.status %>"
             data-candidate-selection-avatar-url-value="<%= application.user.avatar.attached? ? url_for(application.user.avatar) : '' %>">
          <div class="flex items-start justify-between">
            <div class="flex-1">
              <!-- Profile Header - Updated to match talent profile card style -->
              <div class="">
                <div class="flex items-start gap-4">
                  <!-- Profile Image -->
                  <% if application.user.avatar.attached? %>
                    <%= image_tag application.user.avatar, class: "w-16 h-16 rounded-full object-cover border border-stone-200 flex-shrink-0" %>
                  <% else %>
                    <div class="flex items-center justify-center flex-shrink-0 w-16 h-16 text-2xl font-bold border rounded-full text-stone-700 bg-stone-100 border-stone-200">
                      <%= application.user.name.first[0].upcase rescue "?" %>
                    </div>
                  <% end %>
                  
                  <!-- Basic Info -->
                  <div class="flex-1">
                    <div class="flex w-full ">
                      <div class="w-full">
                        <div class="flex items-center justify-between w-full">
                          <div class="flex items-center gap-2">
                            <h2 class="text-xl font-bold text-stone-800"><%= application.user.name %></h2>
                            <span class="px-2 py-0.5 text-xs font-medium rounded-full <%= status_color_class(application.status) %>">
                              <%= application.status.titleize %>
                            </span>
                          </div>
                            <div class="flex items-center text-xs text-stone-500">
                              <%= phosphor_icon "clock", class: "h-3 w-3 mr-1" %>
                              Applied <%= time_ago_in_words(application.created_at) %> ago
                            </div>
                        </div>
                        <p class="font-medium text-stone-600"><%= application.job.title %></p>
                        <% if application.user.talent_profile&.location_preference.present? %>
                          <div class="flex flex-wrap items-center gap-3 mt-1 text-sm text-stone-500">
                            <div class="flex items-center">
                              <%= phosphor_icon "map-pin", class: "h-4 w-4 mr-1" %>
                              <%= application.user.talent_profile.location_preference.to_s.humanize %>
                            </div>
                          </div>
                        <% end %>
                      </div>
                    </div>
                  </div>
                </div>
              </div>


              <!-- Action Buttons -->
              <div class="flex items-center justify-end mt-6">
                <div class="flex items-center gap-3">
                  <button class="flex items-center gap-2 px-4 py-2 text-sm border rounded-md border-stone-200 text-ston-900 bg-stone-100 hover:bg-stone-100"
                         data-controller="stage-update"
                         data-stage-update-id-value="<%= application.id %>"
                         data-stage-update-status-value="<%= application.status %>"
                         data-stage-update-url-value="<%= scout_applicant_path(application) %>"
                         data-action="click->stage-update#openModal click->candidate-selection#stopPropagation">
                    Move Stage 
                  </button>
                  <button type="button"
                      class="flex items-center gap-2 px-4 py-2 text-sm rounded-md text-stone-50 bg-stone-900 hover:bg-stone-800"
                      data-action="click->candidate-selection#stopPropagation click->candidate-selection#openMessageModal"
                      data-applicant-id="<%= application.user.id %>"
                      data-job-id="<%= application.job.id %>">
                    Message
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      <% end %>
    <% end %>
  </div>
<% end %>
