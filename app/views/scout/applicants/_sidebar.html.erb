<aside class="flex flex-col p-8 w-80">
  <div class="mb-12 text-2xl font-semibold letter-spacing-tight">
    Candidates <span class="text-purple-400">.</span>
  </div>
  <div class="p-4 mb-2 border-l-4 rounded-sm border-stone-50 hover:border-stone-300 bg-stone-150">
      <!-- Sidebar with job filters -->
      <div class="w-64 pr-6">
        <h3 class="mb-4 text-sm font-semibold text-stone-900">Filter by Job</h3>
        <div class="space-y-2">
          <%= link_to scout_applicants_path, class: "flex items-center px-3 py-2 text-sm text-stone-700 rounded-lg hover:bg-stone-100 #{'bg-stone-100' unless params[:job_id]}" do %>
            <span>All Jobs</span>
            <span class="ml-auto text-xs text-stone-500"><%= @applications.count %></span>
          <% end %>
          <% @jobs.each do |job| %>
            <%= link_to scout_applicants_path(job_id: job.id), class: "flex items-center px-3 py-2 text-sm text-stone-700 rounded-lg hover:bg-stone-100 #{'bg-stone-100' if params[:job_id].to_i == job.id}" do %>
              <span><%= job.title %></span>
              <span class="ml-auto text-xs text-stone-500"><%= job.job_applications.count %></span>
            <% end %>
          <% end %>
        </div>
      </div>

  </div>
</aside>
