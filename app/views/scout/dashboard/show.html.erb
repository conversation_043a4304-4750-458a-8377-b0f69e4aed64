    <div class="p-8">

      <h1 class="mb-12 text-4xl font-semibold title flex">Good morning, Dakota <span class="text-purple-300">.</span></h1>

      <%= render "shared/stats_grid" %>

        <!-- Features Grid -->
        <div class="grid grid-cols-1 gap-4 mb-12 md:grid-cols-2 lg:grid-cols-3">
            <!-- Source Talent -->
            <%= link_to scout_applicants_path, class:"hover:border-purple-200 px-8 py-8 bg-white border rounded-sm card border-black/10" do %>
                <div class="flex flex-col gap-2">
                    <div class="flex mb-4 items-center justify-center rounded-full w-14 h-14 bg-purple-500/5">
                        <%= phosphor_icon "users", class: "h-8 w-8 text-purple-700" %>
                    </div>
                    <h3 class="text-3xl font-semibold text-black/90">Source Talent</h3>
                    <p class="text-black/60 mb-4">Unique filter help cut through the noise, using our filters and suggestions.</p>

                    <div class="flex items-center text-lg font-semibold text-black/90"> Source 
                        <%= phosphor_icon "arrow-right", class: "h-6 w-6 ml-2 text-stone-500" %>
                    </div>
                </div>
              <% end %> 

            <%= link_to scout_applicants_path, class:"hover:border-purple-200 px-8 py-8 bg-white border rounded-sm card border-black/10" do %>
                <div class="flex flex-col gap-2">
                    <div class="flex mb-4 items-center justify-center rounded-full w-14 h-14 bg-blue-500/5">
                        <%= phosphor_icon "binoculars", class: "h-8 w-8 text-blue-700" %>
                    </div>
                    <h3 class="text-3xl font-semibold text-black/90">Track applicants</h3>
                    <p class="text-black/60 mb-4">Progress ghostwriting applications through your hiring stages.</p>

                    <div class="flex items-center text-lg font-semibold text-black/90">Track 
                        <%= phosphor_icon "arrow-right", class: "h-6 w-6 ml-2 text-stone-500" %>
                    </div>
                </div>
              <% end %> 

            <%= link_to scout_applicants_path, class:"hover:border-purple-200 px-8 py-8 bg-white border rounded-sm card border-black/10" do %>
                <div class="flex flex-col gap-2">
                    <div class="flex mb-4 items-center justify-center rounded-full w-14 h-14 bg-teal-500/5">
                        <%= phosphor_icon "read-cv-logo", class: "h-8 w-8 text-teal-700" %>
                    </div>
                    <h3 class="text-3xl font-semibold text-black/90">Manage Jobs</h3>
                    <p class="text-black/60 mb-4">Add, remove and edit job listing through our easy to use editor and search.</p>

                    <div class="flex items-center text-lg font-semibold text-black/90">Manage 
                        <%= phosphor_icon "arrow-right", class: "h-6 w-6 ml-2 text-stone-500" %>
                    </div>
                </div>
              <% end %> 

        </div>

        <!-- Activity Feed -->
        <div class="space-y-6">
            <div>
                <h2 class="mb-3 text-4xl font-semibold title">Activity Feed</h2>
                <p class="text-xl">Here's a reminder of what you and your team have been up to.</p>
            </div>

            <div class="space-y-4">
                <div class="p-4 border rounded border-black/10">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center gap-3">
                            <div class="flex items-center justify-center w-8 h-8 text-sm font-semibold rounded-full bg-black/5">AD</div>
                            <span>Adam D. posted a new job listing</span>
                        </div>
                        <span class="text-black/60">About 23 hours ago</span>
                    </div>
                </div>

                <div class="p-4 border rounded border-black/10">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center gap-3">
                            <div class="flex items-center justify-center w-8 h-8 text-sm font-semibold rounded-full bg-black/5">AD</div>
                            <span>Adam D. posted a message to Jak</span>
                        </div>
                        <span class="text-black/60">About 23 hours ago</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

</body>
</html>

