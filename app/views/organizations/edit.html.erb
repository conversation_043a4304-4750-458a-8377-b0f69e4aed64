<div class="container mx-auto px-4 py-8">
  <h1 class="text-2xl font-bold mb-6">Edit Organization</h1>
  
  <%= form_with(model: @organization, class: "max-w-lg") do |form| %>
    <% if @organization.errors.any? %>
      <div class="mb-6 p-4 bg-red-50 text-red-700 rounded-lg">
        <h2 class="font-semibold"><%= pluralize(@organization.errors.count, "error") %> prohibited this organization from being saved:</h2>
        <ul class="list-disc pl-5 mt-2">
          <% @organization.errors.full_messages.each do |message| %>
            <li><%= message %></li>
          <% end %>
        </ul>
      </div>
    <% end %>

  <div>
    <%= form.label :name, "Organization Name", class: "block text-sm text-stone-700 font-medium mb-1" %>
    <%= form.text_field :name, 
                        required: true, 
                        autofocus: true,
                        class: "w-full border border-stone-500 rounded px-3 py-2 focus:outline-none focus:ring focus:ring-blue-200 mb-4" %>
  </div>
  
  
  <div>
    <%= form.label :operating_timezone, "Operating Timezone", class: "block text-sm text-stone-700 font-medium mb-1" %>
    <%= form.time_zone_select :operating_timezone, 
                             ActiveSupport::TimeZone.all.sort,
                             { prompt: "Select timezone" },
                             { required: true, class: "w-full border border-stone-500 rounded px-3 py-2 focus:outline-none focus:ring focus:ring-blue-200 mb-4" } %>
  </div>
  
  <div>
    <%= form.label :size, "Organization Size", class: "block text-sm text-stone-700 font-medium mb-1" %>
    <%= form.select :size, 
                   [
                     ["1-10 employees", "small"],
                     ["11-50 employees", "medium"],
                     ["51-200 employees", "large"],
                     ["201-1000 employees", "xlarge"],
                     ["1000+ employees", "enterprise"]
                   ],
                   { prompt: "Select organization size" },
                   { required: true, class: "w-full border border-stone-500 rounded px-3 py-2 focus:outline-none focus:ring focus:ring-blue-200 mb-4" } %>
  </div>

    <div class="flex items-center justify-between">
      <%= form.submit "Update Organization", class: "px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2" %>
      <%= link_to "Cancel", organizations_path, class: "text-stone-600 hover:text-stone-800" %>
    </div>
  <% end %>
</div>