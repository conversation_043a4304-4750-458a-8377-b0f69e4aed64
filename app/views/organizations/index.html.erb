<div class="container mx-auto px-4 py-8">
  <% if Current.organization.present? %>
    <div class="mb-6 p-4 bg-stone-50 rounded-lg">
      <p class="text-stone-600">Currently using:</p>
      <p class="text-lg font-semibold"><%= Current.organization.name %></p>
    </div>
  <% end %>

  <h1 class="text-2xl font-bold mb-6">Select an Organization</h1>
  <p class="mb-6">Choose an organization to continue</p>
  
  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
    <% if @organizations.any? %>
      <% @organizations.each do |org| %>
        <div class="bg-white p-6 rounded-lg shadow hover:shadow-md transition-shadow">
          <h2 class="text-xl font-semibold mb-2"><%= org.name %></h2>
          <% if org.respond_to?(:bio) && org.bio.present? %>
            <p class="text-stone-600 mb-4"><%= org.bio %></p>
          <% end %>
          
          <div class="flex flex-col space-y-2">
            <%= button_to "Use this organization", 
                          switch_organization_path(id: org.id), 
                          method: :post, 
                          class: "w-full py-2 px-4 bg-blue-600 text-white rounded hover:bg-blue-700" %>
            
            <% membership = OrganizationMembership.find_by(user: Current.user, organization: org) %>
            <% if membership && (membership.owner? || membership.admin?) %>
              <%= link_to "Edit organization", 
                          edit_organization_path(org), 
                          class: "w-full py-2 px-4 text-center bg-stone-200 text-stone-800 rounded hover:bg-stone-300" %>
            <% end %>
          </div>
        </div>
      <% end %>
    <% else %>
      <div class="col-span-full text-center py-8">
        <p class="text-lg mb-4">You are not a member of any organization. Please create a new organization to continue.</p>
      </div>
    <% end %>
    
    <!-- New Organization card -->
    <div class="bg-white p-6 rounded-lg shadow border-2 border-dashed border-stone-300 hover:border-stone-400 transition-colors">
      <h2 class="text-xl font-semibold mb-2">Create a New Organization</h2>
      <p class="text-stone-600 mb-4">Start fresh with a new organization</p>
      <%= link_to "New Organization", 
                  new_organization_path, 
                  class: "block w-full py-2 px-4 bg-stone-200 text-center text-stone-800 rounded hover:bg-stone-300" %>
    </div>
  </div>
</div>