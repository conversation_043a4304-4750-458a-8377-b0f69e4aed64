class AdminMailer < ApplicationMailer
  default from: '<EMAIL>'

  def csv_export_ready(admin_user, filename, file_path)
    @admin_user = admin_user
    @filename = filename
    @export_date = Date.current.strftime('%B %d, %Y')
    
    attachments[filename] = File.read(file_path)
    
    mail(
      to: admin_user.email,
      subject: "CSV Export Ready: #{filename}"
    )
  end

  def csv_export_failed(admin_user, controller_name, error_message)
    @admin_user = admin_user
    @controller_name = controller_name.humanize
    @error_message = error_message
    @export_date = Date.current.strftime('%B %d, %Y')
    
    mail(
      to: admin_user.email,
      subject: "CSV Export Failed: #{@controller_name}"
    )
  end
end
