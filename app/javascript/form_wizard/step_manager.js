// Form Wizard Step Manager
// Handles step navigation, state management, and UI updates

import { FormConfig } from "./form_config.js";
import { FormValidator } from "./validator.js";

export class StepManager {
  constructor(formElement, options = {}) {
    this.form = formElement;
    this.config = new FormConfig();
    this.validator = new FormValidator(formElement);
    this.currentStepIndex = 0;
    this.selectedCategory = null;

    // UI element references
    this.stepElements = new Map();
    this.nextButton = null;
    this.previousButton = null;
    this.submitButton = null;
    this.progressBar = null;

    // Options
    this.options = {
      autoHideSteps: true,
      showProgress: true,
      validateOnNext: true,
      ...options,
    };

    this.initialize();
  }

  initialize() {
    this.cacheUIElements();
    this.bindEvents();

    // Ensure first step is visible immediately
    setTimeout(() => {
      this.showCurrentStep();
      this.updateUI();
    }, 0);
  }

  // Cache references to UI elements
  cacheUIElements() {
    // Find step elements
    const stepElements = this.form.querySelectorAll("[data-step-name]");
    console.log("StepManager: Found step elements:", stepElements);

    stepElements.forEach((element) => {
      const stepName = element.dataset.stepName;
      this.stepElements.set(stepName, element);
      console.log(`StepManager: Cached step element: ${stepName}`, element);
    });

    // Find navigation buttons
    this.nextButton =
      this.form.querySelector('[data-form-wizard-target="nextButton"]') ||
      this.form.querySelector('[data-action*="nextStep"]');
    this.previousButton =
      this.form.querySelector('[data-form-wizard-target="previousButton"]') ||
      this.form.querySelector('[data-action*="previousStep"]');
    this.submitButton =
      this.form.querySelector('[data-form-wizard-target="submitButton"]') ||
      this.form.querySelector('input[type="submit"]');

    // Find progress bar
    this.progressBar = this.form.querySelector(
      '[data-form-wizard-target="progressBar"]'
    );
  }

  // Bind event listeners
  bindEvents() {
    // Category selection change - handle both select dropdown and radio buttons
    const categorySelect = this.form.querySelector(
      'select[name="job[job_category]"]'
    );
    if (categorySelect) {
      categorySelect.addEventListener("change", (event) => {
        this.handleCategoryChange(event.target.value);
      });
    }

    // Handle radio button category selection
    const categoryRadios = this.form.querySelectorAll(
      'input[name="job[job_category]"]'
    );
    categoryRadios.forEach((radio) => {
      radio.addEventListener("change", (event) => {
        if (event.target.checked) {
          this.handleCategoryChange(event.target.value);
        }
      });
    });

    // Navigation button events are handled by the main controller
  }

  // Handle category selection change
  handleCategoryChange(category) {
    this.selectedCategory = category;
    this.config.setCategory(category);
    this.updateRequiredAttributes();
    this.disableIrrelevantOutcomeFields();
    this.updateUI();
  }

  // Navigate to next step
  nextStep() {
    if (this.options.validateOnNext && !this.validateCurrentStep()) {
      return false;
    }

    if (this.isLastStep()) {
      return this.submitForm();
    }

    this.currentStepIndex++;
    this.showCurrentStep();
    this.updateUI();
    return true;
  }

  // Navigate to previous step
  previousStep() {
    if (this.isFirstStep()) {
      return false;
    }

    this.currentStepIndex--;
    this.showCurrentStep();
    this.updateUI();
    return true;
  }

  // Jump to a specific step
  goToStep(stepIndex) {
    if (stepIndex < 0 || stepIndex >= this.config.getTotalSteps()) {
      return false;
    }

    // Optionally validate all steps up to the target step
    if (this.options.validateOnNext && stepIndex > this.currentStepIndex) {
      for (let i = this.currentStepIndex; i < stepIndex; i++) {
        if (!this.validateStepByIndex(i)) {
          return false;
        }
      }
    }

    this.currentStepIndex = stepIndex;
    this.showCurrentStep();
    this.updateUI();
    return true;
  }

  // Show the current step and hide others
  showCurrentStep() {
    const currentStepKey = this.config.getCurrentStepKey(this.currentStepIndex);
    const currentStepDef = this.config.getStepDefinition(currentStepKey);

    console.log("StepManager: showCurrentStep called", {
      currentStepIndex: this.currentStepIndex,
      currentStepKey,
      currentStepDef,
      selectedCategory: this.selectedCategory,
      stepSequence: this.config.getStepSequence(),
    });

    if (!currentStepDef) {
      console.warn(`Step definition not found for: ${currentStepKey}`);
      return;
    }

    // Update required attributes for all form fields
    this.updateRequiredAttributes();

    // Hide all steps and category sections if auto-hide is enabled
    if (this.options.autoHideSteps) {
      this.stepElements.forEach((element) => {
        element.classList.add("hidden");
      });

      // Also hide all category sections
      const categorySections = this.form.querySelectorAll(
        "[data-section-name]"
      );
      categorySections.forEach((section) => {
        section.classList.add("hidden");
      });
    }

    // Show current step
    const stepElement = this.findStepElement(currentStepKey, currentStepDef);
    console.log("StepManager: Found step element:", stepElement);

    if (stepElement) {
      stepElement.classList.remove("hidden");
      console.log("StepManager: Step element shown");

      // Enable outcome fields for the current step
      this.enableOutcomeFieldsForCurrentStep();
    } else {
      console.warn("StepManager: Step element not found for:", currentStepKey);
    }
  }

  // Find the DOM element for a step
  findStepElement(stepKey, stepDef) {
    // Try different naming patterns
    const possibleNames = [
      stepKey,
      stepKey.replace(".", "_"),
      stepDef.name.toLowerCase().replace(/\s+/g, "_"),
      stepKey.split(".").pop(), // Just the step name without category
    ];

    for (const name of possibleNames) {
      const element = this.stepElements.get(name);
      if (element) {
        // For category-specific steps, ensure the parent section is visible
        this.ensureCategorySectionVisible(stepKey, element);
        return element;
      }
    }

    // Special handling for category-specific steps
    if (stepKey.includes(".")) {
      const [category, step] = stepKey.split(".");
      const categoryElement = this.form.querySelector(
        `[data-section-name="${category}"]`
      );
      if (categoryElement) {
        const stepElement = categoryElement.querySelector(
          `[data-step-id="${step}"]`
        );
        if (stepElement) {
          // Make sure the category section is visible
          categoryElement.classList.remove("hidden");
          return stepElement;
        }
      }
    }

    // Handle social media and other category steps without dots in stepKey
    if (stepDef && stepDef.category) {
      const categoryElement = this.form.querySelector(
        `[data-section-name="${stepDef.category}"]`
      );
      if (categoryElement) {
        // Look for step by data-step-name attribute
        const stepElement = categoryElement.querySelector(
          `[data-step-name="${stepKey}"]`
        );
        if (stepElement) {
          categoryElement.classList.remove("hidden");
          return stepElement;
        }
      }
    }

    console.warn(`Step element not found for: ${stepKey}`);
    return null;
  }

  // Ensure the category section is visible for category-specific steps
  ensureCategorySectionVisible(stepKey, stepElement) {
    // Find the parent section element
    const sectionElement = stepElement.closest("[data-section-name]");
    if (sectionElement) {
      sectionElement.classList.remove("hidden");
    }
  }

  // Update required attributes based on current step and category
  updateRequiredAttributes() {
    // Get all form fields with potential required attributes
    const allFormFields = this.form.querySelectorAll("input, select, textarea");

    // Remove required attribute from all fields first
    allFormFields.forEach((field) => {
      field.removeAttribute("required");
    });

    // Get current step info
    const currentStepKey = this.config.getCurrentStepKey(this.currentStepIndex);
    const currentStepDef = this.config.getStepDefinition(currentStepKey);

    if (
      !currentStepDef ||
      !currentStepDef.validation ||
      !currentStepDef.validation.fields
    ) {
      return;
    }

    // Add required attribute only to fields in the current step that are visible
    const currentStepElement = this.findStepElement(
      currentStepKey,
      currentStepDef
    );
    if (!currentStepElement) {
      return;
    }

    // Check if this step is relevant to the selected category
    const stepCategory = currentStepDef.category;
    if (stepCategory && stepCategory !== this.selectedCategory) {
      return; // Skip if step doesn't match selected category
    }

    // Add required attribute to fields in current step
    currentStepDef.validation.fields.forEach((fieldName) => {
      const field = currentStepElement.querySelector(
        `[name="job[${fieldName}]"]`
      );
      if (field && this.isFieldVisible(field)) {
        field.setAttribute("required", "true");
      }
    });
  }

  // Check if a field is visible (helper method)
  isFieldVisible(field) {
    if (!field) return false;

    const style = window.getComputedStyle(field);
    const isHidden =
      field.offsetParent === null ||
      field.closest(".hidden") !== null ||
      style.display === "none" ||
      style.visibility === "hidden";

    return !isHidden;
  }

  // Validate the current step
  validateCurrentStep() {
    return this.validateStepByIndex(this.currentStepIndex);
  }

  // Validate a specific step by index
  validateStepByIndex(stepIndex) {
    const stepKey = this.config.getCurrentStepKey(stepIndex);
    const stepDef = this.config.getStepDefinition(stepKey);
    const stepElement = this.findStepElement(stepKey, stepDef);

    if (!stepDef || !stepElement) {
      return true; // Can't validate, assume valid
    }

    return this.validator.validateStep(stepDef, stepElement);
  }

  // Update UI elements (buttons, progress, etc.)
  updateUI() {
    this.updateNavigationButtons();
    this.updateProgressBar();
    this.updateStepTitle();
  }

  // Update navigation button visibility and state
  updateNavigationButtons() {
    if (this.previousButton) {
      this.previousButton.classList.toggle("hidden", this.isFirstStep());
    }

    if (this.nextButton && this.submitButton) {
      const isLast = this.isLastStep();
      this.nextButton.classList.toggle("hidden", isLast);
      this.submitButton.classList.toggle("hidden", !isLast);
    }
  }

  // Update progress bar
  updateProgressBar() {
    if (this.progressBar && this.options.showProgress) {
      const percentage = this.config.getProgressPercentage(
        this.currentStepIndex
      );
      this.progressBar.style.width = `${percentage}%`;
    }
  }

  // Update step title if there's a title element
  updateStepTitle() {
    const titleElement = this.form.querySelector(
      '[data-form-wizard-target="stepTitle"]'
    );
    if (titleElement) {
      const stepKey = this.config.getCurrentStepKey(this.currentStepIndex);
      const stepDef = this.config.getStepDefinition(stepKey);
      if (stepDef) {
        titleElement.textContent = stepDef.title;
      }
    }
  }

  // Submit the form
  submitForm() {
    // Ensure the correct outcome field is enabled before submission
    this.disableIrrelevantOutcomeFields();

    // Skip JavaScript validation for now and let Rails handle validation
    // This allows the form to submit and show server-side validation errors
    console.log("Form submission triggered");

    // Trigger form submission
    if (this.submitButton) {
      console.log("Clicking submit button:", this.submitButton);
      this.submitButton.click();
    } else {
      console.log("Submitting form directly");
      this.form.submit();
    }

    return true;
  }

  // Utility methods
  isFirstStep() {
    return this.config.isFirstStep(this.currentStepIndex);
  }

  isLastStep() {
    return this.config.isLastStep(this.currentStepIndex);
  }

  getCurrentStepIndex() {
    return this.currentStepIndex;
  }

  getCurrentStepKey() {
    return this.config.getCurrentStepKey(this.currentStepIndex);
  }

  getCurrentStepDefinition() {
    const stepKey = this.getCurrentStepKey();
    return this.config.getStepDefinition(stepKey);
  }

  getTotalSteps() {
    return this.config.getTotalSteps();
  }

  getSelectedCategory() {
    return this.selectedCategory;
  }

  // Enable outcome fields for the current step
  enableOutcomeFieldsForCurrentStep() {
    const currentStepKey = this.config.getCurrentStepKey(this.currentStepIndex);
    const currentStepDef = this.config.getStepDefinition(currentStepKey);

    if (!currentStepDef) return;

    // Find the current step element
    const currentStepElement = this.findStepElement(
      currentStepKey,
      currentStepDef
    );
    if (!currentStepElement) return;

    // Enable all outcome fields in the current step
    const outcomeFields = currentStepElement.querySelectorAll(
      'input[name="job[outcome]"], input[name="job[outcome_disabled]"], select[name="job[outcome]"], select[name="job[outcome_disabled]"]'
    );

    outcomeFields.forEach((field) => {
      field.disabled = false;
      field.name = "job[outcome]";
    });
  }

  // Disable outcome fields that are not relevant to the selected category
  disableIrrelevantOutcomeFields() {
    // Find all outcome fields in the form (both select dropdowns and radio buttons)
    const outcomeSelectFields = this.form.querySelectorAll(
      'select[name="job[outcome]"]'
    );
    const outcomeRadioFields = this.form.querySelectorAll(
      'input[name="job[outcome]"]'
    );

    // Handle select dropdowns
    outcomeSelectFields.forEach((field) => {
      const stepElement = field.closest("[data-step-name]");
      if (!stepElement) return;

      const stepName = stepElement.getAttribute("data-step-name");

      // Determine if this outcome field should be enabled based on category
      let shouldEnable = false;

      if (
        this.selectedCategory === "social_media" &&
        stepName === "social_media_goal"
      ) {
        shouldEnable = true;
      } else if (
        this.selectedCategory === "newsletter" &&
        stepName === "newsletter_goal"
      ) {
        shouldEnable = true;
      } else if (
        this.selectedCategory === "lead_magnet" &&
        stepName === "lead_magnet_goal"
      ) {
        shouldEnable = true;
      }

      // Enable/disable the field
      if (shouldEnable) {
        field.disabled = false;
        field.name = "job[outcome]";
      } else {
        field.disabled = true;
        // Change the name so it won't be submitted
        field.name = "job[outcome_disabled]";
        // Clear the value
        field.value = "";
      }
    });

    // Handle radio buttons
    outcomeRadioFields.forEach((field) => {
      const stepElement = field.closest("[data-step-name]");
      if (!stepElement) return;

      const stepName = stepElement.getAttribute("data-step-name");

      // Determine if this outcome field should be enabled based on category
      let shouldEnable = false;

      if (
        this.selectedCategory === "social_media" &&
        stepName === "social_media_goal"
      ) {
        shouldEnable = true;
      } else if (
        this.selectedCategory === "newsletter" &&
        stepName === "newsletter_goal"
      ) {
        shouldEnable = true;
      } else if (
        this.selectedCategory === "lead_magnet" &&
        stepName === "lead_magnet_goal"
      ) {
        shouldEnable = true;
      }

      // Enable/disable the field
      if (shouldEnable) {
        field.disabled = false;
        field.name = "job[outcome]";
      } else {
        field.disabled = true;
        // Change the name so it won't be submitted
        field.name = "job[outcome_disabled]";
        // Clear the checked state
        field.checked = false;
      }
    });
  }

  // Reset the wizard to the first step
  reset() {
    this.currentStepIndex = 0;
    this.selectedCategory = null;
    this.config.setCategory(null);
    this.validator.clearAllErrors();
    this.showCurrentStep();
    this.updateUI();
  }
}
