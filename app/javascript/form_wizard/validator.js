// Form Wizard Validation System
// Provides unified validation across all form steps

export class FormValidator {
  constructor(formElement) {
    this.form = formElement;
    this.errors = new Map();
  }

  // Validate a specific step based on its definition
  validateStep(stepDefinition, stepElement) {
    if (!stepDefinition || !stepElement) {
      return true;
    }

    this.clearStepErrors(stepElement);
    let isValid = true;

    const validation = stepDefinition.validation;
    if (!validation) {
      return true;
    }

    // Skip validation if step is not required
    if (stepDefinition.required === false) {
      return true;
    }

    // Validate required fields
    if (validation.fields) {
      for (const fieldName of validation.fields) {
        if (!this.validateField(fieldName, stepElement)) {
          isValid = false;
        }
      }
    }

    // Handle conditional validation
    if (validation.conditional && isValid) {
      if (
        !this.validateConditionalFields(validation.conditional, stepElement)
      ) {
        isValid = false;
      }
    }

    this.displayStepErrors(stepElement, isValid);
    return isValid;
  }

  // Validate a single field
  validateField(fieldName, stepElement) {
    const field = this.findField(fieldName, stepElement);
    if (!field) {
      return true; // Field not found in this step, assume valid
    }

    // Skip validation if field is not visible
    if (!this.isFieldVisible(field)) {
      return true;
    }

    const isEmpty = this.isFieldEmpty(field);

    if (isEmpty) {
      this.addFieldError(field, `${this.getFieldLabel(field)} is required`);
      return false;
    }

    this.removeFieldError(field);
    return true;
  }

  // Validate conditional fields based on other field values
  validateConditionalFields(conditional, stepElement) {
    const triggerField = this.findField(conditional.field, stepElement);
    if (!triggerField) {
      return true;
    }

    const triggerValue = this.getFieldValue(triggerField);

    // Check if the trigger value requires additional fields
    if (conditional.values.includes(triggerValue)) {
      let isValid = true;

      for (const requiredField of conditional.requiredFields) {
        if (!this.validateField(requiredField, stepElement)) {
          isValid = false;
        }
      }

      return isValid;
    }

    return true;
  }

  // Find a field by name within a step element
  findField(fieldName, stepElement) {
    // Try different field name patterns
    const selectors = [
      `[name="job[${fieldName}]"]`,
      `[name="job[${fieldName}][]"]`, // For checkbox arrays
      `[name="${fieldName}"]`,
      `[name="${fieldName}[]"]`, // For checkbox arrays without job prefix
      `input[name*="${fieldName}"]`,
      `select[name*="${fieldName}"]`,
      `textarea[name*="${fieldName}"]`,
    ];

    for (const selector of selectors) {
      const field = stepElement.querySelector(selector);
      if (field) {
        return field;
      }
    }

    return null;
  }

  // Check if a field is visible to the user
  isFieldVisible(field) {
    if (!field) return false;

    const style = window.getComputedStyle(field);
    const isHidden =
      field.offsetParent === null ||
      field.closest(".hidden") !== null ||
      style.display === "none" ||
      style.visibility === "hidden";

    return !isHidden;
  }

  // Check if a field is empty
  isFieldEmpty(field) {
    if (!field) return true;

    const value = this.getFieldValue(field);

    if (field.type === "checkbox" || field.type === "radio") {
      // For radio buttons, check if any in the group is selected
      if (field.type === "radio") {
        const radioGroup = this.form.querySelectorAll(`[name="${field.name}"]`);
        return !Array.from(radioGroup).some((radio) => radio.checked);
      }

      // For checkboxes, handle both single checkboxes and checkbox arrays
      if (field.type === "checkbox") {
        // Check if this is a checkbox array (name ends with [])
        if (field.name.endsWith("[]")) {
          // For checkbox arrays, check if any checkbox in the group is checked
          const checkboxGroup = this.form.querySelectorAll(
            `[name="${field.name}"]`
          );
          return !Array.from(checkboxGroup).some(
            (checkbox) => checkbox.checked
          );
        } else {
          // For single checkboxes, check if it's checked
          return !field.checked;
        }
      }
    }

    return !value || value.trim() === "";
  }

  // Get the value of a field
  getFieldValue(field) {
    if (!field) return "";

    if (field.type === "checkbox") {
      // Handle checkbox arrays
      if (field.name.endsWith("[]")) {
        // For checkbox arrays, return array of checked values
        const checkboxGroup = this.form.querySelectorAll(
          `[name="${field.name}"]`
        );
        const checkedValues = Array.from(checkboxGroup)
          .filter((checkbox) => checkbox.checked)
          .map((checkbox) => checkbox.value);
        return checkedValues.length > 0 ? checkedValues : "";
      } else {
        // For single checkboxes, return value if checked
        return field.checked ? field.value : "";
      }
    }

    if (field.type === "radio") {
      const checkedRadio = this.form.querySelector(
        `[name="${field.name}"]:checked`
      );
      return checkedRadio ? checkedRadio.value : "";
    }

    return field.value || "";
  }

  // Get a human-readable label for a field
  getFieldLabel(field) {
    if (!field) return "Field";

    // Try to find associated label
    const label =
      this.form.querySelector(`label[for="${field.id}"]`) ||
      field.closest(".mb-4")?.querySelector("label") ||
      field.previousElementSibling?.tagName === "LABEL"
        ? field.previousElementSibling
        : null;

    if (label) {
      return label.textContent.replace("*", "").trim();
    }

    // Fallback to field name
    return field.name
      .replace(/job\[|\]/g, "")
      .replace(/_/g, " ")
      .replace(/\b\w/g, (l) => l.toUpperCase());
  }

  // Add error styling and message to a field
  addFieldError(field, message) {
    if (!field) return;

    field.classList.add("border-red-500");
    this.errors.set(field.name, message);

    // Show error message if it exists
    const errorElement = this.findErrorElement(field);
    if (errorElement) {
      errorElement.textContent = message;
      errorElement.classList.remove("hidden");
    }
  }

  // Remove error styling and message from a field
  removeFieldError(field) {
    if (!field) return;

    field.classList.remove("border-red-500");
    this.errors.delete(field.name);

    // Hide error message
    const errorElement = this.findErrorElement(field);
    if (errorElement) {
      errorElement.classList.add("hidden");
    }
  }

  // Find the error message element for a field
  findErrorElement(field) {
    const container = field.closest(".mb-4") || field.parentElement;
    return (
      container?.querySelector(".error-message") ||
      container?.querySelector(".text-red-500")
    );
  }

  // Clear all errors for a step
  clearStepErrors(stepElement) {
    const errorElements = stepElement.querySelectorAll(
      ".error-message, .step-error-message"
    );
    errorElements.forEach((element) => {
      element.classList.add("hidden");
    });

    const errorFields = stepElement.querySelectorAll(".border-red-500");
    errorFields.forEach((field) => {
      field.classList.remove("border-red-500");
    });
  }

  // Display step-level error messages
  displayStepErrors(stepElement, isValid) {
    const stepErrorElement = stepElement.querySelector(".step-error-message");
    if (stepErrorElement) {
      stepErrorElement.classList.toggle("hidden", isValid);
    }
  }

  // Get all current errors
  getErrors() {
    return Array.from(this.errors.entries()).map(([field, message]) => ({
      field,
      message,
    }));
  }

  // Check if there are any errors
  hasErrors() {
    return this.errors.size > 0;
  }

  // Clear all errors
  clearAllErrors() {
    this.errors.clear();

    const errorElements = this.form.querySelectorAll(
      ".error-message, .step-error-message"
    );
    errorElements.forEach((element) => {
      element.classList.add("hidden");
    });

    const errorFields = this.form.querySelectorAll(".border-red-500");
    errorFields.forEach((field) => {
      field.classList.remove("border-red-500");
    });
  }
}
