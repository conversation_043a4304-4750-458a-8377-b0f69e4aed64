// Debug script for form wizard
// Add this to the browser console to test the form wizard

console.log('Form Wizard Debug Script Loaded');

// Check if the form wizard controller is connected
const formElement = document.querySelector('[data-controller="form-wizard"]');
if (formElement) {
  console.log('✅ Form wizard element found:', formElement);
  
  // Check if Stimulus controller is connected
  const controller = formElement.stimulus?.controller;
  if (controller) {
    console.log('✅ Stimulus controller connected:', controller);
    
    // Test step manager
    if (controller.stepManager) {
      console.log('✅ Step manager initialized:', controller.stepManager);
      console.log('Current step:', controller.stepManager.getCurrentStepIndex());
      console.log('Current step key:', controller.stepManager.getCurrentStepKey());
      console.log('Total steps:', controller.stepManager.getTotalSteps());
    } else {
      console.log('❌ Step manager not initialized');
    }
  } else {
    console.log('❌ Stimulus controller not connected');
  }
} else {
  console.log('❌ Form wizard element not found');
}

// Check for step elements
const stepElements = document.querySelectorAll('[data-step-name]');
console.log(`Found ${stepElements.length} step elements:`, stepElements);

// Check for navigation buttons
const nextButton = document.querySelector('[data-form-wizard-target="nextButton"]');
const prevButton = document.querySelector('[data-form-wizard-target="previousButton"]');
const submitButton = document.querySelector('[data-form-wizard-target="submitButton"]');

console.log('Navigation buttons:', {
  next: nextButton,
  previous: prevButton,
  submit: submitButton
});

// Check for progress bar
const progressBar = document.querySelector('[data-form-wizard-target="progressBar"]');
console.log('Progress bar:', progressBar);

// Test category selection
const categorySelect = document.querySelector('select[name="job[job_category]"]');
if (categorySelect) {
  console.log('✅ Category select found:', categorySelect);
  
  // Add event listener to test category change
  categorySelect.addEventListener('change', function(e) {
    console.log('Category changed to:', e.target.value);
  });
} else {
  console.log('❌ Category select not found');
}

// Export debug functions to window for manual testing
window.formWizardDebug = {
  getController: () => formElement?.stimulus?.controller,
  testNextStep: () => {
    const controller = formElement?.stimulus?.controller;
    if (controller) {
      controller.nextStep();
      console.log('Next step triggered');
    }
  },
  testPreviousStep: () => {
    const controller = formElement?.stimulus?.controller;
    if (controller) {
      controller.previousStep();
      console.log('Previous step triggered');
    }
  },
  getCurrentStep: () => {
    const controller = formElement?.stimulus?.controller;
    return controller?.getCurrentStep();
  },
  validateCurrentStep: () => {
    const controller = formElement?.stimulus?.controller;
    return controller?.validateCurrentStep();
  }
};

console.log('Debug functions available at window.formWizardDebug');
