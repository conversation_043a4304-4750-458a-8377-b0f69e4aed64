import React, { useState } from 'react';
import { Combobox, ComboboxInput, ComboboxOption, ComboboxOptions } from '@headlessui/react'

const people = [
  { id: 1, name: 'AI' },
  { id: 2, name: '<PERSON>' },
  { id: 3, name: '<PERSON><PERSON>' },
  { id: 4, name: 'Copywriting' },
  { id: 5, name: '<PERSON><PERSON>' },
]

const MultiSelect = () => {

const [selectedPeople, setSelectedPeople] = useState([people[0], people[1]])
  const [query, setQuery] = useState('')

const filteredPeople =
    query === ''
      ? people
      : people.filter((person) => {
          return person.name.toLowerCase().includes(query.toLowerCase())
        })

  return (
 <Combobox multiple value={selectedPeople} onChange={setSelectedPeople} onClose={() => setQuery('')}>
      {selectedPeople.length > 0 && (
        <ul>
          {selectedPeople.map((person) => (
            <li key={person.id}>{person.name}</li>
          ))}
        </ul>
      )}
      <ComboboxInput aria-label="Assignees" onChange={(event) => setQuery(event.target.value)} />
      <ComboboxOptions anchor="bottom" className="border empty:invisible">
        {filteredPeople.map((person) => (
          <ComboboxOption key={person.id} value={person} className="data-[focus]:bg-blue-100">
            {person.name}
          </ComboboxOption>
        ))}
      </ComboboxOptions>
    </Combobox>
  )
};

export default MultiSelect;
