import React from "react";
import {
  InstantSearch,
  SearchBox,
  Configure,
  RefinementList,
  Hits,
} from "react-instantsearch";

const ApplicantCard = ({ hit }) => (
  <div className="p-4 mb-4 bg-white rounded-lg shadow">
    <div className="flex items-start justify-between">
      <div>
        <h3 className="text-lg font-semibold">{hit.applicant_name}</h3>
        <p className="text-sm text-stone-600">{hit.applicant_email}</p>
        <p className="text-sm text-stone-600">Applied for: {hit.job_title}</p>
        <p className="mt-2 text-sm">{hit.application_letter}</p>
      </div>
      <span
        className={`px-2 py-1 text-sm rounded-full ${
          hit.status === "accepted"
            ? "bg-green-100 text-green-800"
            : hit.status === "rejected"
            ? "bg-red-100 text-red-800"
            : hit.status === "under_review"
            ? "bg-yellow-100 text-yellow-800"
            : "bg-stone-100 text-stone-800"
        }`}
      >
        {hit.status}
      </span>
    </div>
    <div className="mt-4 text-sm text-stone-500">
      Applied: {new Date(hit.applied_at).toLocaleDateString()}
    </div>
  </div>
);

import searchClient from "../bundles/HelloWorld/search-client";

const ApplicantsSearch = () => {
  return (
    <div className="w-full">
      <InstantSearch indexName="JobApplication" searchClient={searchClient}>
        <div className="grid grid-cols-12 gap-6">
          <div className="col-span-3 p-4 bg-white rounded-lg shadow">
            <div className="mb-6">
              <h3 className="mb-4 text-lg font-semibold">Status</h3>
              <RefinementList attribute="job_title" />
            </div>
            <div className="mb-6">
              <h3 className="mb-4 text-lg font-semibold">Status</h3>
              <RefinementList attribute="status" />
            </div>
          </div>

          <div className="col-span-9">
            <div className="mb-6">
              <SearchBox
                className="w-full"
                translations={{
                  placeholder: "Search applicants...",
                }}
              />
            </div>
            <Configure
              hitsPerPage={10}
              attributesToSnippet={["application_letter:50"]}
            />

            <Hits hitComponent={ApplicantCard} />
          </div>
        </div>
      </InstantSearch>
    </div>
  );
};

export default ApplicantsSearch;
