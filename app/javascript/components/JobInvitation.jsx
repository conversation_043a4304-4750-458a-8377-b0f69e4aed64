import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { useMutation } from "@tanstack/react-query";
import axios from "axios";

const JobInvitation = ({ jobId, talentId, onSuccess }) => {
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm();
  const [successMessage, setSuccessMessage] = useState("");

  const { mutate: sendInvitation, isLoading } = useMutation(
    (data) =>
      axios.post("/scout/job_invitations", {
        job_id: jobId,
        user_id: talentId,
        message: data.message,
      }),
    {
      onSuccess: () => {
        setSuccessMessage("Invitation sent successfully!");
        onSuccess?.();
      },
      onError: (error) => {
        console.error("Error sending invitation:", error);
      },
    }
  );

  return (
    <div className="p-4 bg-white border rounded-lg shadow-sm">
      <h3 className="mb-4 text-lg font-medium">Send Job Invitation</h3>
      {successMessage ? (
        <div className="mb-4 text-green-600">{successMessage}</div>
      ) : (
        <form onSubmit={handleSubmit(sendInvitation)}>
          <div className="mb-4">
            <label
              htmlFor="message"
              className="block mb-1 text-sm font-medium text-gray-700"
            >
              Personal Message (Optional)
            </label>
            <textarea
              id="message"
              {...register("message")}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              rows={4}
            />
          </div>
          <button
            type="submit"
            disabled={isLoading}
            className="flex justify-center w-full px-4 py-2 text-sm font-medium text-white bg-indigo-600 border border-transparent rounded-md shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
          >
            {isLoading ? "Sending..." : "Send Invitation"}
          </button>
        </form>
      )}
    </div>
  );
};

export default JobInvitation;
