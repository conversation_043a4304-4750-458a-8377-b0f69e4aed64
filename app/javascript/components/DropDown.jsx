import React from "react";
import { RefinementList } from "react-instantsearch";
import { Menu, MenuButton, MenuItem, MenuItems } from "@headlessui/react";
import { ChevronDownIcon } from "@heroicons/react/24/outline";

const Dropdown = ({ attribute, title = "Options" }) => {
  return (
    <Menu as="div" className="relative inline-block text-left">
      <div>
        <MenuButton className="inline-flex w-full justify-center gap-x-1.5 rounded-md bg-white px-3 py-2 text-sm font-semibold text-stone-900 shadow-sm ring-1 ring-inset ring-stone-300 hover:bg-stone-50">
          {title}
          <ChevronDownIcon
            aria-hidden="true"
            className="-mr-1 size-5 text-stone-400"
          />
        </MenuButton>
      </div>

      <MenuItems
        unmount={false}
        className="absolute right-0 z-10 mt-2 w-56 origin-top-right rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none transform opacity-100 scale-100 transition ease-out duration-100"
      >
        <div className="py-1">
          <RefinementList
            attribute={attribute}
            className="px-4 py-2"
            searchable={true}
            operator="or"
          />
        </div>
      </MenuItems>
    </Menu>
  );
};

export default Dropdown;
