import { Controller } from "@hotwired/stimulus";

export default class extends Controller {
  static targets = ["container", "content", "overlay"];

  connect() {
    // Bind event handlers to maintain proper context
    this.boundShow = this.show.bind(this);
    this.boundClose = this.close.bind(this);

    // Listen for our custom events
    this.element.addEventListener("show-modal", this.boundShow);
    this.element.addEventListener("modal-close", this.boundClose);
  }

  disconnect() {
    // Clean up event listeners using the bound references
    if (this.boundShow) {
      this.element.removeEventListener("show-modal", this.boundShow);
    }
    if (this.boundClose) {
      this.element.removeEventListener("modal-close", this.boundClose);
    }
  }

  // Prevent clicks on modal content from closing the modal
  stopPropagation(event) {
    event.stopPropagation();
  }

  show(event) {
    // If the event is from a click, prevent default behavior
    if (event) {
      event.preventDefault();
    }

    this.containerTarget.classList.remove("hidden");

    // Prevent scrolling on the body when modal is open
    document.body.style.overflow = "hidden";
  }

  close() {
    console.log("Modal close() method called");
    this.containerTarget.classList.add("hidden");

    // Restore scrolling on the body
    document.body.style.overflow = "";

    // Clear modal actions to avoid duplicate scripts when reopening
    const modalActions = document.getElementById("modal_actions");
    if (modalActions) {
      modalActions.innerHTML = "";
    }

    // Don't clear the content as it will prevent reopening
    // Instead, the content will be replaced when reopening
  }

  // Close when clicking specifically on the overlay (gray background)
  closeOnOverlayClick(event) {
    // Make sure the click was directly on the overlay, not a child element
    if (event.target === this.overlayTarget) {
      this.close();
    }
  }
}
