import { Controller } from "@hotwired/stimulus";

export default class extends Controller {
  static targets = ["button"];

  connect() {
    // Show all applications by default
    this.showAll();
  }

  filter(event) {
    const selectedStatus = event.currentTarget.dataset.status;
    const cards = document.querySelectorAll(".application-card");

    // Update button styles
    this.buttonTargets.forEach((button) => {
      if (button === event.currentTarget) {
        button.classList.add("bg-white", "text-black");
        button.classList.remove("bg-transparent", "text-stone-600");
      } else {
        button.classList.remove("bg-white", "text-black");
        button.classList.add("bg-transparent", "text-stone-600");
      }
    });

    // Filter cards
    cards.forEach((card) => {
      if (
        selectedStatus === "all_candidates" ||
        card.dataset.status === selectedStatus
      ) {
        card.classList.remove("hidden");
      } else {
        card.classList.add("hidden");
      }
    });
  }

  showAll() {
    const cards = document.querySelectorAll(".application-card");
    cards.forEach((card) => card.classList.remove("hidden"));
  }
}
