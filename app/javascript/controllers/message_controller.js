import { Controller } from "@hotwired/stimulus";

export default class extends Controller {
  static values = {
    userId: String,
  };

  connect() {
    if (!document.body.dataset.userId) {
      throw new Error("User ID data attribute not found on body tag");
    }

    const currentUserId = document.body.dataset.userId;
    const messageUserId = this.userIdValue;

    if (messageUserId === currentUserId) {
      this.element.classList.add("message-sent");
    } else {
      this.element.classList.add("message-received");
    }
  }
}
