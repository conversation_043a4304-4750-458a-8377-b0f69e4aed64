import { Controller } from "@hotwired/stimulus";

export default class extends Controller {
  static targets = [
    "aside",
    "toggleButton",
    "main",
    "backdrop",
    "navigationItems",
  ];

  connect() {
    // Initialize the sidebar based on saved state
    this.initializeSidebar();

    // Add resize event listener
    this.resizeHandler = this.handleResize.bind(this);
    window.addEventListener("resize", this.resizeHandler);

    // Set the initial navigation items alignment
    const nav = this.hasNavigationItemsTarget
      ? this.navigationItemsTarget
      : null;
    if (nav) {
      nav.classList.add("items-start");
    }

    this.updateToggleIcon();
  }

  // Initialize sidebar based on saved preference
  initializeSidebar() {
    const savedState = localStorage.getItem("sidebarState");

    // On mobile devices always start with sidebar hidden
    if (window.innerWidth < 1024) {
      this.collapse();
      this.hide();
      return;
    }

    // Desktop: Use saved state if available, otherwise default to expanded
    if (savedState === "collapsed") {
      this.collapse();
    } else {
      this.expand();
    }
  }

  disconnect() {
    // Clean up event listener when controller is disconnected
    window.removeEventListener("resize", this.resizeHandler);
  }

  handleResize() {
    const savedState = localStorage.getItem("sidebarState");

    if (window.innerWidth >= 1024) {
      // 1024px is the lg breakpoint in Tailwind
      // Respect the saved state preference on desktop
      if (savedState === "collapsed") {
        this.collapse();
      } else {
        this.expand();
      }
    } else {
      this.collapse();
      this.hide(); // On mobile, start with sidebar hidden
    }
  }

  toggle() {
    const aside = this.asideTarget;

    // On mobile, toggle between hidden and visible
    if (window.innerWidth < 1024) {
      if (aside.classList.contains("hidden")) {
        this.show();
      } else {
        this.hide();
      }
    }
    // On desktop, toggle between collapsed and expanded
    else {
      if (aside.classList.contains("sidebar-collapsed")) {
        this.expand();
      } else {
        this.collapse();
      }
    }

    this.updateToggleIcon();
  }

  // Show/hide the sidebar (for mobile)
  show() {
    const aside = this.asideTarget;
    const backdrop = this.hasBackdropTarget ? this.backdropTarget : null;

    aside.classList.remove("hidden");

    // Show backdrop on mobile
    if (backdrop) {
      backdrop.classList.remove("hidden");
    }
  }

  hide() {
    const aside = this.asideTarget;
    const backdrop = this.hasBackdropTarget ? this.backdropTarget : null;

    aside.classList.add("hidden");

    // Hide backdrop
    if (backdrop) {
      backdrop.classList.add("hidden");
    }
  }

  // Expand/collapse the sidebar (for desktop)
  expand() {
    const aside = this.asideTarget;
    const main = this.mainTarget;
    const nav = this.hasNavigationItemsTarget
      ? this.navigationItemsTarget
      : null;

    // Show the sidebar if it's hidden
    aside.classList.remove("hidden");

    // Switch to expanded mode
    aside.classList.remove("sidebar-collapsed");
    aside.classList.add("sidebar-expanded");
    aside.dataset.sidebarExpanded = "true";

    // Adjust main content
    main.classList.remove("sidebar-collapsed-main");
    main.classList.add("sidebar-expanded-main");

    // Set navigation items back to start alignment
    if (nav) {
      nav.classList.remove("items-center");
      nav.classList.add("items-start");
    }

    // Save state to localStorage (only on desktop)
    if (window.innerWidth >= 1024) {
      localStorage.setItem("sidebarState", "expanded");
    }
  }

  collapse() {
    const aside = this.asideTarget;
    const main = this.mainTarget;
    const nav = this.hasNavigationItemsTarget
      ? this.navigationItemsTarget
      : null;

    // Switch to collapsed mode
    aside.classList.remove("sidebar-expanded");
    aside.classList.add("sidebar-collapsed");
    aside.dataset.sidebarExpanded = "false";

    // Adjust main content
    main.classList.remove("sidebar-expanded-main");
    main.classList.add("sidebar-collapsed-main");

    // Center navigation items when collapsed
    if (nav) {
      nav.classList.remove("items-start");
      nav.classList.add("items-center");
    }

    // Save state to localStorage (only on desktop)
    if (window.innerWidth >= 1024) {
      localStorage.setItem("sidebarState", "collapsed");
    }
  }

  updateToggleIcon() {
    const aside = this.asideTarget;
    const button = this.toggleButtonTarget;

    // Clear the button content
    button.innerHTML = "";

    // Create the appropriate icon based on sidebar state
    if (window.innerWidth < 1024) {
      // On mobile, show hamburger/X based on visibility
      if (aside.classList.contains("hidden")) {
        // Show menu icon (hamburger) when sidebar is hidden
        button.innerHTML = `
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-6 h-6">
            <path d="M3 5h18" />
            <path d="M3 12h18" />
            <path d="M3 19h18" />
          </svg>
          <span class="sr-only">Show Menu</span>
        `;
      } else {
        // Show close icon (X) when sidebar is visible
        button.innerHTML = `
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-6 h-6">
            <path d="M18 6 6 18" />
            <path d="m6 6 12 12" />
          </svg>
          <span class="sr-only">Hide Menu</span>
        `;
      }
    } else {
      // On desktop, show expand/collapse icons
      if (aside.classList.contains("sidebar-collapsed")) {
        // Show expand icon when sidebar is collapsed
        button.innerHTML = `
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-6 h-6">
            <path d="M3 5h18" />
            <path d="M3 12h18" />
            <path d="M3 19h18" />
          </svg>
          <span class="sr-only">Expand Menu</span>
        `;
      } else {
        // Show collapse icon when sidebar is expanded
        button.innerHTML = `
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-6 h-6">
            <path d="m9 18-6-6 6-6" />
          </svg>
          <span class="sr-only">Collapse Menu</span>
        `;
      }
    }
  }
}
