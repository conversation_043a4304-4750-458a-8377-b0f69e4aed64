import { Controller } from "@hotwired/stimulus"

// Connects to data-controller="badge-modal-trigger"
export default class extends Controller {
  static values = {
    badgeId: String,
    badgeName: String,
    badgeDescription: String,
    badgeIcon: String,
    badgeBackgroundColor: String,
    badgeTextColor: String,
    badgeCriteria: String
  }

  initialize() {
    console.log('=== BADGE MODAL TRIGGER CONTROLLER ===');
    console.log('badge-modal-trigger #initialize');
    console.log('Controller element:', this.element);
    console.log('Available values:', {
      badgeId: this.badgeIdValue,
      badgeName: this.badgeNameValue,
      badgeDescription: this.badgeDescriptionValue,
      badgeIcon: this.badgeIconValue,
      badgeBackgroundColor: this.badgeBackgroundColorValue,
      badgeTextColor: this.badgeTextColorValue,
      badgeCriteria: this.badgeCriteriaValue
    });
  }

  connect() {
    console.log('badge-modal-trigger #connect');
    console.log('Connected element:', this.element);
    console.log('Element classes:', this.element.className);
    console.log('Element tag:', this.element.tagName);
    
    // Add click event listener
    this.element.addEventListener('click', this.handleClick.bind(this));
    console.log('Click event listener added to badge element');
  }

  disconnect() {
    console.log('badge-modal-trigger #disconnect');
    this.element.removeEventListener('click', this.handleClick.bind(this));
  }

  handleClick(event) {
    console.log('=== BADGE CLICK DETECTED ===');
    console.log('Click event:', event);
    console.log('Target element:', event.target);
    console.log('Current element:', this.element);
    
    // Prevent default behavior to avoid any unwanted actions (like CSV downloads)
    event.preventDefault();
    event.stopPropagation();
    
    console.log('Default behavior prevented and propagation stopped');
    
    try {
      this.openBadgeModal();
    } catch (error) {
      console.error('Error opening badge modal:', error);
    }
  }

  openBadgeModal() {
    console.log('=== OPENING BADGE MODAL ===');
    
    // Find the badge modal controller
    const modalElement = document.querySelector('[data-controller*="badge-modal"]');
    console.log('Modal element found:', modalElement);
    
    if (!modalElement) {
      console.error('Badge modal element not found');
      return;
    }
    
    // Get the badge modal controller instance
    const modalController = this.application.getControllerForElementAndIdentifier(modalElement, 'badge-modal');
    console.log('Modal controller found:', modalController);
    
    if (!modalController) {
      console.error('Badge modal controller not found');
      return;
    }
    
    // Prepare badge data
    const badgeData = this.prepareBadgeData();
    console.log('Badge data prepared:', badgeData);
    
    // Set badge data in modal and show it
    if (typeof modalController.setBadgeData === 'function') {
      modalController.setBadgeData(badgeData);
      console.log('Badge data set in modal controller');
    } else {
      console.warn('Modal controller does not have setBadgeData method');
    }
    
    if (typeof modalController.show === 'function') {
      modalController.show();
      console.log('Modal show method called');
    } else {
      console.error('Modal controller does not have show method');
    }
  }

  prepareBadgeData() {
    const badgeData = {
      id: this.badgeIdValue,
      name: this.badgeNameValue,
      description: this.badgeDescriptionValue,
      icon: this.badgeIconValue,
      backgroundColor: this.badgeBackgroundColorValue,
      textColor: this.badgeTextColorValue,
      criteria: this.badgeCriteriaValue
    };
    
    console.log('Prepared badge data:', badgeData);
    return badgeData;
  }

  // Analytics tracking method (optional)
  trackBadgeClick() {
    console.log('Tracking badge click for analytics');
    // Add analytics tracking here if needed
  }
}
