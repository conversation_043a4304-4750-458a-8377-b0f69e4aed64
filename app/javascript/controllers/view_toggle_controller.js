import { Controller } from "@hotwired/stimulus";

// Connects to data-controller="view-toggle"
export default class extends Controller {
  static targets = [
    "listContainer",
    "galleryContainer",
    "toggleButtonList",
    "toggleButtonGallery",
  ];

  connect() {
    // Default to list view
    this.showList();
  }

  showList() {
    this.listContainerTarget.classList.remove("hidden");
    this.galleryContainerTarget.classList.add("hidden");
    if (this.hasToggleButtonListTarget) {
      this.toggleButtonListTarget.classList.add(
        "bg-stone-100",
        "text-stone-700"
      );
      this.toggleButtonListTarget.classList.remove(
        "text-stone-500",
        "hover:bg-stone-50"
      );
    }
    if (this.hasToggleButtonGalleryTarget) {
      this.toggleButtonGalleryTarget.classList.remove(
        "bg-stone-100",
        "text-stone-700"
      );
      this.toggleButtonGalleryTarget.classList.add(
        "text-stone-500",
        "hover:bg-stone-50"
      );
    }
  }

  showGallery() {
    this.galleryContainerTarget.classList.remove("hidden");
    this.listContainerTarget.classList.add("hidden");
    if (this.hasToggleButtonListTarget) {
      this.toggleButtonListTarget.classList.remove(
        "bg-stone-100",
        "text-stone-700"
      );
      this.toggleButtonListTarget.classList.add(
        "text-stone-500",
        "hover:bg-stone-50"
      );
    }
    if (this.hasToggleButtonGalleryTarget) {
      this.toggleButtonGalleryTarget.classList.add(
        "bg-stone-100",
        "text-stone-700"
      );
      this.toggleButtonGalleryTarget.classList.remove(
        "text-stone-500",
        "hover:bg-stone-50"
      );
    }
  }
}
