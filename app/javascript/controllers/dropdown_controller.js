import { Controller } from "@hotwired/stimulus";

// Connects to data-controller="dropdown"
export default class extends Controller {
  static targets = ["button", "menu", "chevron"];

  connect() {
    // Bind the outside click handler
    this.outsideClickHandler = this.outsideClick.bind(this);
    // Use a timeout to prevent the initial click that opens the dropdown from immediately closing it
    setTimeout(() => {
      document.addEventListener("click", this.outsideClickHandler);
    }, 0);
  }

  disconnect() {
    document.removeEventListener("click", this.outsideClickHandler);
  }

  toggle() {
    this.menuTarget.classList.toggle("hidden");
    // Optional: Add/remove ARIA attributes for accessibility
    const isExpanded = !this.menuTarget.classList.contains("hidden");
    this.buttonTarget.setAttribute("aria-expanded", isExpanded);

    // Rotate chevron icon
    if (this.hasChevronTarget) {
      if (isExpanded) {
        this.chevronTarget.style.transform = "rotate(180deg)";
      } else {
        this.chevronTarget.style.transform = "rotate(0deg)";
      }
    }
  }

  outsideClick(event) {
    // If the click is outside the controller's element and the menu is not hidden
    if (
      !this.element.contains(event.target) &&
      !this.menuTarget.classList.contains("hidden")
    ) {
      this.hide(); // Call the new hide method
    }
  }

  hide() {
    if (!this.menuTarget.classList.contains("hidden")) {
      this.menuTarget.classList.add("hidden");
      this.buttonTarget.setAttribute("aria-expanded", "false");

      // Reset chevron rotation
      if (this.hasChevronTarget) {
        this.chevronTarget.style.transform = "rotate(0deg)";
      }
    }
  }
}
