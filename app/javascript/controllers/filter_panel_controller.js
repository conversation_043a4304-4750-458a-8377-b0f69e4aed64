import { Controller } from "@hotwired/stimulus";

export default class extends Controller {
  static targets = [
    "panel",
    "toggleButton",
    "activeFilters",
    "filterTag",
    "clearButton",
  ];

  connect() {
    console.log("Filter panel controller connected");
    this.updateActiveFilters();
  }

  toggle() {
    this.panelTarget.classList.toggle("hidden");

    // Update button icon/text
    const isHidden = this.panelTarget.classList.contains("hidden");
    this.toggleButtonTarget.innerHTML = isHidden
      ? `<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
        <path fill-rule="evenodd" d="M3 3a1 1 0 011-1h12a1 1 0 011 1v3a1 1 0 01-.293.707L12 11.414V15a1 1 0 01-.293.707l-2 2A1 1 0 018 17v-5.586L3.293 6.707A1 1 0 013 6V3z" clip-rule="evenodd" />
      </svg> Filters`
      : `<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
        <path fill-rule="evenodd" d="M3 3a1 1 0 011-1h12a1 1 0 011 1v3a1 1 0 01-.293.707L12 11.414V15a1 1 0 01-.293.707l-2 2A1 1 0 018 17v-5.586L3.293 6.707A1 1 0 013 6V3z" clip-rule="evenodd" />
      </svg> Hide Filters`;
  }

  clearFilters(event) {
    event.preventDefault();

    // Reset all form inputs
    const form = this.element.closest("form");
    if (!form) {
      console.error("Form not found");
      return;
    }

    const inputs = form.querySelectorAll(
      "input[type='checkbox'], input[type='radio']"
    );

    inputs.forEach((input) => {
      if (input.type === "checkbox") {
        input.checked = false;
      } else if (input.type === "radio" && input.value === "") {
        input.checked = true;
      } else if (input.type === "radio") {
        input.checked = false;
      }
    });

    // Reset the search input
    const searchInput = form.querySelector("input[type='text']");
    if (searchInput) searchInput.value = "";

    // Submit the form automatically to update results
    form.requestSubmit();

    // Clear active filters display
    this.updateActiveFilters();
  }

  removeFilter(event) {
    event.preventDefault();
    const filterType = event.currentTarget.dataset.filterType;
    const filterValue = event.currentTarget.dataset.filterValue;

    // Find the form
    const form = this.element.closest("form");
    if (!form) {
      console.error("Form not found");
      return;
    }

    // Handle search filter specially
    if (filterType === "search") {
      const searchInput = form.querySelector(`input[name='search']`);
      if (searchInput) {
        searchInput.value = "";
      }
    } else {
      // Find and uncheck the corresponding checkbox/radio button
      const input = form.querySelector(
        `input[name*='[${filterType}]'][value='${filterValue}']`
      );
      if (input) {
        input.checked = false;
      }
    }

    // Submit the form automatically
    form.requestSubmit();

    // Update the UI
    this.updateActiveFilters();
  }

  updateActiveFilters() {
    console.log("Updating active filters");
    const activeFilters = [];
    const form = this.element.closest("form");
    if (!form) {
      console.error("Form not found for active filters");
      return;
    }

    // Get all checked inputs
    const checkedInputs = form.querySelectorAll(
      "input[type='checkbox']:checked, input[type='radio']:checked"
    );

    checkedInputs.forEach((input) => {
      if (input.value && input.value !== "") {
        let filterName = "";
        const nameMatch = input.name.match(/\[(.*?)\]/);
        if (nameMatch && nameMatch[1]) {
          filterName = nameMatch[1];
        } else {
          return; // Skip if we can't determine the filter name
        }

        const label = input.nextElementSibling;
        let filterLabel = label ? label.textContent.trim() : input.value;

        // Format specific filter types for better display
        if (filterName === "budget_range" && filterLabel.includes("$")) {
          // For budget range, display the actual price range
          filterLabel = filterLabel.replace("USD", "").trim();
        }

        activeFilters.push({
          type: filterName,
          value: input.value,
          label: filterLabel,
        });
      }
    });

    // Get search input if it has a value
    const searchInput = form.querySelector("input[name='search']");
    if (searchInput && searchInput.value.trim()) {
      activeFilters.push({
        type: "search",
        value: searchInput.value.trim(),
        label: `"${searchInput.value.trim()}"`,
      });
    }

    console.log("Active filters:", activeFilters);

    // Update the active filters display
    this.renderActiveFilters(activeFilters);
  }

  renderActiveFilters(filters) {
    console.log("Rendering active filters", filters);
    // Create the active filters display
    const activeFiltersContainer = this.activeFiltersTarget;

    // Clear existing content
    activeFiltersContainer.innerHTML = "";

    if (filters.length > 0) {
      // Create label
      const label = document.createElement("span");
      label.textContent = "Active filters:";
      label.className = "mr-2 text-stone-600";
      activeFiltersContainer.appendChild(label);

      // Create filter tags
      filters.forEach((filter) => {
        const tag = document.createElement("span");
        tag.className =
          "inline-flex items-center px-3 py-1.5 rounded-full text-sm bg-indigo-50 border border-indigo-100 text-indigo-700";
        tag.dataset.filterType = filter.type;
        tag.dataset.filterValue = filter.value;
        tag.innerHTML = `
          ${filter.label}
          <button type="button" class="ml-1.5 text-indigo-400 hover:text-indigo-700" data-action="filter-panel#removeFilter" data-filter-type="${filter.type}" data-filter-value="${filter.value}">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
            </svg>
          </button>
        `;
        activeFiltersContainer.appendChild(tag);
      });

      // Show the clear button if we have filters
      if (this.hasClearButtonTarget) {
        this.clearButtonTarget.classList.remove("hidden");
      }
    } else {
      // Hide the clear button if no filters
      if (this.hasClearButtonTarget) {
        this.clearButtonTarget.classList.add("hidden");
      }
    }
  }

  submitForm() {
    this.updateActiveFilters();

    // Close the filter panel after form submission
    if (this.hasPanelTarget) {
      this.panelTarget.classList.add("hidden");

      // Update the toggle button text
      if (this.hasToggleButtonTarget) {
        this.toggleButtonTarget.innerHTML = `<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
          <path fill-rule="evenodd" d="M3 3a1 1 0 011-1h12a1 1 0 011 1v3a1 1 0 01-.293.707L12 11.414V15a1 1 0 01-.293.707l-2 2A1 1 0 018 17v-5.586L3.293 6.707A1 1 0 013 6V3z" clip-rule="evenodd" />
        </svg> Filters`;
      }
    }

    window.scrollTo({
      top: 0,
      behavior: "smooth",
    });
  }
}
