import { Controller } from "@hotwired/stimulus";

export default class extends Controller {
  static targets = ["content"];
  static values = {
    talentUserId: Number,
    talentName: String,
  };

  connect() {
    console.log("Chat request modal controller connecting");

    // Bind event handlers to maintain proper context
    this.boundHandleSubmit = this.handleSubmit.bind(this);
    this.boundHandleSubmitEnd = this.handleSubmitEnd.bind(this);
    this.boundHandleCustomSuccess = this.handleCustomSuccess.bind(this);

    // Listen for custom success event (fallback method)
    document.addEventListener(
      "chat-request-success",
      this.boundHandleCustomSuccess
    );

    // Store reference to this controller instance for server-side access
    this.element.chatRequestModalController = this;
  }

  disconnect() {
    console.log("Chat request modal controller disconnecting");

    // Remove custom success event listener
    if (this.boundHandleCustomSuccess) {
      document.removeEventListener(
        "chat-request-success",
        this.boundHandleCustomSuccess
      );
    }

    // Clean up all listeners and observers
    this.cleanup();

    // Remove controller reference
    if (this.element.chatRequestModalController === this) {
      delete this.element.chatRequestModalController;
    }

    // Clear bound function references
    this.boundHandleSubmit = null;
    this.boundHandleSubmitEnd = null;
    this.boundHandleCustomSuccess = null;
  }

  // Open the modal and load the form
  openModal(event) {
    event.preventDefault();

    // Get talent user ID from the clicked element
    const talentUserId = event.target.closest("[data-talent-user-id]")?.dataset
      .talentUserId;

    if (!talentUserId) {
      console.error("No talent user ID found on clicked element");
      return;
    }

    // Store talent user ID for later use
    this.talentUserIdValue = talentUserId;

    // Show the modal first
    this.showModal();

    // Fetch the modal content
    this.loadModalContent(talentUserId);
  }

  // Show the modal using the modal controller
  showModal() {
    const modalController = this.getModalController();
    if (modalController) {
      const showEvent = new CustomEvent("show-modal");
      modalController.dispatchEvent(showEvent);
    }
  }

  // Close the modal using the modal controller
  closeModal() {
    console.log("closeModal() called");
    const modalController = this.getModalController();
    console.log("Modal controller found:", modalController);

    if (modalController) {
      const closeEvent = new CustomEvent("modal-close");
      console.log("Dispatching modal-close event");
      modalController.dispatchEvent(closeEvent);

      // Clean up after closing
      this.resetControllerState();
    } else {
      console.error("No modal controller found!");
    }
  }

  // Reset controller state for next use
  resetControllerState() {
    console.log("Resetting controller state");

    // Clean up event listeners and observers
    this.cleanup();

    // Reset any stored values
    this.talentUserIdValue = null;

    // Clear any error states
    this.clearError();
  }

  // Get reference to the modal controller element
  getModalController() {
    // Since both controllers are on the same element, return this element
    return this.element;
  }

  // Load modal content via fetch
  loadModalContent(talentUserId) {
    // Clean up any existing event listeners before loading new content
    this.cleanup();

    fetch(`/scout/chat_requests/new?talent_user_id=${talentUserId}`, {
      headers: {
        Accept: "text/vnd.turbo-stream.html",
      },
    })
      .then((response) => {
        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        return response.text();
      })
      .then((html) => {
        // Render the turbo stream response
        Turbo.renderStreamMessage(html);

        // Set up form event listeners after content is loaded
        this.updateTalentName();
        this.setupFormEventListeners();
      })
      .catch((error) => {
        console.error("Error loading chat request form:", error);
        this.showError("Failed to load chat request form. Please try again.");
      });
  }

  // Set up event listeners after form is loaded
  setupFormEventListeners() {
    // Find and attach form submission handler
    const form = this.element.querySelector('form[data-turbo="true"]');
    if (form && this.boundHandleSubmit) {
      form.addEventListener("submit", this.boundHandleSubmit);
      // Store reference for cleanup
      this.currentForm = form;
    }

    // Add turbo:submit-end listener for this specific form
    document.addEventListener("turbo:submit-end", this.boundHandleSubmitEnd);

    // Add turbo stream listener to detect success response
    this.boundHandleStreamRender = this.handleStreamRender.bind(this);
    document.addEventListener(
      "turbo:before-stream-render",
      this.boundHandleStreamRender
    );

    // Add mutation observer to watch for success marker (backup method)
    this.setupSuccessObserver();
  }

  // Set up mutation observer to watch for success marker (backup method)
  setupSuccessObserver() {
    // Create observer to watch for success marker being added
    this.successObserver = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        mutation.addedNodes.forEach((node) => {
          if (node.nodeType === Node.ELEMENT_NODE) {
            // Check if the added node contains our success marker
            const successElement =
              node.querySelector?.('[data-chat-request-success="true"]') ||
              (node.dataset?.chatRequestSuccess === "true" ? node : null);

            if (successElement) {
              // Success marker found, handle success (backup method)
              this.handleSuccess(successElement);
            }
          }
        });
      });
    });

    // Start observing the modal content area within this modal
    const modalContent = this.element.querySelector(
      '[data-modal-target="content"]'
    );
    if (modalContent) {
      this.successObserver.observe(modalContent, {
        childList: true,
        subtree: true,
      });
    }
  }

  // Handle turbo stream render event (backup method)
  handleStreamRender(event) {
    // Check if the stream contains success content
    const streamContent = event.detail?.newStream || event.detail?.render || "";

    // Ensure streamContent is a string before calling includes
    if (
      typeof streamContent === "string" &&
      streamContent.includes('data-chat-request-success="true"')
    ) {
      // Handle success after a short delay to let the DOM update (backup method)
      setTimeout(() => {
        const successElement = document.querySelector(
          '[data-chat-request-success="true"]'
        );
        if (successElement) {
          this.handleSuccess(successElement);
        }
      }, 10);
    }
  }

  // Handle form submission
  handleSubmit(event) {
    const submitter = event.submitter;

    // Validate "Send Request with Note" button
    if (submitter && submitter.dataset.action === "send-with-note") {
      if (!this.validatePitch()) {
        event.preventDefault();
        return false;
      }
    }

    // Clear any existing error messages
    this.clearError();

    // Set loading state on submit button
    this.setButtonLoadingState(submitter);

    // Allow form to submit normally via Turbo
    return true;
  }

  // Handle turbo submit end event (fallback for error handling)
  handleSubmitEnd(event) {
    // Only handle events from our form
    if (!this.isOurForm(event.target)) {
      return;
    }

    // Use a longer delay to check for success, as the primary success detection
    // is now handled by the mutation observer
    setTimeout(() => {
      if (!this.isSuccessfulSubmission()) {
        // Reset button states on error (success is handled by observer)
        this.resetButtonStates(event.target);
      }
    }, 200);
  }

  // Check if the event target is our form
  isOurForm(target) {
    return target && target.closest("[data-controller='chat-request-modal']");
  }

  // Check if submission was successful by looking for success marker
  isSuccessfulSubmission() {
    return document.querySelector('[data-chat-request-success="true"]');
  }

  // Handle success from server-side script (primary method)
  handleSuccessFromServer(talentName) {
    console.log("handleSuccessFromServer called with talent:", talentName);

    // Show success toast
    this.showSuccessToast(`Chat request sent to ${talentName}!`);

    // Close modal immediately (within 100ms as required)
    console.log("Closing modal in 50ms...");
    this.closeTimeout = setTimeout(() => {
      console.log("Attempting to close modal now");
      this.closeModal();
    }, 50);
  }

  // Handle custom success event (fallback method)
  handleCustomSuccess(event) {
    console.log("handleCustomSuccess called with event:", event);
    const talentName = event.detail?.talentName || "the talent";
    this.handleSuccessFromServer(talentName);
  }

  // Handle successful submission (legacy method for DOM-based detection)
  handleSuccess(successElement = null) {
    console.log("handleSuccess called with:", successElement);

    // Get success element if not provided
    if (!successElement) {
      successElement = document.querySelector(
        '[data-chat-request-success="true"]'
      );
    }

    if (!successElement) {
      console.error("No success element found!");
      return;
    }

    const talentName = successElement?.dataset.talentName || "the talent";
    console.log("Success detected for talent:", talentName);

    // Use the server method for consistency
    this.handleSuccessFromServer(talentName);
  }

  // Clean up event listeners and observers
  cleanup() {
    console.log("Cleaning up chat request modal controller");

    // Remove form event listener
    if (this.currentForm && this.boundHandleSubmit) {
      this.currentForm.removeEventListener("submit", this.boundHandleSubmit);
      this.currentForm = null;
    }

    // Remove document-level event listeners
    if (this.boundHandleSubmitEnd) {
      document.removeEventListener(
        "turbo:submit-end",
        this.boundHandleSubmitEnd
      );
      this.boundHandleSubmitEnd = null;
    }

    if (this.boundHandleStreamRender) {
      document.removeEventListener(
        "turbo:before-stream-render",
        this.boundHandleStreamRender
      );
      this.boundHandleStreamRender = null;
    }

    // Disconnect mutation observer
    if (this.successObserver) {
      this.successObserver.disconnect();
      this.successObserver = null;
    }

    // Clear any pending timeouts
    if (this.closeTimeout) {
      clearTimeout(this.closeTimeout);
      this.closeTimeout = null;
    }
  }

  // Set loading state on submit button
  setButtonLoadingState(button) {
    if (!button) return;

    button.disabled = true;
    const originalText = button.textContent;

    // Store original text for reset
    button.dataset.originalText = originalText;

    // Set loading text based on button type
    button.textContent =
      button.dataset.action === "send-with-note"
        ? "Sending with Note..."
        : "Sending Request...";

    // Reset after timeout (fallback for network issues)
    setTimeout(() => {
      this.resetButton(button);
    }, 10000);
  }

  // Reset button to original state
  resetButton(button) {
    if (!button) return;

    button.disabled = false;
    button.textContent =
      button.dataset.originalText ||
      (button.dataset.action === "send-with-note"
        ? "Send Request with Note"
        : "Send Request");
  }

  // Reset all button states in the form
  resetButtonStates(form) {
    const buttons = form.querySelectorAll(
      'input[type="submit"], button[type="submit"]'
    );
    buttons.forEach((button) => this.resetButton(button));
  }

  // Validate pitch field for "Send Request with Note" button
  validatePitch() {
    const pitchTextarea = this.element.querySelector(
      '[data-chat-request-modal-target="pitchTextarea"]'
    );
    if (!pitchTextarea) {
      return true; // No pitch field found, allow submission
    }

    const pitchValue = pitchTextarea.value.trim();

    if (pitchValue === "") {
      this.showError(
        "Please write a message before sending a request with note."
      );
      pitchTextarea.focus();
      return false;
    }

    return true;
  }

  // Show error message
  showError(message) {
    const errorElement = this.element.querySelector(
      '[data-chat-request-modal-target="errorMessage"]'
    );
    if (errorElement) {
      errorElement.textContent = message;
      errorElement.classList.remove("hidden");
    }
  }

  // Clear error message
  clearError() {
    const errorElement = this.element.querySelector(
      '[data-chat-request-modal-target="errorMessage"]'
    );
    if (errorElement) {
      errorElement.textContent = "";
      errorElement.classList.add("hidden");
    }
  }

  // Show success toast notification
  showSuccessToast(message) {
    // Create toast element
    const toast = document.createElement("div");
    toast.className =
      "fixed z-50 px-6 py-3 text-white transition-all duration-300 ease-in-out transform translate-x-full bg-green-500 rounded-md shadow-lg opacity-0 top-4 right-4";
    toast.textContent = message;

    // Add to DOM
    document.body.appendChild(toast);

    // Animate in
    setTimeout(() => {
      toast.classList.remove("translate-x-full", "opacity-0");
    }, 10);

    // Auto-remove after 4 seconds
    setTimeout(() => {
      toast.classList.add("translate-x-full", "opacity-0");
      setTimeout(() => {
        if (toast.parentNode) {
          toast.parentNode.removeChild(toast);
        }
      }, 300);
    }, 4000);
  }

  // Update talent name in the modal
  updateTalentName() {
    const talentNameElement = this.element.querySelector(
      '[data-chat-request-modal-target="talentName"]'
    );
    if (talentNameElement) {
      talentNameElement.textContent = this.talentNameValue;
    }
  }
}
