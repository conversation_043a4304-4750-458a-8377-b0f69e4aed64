import { Controller } from "@hotwired/stimulus";

export default class extends Controller {
  static targets = [
    "step",
    "progressBar",
    "progressLabels",
    "socialMediaGoalSection",
    "newsletterStep",
    "socialMediaStep",
    "previousButton",
    "nextButton",
    "submitButton",
  ];
  static values = {
    currentStep: { type: Number, default: 1 },
  };

  connect() {
    console.log("FormBranchingController connected");
    console.log("Element:", this.element);
    console.log("Has step targets:", this.hasStepTargets);
    console.log("Step targets count:", this.stepTargets?.length);
    console.log("Has next button target:", this.hasNextButtonTarget);

    this.selectedCategory = null;
    this.currentSocialMediaStep = 1;
    this.currentNewsletterStep = 1;
    this.showInitialStep();
  }

  showInitialStep() {
    // Show the initial questions step
    const initialStep = this.element.querySelector(
      '[data-step-name="initial_questions"]'
    );
    if (initialStep) {
      initialStep.classList.remove("hidden");
    }

    // Hide all sections initially
    const sections = this.element.querySelectorAll("[data-section-name]");
    sections.forEach((section) => {
      section.classList.add("hidden");
    });
  }

  handleCategoryChange(event) {
    this.selectedCategory = event.target.value;
    this.updateProgressLabels();
    this.updateDisplay();
  }

  showHideSections(event) {
    this.selectedCategory = event.target.value;
    this.currentSocialMediaStep = 1;
    this.currentNewsletterStep = 1;
    this.updateProgressLabels();
    this.updateDisplay();
    this.showHideSpecificSections();
  }

  showHideSpecificSections() {
    console.log(
      "showHideSpecificSections called with category:",
      this.selectedCategory
    );

    // Hide all sections first
    const sections = this.element.querySelectorAll("[data-section-name]");
    console.log("Found sections:", sections.length);
    sections.forEach((section) => {
      console.log("Hiding section:", section.dataset.sectionName);
      section.classList.add("hidden");
    });

    // Show the appropriate section based on selected category
    if (this.selectedCategory) {
      const targetSection = this.element.querySelector(
        `[data-section-name="${this.selectedCategory}"]`
      );
      console.log(
        "Target section for",
        this.selectedCategory,
        ":",
        targetSection
      );
      if (targetSection) {
        targetSection.classList.remove("hidden");
        console.log("Showing target section");
        this.showCategorySteps();
      } else {
        console.log("Target section not found!");
      }
    }
  }

  showCategorySteps() {
    if (this.selectedCategory === "social_media") {
      this.showSocialMediaSteps();
    } else if (this.selectedCategory === "newsletter") {
      this.showNewsletterSteps();
    }
  }

  showSocialMediaSteps() {
    // Hide all social media steps first
    if (this.hasSocialMediaStepTargets) {
      this.socialMediaStepTargets.forEach((step) => {
        step.classList.add("hidden");
      });

      // Show current social media step
      const stepId = this.getSocialMediaStepId(this.currentSocialMediaStep);
      const currentStep = this.socialMediaStepTargets.find(
        (step) => step.dataset.stepId === stepId
      );
      if (currentStep) {
        currentStep.classList.remove("hidden");
      }
    }
  }

  showNewsletterSteps() {
    console.log(
      "showNewsletterSteps called, current step:",
      this.currentNewsletterStep
    );
    console.log("Has newsletter step targets:", this.hasNewsletterStepTargets);

    // Hide all newsletter steps first
    if (this.hasNewsletterStepTargets) {
      console.log(
        "Newsletter step targets:",
        this.newsletterStepTargets.length
      );
      this.newsletterStepTargets.forEach((step) => {
        step.classList.add("hidden");
      });

      // Show current newsletter step
      const stepId = this.getNewsletterStepId(this.currentNewsletterStep);
      console.log("Looking for newsletter step ID:", stepId);
      const currentStep = this.newsletterStepTargets.find(
        (step) => step.dataset.stepId === stepId
      );
      console.log("Found current newsletter step:", currentStep);
      if (currentStep) {
        currentStep.classList.remove("hidden");
      }
    }
  }

  getSocialMediaStepId(stepNumber) {
    const steps = ["platform", "goal"];
    return steps[stepNumber - 1];
  }

  getNewsletterStepId(stepNumber) {
    const steps = ["goal", "frequency", "length", "budget"];
    return steps[stepNumber - 1];
  }

  nextStep(event) {
    event?.preventDefault();
    console.log("nextStep called");

    // If we're already in a category-specific flow, handle accordingly
    if (this.selectedCategory === "social_media") {
      console.log("In social media flow, calling nextSocialMediaStep");
      if (!this.validateCurrentStep()) {
        console.log("Validation failed");
        return;
      }
      this.nextSocialMediaStep();
      return;
    } else if (this.selectedCategory === "newsletter") {
      console.log("In newsletter flow, calling nextNewsletterStep");
      if (!this.validateCurrentStep()) {
        console.log("Validation failed");
        return;
      }
      this.nextNewsletterStep();
      return;
    }

    // Handle initial category selection
    const categorySelect = this.element.querySelector(
      'select[name="job[job_category]"]'
    );
    console.log("Category select:", categorySelect);
    console.log("Category value:", categorySelect?.value);

    if (!categorySelect || !categorySelect.value) {
      console.log("No category selected");
      alert("Please select a category first");
      return;
    }

    this.selectedCategory = categorySelect.value;
    console.log("Selected category:", this.selectedCategory);

    // Hide initial step
    const initialStep = this.element.querySelector(
      '[data-step-name="category"]'
    );
    if (initialStep) {
      initialStep.classList.add("hidden");
      console.log("Hidden initial step");
    }

    // Show appropriate section
    if (this.selectedCategory === "social_media") {
      this.showSocialMediaSection();
    } else if (this.selectedCategory === "newsletter") {
      this.showNewsletterSection();
    } else {
      console.log("Unknown category:", this.selectedCategory);
    }
  }

  showSocialMediaSection() {
    console.log("Showing social media section");
    const section = this.element.querySelector(
      '[data-section-name="social_media"]'
    );
    if (section) {
      section.classList.remove("hidden");
      console.log("Social media section shown");

      // Show first step
      const firstStep = section.querySelector('[data-step-id="platform"]');
      if (firstStep) {
        firstStep.classList.remove("hidden");
        console.log("First social media step shown");
      }
    }
  }

  showNewsletterSection() {
    console.log("Showing newsletter flow");

    // Hide the category step
    const categoryStep = this.element.querySelector(
      '[data-step-name="category"]'
    );
    if (categoryStep) {
      categoryStep.classList.add("hidden");
    }

    // Show the first newsletter step
    const firstNewsletterStep = this.element.querySelector(
      '[data-step-name="newsletter_goal"]'
    );
    if (firstNewsletterStep) {
      firstNewsletterStep.classList.remove("hidden");
      firstNewsletterStep.classList.add("block");
      console.log("Showing newsletter goal step");
    } else {
      console.log("Newsletter goal step not found");
    }

    // Initialize newsletter step tracking
    this.currentNewsletterStep = 1;
  }

  previousStep(event) {
    event?.preventDefault();

    // Handle category-specific step navigation
    if (this.selectedCategory === "social_media") {
      this.previousSocialMediaStep();
    } else if (this.selectedCategory === "newsletter") {
      this.previousNewsletterStep();
    } else {
      // Handle general step navigation
      const prevStepIndex = this.getPreviousStepIndex();
      if (prevStepIndex !== -1) {
        this.currentStepValue = prevStepIndex;
        this.updateDisplay();
      }
    }
  }

  goBackToInitialStep() {
    // Hide all sections
    const sections = this.element.querySelectorAll("[data-section-name]");
    sections.forEach((section) => {
      section.classList.add("hidden");
    });

    // Hide job details step
    const jobDetailsStep = this.element.querySelector(
      '[data-step-name="job_listing_details"]'
    );
    if (jobDetailsStep) {
      jobDetailsStep.classList.add("hidden");
    }

    // Show initial step
    const initialStep = this.element.querySelector(
      '[data-step-name="initial_questions"]'
    );
    if (initialStep) {
      initialStep.classList.remove("hidden");
    }

    // Reset category selection
    this.selectedCategory = null;
    this.currentSocialMediaStep = 1;
    this.currentNewsletterStep = 1;
  }

  nextSocialMediaStep() {
    const maxSteps = 2; // platform, goal
    if (this.currentSocialMediaStep < maxSteps) {
      this.currentSocialMediaStep++;
      this.showSocialMediaSteps();
    } else {
      // Move to common job details section
      this.moveToJobDetails();
    }
  }

  previousSocialMediaStep() {
    if (this.currentSocialMediaStep > 1) {
      this.currentSocialMediaStep--;
      this.showSocialMediaSteps();
    } else {
      // Go back to category selection
      this.goBackToInitialStep();
    }
  }

  nextNewsletterStep() {
    const maxSteps = 4; // goal, frequency, length, budget
    if (this.currentNewsletterStep < maxSteps) {
      this.currentNewsletterStep++;
      this.showNewsletterSteps();
    } else {
      // Move to common job details section
      this.moveToJobDetails();
    }
  }

  showNewsletterSteps() {
    console.log("Showing newsletter step:", this.currentNewsletterStep);

    // Hide all newsletter steps
    const newsletterSteps = this.element.querySelectorAll(
      '[data-category="newsletter"]'
    );
    newsletterSteps.forEach((step) => {
      step.classList.add("hidden");
      step.classList.remove("block");
    });

    // Show current step
    const stepNames = [
      "newsletter_goal",
      "newsletter_frequency",
      "newsletter_length",
    ];
    const currentStepName = stepNames[this.currentNewsletterStep - 1];

    const currentStep = this.element.querySelector(
      `[data-step-name="${currentStepName}"]`
    );
    if (currentStep) {
      currentStep.classList.remove("hidden");
      currentStep.classList.add("block");
      console.log(`Showing ${currentStepName} step`);
    } else {
      console.log(`${currentStepName} step not found`);
    }
  }

  previousNewsletterStep() {
    if (this.currentNewsletterStep > 1) {
      this.currentNewsletterStep--;
      this.showNewsletterSteps();
    } else {
      // Go back to category selection
      this.goBackToInitialStep();
    }
  }

  moveToJobDetails() {
    // Hide category-specific sections and show job details
    const sections = this.element.querySelectorAll("[data-section-name]");
    sections.forEach((section) => {
      section.classList.add("hidden");
    });

    // Hide the category step
    const categoryStep = this.element.querySelector(
      '[data-step-name="category"]'
    );
    if (categoryStep) {
      categoryStep.classList.add("hidden");
    }

    // Show job details step
    const jobDetailsStep = this.element.querySelector(
      '[data-step-name="job_details"]'
    );
    if (jobDetailsStep) {
      jobDetailsStep.classList.remove("hidden");
      jobDetailsStep.classList.add("block");
      console.log("Showing job details step");
    } else {
      console.log("Job details step not found");
    }
  }

  getStepSequence() {
    const baseSequence = [{ name: "category", required: true }];

    // Add conditional steps based on category
    if (this.selectedCategory === "social_media") {
      baseSequence.push(
        {
          name: "social_media_platform",
          required: true,
          category: "social_media",
        },
        { name: "social_media_goal", required: true, category: "social_media" }
      );
    } else if (this.selectedCategory === "newsletter") {
      baseSequence.push(
        { name: "newsletter_goal", required: true, category: "newsletter" },
        {
          name: "newsletter_frequency",
          required: true,
          category: "newsletter",
        },
        { name: "newsletter_length", required: true, category: "newsletter" }
      );
    }

    // Add remaining steps
    baseSequence.push(
      { name: "job_details", required: true },
      { name: "topics", required: false },
      { name: "budget", required: true },
      { name: "work_duration", required: true },
      { name: "client_info", required: false },
      { name: "final_details", required: false }
    );

    return baseSequence;
  }

  getCurrentStepName() {
    const sequence = this.getStepSequence();
    return sequence[this.currentStepValue - 1]?.name;
  }

  getNextStepIndex() {
    const sequence = this.getStepSequence();
    return this.currentStepValue < sequence.length
      ? this.currentStepValue + 1
      : -1;
  }

  getPreviousStepIndex() {
    return this.currentStepValue > 1 ? this.currentStepValue - 1 : -1;
  }

  updateDisplay() {
    this.showCurrentStep();
    this.updateProgressBar();
    this.updateNavigationButtons();
  }

  showCurrentStep() {
    const currentStepName = this.getCurrentStepName();

    this.stepTargets.forEach((step) => {
      const stepName = step.dataset.stepName;
      const stepCategory = step.dataset.category;

      const shouldShow =
        stepName === currentStepName &&
        (!stepCategory || stepCategory === this.selectedCategory);

      step.classList.toggle("hidden", !shouldShow);
      step.classList.toggle("block", shouldShow);
    });
  }

  updateProgressBar() {
    if (this.hasProgressBarTarget) {
      const sequence = this.getStepSequence();
      const progress = (this.currentStepValue / sequence.length) * 100;
      this.progressBarTarget.style.width = `${progress}%`;
    }
  }

  updateProgressLabels() {
    if (!this.hasProgressLabelsTarget) return;

    let labels = [];

    if (this.selectedCategory === "social_media") {
      labels = [
        "Category",
        "Platform",
        "Goal",
        "Job Details",
        "Topics",
        "Budget",
        "Duration",
        "Client Info",
        "Final Details",
      ];
    } else if (this.selectedCategory === "newsletter") {
      labels = [
        "Category",
        "Goal",
        "Frequency",
        "Length",
        "Budget",
        "Job Details",
        "Topics",
        "Duration",
        "Client Info",
        "Final Details",
      ];
    } else {
      labels = [
        "Category",
        "Job Details",
        "Topics",
        "Budget",
        "Duration",
        "Client Info",
        "Final Details",
      ];
    }

    this.progressLabelsTarget.innerHTML = labels
      .map((label) => `<span class="text-xs text-gray-600">${label}</span>`)
      .join("");
  }

  updateNavigationButtons() {
    if (
      !this.hasPreviousButtonTarget ||
      !this.hasNextButtonTarget ||
      !this.hasSubmitButtonTarget
    ) {
      return;
    }

    const isFirstStep = this.currentStepValue === 1;
    const sequence = this.getStepSequence();
    const isLastStep = this.currentStepValue === sequence.length;

    // Previous button
    this.previousButtonTarget.classList.toggle("hidden", isFirstStep);
    this.previousButtonTarget.disabled = isFirstStep;

    // Next/Submit button
    if (isLastStep) {
      this.nextButtonTarget.classList.add("hidden");
      this.submitButtonTarget.classList.remove("hidden");
    } else {
      this.nextButtonTarget.classList.remove("hidden");
      this.submitButtonTarget.classList.add("hidden");
    }
  }

  validateCurrentStep() {
    // Handle category-specific validation
    if (this.selectedCategory === "social_media") {
      return this.validateSocialMediaStep();
    } else if (this.selectedCategory === "newsletter") {
      return this.validateNewsletterStep();
    } else {
      // Handle general step validation
      const currentStepName = this.getCurrentStepName();
      const currentStepElement = this.stepTargets.find(
        (step) =>
          step.dataset.stepName === currentStepName &&
          (!step.dataset.category ||
            step.dataset.category === this.selectedCategory)
      );

      if (!currentStepElement) return true;
      return this.validateStepInputs(currentStepElement);
    }
  }

  validateSocialMediaStep() {
    const currentStepId = this.getSocialMediaStepId(
      this.currentSocialMediaStep
    );
    const currentStep = this.element.querySelector(
      `[data-step-id="${currentStepId}"]`
    );

    if (!currentStep) return true;
    return this.validateStepInputs(currentStep);
  }

  validateNewsletterStep() {
    const stepNames = [
      "newsletter_goal",
      "newsletter_frequency",
      "newsletter_length",
    ];
    const currentStepName = stepNames[this.currentNewsletterStep - 1];
    const currentStep = this.element.querySelector(
      `[data-step-name="${currentStepName}"]`
    );

    if (!currentStep) return true;
    return this.validateStepInputs(currentStep);
  }

  getNewsletterStepId(stepNumber) {
    const stepIds = ["goal", "frequency", "length", "budget"];
    return stepIds[stepNumber - 1];
  }

  validateStepInputs(stepElement) {
    const requiredInputs = stepElement.querySelectorAll(
      "input[required], select[required], textarea[required]"
    );
    let isValid = true;

    requiredInputs.forEach((input) => {
      const isVisible =
        input.offsetParent !== null && !input.closest(".hidden");

      if (!isVisible) {
        return;
      }

      // Special handling for goal-dependent required fields
      if (input.dataset.goalDependentRequired) {
        const goalField = this.element.querySelector(
          'select[name="job[outcome]"]'
        );
        if (goalField) {
          const selectedGoal = goalField.value;
          if (selectedGoal !== "leads" && selectedGoal !== "booked_calls") {
            return; // Skip validation if goal doesn't require this field
          }
        }
      }

      let isEmpty = false;

      if (input.type === "radio") {
        const radioGroup = stepElement.querySelectorAll(
          `input[name="${input.name}"]`
        );
        isEmpty = !Array.from(radioGroup).some((radio) => radio.checked);
      } else if (input.type === "checkbox") {
        isEmpty = !input.checked;
      } else {
        isEmpty = !input.value.trim();
      }

      if (isEmpty) {
        isValid = false;
        input.classList.add("border-red-500");
        const errorElement =
          input.closest(".mb-4")?.querySelector(".error-message") ||
          input.parentElement.querySelector(".error-message") ||
          stepElement.querySelector(".error-message");
        if (errorElement) errorElement.classList.remove("hidden");
      } else {
        input.classList.remove("border-red-500");
        const errorElement =
          input.closest(".mb-4")?.querySelector(".error-message") ||
          input.parentElement.querySelector(".error-message");
        if (errorElement) errorElement.classList.add("hidden");
      }
    });

    const stepErrorMessage = stepElement.querySelector(".step-error-message");
    if (stepErrorMessage) {
      stepErrorMessage.classList.toggle("hidden", isValid);
    }

    return isValid;
  }

  showHideSocialMediaGoals(event) {
    if (!this.hasSocialMediaGoalSectionTarget) return;

    const selectedGoal = event.target.value;
    const goalSection = this.socialMediaGoalSectionTarget;

    if (selectedGoal === "leads" || selectedGoal === "booked_calls") {
      goalSection.classList.remove("hidden");
      goalSection.classList.add("block");

      // Ensure the checkbox is required for these goals
      const riskCheckbox = goalSection.querySelector(
        "input[data-goal-dependent-required]"
      );
      if (riskCheckbox) {
        riskCheckbox.required = true;
      }
    } else {
      goalSection.classList.remove("block");
      goalSection.classList.add("hidden");

      // Remove requirement when not needed
      const riskCheckbox = goalSection.querySelector(
        "input[data-goal-dependent-required]"
      );
      if (riskCheckbox) {
        riskCheckbox.required = false;
        riskCheckbox.classList.remove("border-red-500");
        const errorElement =
          riskCheckbox.parentElement.querySelector(".error-message");
        if (errorElement) errorElement.classList.add("hidden");
      }
    }
  }

  submitForm() {
    const form = this.element.closest("form");
    if (form) {
      form.requestSubmit();
    }
  }
}
