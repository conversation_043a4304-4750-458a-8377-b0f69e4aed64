import { Controller } from "@hotwired/stimulus";

// Badge View Tracker Controller
// Automatically tracks when a profile with badges is viewed
// Should be attached to profile pages or sections that display badges
export default class extends Controller {
  static values = {
    viewedUserId: Number,
    badgeTypesDisplayed: Array,
    trackingEnabled: { type: Boolean, default: true },
    trackingDelay: { type: Number, default: 2000 }, // 2 second delay before tracking
  };

  connect() {
    console.log("Badge view tracker connected");
    
    // Only track if tracking is enabled and we have the required data
    if (this.trackingEnabledValue && this.shouldTrackView()) {
      this.scheduleViewTracking();
    }
  }

  disconnect() {
    // Clear any pending tracking when controller disconnects
    if (this.trackingTimeout) {
      clearTimeout(this.trackingTimeout);
    }
  }

  // Check if we should track this view
  shouldTrackView() {
    // Must have viewed user ID and badges displayed
    if (!this.viewedUserIdValue || !this.badgeTypesDisplayedValue?.length) {
      console.log("Badge view tracking skipped: missing required data");
      return false;
    }

    // Don't track if user is viewing their own profile (optional)
    const currentUserId = this.getCurrentUserId();
    if (currentUserId && currentUserId === this.viewedUserIdValue) {
      console.log("Badge view tracking skipped: user viewing own profile");
      return false;
    }

    // Check if we've already tracked this view recently (prevent duplicate tracking)
    const trackingKey = `badge_view_tracked_${this.viewedUserIdValue}`;
    const lastTracked = sessionStorage.getItem(trackingKey);
    const now = Date.now();
    
    if (lastTracked && (now - parseInt(lastTracked)) < 60000) { // 1 minute cooldown
      console.log("Badge view tracking skipped: recently tracked");
      return false;
    }

    return true;
  }

  // Schedule view tracking with a delay to ensure user actually viewed the content
  scheduleViewTracking() {
    this.trackingTimeout = setTimeout(() => {
      this.trackBadgeView();
    }, this.trackingDelayValue);
  }

  // Track the badge view
  trackBadgeView() {
    const analyticsData = {
      viewed_user_id: this.viewedUserIdValue,
      badge_types_displayed: this.badgeTypesDisplayedValue,
      source: "badge_view_tracker",
      page_title: document.title,
    };

    console.log("Tracking badge view:", analyticsData);

    // Send analytics data asynchronously
    fetch("/analytics/badge_view", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "X-Requested-With": "XMLHttpRequest",
      },
      body: JSON.stringify(analyticsData),
    })
      .then((response) => {
        if (response.ok) {
          // Mark this view as tracked to prevent duplicates
          const trackingKey = `badge_view_tracked_${this.viewedUserIdValue}`;
          sessionStorage.setItem(trackingKey, Date.now().toString());
          console.log("Badge view tracked successfully");
        } else {
          console.warn("Badge view tracking failed:", response.statusText);
        }
      })
      .catch((error) => {
        console.warn("Badge view tracking error:", error);
      });
  }

  // Get current user ID from meta tag or data attribute
  getCurrentUserId() {
    // Try to get from meta tag first
    const metaTag = document.querySelector('meta[name="current-user-id"]');
    if (metaTag) {
      return parseInt(metaTag.content);
    }

    // Try to get from body data attribute
    const bodyUserId = document.body.dataset.currentUserId;
    if (bodyUserId) {
      return parseInt(bodyUserId);
    }

    // Try to get from a global variable (if set)
    if (window.currentUserId) {
      return parseInt(window.currentUserId);
    }

    return null;
  }

  // Manual trigger for tracking (can be called from other controllers)
  triggerTracking() {
    if (this.shouldTrackView()) {
      this.trackBadgeView();
    }
  }

  // Update tracking data (useful for dynamic content)
  updateTrackingData(newData) {
    if (newData.viewedUserId) {
      this.viewedUserIdValue = newData.viewedUserId;
    }
    if (newData.badgeTypesDisplayed) {
      this.badgeTypesDisplayedValue = newData.badgeTypesDisplayed;
    }
  }

  // Enable/disable tracking
  setTrackingEnabled(enabled) {
    this.trackingEnabledValue = enabled;
  }
}
