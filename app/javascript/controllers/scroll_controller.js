import { Controller } from "@hotwired/stimulus";

export default class extends Controller {
  static targets = ["container"];

  connect() {
    this.scrollToBottom();
    this.observer = new MutationObserver(this.handleMutation.bind(this));

    this.observer.observe(this.containerTarget, {
      childList: true,
      subtree: true,
    });
  }

  disconnect() {
    if (this.observer) {
      this.observer.disconnect();
    }
  }

  handleMutation() {
    this.scrollToBottom();
  }

  scrollToBottom() {
    requestAnimationFrame(() => {
      this.containerTarget.scrollTo({
        top: this.containerTarget.scrollHeight,
        behavior: "smooth",
      });
    });
  }
}
