import { Controller } from "@hotwired/stimulus";

// Connects to data-controller="multi-step-form"
export default class extends Controller {
  static targets = [
    "step", // All step divs
    "progressStep", // Elements in the progress indicator
    "categoryOption", // Copied from job_form_controller
    "categoryRadio", // Copied from job_form_controller
    "categoryIcon", // Copied from job_form_controller
    "otherTopicField", // Copied from job_form_controller
    "platformQuestion", // Platform question div in step 2
    "nextButton",
    "previousButton",
    "submitButton", // The final submit button (e.g. "Post Job")
  ];

  static values = {
    currentStep: { type: Number, default: 1 },
  };

  connect() {
    this.showCurrentStep();
    this.updateProgressIndicator();
    this.updateButtonVisibility();
    // Initialize category selection from job_form_controller logic
    if (this.hasCategoryRadioTarget) {
      this.updateSelectedCategoryVisuals();
    }
    // Initialize platform question visibility
    this.updatePlatformQuestionVisibility();
  }

  showCurrentStep() {
    this.stepTargets.forEach((element, index) => {
      element.classList.toggle("hidden", index + 1 !== this.currentStepValue);
    });
    this.updateButtonVisibility();
    this.updateProgressIndicator();
  }

  nextStep() {
    if (this.currentStepValue < this.stepTargets.length) {
      this.currentStepValue++;
      this.showCurrentStep();
    }
  }

  previousStep() {
    if (this.currentStepValue > 1) {
      this.currentStepValue--;
      this.showCurrentStep();
    }
  }

  goToStep(event) {
    const targetStep = parseInt(event.currentTarget.dataset.step);
    if (targetStep && targetStep <= this.currentStepValue && targetStep > 0) {
      // Allow going to previous/current steps
      this.currentStepValue = targetStep;
      this.showCurrentStep();
    } else if (targetStep && targetStep > this.currentStepValue) {
      // Potentially allow jumping to a future step if all intermediate steps are valid
      // For now, only allow if it's the next logical step or a previous one.
      // This could be enhanced with validation.
      // For simplicity, let's assume for now we only allow sequential next or jump to previous.
      // If you want to jump to any validated future step, this logic needs to be more complex.
    }
  }

  updateButtonVisibility() {
    if (this.hasPreviousButtonTarget) {
      this.previousButtonTarget.classList.toggle(
        "hidden",
        this.currentStepValue === 1
      );
    }
    if (this.hasNextButtonTarget) {
      this.nextButtonTarget.classList.toggle(
        "hidden",
        this.currentStepValue === this.stepTargets.length
      );
    }
    if (this.hasSubmitButtonTarget) {
      this.submitButtonTarget.classList.toggle(
        "hidden",
        this.currentStepValue !== this.stepTargets.length
      );
    }
  }

  updateProgressIndicator() {
    this.progressStepTargets.forEach((element, index) => {
      const stepNumber = index + 1;
      // Remove all state classes first
      element.classList.remove(
        "state-active",
        "state-completed",
        "state-default"
      );

      if (stepNumber === this.currentStepValue) {
        element.classList.add("state-active");
      } else if (stepNumber < this.currentStepValue) {
        element.classList.add("state-completed");
      } else {
        element.classList.add("state-default");
      }
    });
  }

  // --- Logic merged from job_form_controller.js ---
  selectCategory(event) {
    const selectedLabel = event.currentTarget;

    this.categoryOptionTargets.forEach((option) => {
      option.classList.remove("border-purple-600", "ring-2", "ring-purple-600");
      option.classList.add("border-stone-300");
    });

    selectedLabel.classList.remove("border-stone-300");
    selectedLabel.classList.add(
      "border-purple-600",
      "ring-2",
      "ring-purple-600"
    );

    const radio = selectedLabel.querySelector('input[type="radio"]');
    if (radio) radio.checked = true;

    this.categoryIconTargets.forEach((icon) => icon.classList.add("hidden"));
    const iconSGV = selectedLabel.querySelector("svg");
    if (iconSGV) iconSGV.classList.remove("hidden");

    // Update platform question visibility based on selected category
    this.updatePlatformQuestionVisibility();
  }

  updateSelectedCategoryVisuals() {
    const checkedRadio = this.categoryRadioTargets.find(
      (radio) => radio.checked
    );
    if (checkedRadio) {
      const selectedLabel = checkedRadio.closest(
        "label[data-multi-step-form-target='categoryOption']"
      );
      if (selectedLabel) {
        this.categoryOptionTargets.forEach((option) => {
          option.classList.remove(
            "border-purple-600",
            "ring-2",
            "ring-purple-600"
          );
          option.classList.add("border-stone-300");
        });
        selectedLabel.classList.remove("border-stone-300");
        selectedLabel.classList.add(
          "border-purple-600",
          "ring-2",
          "ring-purple-600"
        );

        this.categoryIconTargets.forEach((icon) =>
          icon.classList.add("hidden")
        );
        const iconSGV = selectedLabel.querySelector("svg");
        if (iconSGV) iconSGV.classList.remove("hidden");
      }
    }
  }

  updatePlatformQuestionVisibility() {
    if (!this.hasPlatformQuestionTarget) return;

    const checkedRadio = this.categoryRadioTargets.find(
      (radio) => radio.checked
    );
    
    if (checkedRadio) {
      const selectedCategory = checkedRadio.value;
      
      // Hide platform question if emails category is selected
      if (selectedCategory === "emails") {
        this.platformQuestionTarget.style.display = "none";
      } else {
        this.platformQuestionTarget.style.display = "block";
      }
    }
  }

  toggleOtherTopic(event) {
    const checkbox = event.currentTarget;
    if (this.hasOtherTopicFieldTarget) {
      if (checkbox.checked) {
        this.otherTopicFieldTarget.style.display = "block";
        this.otherTopicFieldTarget.focus();
      } else {
        this.otherTopicFieldTarget.style.display = "none";
        this.otherTopicFieldTarget.value = "";
      }
    }
  }
  // --- End of merged logic ---

  // Placeholder for form submission if needed via JS, though Rails form will handle it by default
  // submitForm(event) {
  //   // event.preventDefault(); // if we want to hijack submission
  //   // this.element.submit();
  // }
}