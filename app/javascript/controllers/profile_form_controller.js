import { Controller } from "@hotwired/stimulus";

// Connects to data-controller="profile-form"
export default class extends Controller {
  static targets = [
    "agencyOption",
    "independentOption",
    "agencyRadio",
    "independentRadio",
    "agencyIcon",
    "independentIcon",
  ];

  connect() {
    // Initialize the form - check if an option is already selected
    this.updateSelectedOption();
  }

  selectAgencyOption(event) {
    // Select agency option
    this.selectOption(true);
  }

  selectIndependentOption(event) {
    // Select independent option
    this.selectOption(false);
  }

  selectOption(isAgency) {
    // Update radio buttons
    this.agencyRadioTarget.checked = isAgency;
    this.independentRadioTarget.checked = !isAgency;

    // Update agency option styling
    if (isAgency) {
      this.agencyOptionTarget.classList.remove("border-gray-300");
      this.agencyOptionTarget.classList.add(
        "border-indigo-600",
        "ring-2",
        "ring-indigo-600"
      );

      this.independentOptionTarget.classList.remove(
        "border-indigo-600",
        "ring-2",
        "ring-indigo-600"
      );
      this.independentOptionTarget.classList.add("border-gray-300");
    } else {
      this.independentOptionTarget.classList.remove("border-gray-300");
      this.independentOptionTarget.classList.add(
        "border-indigo-600",
        "ring-2",
        "ring-indigo-600"
      );

      this.agencyOptionTarget.classList.remove(
        "border-indigo-600",
        "ring-2",
        "ring-indigo-600"
      );
      this.agencyOptionTarget.classList.add("border-gray-300");
    }

    // Update icons
    this.agencyIconTarget.classList.toggle("hidden", !isAgency);
    this.independentIconTarget.classList.toggle("hidden", isAgency);
  }

  updateSelectedOption() {
    // Check which radio is selected and update UI accordingly
    const isAgency = this.agencyRadioTarget.checked;
    this.selectOption(isAgency);
  }
}
