import { Controller } from "@hotwired/stimulus";

export default class extends Controller {
  static targets = ["modal", "userName", "userEmail", "form"];
  static values = { userId: Number, userName: String, userEmail: String };

  connect() {
    console.log("Impersonation controller connected");
    console.log("Targets found:", {
      modal: this.hasModalTarget,
      userName: this.hasUserNameTarget,
      userEmail: this.hasUserEmailTarget,
      form: this.hasFormTarget,
    });

    // Bind the escape key to close modal
    this.boundHandleKeydown = this.handleKeydown.bind(this);
    document.addEventListener("keydown", this.boundHandleKeydown);
  }

  disconnect() {
    document.removeEventListener("keydown", this.boundHandleKeydown);
  }

  showConfirmation(event) {
    console.log("showConfirmation called");
    event.preventDefault();

    // Get user data from the clicked button
    const button = event.currentTarget;
    const userId = button.dataset.userId;
    const userName = button.dataset.userName;
    const userEmail = button.dataset.userEmail;

    console.log("Button data:", { userId, userName, userEmail });

    // Update modal content
    this.userNameTarget.textContent = userName;
    this.userEmailTarget.textContent = userEmail;

    // Ensure the form action is set correctly
    this.formTarget.action = `/super_admin/masquerades`;
    this.formTarget.method = "post"; // Ensure method is POST

    // Add hidden input for user_id
    let userIdInput = this.formTarget.querySelector('input[name="user_id"]');
    if (!userIdInput) {
      userIdInput = document.createElement("input");
      userIdInput.type = "hidden";
      userIdInput.name = "user_id";
      this.formTarget.appendChild(userIdInput);
    }
    userIdInput.value = userId;

    // Add CSRF token
    let csrfInput = this.formTarget.querySelector(
      'input[name="authenticity_token"]'
    );
    if (!csrfInput) {
      csrfInput = document.createElement("input");
      csrfInput.type = "hidden";
      csrfInput.name = "authenticity_token";
      this.formTarget.appendChild(csrfInput);
    }
    const csrfToken = document
      .querySelector('meta[name="csrf-token"]')
      ?.getAttribute("content");
    if (csrfToken) {
      csrfInput.value = csrfToken;
    }

    // Show modal
    this.modalTarget.classList.remove("hidden");
    document.body.classList.add("overflow-hidden");

    // Focus on the confirm button
    const confirmButton = this.modalTarget.querySelector(
      '[data-action*="confirm"]'
    );
    if (confirmButton) {
      setTimeout(() => confirmButton.focus(), 100);
    }
  }

  confirm(event) {
    event.preventDefault();

    // Disable the button to prevent double submission
    const button = event.currentTarget;
    button.disabled = true;
    button.textContent = "Starting Impersonation...";

    // Submit the form
    this.formTarget.submit();
  }

  cancel(event) {
    event.preventDefault();
    this.close();
  }

  close() {
    this.modalTarget.classList.add("hidden");
    document.body.classList.remove("overflow-hidden");

    // Reset form and button states
    const confirmButton = this.modalTarget.querySelector(
      '[data-action*="confirm"]'
    );
    if (confirmButton) {
      confirmButton.disabled = false;
      confirmButton.textContent = "Start Impersonation";
    }
  }

  handleKeydown(event) {
    if (
      event.key === "Escape" &&
      !this.modalTarget.classList.contains("hidden")
    ) {
      this.close();
    }
  }

  // Close modal when clicking on overlay
  closeOnOverlay(event) {
    if (event.target === event.currentTarget) {
      this.close();
    }
  }
}
