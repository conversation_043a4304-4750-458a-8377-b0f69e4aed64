import { Controller } from "@hotwired/stimulus";

export default class extends Controller {
  static targets = ["input"];

  connect() {
    console.log("Form reset controller connected");
  }

  reset(event) {
    if (event.detail.success) {
      // Clear input
      this.inputTarget.value = "";

      // Find chat controller and trigger scroll
      const chatController =
        this.application.getControllerForElementAndIdentifier(
          document.querySelector('[data-controller="chat"]'),
          "chat",
        );

      if (chatController) {
        chatController.scrollToBottom();
      }

      // Keep focus on input
      this.inputTarget.focus();
    }
  }
}
