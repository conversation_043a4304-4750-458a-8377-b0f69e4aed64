import { Controller } from "@hotwired/stimulus";

export default class extends Controller {
  static targets = ["messages"];

  connect() {
    // Only scroll if we're actually showing a conversation
    if (this.messagesTarget.children.length > 0) {
      this.scrollToBottom();
    }
  }

  // Called after initial load and when sending a message
  scrollToBottom() {
    const messages = this.messagesTarget;
    const shouldScroll =
      messages.scrollTop + messages.clientHeight === messages.scrollHeight;

    requestAnimationFrame(() => {
      messages.scrollTo({
        top: messages.scrollHeight,
        behavior: "smooth",
      });
    });
  }

  // Called when new messages arrive via Turbo Streams
  messageReceived(event) {
    // Check if the new message is from the current user
    const messageElement = event.target.lastElementChild
    
    if (messageElement) {
      // Only scroll if it's not a duplicate message from the current user
      const messageUserId = messageElement.dataset.messageUserId
      
      // If this is a message from another user, or we can't determine, scroll
      if (messageUserId !== this.currentUserId) {
        this.scrollToBottom()
      } else {
        // For messages from the current user, check if it's a duplicate
        // If it's a duplicate, we might want to remove one of them
        const messages = this.messagesTarget.querySelectorAll(`[data-message-user-id="${messageUserId}"]`)
        const lastTwoMessages = Array.from(messages).slice(-2)
        
        if (lastTwoMessages.length === 2) {
          const secondLast = lastTwoMessages[0]
          const last = lastTwoMessages[1]
          
          // If the last two messages have the same content and timestamp, remove one
          if (secondLast.querySelector('.message-bubble').textContent === 
              last.querySelector('.message-bubble').textContent) {
            last.remove()
          }
        }
      }
    }
    
    this.scrollToBottom()
  }
}
