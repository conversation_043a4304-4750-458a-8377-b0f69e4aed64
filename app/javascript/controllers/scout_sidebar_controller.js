import { Controller } from "@hotwired/stimulus";

export default class extends Controller {
  static targets = ["aside", "toggleButton", "main"];
  transitionProperties = ["transition-all", "duration-300", "ease-in-out"];

  getStorageKey() {
    return `sidebarVisible_${window.location.pathname}`;
  }

  connect() {
    const aside = this.asideTarget;
    const main = this.mainTarget;
    const storedState = localStorage.getItem(this.getStorageKey());

    // Set initial state based on localStorage, without transitions yet.
    // HTML defaults to: aside hidden, main full-width.
    if (storedState === "true") {
      // Change to: aside visible, main 8/12.
      aside.classList.remove("hidden");
      main.classList.remove("w-full");
      main.classList.add("w-8/12");
    }
    // If storedState is "false" or null, HTML is already in the correct "closed" state.

    // Defer adding transition classes until after the browser has painted the initial state
    // and the current execution stack is clear. This ensures that the very first
    // appearance/layout is not animated.
    setTimeout(() => {
      aside.classList.add(...this.transitionProperties);
      // Make the main controller element (wrapper) visible
      this.element.classList.remove("opacity-0");
    }, 0);
  }

  toggle() {
    const aside = this.asideTarget;
    const main = this.mainTarget;

    // Check if the sidebar is currently hidden (i.e., about to be shown)
    const isCurrentlyHidden = aside.classList.contains("hidden");

    if (isCurrentlyHidden) {
      // Show the sidebar
      aside.classList.remove("hidden");
      main.classList.remove("w-full");
      main.classList.add("w-8/12");
      localStorage.setItem(this.getStorageKey(), "true");
    } else {
      // Hide the sidebar
      aside.classList.add("hidden");
      main.classList.remove("w-8/12");
      main.classList.add("w-full");
      localStorage.setItem(this.getStorageKey(), "false");
    }
  }
}
