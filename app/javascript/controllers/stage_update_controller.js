import { Controller } from "@hotwired/stimulus";

export default class extends Controller {
  static values = {
    id: Number,
    status: String,
    url: String,
  };

  connect() {
    // Store available statuses for use in the modal
    this.availableStatuses = [
      "applied",
      "reviewed",
      "interviewing",
      "offered",
      "hired",
      "rejected",
    ];

    // Bind event handlers to maintain proper context
    this.boundHandleSuccess = this.handleSuccessFromServer.bind(this);
    this.boundHandleStreamRender = this.handleStreamRender.bind(this);
    this.boundHandleCustomSuccess = this.handleCustomSuccess.bind(this);

    // Listen for custom success event (global fallback)
    document.addEventListener(
      "stage-update-success",
      this.boundHandleCustomSuccess
    );

    // Store reference to this controller instance for server-side access
    // Use a slight delay to ensure this happens after DOM is fully ready
    setTimeout(() => {
      this.element.stageUpdateController = this;
    }, 10);

    console.log("Stage update controller connected for element:", this.element);
  }

  disconnect() {
    console.log(
      "Stage update controller disconnecting for element:",
      this.element
    );

    // Clean up event listeners
    if (this.boundHandleCustomSuccess) {
      document.removeEventListener(
        "stage-update-success",
        this.boundHandleCustomSuccess
      );
    }
    if (this.boundHandleStreamRender) {
      document.removeEventListener(
        "turbo:before-stream-render",
        this.boundHandleStreamRender
      );
    }

    // Clear any pending timeouts
    if (this.closeTimeout) {
      clearTimeout(this.closeTimeout);
    }

    // Clear controller reference
    if (this.element.stageUpdateController === this) {
      this.element.stageUpdateController = null;
    }
  }

  openModal(event) {
    event.preventDefault();
    event.stopPropagation();

    // Get the application ID and current status
    const applicationId = this.idValue;
    const currentStatus = this.statusValue;

    // Fetch the modal content
    fetch(
      `/scout/applicants/${applicationId}/stage_change_form?current_status=${currentStatus}`,
      {
        headers: {
          Accept: "text/html, application/xhtml+xml",
        },
      }
    )
      .then((response) => {
        if (!response.ok) {
          throw new Error(`HTTP error! Status: ${response.status}`);
        }
        return response.text();
      })
      .then((html) => {
        // Update modal content - find or create the modal content element
        let modalContent = document.getElementById("message-modal-content");

        if (!modalContent) {
          console.log(
            "Modal content element not found, attempting to recreate..."
          );

          // Find the modal content container
          const modalContentContainer = document.querySelector(
            '[data-modal-target="content"]'
          );
          if (modalContentContainer) {
            // Recreate the turbo frame
            modalContent = document.createElement("turbo-frame");
            modalContent.id = "message-modal-content";
            modalContentContainer.appendChild(modalContent);
            console.log("Modal content element recreated");
          } else {
            console.error("Modal content container not found");
            return;
          }
        }

        if (modalContent) {
          modalContent.innerHTML = html;

          // Show modal by dispatching an event to modal controller
          // Use a more reliable method to find the modal element
          const modalElement = document.querySelector(
            '[data-controller*="modal"]'
          );
          console.log("Modal element found:", modalElement);
          console.log("Modal element classes:", modalElement?.className);
          console.log(
            "Modal element data-controller:",
            modalElement?.getAttribute("data-controller")
          );

          if (modalElement) {
            // Ensure modal element has the proper hidden class before showing
            // This fixes the issue where modal element loses its CSS classes after DOM updates
            if (!modalElement.classList.contains("hidden")) {
              console.log(
                "Modal element missing 'hidden' class, adding it back"
              );
              modalElement.classList.add("hidden");
            }

            console.log("Dispatching show-modal event to:", modalElement);
            modalElement.dispatchEvent(new CustomEvent("show-modal"));
            console.log("Show-modal event dispatched");
          } else {
            console.error("Modal controller element not found");
          }
        } else {
          console.error("Failed to find or create modal content element");
        }
      })
      .catch((error) => {
        console.error("Error loading stage change modal:", error);
      });
  }

  updateStage(event) {
    // Prevent default form submission
    event.preventDefault();
    event.stopPropagation();

    // Get form data
    const form = event.target;
    const formData = new FormData(form);
    const csrfToken = document.querySelector('meta[name="csrf-token"]').content;

    // Set up stream render listener to detect success
    this.setupStreamListener();

    // Submit form
    fetch(this.urlValue, {
      method: "PATCH",
      headers: {
        "X-CSRF-Token": csrfToken,
        Accept: "text/vnd.turbo-stream.html",
      },
      body: formData,
    })
      .then((response) => {
        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        return response.text();
      })
      .then((html) => {
        // Process Turbo Stream response
        Turbo.renderStreamMessage(html);
      })
      .catch((error) => {
        console.error("Error updating stage:", error);
        // Clean up stream listener on error
        this.cleanupStreamListener();
        // Keep modal open on error - don't close it
        this.showError("Failed to update stage. Please try again.");
      });
  }

  // Set up listener to detect successful Turbo Stream responses
  setupStreamListener() {
    if (!this.boundHandleStreamRender) {
      this.boundHandleStreamRender = this.handleStreamRender.bind(this);
    }
    document.addEventListener(
      "turbo:before-stream-render",
      this.boundHandleStreamRender
    );
  }

  // Clean up stream listener
  cleanupStreamListener() {
    if (this.boundHandleStreamRender) {
      document.removeEventListener(
        "turbo:before-stream-render",
        this.boundHandleStreamRender
      );
    }
  }

  // Handle turbo stream render event to detect success
  handleStreamRender(event) {
    // Check if the stream contains success content
    const streamContent = event.detail?.newStream || event.detail?.render || "";

    // Ensure streamContent is a string before calling includes
    if (
      typeof streamContent === "string" &&
      streamContent.includes('data-stage-update-success="true"')
    ) {
      // Handle success after a short delay to let the DOM update
      setTimeout(() => {
        const successElement = document.querySelector(
          '[data-stage-update-success="true"]'
        );
        if (successElement) {
          const newStatus = successElement.dataset.newStatus || "updated";
          this.handleSuccessFromServer(newStatus);
        }
      }, 10);
    }
  }

  // Handle success from server-side script (primary method)
  handleSuccessFromServer(newStatus) {
    console.log("handleSuccessFromServer called with new status:", newStatus);

    // Prevent multiple success handling
    if (this.hasHandledSuccess) {
      console.log("Success already handled, skipping...");
      return;
    }
    this.hasHandledSuccess = true;

    // Show success toast
    this.showSuccessToast(
      `Application status updated to ${this.titleize(newStatus)}!`
    );

    // Clean up stream listener
    this.cleanupStreamListener();

    // Close modal immediately
    this.closeModal();

    // Use Turbo navigation to refresh the page state (more elegant than window.location.reload)
    console.log("Refreshing page state with Turbo in 1.5 seconds...");
    setTimeout(() => {
      Turbo.visit(window.location.href, { action: "replace" });
    }, 1500); // Give time for toast to be visible
  }

  // Handle custom success event (fallback method)
  handleCustomSuccess(event) {
    console.log("handleCustomSuccess called with event:", event);
    const newStatus = event.detail?.newStatus || "updated";

    // Only handle this event if we haven't already handled success
    // This prevents multiple controllers from all responding to the same event
    if (!this.hasHandledSuccess) {
      this.hasHandledSuccess = true;
      this.handleSuccessFromServer(newStatus);

      // Reset the flag after a delay to allow for future operations
      setTimeout(() => {
        this.hasHandledSuccess = false;
      }, 1000);
    }
  }

  // Show success toast notification
  showSuccessToast(message) {
    // Create toast element
    const toast = document.createElement("div");
    toast.className =
      "fixed top-4 right-4 bg-green-500 text-white px-6 py-3 rounded-md shadow-lg z-50 transition-opacity duration-300";
    toast.textContent = message;

    // Add to DOM
    document.body.appendChild(toast);

    // Remove after 3 seconds
    setTimeout(() => {
      toast.style.opacity = "0";
      setTimeout(() => {
        if (toast.parentNode) {
          toast.parentNode.removeChild(toast);
        }
      }, 300);
    }, 3000);
  }

  // Show error message in modal
  showError(message) {
    const errorContainer = document.getElementById("modal_actions");
    if (errorContainer) {
      errorContainer.innerHTML = `
        <div class="p-3 mb-4 text-sm text-red-700 bg-red-100 border border-red-200 rounded-md">
          ${message}
        </div>
      `;
    }
  }

  // Safe modal closing method
  closeModal() {
    try {
      // Restore body scroll
      document.body.style.overflow = "";

      // Method 1: Try to find and use the modal controller
      const modalController = document.querySelector(
        '[data-controller="modal"]'
      );
      if (modalController) {
        // Try to get the Stimulus controller instance
        const stimulusApp = window.Stimulus || this.application;
        if (stimulusApp) {
          const controllerInstance =
            stimulusApp.getControllerForElementAndIdentifier(
              modalController,
              "modal"
            );
          if (
            controllerInstance &&
            typeof controllerInstance.close === "function"
          ) {
            controllerInstance.close();
            return;
          }
        }

        // Method 2: Dispatch event safely
        try {
          modalController.dispatchEvent(new CustomEvent("modal-close"));
          return;
        } catch (e) {
          console.warn("Could not dispatch modal-close event:", e);
        }
      }

      // Method 3: Direct DOM manipulation as fallback
      const modalElements = document.querySelectorAll(".fixed.inset-0");
      modalElements.forEach((el) => {
        el.classList.add("hidden");
      });
    } catch (error) {
      console.error("Error closing modal:", error);
    }
  }

  // Helper method to titleize strings (capitalize first letter of each word)
  titleize(str) {
    return str
      .split("_")
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(" ");
  }
}
