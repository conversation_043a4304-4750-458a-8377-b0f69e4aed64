import { Controller } from "@hotwired/stimulus";
import { FetchRequest } from "@rails/request.js";

// Connects to data-controller="bookmark-button"
export default class extends Controller {
  static values = {
    talentProfileId: Number,
    bookmarked: Boolean,
    createUrl: String,
    destroyUrl: String,
  };
  static targets = [
    "buttonElement",
    "iconDisplay",
    "bookmarkedIconTemplate",
    "unbookmarkedIconTemplate",
  ];

  connect() {
    this.updateIcon();
  }

  toggle(event) {
    event.preventDefault();
    event.stopPropagation(); // Prevent interference from other click listeners on the card

    const initiallyBookmarked = this.bookmarkedValue;
    this.bookmarkedValue = !this.bookmarkedValue;
    this.updateIcon(); // Optimistic update

    const url = this.bookmarkedValue
      ? this.createUrlValue
      : this.destroyUrlValue;
    const method = this.bookmarkedValue ? "post" : "delete";

    const request = new FetchRequest(method, url, {
      responseKind: "json", // Or "turbo-stream" or "html" if your controller responds that way
    });

    request
      .perform()
      .then((response) => {
        if (!response.ok) {
          // Revert optimistic update on failure
          this.bookmarkedValue = initiallyBookmarked;
          this.updateIcon();
          console.error("Bookmark toggle failed:", response);
          // Optionally, display a user-facing error message
        }
        // On success, the optimistic update is correct.
        // If the server sends back updated data (e.g., new counts), handle it here.
      })
      .catch((error) => {
        // Revert optimistic update on network error
        this.bookmarkedValue = initiallyBookmarked;
        this.updateIcon();
        console.error("Bookmark toggle network error:", error);
      });
  }

  updateIcon() {
    // Clear previous classes first to avoid conflicts
    this.buttonElementTarget.classList.remove(
      "text-blue-600",
      "hover:text-blue-700",
      "text-stone-400",
      "hover:text-stone-600"
    );

    if (this.bookmarkedValue) {
      this.iconDisplayTarget.innerHTML =
        this.bookmarkedIconTemplateTarget.innerHTML;
      this.buttonElementTarget.classList.add(
        "text-blue-600",
        "hover:text-blue-700"
      );
    } else {
      this.iconDisplayTarget.innerHTML =
        this.unbookmarkedIconTemplateTarget.innerHTML;
      this.buttonElementTarget.classList.add(
        "text-stone-400",
        "hover:text-stone-600"
      );
    }
  }
}
