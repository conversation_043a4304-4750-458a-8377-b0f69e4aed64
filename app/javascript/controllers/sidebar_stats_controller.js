import { Controller } from "@hotwired/stimulus";

export default class extends Controller {
  static targets = ["totalDisplay"];

  connect() {
    // Initial update if target is present on connect
    if (this.hasTotalDisplayTarget) {
      this.updateStats();
    }
  }

  // Called when the target is connected to the DOM and controller
  totalDisplayTargetConnected(target) {
    this.updateStats();
  }

  // Optional: if you need to do something when it disconnects
  // totalDisplayTargetDisconnected(target) {
  // }

  updateStats() {
    const sourceElement = document.getElementById(
      "application-stage-total-count"
    );
    if (sourceElement && this.hasTotalDisplayTarget) {
      this.totalDisplayTarget.textContent = sourceElement.textContent.trim();
    } else {
      if (!sourceElement) {
        console.warn(
          "SidebarStatsController: Source element #application-stage-total-count not found."
        );
      }
      if (!this.hasTotalDisplayTarget) {
        // This might happen if the placeholder isn't visible
        // console.warn("SidebarStatsController: totalDisplayTarget not found.")
      }
    }
  }
}
