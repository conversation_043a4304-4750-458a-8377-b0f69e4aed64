import { Controller } from "@hotwired/stimulus";

export default class extends Controller {
  update(event) {
    const status = event.target.value;
    const csrfToken = document.querySelector('meta[name="csrf-token"]').content;
    const controllerElement = event.target.closest('[data-controller="status-update"]');
    const url = controllerElement.dataset.statusUpdateUrl;
    const applicationId = controllerElement.dataset.statusUpdateApplicationId;

    fetch(url, {
      method: "PATCH",
      headers: {
        "Content-Type": "application/json",
        "X-CSRF-Token": csrfToken,
      },
      body: JSON.stringify({
        application: {
          status: status,
        },
      }),
    })
      .then((response) => {
        if (!response.ok) {
          throw new Error("Network response was not ok");
        }
        window.location.reload();
      })
      .catch((error) => {
        console.error("Error updating status:", error);
      });
  }
}
