import { Controller } from "@hotwired/stimulus"

// Connects to data-controller="admin-sidebar"
export default class extends Controller {
  static targets = ["sidebar", "overlay", "toggleButton"]
  static classes = ["open", "collapsed"]

  connect() {
    // Initialize sidebar state based on screen size
    this.initializeSidebar()
    
    // Listen for window resize events
    this.resizeHandler = this.handleResize.bind(this)
    window.addEventListener('resize', this.resizeHandler)
    
    // Listen for escape key to close mobile sidebar
    this.escapeHandler = this.handleEscape.bind(this)
    document.addEventListener('keydown', this.escapeHandler)
  }

  disconnect() {
    window.removeEventListener('resize', this.resizeHandler)
    document.removeEventListener('keydown', this.escapeHandler)
  }

  initializeSidebar() {
    const isMobile = window.innerWidth < 1024 // lg breakpoint
    
    if (isMobile) {
      this.closeMobileSidebar()
    } else {
      this.openDesktopSidebar()
    }
  }

  handleResize() {
    this.initializeSidebar()
  }

  handleEscape(event) {
    if (event.key === 'Escape' && this.isMobileSidebarOpen()) {
      this.closeMobileSidebar()
    }
  }

  toggle() {
    const isMobile = window.innerWidth < 1024
    
    if (isMobile) {
      this.toggleMobileSidebar()
    } else {
      this.toggleDesktopSidebar()
    }
  }

  toggleMobileSidebar() {
    if (this.isMobileSidebarOpen()) {
      this.closeMobileSidebar()
    } else {
      this.openMobileSidebar()
    }
  }

  toggleDesktopSidebar() {
    if (this.isDesktopSidebarCollapsed()) {
      this.expandDesktopSidebar()
    } else {
      this.collapseDesktopSidebar()
    }
  }

  openMobileSidebar() {
    // Add overlay
    this.createOverlay()
    
    // Show sidebar
    this.element.classList.add('admin-sidebar-open')
    this.element.classList.remove('-translate-x-full')
    
    // Prevent body scroll
    document.body.style.overflow = 'hidden'
    
    // Focus management
    this.trapFocus()
  }

  closeMobileSidebar() {
    // Remove overlay
    this.removeOverlay()
    
    // Hide sidebar
    this.element.classList.remove('admin-sidebar-open')
    this.element.classList.add('-translate-x-full')
    
    // Restore body scroll
    document.body.style.overflow = ''
    
    // Release focus trap
    this.releaseFocusTrap()
  }

  openDesktopSidebar() {
    this.element.classList.remove('admin-sidebar-collapsed')
    this.element.classList.add('w-64')
    this.element.classList.remove('w-16')
  }

  collapseDesktopSidebar() {
    this.element.classList.add('admin-sidebar-collapsed')
    this.element.classList.remove('w-64')
    this.element.classList.add('w-16')
  }

  expandDesktopSidebar() {
    this.element.classList.remove('admin-sidebar-collapsed')
    this.element.classList.add('w-64')
    this.element.classList.remove('w-16')
  }

  isMobileSidebarOpen() {
    return this.element.classList.contains('admin-sidebar-open')
  }

  isDesktopSidebarCollapsed() {
    return this.element.classList.contains('admin-sidebar-collapsed')
  }

  createOverlay() {
    if (this.overlayElement) return
    
    this.overlayElement = document.createElement('div')
    this.overlayElement.className = 'fixed inset-0 bg-stone-900 bg-opacity-50 z-40 lg:hidden'
    this.overlayElement.addEventListener('click', () => this.closeMobileSidebar())
    
    document.body.appendChild(this.overlayElement)
  }

  removeOverlay() {
    if (this.overlayElement) {
      this.overlayElement.remove()
      this.overlayElement = null
    }
  }

  trapFocus() {
    // Simple focus trap - focus first focusable element in sidebar
    const focusableElements = this.element.querySelectorAll(
      'a[href], button:not([disabled]), [tabindex]:not([tabindex="-1"])'
    )
    
    if (focusableElements.length > 0) {
      focusableElements[0].focus()
    }
  }

  releaseFocusTrap() {
    // Release focus trap by focusing the toggle button if it exists
    if (this.hasToggleButtonTarget) {
      this.toggleButtonTarget.focus()
    }
  }

  // Action methods for template usage
  open() {
    const isMobile = window.innerWidth < 1024
    
    if (isMobile) {
      this.openMobileSidebar()
    } else {
      this.expandDesktopSidebar()
    }
  }

  close() {
    const isMobile = window.innerWidth < 1024
    
    if (isMobile) {
      this.closeMobileSidebar()
    } else {
      this.collapseDesktopSidebar()
    }
  }

  // Utility method to check if sidebar should be shown
  shouldShowSidebar() {
    const isMobile = window.innerWidth < 1024
    return !isMobile || this.isMobileSidebarOpen()
  }
}
