import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["step", "form", "submitButton"]
  
  connect() {
    this.validateCurrentStep()
  }
  
  validateCurrentStep() {
    const requiredFields = this.formTarget.querySelectorAll('[required]')
    let valid = true
    
    requiredFields.forEach(field => {
      if (!field.value) {
        valid = false
        field.classList.add('border-red-500')
      } else {
        field.classList.remove('border-red-500')
      }
    })
    
    if (this.hasSubmitButtonTarget) {
      this.submitButtonTarget.disabled = !valid
    }
    
    return valid
  }
  
  handleInput() {
    this.validateCurrentStep()
  }
  
  submit(event) {
    if (!this.validateCurrentStep()) {
      event.preventDefault()
      this.showValidationErrors()
    }
  }
  
  showValidationErrors() {
    const errorContainer = document.createElement('div')
    errorContainer.className = 'bg-red-50 text-red-500 p-4 rounded mt-4'
    errorContainer.textContent = 'Please fill out all required fields before proceeding.'
    
    const existingError = this.element.querySelector('.validation-error')
    if (existingError) {
      existingError.remove()
    }
    
    errorContainer.classList.add('validation-error')
    this.element.appendChild(errorContainer)
  }
}