import { Controller } from "@hotwired/stimulus";

// Badge Click Controller
// Handles click events on badges to open the badge modal
export default class extends Controller {
  static targets = ["badge"];
  
  static values = {
    badgeId: Number,
    badgeName: String,
    badgeDescription: String,
    badgeIcon: String,
    badgeBackgroundColor: String,
    badgeTextColor: String,
    badgeCriteria: String,
  };

  connect() {
    console.log("Badge click controller connected");
  }

  // Handle badge click to open modal
  click(event) {
    try {
      console.log("Badge clicked! Opening modal...");

      // Prevent default behavior
      event.preventDefault();
      event.stopPropagation();

      // Validate badge data
      if (!this.badgeIdValue || !this.badgeNameValue) {
        console.error("Badge click failed: Missing badge data", {
          badgeId: this.badgeIdValue,
          badgeName: this.badgeNameValue,
        });
        return;
      }

      // Find the badge modal controller
      const badgeModal = document.getElementById("badge-modal");
      if (!badgeModal) {
        console.error("Badge modal element not found");
        return;
      }

      const badgeModalController =
        this.application.getControllerForElementAndIdentifier(
          badgeModal,
          "badge-modal"
        );
      if (!badgeModalController) {
        console.error("Badge modal controller not found");
        return;
      }

      // Prepare badge data for modal
      const badgeData = {
        id: this.badgeIdValue,
        name: this.badgeNameValue,
        description: this.badgeDescriptionValue,
        icon: this.badgeIconValue,
        backgroundColor: this.badgeBackgroundColorValue,
        textColor: this.badgeTextColorValue,
        criteria: this.badgeCriteriaValue,
      };

      console.log("Opening badge modal with data:", badgeData);

      // Set badge data and show modal
      badgeModalController.setBadgeData(badgeData);
      badgeModalController.show();
    } catch (error) {
      console.error("Error handling badge click:", error);
    }
  }
}
