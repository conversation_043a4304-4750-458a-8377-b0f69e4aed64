import { Controller } from "@hotwired/stimulus";

export default class extends Controller {
  toggle(event) {
    const section = event.currentTarget.nextElementSibling;
    const arrow = event.currentTarget.querySelector("svg");

    if (section.style.display === "none") {
      section.style.display = "block";
      arrow.classList.remove("rotate-180");
    } else {
      section.style.display = "none";
      arrow.classList.add("rotate-180");
    }
  }
}
