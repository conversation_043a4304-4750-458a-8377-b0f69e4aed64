import { Controller } from "@hotwired/stimulus";

// Connects to data-controller="badge"
// Holographic Badge Controller based on Artifact UI with Modal Trigger functionality
export default class extends Controller {
  static targets = ["badge"];
  static values = {
    holographicIntensity: { type: Number, default: 0.4 },
    rotationFactor: { type: Number, default: 12 },
    glowIntensity: { type: Number, default: 0.8 },
    prismaticEffect: { type: Boolean, default: true },
    scanlineEffect: { type: Boolean, default: false },
    refractionPattern: { type: String, default: "none" }, // "none", "diamond", "wave", "circuit"
    depthEffect: { type: Boolean, default: true },
    glitchEffect: { type: Boolean, default: false },
    shadowColor: { type: String, default: "rgba(0, 0, 0, 0.5)" },
    // Badge modal trigger values
    badgeId: { type: Number, default: null },
    badgeName: { type: String, default: "" },
    badgeDescription: { type: String, default: "" },
    badgeIcon: { type: String, default: "" },
    badgeBackgroundColor: { type: String, default: "" },
    badgeTextColor: { type: String, default: "" },
    badgeCriteria: { type: String, default: "" },
  };

  connect() {
    console.log("Badge controller connecting with modal trigger functionality");
    this.mousePosition = { x: 0, y: 0 };
    this.isHovered = false;
    this.glitchOffset = { x: 0, y: 0 };
    this.glitchInterval = null;

    this.checkReducedMotion();
    this.addEventListeners();
    this.initializeStyles();
  }

  disconnect() {
    this.removeEventListeners();
    this.clearGlitchInterval();
  }

  addEventListeners() {
    this.badgeTargets.forEach((badge) => {
      badge.addEventListener("mousemove", this.handleMouseMove.bind(this));
      badge.addEventListener("mouseenter", this.handleMouseEnter.bind(this));
      badge.addEventListener("mouseleave", this.handleMouseLeave.bind(this));
      badge.addEventListener("click", this.handleClick.bind(this));
    });
  }

  removeEventListeners() {
    this.badgeTargets.forEach((badge) => {
      badge.removeEventListener("mousemove", this.handleMouseMove.bind(this));
      badge.removeEventListener("mouseenter", this.handleMouseEnter.bind(this));
      badge.removeEventListener("mouseleave", this.handleMouseLeave.bind(this));
      badge.removeEventListener("click", this.handleClick.bind(this));
    });
  }

  checkReducedMotion() {
    // Check if user prefers reduced motion for accessibility
    this.prefersReducedMotion = window.matchMedia(
      "(prefers-reduced-motion: reduce)"
    ).matches;
  }

  initializeStyles() {
    this.badgeTargets.forEach((badge) => {
      // Set initial CSS custom properties
      badge.style.setProperty("--mouse-x", "50%");
      badge.style.setProperty("--mouse-y", "50%");
      badge.style.setProperty("--glow-intensity", this.glowIntensityValue);
      badge.style.setProperty(
        "--holographic-intensity",
        this.holographicIntensityValue
      );

      // Apply initial box shadow
      badge.style.boxShadow = `
        0 10px 30px -10px ${this.shadowColorValue},
        0 0 ${this.glowIntensityValue * 20}px ${
        this.glowIntensityValue * 5
      }px rgba(255, 255, 255, 0.1)
      `;

      // Add refraction pattern if specified
      if (this.refractionPatternValue !== "none") {
        this.applyRefractionPattern(badge);
      }
    });
  }

  applyRefractionPattern(badge) {
    const opacity = "0.1";
    let pattern;

    switch (this.refractionPatternValue) {
      case "diamond":
        pattern = `repeating-linear-gradient(45deg, transparent, transparent 10px, rgba(255,255,255,${opacity}) 10px, rgba(255,255,255,${opacity}) 20px)`;
        break;
      case "wave":
        pattern = `repeating-radial-gradient(circle at 50% 50%, transparent 0, transparent 20px, rgba(255,255,255,${opacity}) 20px, rgba(255,255,255,${opacity}) 40px)`;
        break;
      case "circuit":
        pattern = `
          linear-gradient(90deg, transparent 50%, rgba(255,255,255,${opacity}) 50%),
          linear-gradient(0deg, transparent 50%, rgba(255,255,255,${opacity}) 50%)
        `;
        break;
      default:
        pattern = "none";
    }

    badge.style.setProperty("--refraction-pattern", pattern);
  }

  startGlitchEffect() {
    if (!this.glitchEffectValue || !this.isHovered) return;

    this.glitchInterval = setInterval(() => {
      if (Math.random() > 0.92) {
        this.glitchOffset = {
          x: (Math.random() - 0.5) * 10,
          y: (Math.random() - 0.5) * 10,
        };
        setTimeout(() => {
          this.glitchOffset = { x: 0, y: 0 };
        }, 50);
      }
    }, 100);
  }

  clearGlitchInterval() {
    if (this.glitchInterval) {
      clearInterval(this.glitchInterval);
      this.glitchInterval = null;
    }
  }

  handleMouseMove(e) {
    if (this.prefersReducedMotion) return;

    const badge = e.currentTarget;
    const rect = badge.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;

    // Convert coordinates to percentages
    const percentX = x / rect.width;
    const percentY = y / rect.height;

    this.mousePosition = { x: percentX, y: percentY };

    // Calculate rotation angles based on mouse position
    const rotateY = (percentX - 0.5) * this.rotationFactorValue;
    const rotateX = (0.5 - percentY) * this.rotationFactorValue;

    // Calculate holographic gradient angle
    const angle =
      Math.atan2(y - rect.height / 2, x - rect.width / 2) * (180 / Math.PI);

    // Apply transforms with depth effect
    const scale = this.isHovered ? 1.02 : 1;
    const translateZ = this.depthEffectValue
      ? this.isHovered
        ? "50px"
        : "0px"
      : "0px";

    badge.style.transform = `
      perspective(1000px)
      rotateX(${rotateX}deg)
      rotateY(${rotateY}deg)
      scale(${scale})
      translateZ(${translateZ})
      translate(${this.glitchOffset.x}px, ${this.glitchOffset.y}px)
    `;

    // Update CSS custom properties for mouse position
    badge.style.setProperty("--mouse-x", `${percentX * 100}%`);
    badge.style.setProperty("--mouse-y", `${percentY * 100}%`);

    // Update holographic effect if prismatic effect is enabled
    if (this.prismaticEffectValue) {
      const hue = (angle + 360) % 360;
      const lightness = "60%";
      const gradient = `
        linear-gradient(
          ${angle}deg,
          hsl(${hue}, 100%, ${lightness}) 0%,
          hsl(${(hue + 60) % 360}, 100%, ${lightness}) 50%,
          hsl(${(hue + 120) % 360}, 100%, ${lightness}) 100%
        )
      `;
      badge.style.setProperty("--holographic-gradient", gradient);
    }
  }

  handleMouseEnter(e) {
    if (this.prefersReducedMotion) return;

    const badge = e.currentTarget;
    this.isHovered = true;
    badge.style.transition = "transform 0.1s ease-out";

    // Start glitch effect if enabled
    if (this.glitchEffectValue) {
      this.startGlitchEffect();
    }
  }

  handleMouseLeave(e) {
    if (this.prefersReducedMotion) return;

    const badge = e.currentTarget;
    this.isHovered = false;
    this.glitchOffset = { x: 0, y: 0 };

    // Clear glitch effect
    this.clearGlitchInterval();

    // Reset transform
    badge.style.transform =
      "perspective(1000px) rotateX(0deg) rotateY(0deg) scale(1) translateZ(0px)";
    badge.style.transition = "transform 0.5s ease-out";

    // Reset mouse position
    badge.style.setProperty("--mouse-x", "50%");
    badge.style.setProperty("--mouse-y", "50%");
  }

  // Badge click handler to open modal
  handleClick(e) {
    try {
      console.log("Badge clicked! Opening modal...");

      // Prevent default behavior (CSV download)
      e.preventDefault();
      e.stopPropagation();

      // Validate badge data
      if (!this.badgeIdValue || !this.badgeNameValue) {
        console.error("Badge click failed: Missing badge data", {
          badgeId: this.badgeIdValue,
          badgeName: this.badgeNameValue,
        });
        return;
      }

      // Find the badge modal controller
      const badgeModal = document.getElementById("badge-modal");
      if (!badgeModal) {
        console.error("Badge modal element not found");
        return;
      }

      const badgeModalController =
        this.application.getControllerForElementAndIdentifier(
          badgeModal,
          "badge-modal"
        );
      if (!badgeModalController) {
        console.error("Badge modal controller not found");
        return;
      }

      // Prepare badge data for modal
      const badgeData = {
        id: this.badgeIdValue,
        name: this.badgeNameValue,
        description: this.badgeDescriptionValue,
        icon: this.badgeIconValue,
        backgroundColor: this.badgeBackgroundColorValue,
        textColor: this.badgeTextColorValue,
        criteria: this.badgeCriteriaValue,
      };

      console.log("Opening badge modal with data:", badgeData);

      // Set badge data and show modal
      badgeModalController.setBadgeData(badgeData);
      badgeModalController.show();
    } catch (error) {
      console.error("Error handling badge click:", error);
    }
  }
}
