import { Controller } from "@hotwired/stimulus";

export default class extends Controller {
  static targets = ["button", "panel", "frame"];

  connect() {
    // Set the first tab as active by default if none is active
    if (
      this.buttonTargets.length > 0 &&
      !this.buttonTargets.some((btn) => btn.classList.contains("bg-black"))
    ) {
      this.activateTab(this.buttonTargets[0].dataset.segmentId);
    }
  }

  switchTab(event) {
    const tabId = event.currentTarget.dataset.segmentId;
    this.activateTab(tabId);
  }

  activateTab(tabId) {
    // Update button styles
    this.buttonTargets.forEach((btn) => {
      if (btn.dataset.segmentId === tabId) {
        btn.classList.remove(
          "bg-transparent",
          "text-stone-600",
          "hover:underline"
        );
        btn.classList.add("bg-white", "text-black", "border-stone-100");
      } else {
        btn.classList.remove("bg-white", "text-black", "border-stone-100");
        btn.classList.add(
          "bg-transparent",
          "text-stone-600",
          "hover:underline"
        );
      }
    });

    // Update panel visibility if using local panels
    this.panelTargets.forEach((panel) => {
      if (panel.dataset.segmentId === tabId) {
        panel.classList.remove("hidden");
      } else {
        panel.classList.add("hidden");
      }
    });

    // Update Turbo Frame src if using remote content
    if (this.hasFrameTarget) {
      const url = this.buttonTargets.find(
        (btn) => btn.dataset.segmentId === tabId
      ).dataset.url;
      if (url) {
        this.frameTarget.src = url;
      }
    }
  }
}
