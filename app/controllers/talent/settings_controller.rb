module Talent
  class SettingsController < BaseController
    def show
      @user = Current.user
    end

    def edit; end

    def update
      @user = Current.user # Set @user
      if @user.update(user_params)
        # Update @user with user_params
        redirect_to talent_settings_path,
                    notice: 'Settings updated successfully.' # Use correct path helper
      else
        # Re-render the 'show' template on failure, as there's no separate 'edit' view for general settings
        render :show, status: :unprocessable_entity
      end
    end

    private

    # Renamed and updated params method
    def user_params
      params.require(:user) # Require :user key
        .permit(
        :first_name,
        :last_name,
        :email,
        :time_zone,
        :avatar, # Add other relevant user fields from the form
      )
    end
  end
end
