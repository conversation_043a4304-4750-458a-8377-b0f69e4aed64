module Talent
  class JobsController < Talent::BaseController
    before_action :set_job, only: [:show]

    def index
      @tab = params[:tab] || 'all'

      # Get counts for segment control
      @all_jobs_count = Job.count
      @saved_jobs_count = Current.user.saved_jobs.count

      # Base scope - exclude premium jobs if Talent profile is not premium
      # We'll apply this differently for saved vs all/search
      show_premium = Current.user.talent_profile&.is_premium? # Check the flag on the profile

      if @tab == 'saved'
        # Get saved jobs and then filter based on premium status
        @jobs = Current.user.saved_jobs_list # Assuming Current.user is available
        @jobs = @jobs.where(is_premium: false) unless show_premium
      else
        # Build the complete where conditions for Searchkick
        where_conditions = {}

        # Add premium filter first if necessary
        where_conditions[:is_premium] = false unless show_premium

        # Add other filters if present
        if params[:filter].present?
          # Add job category filter
          if params[:filter][:job_category].present?
            where_conditions[:job_category] = params[:filter][:job_category]
          end

          # Add platform filter
          if params[:filter][:platform].present?
            where_conditions[:platform] = params[:filter][:platform]
          end

          # Add budget range filter
          if params[:filter][:budget_range].present? &&
               !params[:filter][:budget_range].empty?
            where_conditions[:budget_range] = params[:filter][:budget_range]
          end

          # Add payment frequency filter
          if params[:filter][:payment_frequency].present?
            where_conditions[:payment_frequency] =
              params[:filter][:payment_frequency]
          end

          # Add status filter
          if params[:filter][:status].present? &&
               !params[:filter][:status].empty?
            where_conditions[:status] = params[:filter][:status]
          end

          # Add topics filter
          if params[:filter][:topics].present?
            where_conditions[:topics] = params[:filter][:topics]
          end
        end

        # Determine search query
        search_query = params[:search].present? ? params[:search] : '*'

        # Execute search directly on the Job model with combined conditions
        search_results =
          if where_conditions.present?
            Job.search(search_query, where: where_conditions)
          else
            # If no where conditions (no filters and show_premium is true), search without the where clause
            Job.search(search_query)
          end

        # Load full ActiveRecord objects to ensure correct typecasting (e.g., for topics array)
        @jobs = search_results.results
      end

      # For debugging (optional, can be removed later)
      Rails
        .logger.debug "Search query: #{search_query}, Final where conditions: #{where_conditions.inspect}, Jobs count: #{@jobs.try(:count)}"
    end

    def show
      @has_applied = Current.user.job_applications.exists?(job: @job)
      @is_saved = Current.user.saved_jobs.exists?(job: @job)
    end

    def save
      # job = Job.find(params[:id]) # Not needed as @job is set below
      @job = Job.find(params[:id]) # Use instance variable for rendering
      saved_job = Current.user.saved_jobs.create(job: @job)
      Rails
        .logger.info "Talent::JobsController#save: Attempted to save job #{@job.id} for user #{Current.user.id}. Success: #{saved_job.persisted?}. Errors: #{saved_job.errors.full_messages.join(', ')}"

      respond_to do |format|
        format.turbo_stream do
          # Render the partial directly within the replace action
          render turbo_stream:
                   turbo_stream.replace(
                     "save_job_#{@job.id}",
                     partial: 'talent/jobs/save_button',
                     locals: {
                       job: @job,
                     },
                   )
        end
        format.html do
          redirect_back fallback_location: talent_jobs_path,
                        notice: 'Job saved successfully'
        end
      end
    end

    def unsave
      @job = Job.find(params[:id]) # Use instance variable for rendering
      saved_job = Current.user.saved_jobs.find_by(job: @job)
      if saved_job
        result = saved_job.destroy
        Rails
          .logger.info "Talent::JobsController#unsave: Attempted to unsave job #{@job.id} for user #{Current.user.id}. Success: #{result}. SavedJob ID: #{saved_job.id}"
      else
        Rails
          .logger.info "Talent::JobsController#unsave: SavedJob not found for job #{@job.id} and user #{Current.user.id}."
      end

      respond_to do |format|
        format.turbo_stream do
          # Render the partial directly within the replace action
          render turbo_stream:
                   turbo_stream.replace(
                     "save_job_#{@job.id}",
                     partial: 'talent/jobs/save_button',
                     locals: {
                       job: @job,
                     },
                   )
        end
        format.html do
          redirect_back fallback_location: talent_jobs_path,
                        notice: 'Job removed from saved list'
        end
      end
    end

    private

    def set_job
      @job = Job.find(params[:id])
    end
  end # End class
end # End module
