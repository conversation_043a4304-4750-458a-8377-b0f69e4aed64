module Talent
  class JobInvitationsController < Talent::BaseController
    def index
      invitations = Current.user.job_invitations.includes(:job, :user)
      @counts = {
        all: invitations.count,
        pending: invitations.pending.count,
        accepted: invitations.accepted.count,
        ignored: invitations.ignored.count
      }
      @invitations = case params[:status]
                     when 'pending'
                       invitations.pending
                     when 'accepted'
                       invitations.accepted
                     when 'ignored'
                       invitations.ignored
                     else
                       invitations
                     end
    end

    def accept
      @invitation = Current.user.job_invitations.find(params[:id])
      @invitation.accept!

      flash[:notice] = 'You have accepted the job invitation.'
      redirect_to talent_job_invitations_path
    end

    def ignore
      @invitation = Current.user.job_invitations.find(params[:id])
      @invitation.ignore!

      flash[:notice] = 'You have ignored the job invitation.'
      redirect_to talent_job_invitations_path
    end

    def destroy
      @invitation = JobInvitation.find(params[:id])
      if @invitation.user == Current.user
        @invitation.destroy
        flash[:notice] = 'Invitation deleted successfully.'
      else
        flash[:alert] = 'You can only delete invitations you sent.'
      end
      redirect_to talent_job_invitations_path
    end
  end
end
