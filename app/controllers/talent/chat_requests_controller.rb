module Talent
  class ChatRequestsController < Talent::BaseController
    before_action :set_chat_request, only: %i[accept decline]

    def index
      @pending_requests =
        Current
          .user
          .received_chat_requests
          .pending
          .includes(:scout)
          .order(created_at: :desc)
    end

    def accept
      @chat_request.accept!

      # Create or find conversation using existing method
      @conversation =
        Conversation.find_or_create_by_participants(
          @chat_request.scout,
          @chat_request.talent,
        )

      redirect_to talent_conversation_path(@conversation),
                  notice:
                    "Chat request accepted! You can now message #{@chat_request.scout.name}."
    end

    def decline
      @chat_request.decline!

      redirect_to talent_conversations_path(status: 'chat_requests'),
                  notice:
                    "Chat request from #{@chat_request.scout.name} declined."
    end

    private

    def set_chat_request
      @chat_request = Current.user.received_chat_requests.find(params[:id])
    end
  end
end
