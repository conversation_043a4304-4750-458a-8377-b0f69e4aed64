# frozen_string_literal: true

module Talent
  module Settings
    class SubscriptionsController < Talent::BaseController
      def show
        @subscription = Current.user.subscriptions.active.first # Get the first active subscription

        if @subscription
          # Ensure the default payment processor is set if needed (might be redundant if always Stripe)
          Current.user.set_payment_processor(:stripe) unless Current.user.payment_processor
          pay_customer = Current.user.payment_processor # Use the default payment processor

          if pay_customer&.processor == :stripe && pay_customer.processor_id.present?
            begin
              # Use the existing pay_customer object directly
              @billing_portal_session = pay_customer.billing_portal(return_url: talent_settings_subscription_url)
              @billing_portal_url = @billing_portal_session.url
            rescue Stripe::InvalidRequestError => e
              # Specific error for invalid Stripe requests (e.g., customer deleted)
              Rails.logger.error "Stripe InvalidRequestError creating Billing Portal session for user #{Current.user.id}, Pay::Customer #{pay_customer.id}, Stripe ID #{pay_customer.processor_id}: #{e.message}"
              flash.now[:alert] = "There was an issue accessing your billing details with <PERSON><PERSON> (#{e.code}). Please contact support."
              @billing_portal_url = nil
            rescue Pay::Error => e
              # General Pay gem errors
              Rails.logger.error "Pay::Error creating Billing Portal session for user #{Current.user.id}, Pay::Customer #{pay_customer.id}: #{e.message}"
              flash.now[:alert] = "Could not access the billing portal due to a payment system error. Please try again later."
              @billing_portal_url = nil
            rescue StandardError => e
              # Catch other potential errors
              Rails.logger.error "Unexpected error creating Billing Portal session for user #{Current.user.id}: #{e.class} - #{e.message}\n#{e.backtrace.join("\n")}"
              flash.now[:alert] = "An unexpected error occurred while accessing the billing portal."
              @billing_portal_url = nil
            end
          else
            # User has subscription but no valid Stripe Pay::Customer record
            Rails.logger.warn "User #{Current.user.id} has active subscription #{@subscription.id} but no valid Stripe Pay::Customer record."
            flash.now[:alert] = "Your payment processor details seem to be missing or invalid. Please contact support."
            @billing_portal_url = nil
          end
        end
      end
    end
  end
end
