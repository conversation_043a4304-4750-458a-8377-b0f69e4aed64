module Talent
  class BaseController < ApplicationController
    layout 'talent'

    before_action :set_current_request_details
    before_action :authenticate
    before_action :check_verification
    before_action :check_impersonation_timeout
    before_action :require_onboarding_completion
    before_action :set_current_organization
    before_action :require_organization_selected
    before_action :check_impersonation_restrictions
    before_action :require_talent_signup_completed # Add check for talent access

    private

    def require_talent_signup_completed
      unless Current.user&.talent_signup_completed?
        redirect_to launchpad_path,
                    alert:
                      'You need to complete Talent signup to access this area.'
      end
    end
  end
end
