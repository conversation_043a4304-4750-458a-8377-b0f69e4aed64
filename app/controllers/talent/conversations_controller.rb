module Talent
  class ConversationsController < Talent::BaseController
    # Add bookmark/unbookmark to set_conversation filter
    before_action :set_conversation, only: %i[show archive unarchive bookmark unbookmark]

    def index
      # Base query for user's conversations
      base_conversations =
        Conversation
          .joins(:conversation_participants)
          .where(conversation_participants: { user_id: Current.user.id })

      # Calculate counts before filtering by status
      active_count =
        base_conversations.where(
          conversation_participants: {
            archived: [false, nil], # Include nil for active
          },
        ).count
      archived_count =
        base_conversations.where(conversation_participants: { archived: true }) # Use boolean true for archived
          .count

      # Calculate chat requests count
      chat_requests_count = Current.user.received_chat_requests.pending.count

      @counts = {
        active: active_count,
        archived: archived_count,
        chat_requests: chat_requests_count
      }

      # Determine filter status
      archived_filter = params[:status] == 'archived'
      chat_requests_filter = params[:status] == 'chat_requests'

      # Handle chat requests filter separately
      if chat_requests_filter
        @chat_requests = Current.user.received_chat_requests
                                    .pending
                                    .includes(:scout)
                                    .order(created_at: :desc)
        @conversations = []
        return
      end

      # Apply search or load conversations based on status
      if params[:query].present?
        # Start with search results
        @conversations =
          Conversation.search(
            params[:query],
            where: {
              user_ids: Current.user.id,
            }, # Keep user scope in search
            order: {
              updated_at: :desc,
            },
            includes: %i[users job messages conversation_participants], # Ensure participants are included for filtering
          )

        # Apply status filter to search results
        if archived_filter
          @conversations =
            @conversations.where(
              conversation_participants: {
                archived: true, # Filter for archived
              },
            )
        else
          @conversations =
            @conversations.where(
              conversation_participants: {
                archived: [false, nil], # Include nil for active
              },
            )
        end
      else
        # Start with base query and apply status filter
        if archived_filter
          @conversations =
            base_conversations.where(
              conversation_participants: {
                archived: true, # Filter for archived
              },
            )
        else
          @conversations =
            base_conversations.where(
              conversation_participants: {
                archived: [false, nil], # Include nil for active
              },
            )
        end

        # Apply includes and order - Updated Order Clause
        @conversations =
          @conversations
            .includes(:users, :job, messages: :user)
            # Explicitly select columns needed for DISTINCT + ORDER BY
            .select('DISTINCT conversations.*, conversation_participants.bookmarked, conversations.updated_at')
            # Order by bookmarked first (true/not null), then by updated_at
            .order('conversation_participants.bookmarked DESC NULLS LAST, conversations.updated_at DESC')
            # .distinct # Replaced by select('DISTINCT ...')
       end

       # Apply the same order after filtering search results (Searchkick handles distinct internally)
       if params[:query].present?
         @conversations = @conversations
           .order('conversation_participants.bookmarked DESC NULLS LAST, conversations.updated_at DESC')
       end

      @selected_conversation =
        params[:id] ? @conversations.find_by(id: params[:id]) : nil
    end

    def show
      @conversations =
        Conversation
          .joins(:conversation_participants)
          .where(conversation_participants: { user_id: Current.user.id })
          .includes(:users, :job)
          .order(updated_at: :desc)
          .distinct
      @conversation.messages.reload # Explicitly reload messages association
      @messages = @conversation.messages.includes(:user).order(created_at: :asc) # Explicitly order messages
      @message = Message.new
      mark_messages_as_read
    end

    def create
      @job = Job.find(params[:job_id])
      @conversation =
        Conversation.find_or_create_by_participants(
          Current.user,
          @job.company.user,
          @job,
        )
      redirect_to @conversation
    end

    def archive
      # @conversation is set by before_action
      @participant =
        @conversation.conversation_participants.find_by(user: Current.user)
      @participant.archive!

      redirect_to talent_conversations_path(status: 'archived'),
                  notice: 'Conversation archived'
    end

    def unarchive
      # @conversation is set by before_action
      @participant =
        @conversation.conversation_participants.find_by(user: Current.user)
      @participant.unarchive!

      redirect_to talent_conversations_path, notice: 'Conversation unarchived'
    end

    # New Bookmark Actions
    def bookmark
      @participant = @conversation.conversation_participants.find_by(user: Current.user)
      @participant&.bookmark!
      # Redirect back, potentially preserving status filter
      redirect_to talent_conversations_path(status: params[:status]), notice: 'Conversation bookmarked'
    end

    def unbookmark
      @participant = @conversation.conversation_participants.find_by(user: Current.user)
      @participant&.unbookmark!
      # Redirect back, potentially preserving status filter
      redirect_to talent_conversations_path(status: params[:status]), notice: 'Conversation unbookmarked'
    end

    private

    def set_conversation
      # Ensure the join is present for finding via participant user_id if needed,
      # but finding directly via Current.user.conversations is usually sufficient.
      @conversation = Current.user.conversations.find(params[:id])
    rescue ActiveRecord::RecordNotFound
      redirect_to talent_conversations_path, alert: "Conversation not found."
    end

    def mark_messages_as_read
      @conversation
        .messages
        .where.not(user: Current.user)
        .unread
        .update_all(read_at: Time.current)
    end
  end
end
