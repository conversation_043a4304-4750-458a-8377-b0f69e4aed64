class OrganizationsController < ApplicationController
  # Skip any filters that require an organization context
  skip_before_action :require_organization_selected, only: [:index, :new, :create, :switch], if: -> { respond_to?(:require_organization_selected) }
  
  # Remove the select method since we're using index directly
  
  before_action :set_organization, only: [:edit, :update]
  before_action :ensure_can_manage_organization, only: [:edit, :update]

  def index
    @organizations = Current.user.organizations
  end

  def new
    @organization = Organization.new
  end

  def create
    @organization = Organization.new(organization_params)
    
    # Create the organization and add the current user as an owner
    if @organization.save
      OrganizationMembership.create!(
        user: Current.user,
        organization: @organization,
        org_role: :owner
      )
      
      # Set this as the current organization
      Current.organization = @organization
      session[:organization_id] = @organization.id
      
      redirect_to scout_root_path, notice: "Organization '#{@organization.name}' was successfully created."
    else
      render :new, status: :unprocessable_entity
    end
  end

  def edit
    # @organization is set by set_organization
  end

  def update
    if @organization.update(organization_params)
      redirect_to organizations_path, notice: "Organization was successfully updated."
    else
      render :edit, status: :unprocessable_entity
    end
  end

  def switch
    org = Current.user.organizations.find(params[:id])
    Current.organization = org
    session[:organization_id] = org.id

    redirect_back(fallback_location: scout_root_path, notice: "Switched to #{org.name}")
  end

  private

  def set_organization
    @organization = Current.user.organizations.find(params[:id])
  end

  def ensure_can_manage_organization
    membership = OrganizationMembership.find_by(user: Current.user, organization: @organization)
    unless membership && (membership.owner? || membership.admin?)
      redirect_to organizations_path, alert: "You don't have permission to edit this organization."
    end
  end

  def organization_params
    params.require(:organization).permit(:name, :operating_timezone, :size)
  end
end
