# frozen_string_literal: true

class BadgeAnalyticsController < ApplicationController
  # Skip CSRF protection for analytics endpoints to allow cross-origin tracking
  skip_before_action :verify_authenticity_token, only: [:track_click, :track_view]
  
  # Rate limiting to prevent spam
  before_action :check_rate_limit, only: [:track_click, :track_view]

  # Track badge clicks via AJAX
  def track_click
    badge_type = BadgeType.find_by(id: params[:badge_type_id])
    badge_owner = User.find_by(id: params[:badge_owner_id])
    click_context = params[:click_context]

    unless badge_type && badge_owner
      render json: { error: 'Invalid badge or owner' }, status: :unprocessable_entity
      return
    end

    # Log the badge click
    badge_click = BadgeClick.log_click(
      badge_type: badge_type,
      badge_owner: badge_owner,
      click_context: click_context,
      clicker_user: Current.user,
      session: Current.session,
      request: request,
      metadata: {
        source: params[:source],
        page_title: params[:page_title],
        timestamp: Time.current.iso8601
      }
    )

    if badge_click
      render json: { 
        success: true, 
        message: 'Click tracked successfully',
        click_id: badge_click.id
      }, status: :ok
    else
      render json: { 
        success: false, 
        message: 'Failed to track click' 
      }, status: :internal_server_error
    end
  rescue => e
    Rails.logger.error "Badge click tracking error: #{e.message}"
    render json: { 
      success: false, 
      message: 'Tracking error occurred' 
    }, status: :internal_server_error
  end

  # Track badge views (called when profile with badges is viewed)
  def track_view
    viewed_user = User.find_by(id: params[:viewed_user_id])
    badge_types_displayed = params[:badge_types_displayed]&.map(&:to_i) || []

    unless viewed_user
      render json: { error: 'Invalid user' }, status: :unprocessable_entity
      return
    end

    if badge_types_displayed.empty?
      render json: { error: 'No badges displayed' }, status: :unprocessable_entity
      return
    end

    # Log the badge view
    badge_view = BadgeView.log_view(
      viewed_user: viewed_user,
      badge_types_displayed: badge_types_displayed,
      viewer_user: Current.user,
      session: Current.session,
      request: request,
      metadata: {
        source: params[:source],
        page_title: params[:page_title],
        timestamp: Time.current.iso8601
      }
    )

    if badge_view
      render json: { 
        success: true, 
        message: 'View tracked successfully',
        view_id: badge_view.id
      }, status: :ok
    else
      render json: { 
        success: false, 
        message: 'Failed to track view' 
      }, status: :internal_server_error
    end
  rescue => e
    Rails.logger.error "Badge view tracking error: #{e.message}"
    render json: { 
      success: false, 
      message: 'Tracking error occurred' 
    }, status: :internal_server_error
  end

  private

  def check_rate_limit
    # Simple rate limiting based on IP address
    # In production, consider using Redis or a more sophisticated rate limiting solution
    cache_key = "badge_analytics_rate_limit:#{request.remote_ip}"
    current_count = Rails.cache.read(cache_key) || 0

    if current_count >= 100 # 100 requests per minute
      render json: { 
        error: 'Rate limit exceeded' 
      }, status: :too_many_requests
      return
    end

    Rails.cache.write(cache_key, current_count + 1, expires_in: 1.minute)
  end
end
