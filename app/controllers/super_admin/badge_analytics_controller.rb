# frozen_string_literal: true

class SuperAdmin::BadgeAnalyticsController < SuperAdmin::BaseController
  before_action :set_date_range_and_badge_filter
  before_action :set_badge_types, only: [:index, :distribution, :performance]

  def index
    Rails.logger.debug "[BADGE_ANALYTICS] Controller: #{controller_name}, Action: #{action_name}, Request method: #{request.method}"

    # Generate badge analytics with caching for performance
    AdminPerformanceMonitorService.monitor_query('Badge Analytics Load') do
      @analytics = AdminBadgeAnalyticsService.generate_badge_analytics(
        date_range: @date_range,
        badge_type_id: @badge_type_id
      )

      # Additional data for the view
      @total_badge_types = BadgeType.active.count
      @total_badge_assignments = BadgeAssignment.count
      @recent_badge_activity = recent_badge_activity
    end

    Rails.logger.debug "[BADGE_ANALYTICS] Index action completed successfully"
  end

  def distribution
    Rails.logger.debug "[BADGE_ANALYTICS] Distribution action started"

    # Generate badge distribution report with caching for performance
    AdminPerformanceMonitorService.monitor_query('Badge Distribution Report Load') do
      @distribution_report = BadgeDistributionReportService.generate_distribution_report(
        date_range: @date_range,
        badge_type_id: @badge_type_id
      )
    end

    # Handle CSV export for distribution report
    respond_to do |format|
      format.html # Render the distribution view
      format.csv do
        csv_data = BadgeDistributionReportService.export_to_csv(
          date_range: @date_range,
          badge_type_id: @badge_type_id
        )

        send_data csv_data,
                  filename: "badge_distribution_report_#{Date.current.strftime('%Y%m%d')}.csv",
                  type: 'text/csv',
                  disposition: 'attachment'
      end
    end

    Rails.logger.debug "[BADGE_ANALYTICS] Distribution action completed successfully"
  end

  def performance
    Rails.logger.debug "[BADGE_ANALYTICS] Performance monitoring action started"

    # Get performance timeframe from params (default to 1 hour)
    timeframe_hours = params[:timeframe]&.to_i || 1
    @timeframe = timeframe_hours.hours

    # Get performance summary
    @performance_summary = BadgePerformanceMonitorService.get_performance_summary(timeframe: @timeframe)

    # Get recent alerts
    @recent_alerts = @performance_summary[:alerts][:recent_critical] || []

    # Get system health status
    @health_status = @performance_summary[:health_status]

    # Handle performance data export
    respond_to do |format|
      format.html # Render the performance view
      format.json do
        render json: @performance_summary
      end
      format.csv do
        csv_data = BadgePerformanceMonitorService.export_performance_data(
          format: :csv,
          timeframe: @timeframe
        )

        send_data csv_data,
                  filename: "badge_performance_#{Date.current.strftime('%Y%m%d')}.csv",
                  type: 'text/csv',
                  disposition: 'attachment'
      end
    end

    Rails.logger.debug "[BADGE_ANALYTICS] Performance monitoring action completed successfully"
  end

  def export
    # Export badge analytics data as CSV
    respond_to do |format|
      format.csv do
        analytics_data = AdminBadgeAnalyticsService.generate_badge_analytics(
          date_range: @date_range,
          badge_type_id: @badge_type_id
        )

        csv_data = generate_csv_export(analytics_data)

        send_data csv_data,
                  filename: "badge_analytics_#{Date.current.strftime('%Y%m%d')}.csv",
                  type: 'text/csv',
                  disposition: 'attachment'
      end
    end
  end

  private

  def set_date_range_and_badge_filter
    # Parse date range parameter (default to 30 days)
    @date_range = case params[:date_range]
                  when '7'
                    7.days
                  when '14'
                    14.days
                  when '30'
                    30.days
                  when '90'
                    90.days
                  when '365'
                    365.days
                  else
                    30.days
                  end

    # Parse badge type filter
    @badge_type_id = params[:badge_type_id].present? ? params[:badge_type_id].to_i : nil
    @selected_badge_type = BadgeType.find(@badge_type_id) if @badge_type_id
  end

  def set_badge_types
    # Get all active badge types for the filter dropdown
    @badge_types = BadgeType.active.order(:name)
  end

  def recent_badge_activity
    # Get recent badge-related activity for the dashboard
    {
      recent_assignments: BadgeAssignment.includes(:badge_type, :user, :admin)
                                        .order(created_at: :desc)
                                        .limit(5),
      recent_clicks: BadgeClick.includes(:badge_type, :badge_owner)
                              .order(created_at: :desc)
                              .limit(5),
      recent_views: BadgeView.includes(:viewed_user)
                            .order(created_at: :desc)
                            .limit(5)
    }
  end

  def generate_csv_export(analytics_data)
    require 'csv'
    
    CSV.generate(headers: true) do |csv|
      # Overview metrics
      csv << ['Badge Analytics Export', "Generated: #{Time.current.strftime('%Y-%m-%d %H:%M:%S')}"]
      csv << ['Date Range', "#{@date_range.to_i} days"]
      csv << ['Badge Filter', @selected_badge_type&.name || 'All Badges']
      csv << []
      
      # Overview section
      csv << ['OVERVIEW METRICS']
      overview = analytics_data[:overview]
      csv << ['Total Badges Assigned', overview[:total_badges_assigned]]
      csv << ['Total Badge Clicks', overview[:total_badge_clicks]]
      csv << ['Total Badge Views', overview[:total_badge_views]]
      csv << ['Unique Badge Clickers', overview[:unique_badge_clickers]]
      csv << ['Unique Badge Viewers', overview[:unique_badge_viewers]]
      csv << ['Active Badge Types', overview[:active_badge_types]]
      csv << ['Users with Badges', overview[:users_with_badges]]
      csv << ['Avg Clicks per Badge', overview[:avg_clicks_per_badge]]
      csv << ['Avg Views per Badge', overview[:avg_views_per_badge]]
      csv << []
      
      # Badge performance section
      csv << ['BADGE PERFORMANCE']
      csv << ['Badge Name', 'Total Assignments', 'Total Clicks', 'Total Views', 'Unique Clickers', 'Unique Viewers', 'Click Through Rate', 'Avg Clicks per Assignment', 'Avg Views per Assignment']
      
      analytics_data[:badge_performance].each do |badge_data|
        badge = badge_data[:badge_type]
        metrics = badge_data[:metrics]
        
        csv << [
          badge[:name],
          metrics[:total_assignments],
          metrics[:total_clicks],
          metrics[:total_views],
          metrics[:unique_clickers],
          metrics[:unique_viewers],
          "#{metrics[:click_through_rate]}%",
          metrics[:avg_clicks_per_assignment],
          metrics[:avg_views_per_assignment]
        ]
      end
      
      csv << []
      
      # User engagement section
      csv << ['USER ENGAGEMENT METRICS']
      engagement = analytics_data[:user_engagement]
      csv << ['Total Users with Badges', engagement[:total_users_with_badges]]
      csv << ['Users with Badge Clicks', engagement[:users_with_badge_clicks]]
      csv << ['Users with Badge Views', engagement[:users_with_badge_views]]
      csv << ['Click Engagement Rate', "#{engagement[:click_engagement_rate]}%"]
      csv << ['View Engagement Rate', "#{engagement[:view_engagement_rate]}%"]
      csv << ['Avg Badges per User', engagement[:avg_badges_per_user]]
      csv << []
      
      # Trends section
      csv << ['TRENDS']
      trends = analytics_data[:trends]
      csv << ['Metric', 'Current Period', 'Previous Period', 'Change %']
      csv << ['Clicks', trends[:clicks][:current_period], trends[:clicks][:previous_period], "#{trends[:clicks][:change_percent]}%"]
      csv << ['Views', trends[:views][:current_period], trends[:views][:previous_period], "#{trends[:views][:change_percent]}%"]
      csv << []
      
      # Top performers section
      csv << ['TOP CLICKED USERS']
      csv << ['User Name', 'Email', 'Total Clicks']
      analytics_data[:top_performers][:top_clicked_users].each do |user|
        csv << [user[:name], user[:email], user[:clicks]]
      end
      
      csv << []
      csv << ['TOP VIEWED USERS']
      csv << ['User Name', 'Email', 'Total Views']
      analytics_data[:top_performers][:top_viewed_users].each do |user|
        csv << [user[:name], user[:email], user[:views]]
      end
    end
  end
end
