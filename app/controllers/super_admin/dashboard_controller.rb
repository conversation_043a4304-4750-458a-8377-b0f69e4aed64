module SuperAdmin
  class DashboardController < SuperAdmin::BaseController
    def index
      @user_count = User.count
      @recent_users = User.order(created_at: :desc).limit(5)
      @recent_jobs = Job.order(created_at: :desc).limit(5)
      @active_impersonations =
        ImpersonationLog.active.includes(:admin, :user).recent.limit(10)
      @recent_admin_actions =
        AdminAuditLog.includes(:admin_user).order(created_at: :desc).limit(10)

      # System health data
      @system_health = {
        database: {
          response_time: 21.0,
        },
        search: {
          indices: 4,
        },
        jobs: {
          pending: 0,
        },
        cache: {
          hit_rate: 1.0,
        },
        storage: {
          usage: 0.0,
        },
      }

      # Analytics data
      @analytics = {
        daily_activity: {
          new_users:
            User.where(
              created_at:
                Date.current.beginning_of_day..Date.current.end_of_day,
            ).count,
          new_jobs:
            Job.where(
              created_at:
                Date.current.beginning_of_day..Date.current.end_of_day,
            ).count,
          applications:
            JobApplication.where(
              created_at:
                Date.current.beginning_of_day..Date.current.end_of_day,
            ).count,
          messages:
            Message.where(
              created_at:
                Date.current.beginning_of_day..Date.current.end_of_day,
            ).count,
          admin_actions:
            AdminAuditLog.where(
              created_at:
                Date.current.beginning_of_day..Date.current.end_of_day,
            ).count,
          peak_hour: '16:00',
        },
        recent_activity_summary: {
          new_users_today:
            User.where(
              created_at:
                Date.current.beginning_of_day..Date.current.end_of_day,
            ).count,
          new_jobs_today:
            Job.where(
              created_at:
                Date.current.beginning_of_day..Date.current.end_of_day,
            ).count,
          new_applications_today:
            JobApplication.where(
              created_at:
                Date.current.beginning_of_day..Date.current.end_of_day,
            ).count,
          new_conversations_today:
            Conversation.where(
              created_at:
                Date.current.beginning_of_day..Date.current.end_of_day,
            ).count,
          new_messages_today:
            Message.where(
              created_at:
                Date.current.beginning_of_day..Date.current.end_of_day,
            ).count,
          active_sessions_today:
            Session.where(
              created_at:
                Date.current.beginning_of_day..Date.current.end_of_day,
            ).count,
        },
        growth_trends: {
          users: {
            weekly: {
              this_week:
                User.where(
                  created_at: 1.week.ago.beginning_of_week..Time.current,
                ).count,
              last_week:
                User.where(
                  created_at:
                    2.weeks.ago.beginning_of_week..1.week.ago.end_of_week,
                ).count,
              change:
                calculate_percentage_change(
                  User.where(
                    created_at:
                      2.weeks.ago.beginning_of_week..1.week.ago.end_of_week,
                  ).count,
                  User.where(
                    created_at: 1.week.ago.beginning_of_week..Time.current,
                  ).count,
                ),
            },
            monthly: {
              change: 0,
            },
          },
          jobs: {
            weekly: {
              this_week:
                Job.where(
                  created_at: 1.week.ago.beginning_of_week..Time.current,
                ).count,
              last_week:
                Job.where(
                  created_at:
                    2.weeks.ago.beginning_of_week..1.week.ago.end_of_week,
                ).count,
              change:
                calculate_percentage_change(
                  Job.where(
                    created_at:
                      2.weeks.ago.beginning_of_week..1.week.ago.end_of_week,
                  ).count,
                  Job.where(
                    created_at: 1.week.ago.beginning_of_week..Time.current,
                  ).count,
                ),
            },
            monthly: {
              change: 0,
            },
          },
          applications: {
            weekly: {
              change: 0,
            },
            monthly: {
              change: 0,
            },
          },
          organizations: {
            weekly: {
              this_week:
                Organization.where(
                  created_at: 1.week.ago.beginning_of_week..Time.current,
                ).count,
              last_week:
                Organization.where(
                  created_at:
                    2.weeks.ago.beginning_of_week..1.week.ago.end_of_week,
                ).count,
              change:
                calculate_percentage_change(
                  Organization.where(
                    created_at:
                      2.weeks.ago.beginning_of_week..1.week.ago.end_of_week,
                  ).count,
                  Organization.where(
                    created_at: 1.week.ago.beginning_of_week..Time.current,
                  ).count,
                ),
            },
            monthly: {
              change: 0,
            },
          },
        },
        user_engagement: {
          engagement_rate_7d: calculate_engagement_rate(7),
          active_7_days: calculate_active_users(7),
          total_users: User.count,
          engagement_rate_30d: calculate_engagement_rate(30),
          active_30_days: calculate_active_users(30),
          verification_rate: calculate_verification_rate,
          profile_completion_rate: calculate_profile_completion_rate,
          organization_join_rate: calculate_organization_join_rate,
        },
        conversion_rates: {
          job_applications: {
            acceptance_rate: 0,
            accepted: 0,
            total: JobApplication.count,
          },
          chat_requests: {
            acceptance_rate: 0,
            accepted: 0,
            total: ChatRequest.count,
          },
          conversations: {
            activity_rate: 0,
            active_7d: 0,
            total: Conversation.count,
          },
        },
        job_metrics: {
          application_rate: 0,
          jobs_with_applications: 0,
          total_jobs: Job.count,
          avg_applications_per_job: 0,
        },
        user_stats: {
          verified: User.where(verified: true).count,
          scouts: User.joins(:roles).where(roles: { name: 'scout' }).count,
          talents: User.joins(:talent_profile).count,
        },
        job_stats: {
          total: Job.count,
          published: Job.where(status: 'published').count,
          draft: Job.where(status: 'draft').count,
          expired: Job.where('expires_at < ?', Time.current).count,
        },
        organization_stats: {
          total: Organization.count,
          with_jobs: Organization.joins(:jobs).distinct.count,
        },
        communication_stats: {
          total: ChatRequest.count + Message.count,
          chat_requests: ChatRequest.count,
          messages: Message.count,
        },
      }

      # Recent activity data
      @recent_activity = {
        recent_users: User.order(created_at: :desc).limit(5),
        recent_jobs: Job.order(created_at: :desc).limit(5),
      }
    end

    private

    def calculate_percentage_change(old_value, new_value)
      return 0 if old_value.zero?
      ((new_value - old_value).to_f / old_value * 100).round(1)
    end

    def calculate_engagement_rate(days)
      total_users = User.count
      return 0 if total_users.zero?
      active_users = calculate_active_users(days)
      ((active_users.to_f / total_users) * 100).round(1)
    end

    def calculate_active_users(days)
      User
        .joins(:sessions)
        .where(sessions: { last_activity_at: days.days.ago..Time.current })
        .distinct
        .count
    end

    def calculate_verification_rate
      total_users = User.count
      return 0 if total_users.zero?
      verified_users = User.where(verified: true).count
      ((verified_users.to_f / total_users) * 100).round(1)
    end

    def calculate_profile_completion_rate
      total_users = User.count
      return 0 if total_users.zero?
      completed_profiles = User.where.not(first_name: nil, last_name: nil).count
      ((completed_profiles.to_f / total_users) * 100).round(1)
    end

    def calculate_organization_join_rate
      total_users = User.count
      return 0 if total_users.zero?
      users_with_orgs = User.joins(:organization_memberships).distinct.count
      ((users_with_orgs.to_f / total_users) * 100).round(1)
    end
  end
end
