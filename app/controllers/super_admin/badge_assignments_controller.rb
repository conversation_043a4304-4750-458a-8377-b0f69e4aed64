# app/controllers/super_admin/badge_assignments_controller.rb
class SuperAdmin::BadgeAssignmentsController < SuperAdmin::BaseController
  before_action :set_user, only: %i[index new create]
  before_action :set_badge_assignment, only: [:destroy]

  def index
    @badge_assignments =
      @user
        .badge_assignments
        .includes(:badge_type, :admin)
        .order(assigned_at: :desc)
  end

  def new
    @badge_assignment =
      BadgeAssignment.new(
        user: @user,
        admin: Current.user,
        assigned_at: Time.current,
      )
    @available_badges = BadgeType.active.order(:name)
  end

  def create
    @badge_assignment = BadgeAssignment.new(badge_assignment_params)

    if @badge_assignment.save
      AdminAuditLog.log_action(
        user: Current.user,
        action: 'create',
        resource: @badge_assignment,
        change_data: {
          badge_assignment: @badge_assignment.attributes,
          user: @user.attributes,
        },
      )

      redirect_to super_admin_user_badge_assignments_path(@user),
                  notice: 'Badge assigned successfully.'
    else
      @available_badges = BadgeType.active.order(:name)
      render :new
    end
  end

  def destroy
    @user = @badge_assignment.user
    @badge_assignment.destroy

    AdminAuditLog.log_action(
      user: Current.user,
      action: 'destroy',
      resource: @badge_assignment,
      change_data: {
        badge_assignment: @badge_assignment.attributes,
        user: @user.attributes,
      },
    )

    redirect_to super_admin_user_badge_assignments_path(@user),
                notice: 'Badge assignment was successfully revoked.'
  end

  private

  def set_user
    @user = User.find(params[:user_id])
  end

  def set_badge_assignment
    @badge_assignment = BadgeAssignment.find(params[:id])
  end

  def badge_assignment_params
    params
      .require(:badge_assignment)
      .permit(:badge_type_id, :user_id, :expires_at, :notes)
      .merge(admin_id: Current.user.id, assigned_at: Time.current)
  end
end
