class SuperAdmin::AdminErrorMonitoringController < SuperAdmin::BaseController
  before_action :require_superadmin

  def index
    @error_summary = build_error_summary
    @recent_errors = recent_error_logs
    @error_trends = calculate_error_trends
    @top_error_types = top_error_types
    @affected_users = most_affected_users
  end

  def show
    @error_log = AdminAuditLog.find(params[:id])
    @error_details = parse_error_details(@error_log)
    @related_errors = find_related_errors(@error_log)
  end

  def resolve
    @error_log = AdminAuditLog.find(params[:id])
    
    @error_log.update!(
      change_data: @error_log.change_data.merge(
        'resolved_at' => Time.current,
        'resolved_by' => Current.user.id,
        'resolution_notes' => params[:resolution_notes]
      )
    )

    log_admin_action('error_resolved', resource: @error_log, details: {
      resolution_notes: params[:resolution_notes]
    })

    redirect_to super_admin_admin_error_monitoring_index_path,
                notice: 'Error marked as resolved.'
  end

  def bulk_resolve
    error_ids = params[:error_ids]
    resolution_notes = params[:resolution_notes]

    return redirect_to super_admin_admin_error_monitoring_index_path,
                       alert: 'No errors selected.' if error_ids.blank?

    resolved_count = 0
    
    AdminAuditLog.where(id: error_ids).find_each do |error_log|
      error_log.update!(
        change_data: error_log.change_data.merge(
          'resolved_at' => Time.current,
          'resolved_by' => Current.user.id,
          'resolution_notes' => resolution_notes
        )
      )
      resolved_count += 1
    end

    log_admin_action('bulk_error_resolution', details: {
      resolved_count: resolved_count,
      resolution_notes: resolution_notes
    })

    redirect_to super_admin_admin_error_monitoring_index_path,
                notice: "#{resolved_count} errors marked as resolved."
  end

  def export
    errors = filtered_error_logs
    
    respond_to do |format|
      format.csv do
        csv_data = generate_error_csv(errors)
        send_data csv_data,
                  filename: "admin_errors_#{Date.current}.csv",
                  type: 'text/csv'
        
        log_admin_action('error_export', details: {
          exported_count: errors.count,
          date_range: [params[:start_date], params[:end_date]].compact
        })
      end
    end
  end

  private

  def build_error_summary
    base_query = AdminAuditLog.where(action: 'error')
    
    {
      total_errors_24h: base_query.where('created_at > ?', 24.hours.ago).count,
      total_errors_7d: base_query.where('created_at > ?', 7.days.ago).count,
      total_errors_30d: base_query.where('created_at > ?', 30.days.ago).count,
      unresolved_errors: base_query.where("change_data->>'resolved_at' IS NULL").count,
      critical_errors: base_query.joins(:admin_user)
                                .where("change_data->>'error_class' LIKE ?", '%Error')
                                .where('created_at > ?', 24.hours.ago).count
    }
  end

  def recent_error_logs
    AdminAuditLog.includes(:admin_user)
                 .where(action: 'error')
                 .where("change_data->>'resolved_at' IS NULL")
                 .order(created_at: :desc)
                 .limit(20)
  end

  def calculate_error_trends
    # Get error counts by hour for the last 24 hours
    24.times.map do |hours_ago|
      time_start = hours_ago.hours.ago.beginning_of_hour
      time_end = time_start.end_of_hour
      
      {
        hour: time_start.strftime('%H:00'),
        count: AdminAuditLog.where(action: 'error')
                           .where(created_at: time_start..time_end)
                           .count
      }
    end.reverse
  end

  def top_error_types
    AdminAuditLog.where(action: 'error')
                 .where('created_at > ?', 7.days.ago)
                 .group("change_data->>'error_class'")
                 .count
                 .sort_by { |_, count| -count }
                 .first(10)
  end

  def most_affected_users
    AdminAuditLog.joins(:admin_user)
                 .where(action: 'error')
                 .where('created_at > ?', 7.days.ago)
                 .group('admin_users.email')
                 .count
                 .sort_by { |_, count| -count }
                 .first(10)
  end

  def parse_error_details(error_log)
    error_context = error_log.change_data&.dig('error_context') || {}
    
    {
      error_class: error_log.change_data&.dig('error_class'),
      error_message: error_log.change_data&.dig('error_message'),
      controller: error_context['controller'],
      action: error_context['action'],
      user_email: error_context['user_email'],
      ip_address: error_context['ip_address'],
      request_path: error_context['request_path'],
      request_method: error_context['request_method'],
      params: error_context['params'],
      resolved_at: error_log.change_data&.dig('resolved_at'),
      resolved_by: error_log.change_data&.dig('resolved_by'),
      resolution_notes: error_log.change_data&.dig('resolution_notes')
    }
  end

  def find_related_errors(error_log)
    error_class = error_log.change_data&.dig('error_class')
    return AdminAuditLog.none unless error_class

    AdminAuditLog.where(action: 'error')
                 .where("change_data->>'error_class' = ?", error_class)
                 .where.not(id: error_log.id)
                 .where('created_at > ?', 7.days.ago)
                 .order(created_at: :desc)
                 .limit(10)
  end

  def filtered_error_logs
    logs = AdminAuditLog.where(action: 'error')

    if params[:start_date].present?
      logs = logs.where('created_at >= ?', Date.parse(params[:start_date]))
    end

    if params[:end_date].present?
      logs = logs.where('created_at <= ?', Date.parse(params[:end_date]).end_of_day)
    end

    if params[:error_class].present?
      logs = logs.where("change_data->>'error_class' = ?", params[:error_class])
    end

    if params[:resolved].present?
      case params[:resolved]
      when 'true'
        logs = logs.where("change_data->>'resolved_at' IS NOT NULL")
      when 'false'
        logs = logs.where("change_data->>'resolved_at' IS NULL")
      end
    end

    logs.includes(:admin_user).order(created_at: :desc)
  end

  def generate_error_csv(errors)
    CSV.generate(headers: true) do |csv|
      csv << [
        'Timestamp',
        'Error Class',
        'Error Message',
        'Controller',
        'Action',
        'User Email',
        'IP Address',
        'Request Path',
        'Resolved At',
        'Resolved By',
        'Resolution Notes'
      ]

      errors.find_each do |error_log|
        details = parse_error_details(error_log)
        csv << [
          error_log.created_at.iso8601,
          details[:error_class],
          details[:error_message],
          details[:controller],
          details[:action],
          details[:user_email],
          details[:ip_address],
          details[:request_path],
          details[:resolved_at],
          details[:resolved_by],
          details[:resolution_notes]
        ]
      end
    end
  end
end
