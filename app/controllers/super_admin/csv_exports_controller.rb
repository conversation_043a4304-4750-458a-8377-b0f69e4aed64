module SuperAdmin
  class CsvExportsController < SuperAdmin::AdminBaseController
    before_action :set_csv_export, only: %i[show retry]

    def index
      @csv_exports = CsvExport.includes(:admin_user).recent

      # Apply filters
      if params[:status].present? && params[:status] != 'all'
        @csv_exports = @csv_exports.where(status: params[:status])
      end

      if params[:controller_filter].present? &&
           params[:controller_filter] != 'all'
        @csv_exports =
          @csv_exports.where(controller_name: params[:controller_filter])
      end

      if params[:user_filter].present? && params[:user_filter] != 'all'
        @csv_exports = @csv_exports.where(admin_user_id: params[:user_filter])
      end

      # Get filter options
      @available_statuses =
        CsvExport.statuses.keys.map { |status| [status.humanize, status] }
      @available_controllers =
        CsvExport
          .distinct
          .pluck(:controller_name)
          .compact
          .sort
          .map { |name| [name.humanize, name] }
      @available_users =
        User
          .joins(:csv_exports)
          .distinct
          .select(:id, :first_name, :last_name, :email)
          .order(:first_name, :last_name)

      # Paginate results
      @pagy, @csv_exports = pagy(@csv_exports, limit: 25)
    end

    def show
      @filters =
        @csv_export.filters.present? ? JSON.parse(@csv_export.filters) : {}
    end

    def retry
      if @csv_export.failed?
        # Create a new export with the same parameters
        new_export_id = SecureRandom.uuid
        filters =
          @csv_export.filters.present? ? JSON.parse(@csv_export.filters) : {}

        new_export =
          CsvExport.create!(
            export_id: new_export_id,
            admin_user: @csv_export.admin_user,
            controller_name: @csv_export.controller_name,
            filters: @csv_export.filters,
            status: :queued,
          )

        CsvExportJob.perform_later(
          @csv_export.admin_user.id,
          @csv_export.controller_name,
          filters,
          new_export_id,
        )

        redirect_to super_admin_csv_exports_path,
                    notice:
                      "Export retry queued successfully (Export ID: #{new_export_id[0..7]})"
      else
        redirect_to super_admin_csv_exports_path,
                    alert: 'Only failed exports can be retried'
      end
    end

    def destroy
      @csv_export = CsvExport.find(params[:id])

      if @csv_export.queued? || @csv_export.processing?
        redirect_to super_admin_csv_exports_path,
                    alert: 'Cannot delete exports that are queued or processing'
      else
        @csv_export.destroy
        redirect_to super_admin_csv_exports_path,
                    notice: 'Export record deleted successfully'
      end
    end

    protected

    def resource_name
      'csv_exports'
    end

    private

    def set_csv_export
      @csv_export = CsvExport.find(params[:id])
    end
  end
end
