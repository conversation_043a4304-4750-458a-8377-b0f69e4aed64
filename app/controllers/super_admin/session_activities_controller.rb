module SuperAdmin
  class SessionActivitiesController < SuperAdmin::BaseController
    before_action :set_session_activity, only: %i[show]

    def index
      @session_activities = SessionActivity.includes(:session => :user).recent

      # Apply filters
      if params[:activity_type].present? && params[:activity_type] != 'all'
        @session_activities = @session_activities.where(activity_type: params[:activity_type])
      end

      if params[:user_filter].present? && params[:user_filter] != 'all'
        @session_activities = @session_activities.joins(session: :user)
                                                 .where(users: { id: params[:user_filter] })
      end

      if params[:ip_filter].present?
        @session_activities = @session_activities.where(ip_address: params[:ip_filter])
      end

      if params[:date_from].present?
        @session_activities = @session_activities.where('created_at >= ?', Date.parse(params[:date_from]))
      end

      if params[:date_to].present?
        @session_activities = @session_activities.where('created_at <= ?', Date.parse(params[:date_to]).end_of_day)
      end

      # Get filter options
      @available_activity_types = SessionActivity::ACTIVITY_TYPES.map { |type| [type.humanize, type] }
      @available_users = User.joins(sessions: :session_activities)
                            .distinct
                            .select(:id, :first_name, :last_name, :email)
                            .order(:first_name, :last_name)
      @available_ips = SessionActivity.distinct.pluck(:ip_address).compact.sort

      # Paginate results
      @pagy, @session_activities = pagy(@session_activities.order(created_at: :desc), limit: 50)
    end

    def show
      @session = @session_activity.session
      @user = @session.user
    end

    private

    def set_session_activity
      @session_activity = SessionActivity.find(params[:id])
    end
  end
end
