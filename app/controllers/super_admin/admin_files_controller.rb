require 'csv'

module SuperAdmin
  class AdminFilesController < SuperAdmin::AdminBaseController
    include SuperAdmin::CsvExportable
    include AdminBulkOperations
    before_action :require_superadmin
    before_action :set_attachment, only: [:show, :destroy, :download]

    def index
      @q = params[:q]
      @record_type_filter = params[:record_type_filter]
      @content_type_filter = params[:content_type_filter]
      @size_filter = params[:size_filter]
      @date_filter = params[:date_filter]
      @sort_by = params[:sort_by] || 'created_at'
      @sort_direction = params[:sort_direction] || 'desc'

      # Start with all attachments, including associated blobs
      attachments = ActiveStorage::Attachment.includes(:blob)

      # Apply search filter
      if @q.present?
        attachments = attachments.joins(:blob).where(
          "active_storage_blobs.filename ILIKE ? OR active_storage_blobs.content_type ILIKE ? OR CAST(active_storage_attachments.id AS TEXT) ILIKE ?",
          "%#{@q}%", "%#{@q}%", "%#{@q}%"
        )
      end

      # Apply record type filter
      if @record_type_filter.present? && @record_type_filter != 'all'
        attachments = attachments.where(record_type: @record_type_filter)
      end

      # Apply content type filter
      if @content_type_filter.present? && @content_type_filter != 'all'
        case @content_type_filter
        when 'images'
          attachments = attachments.joins(:blob).where("active_storage_blobs.content_type LIKE 'image/%'")
        when 'documents'
          attachments = attachments.joins(:blob).where("active_storage_blobs.content_type IN ('application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document')")
        when 'videos'
          attachments = attachments.joins(:blob).where("active_storage_blobs.content_type LIKE 'video/%'")
        when 'audio'
          attachments = attachments.joins(:blob).where("active_storage_blobs.content_type LIKE 'audio/%'")
        else
          attachments = attachments.joins(:blob).where("active_storage_blobs.content_type LIKE ?", "#{@content_type_filter}%")
        end
      end

      # Apply size filter
      if @size_filter.present? && @size_filter != 'all'
        case @size_filter
        when 'small'
          attachments = attachments.joins(:blob).where("active_storage_blobs.byte_size < ?", 1.megabyte)
        when 'medium'
          attachments = attachments.joins(:blob).where("active_storage_blobs.byte_size BETWEEN ? AND ?", 1.megabyte, 10.megabytes)
        when 'large'
          attachments = attachments.joins(:blob).where("active_storage_blobs.byte_size BETWEEN ? AND ?", 10.megabytes, 100.megabytes)
        when 'xlarge'
          attachments = attachments.joins(:blob).where("active_storage_blobs.byte_size > ?", 100.megabytes)
        end
      end

      # Apply date filter
      if @date_filter.present? && @date_filter != 'all'
        case @date_filter
        when 'today'
          attachments = attachments.where(created_at: Date.current.beginning_of_day..Date.current.end_of_day)
        when 'week'
          attachments = attachments.where(created_at: 1.week.ago..Time.current)
        when 'month'
          attachments = attachments.where(created_at: 1.month.ago..Time.current)
        when 'quarter'
          attachments = attachments.where(created_at: 3.months.ago..Time.current)
        end
      end

      # Apply sorting
      case @sort_by
      when 'filename'
        attachments = attachments.joins(:blob).order("active_storage_blobs.filename #{@sort_direction}")
      when 'size'
        attachments = attachments.joins(:blob).order("active_storage_blobs.byte_size #{@sort_direction}")
      when 'content_type'
        attachments = attachments.joins(:blob).order("active_storage_blobs.content_type #{@sort_direction}")
      when 'record_type'
        attachments = attachments.order("record_type #{@sort_direction}")
      when 'created_at'
        attachments = attachments.order("active_storage_attachments.created_at #{@sort_direction}")
      else
        attachments = attachments.order("active_storage_attachments.created_at #{@sort_direction}")
      end

      # Calculate statistics
      @stats = calculate_file_stats(attachments)

      # Get filter options
      @available_record_types = ActiveStorage::Attachment.distinct.pluck(:record_type).compact.sort
      @available_content_types = ActiveStorage::Blob.distinct.pluck(:content_type).compact.sort

      # Paginate results
      respond_to do |format|
        format.html do
          @pagy, @attachments = pagy(attachments, limit: 20)
        end
        format.csv do
          @attachments_for_export = attachments
          send_csv_data
        end
      end
    end

    def show
      @blob = @attachment.blob
      @record = @attachment.record
      @metadata = @blob.metadata || {}
      
      # Get related attachments from the same record
      @related_attachments = if @record
        @record.class.reflect_on_all_attachments.flat_map do |reflection|
          attachment_relation = @record.send(reflection.name)
          if attachment_relation.respond_to?(:where)
            # This is a has_many_attached relationship
            attachment_relation.where.not(id: @attachment.id).limit(5)
          elsif attachment_relation.present? && attachment_relation.id != @attachment.id
            # This is a has_one_attached relationship
            [attachment_relation]
          else
            []
          end
        end.compact
      else
        []
      end
    end

    def download
      redirect_to rails_blob_path(@attachment.blob, disposition: "attachment")
    end

    def destroy
      @blob = @attachment.blob
      record_info = {
        filename: @blob.filename.to_s,
        record_type: @attachment.record_type,
        record_id: @attachment.record_id
      }

      begin
        @attachment.purge

        # Log the file deletion
        AdminAuditLog.log_action(
          action: 'delete',
          controller: controller_path,
          resource: @attachment,
          changes: {
            'file_deleted' => [record_info, nil],
            'filename' => [@blob.filename.to_s, nil]
          },
          admin_user: Current.user
        )

        redirect_to super_admin_admin_files_path, notice: 'File deleted successfully.'
      rescue => e
        Rails.logger.error "Error deleting file: #{e.message}"
        redirect_to super_admin_admin_files_path, alert: 'Failed to delete file.'
      end
    end

    def bulk_update
      action = params[:bulk_action]
      attachment_ids = params[:attachment_ids]&.reject(&:blank?) || []

      if attachment_ids.empty?
        redirect_to super_admin_admin_files_path, alert: 'No files selected.'
        return
      end

      case action
      when 'delete'
        bulk_delete_attachments(attachment_ids)
      else
        redirect_to super_admin_admin_files_path, alert: 'Invalid bulk action.'
      end
    end

    private

    def set_attachment
      @attachment = ActiveStorage::Attachment.find(params[:id])
    end

    def calculate_file_stats(attachments_scope)
      base_scope = attachments_scope.joins(:blob)
      
      {
        total_files: base_scope.count,
        total_size: base_scope.sum('active_storage_blobs.byte_size'),
        avg_size: base_scope.average('active_storage_blobs.byte_size')&.to_i || 0,
        images_count: base_scope.where("active_storage_blobs.content_type LIKE 'image/%'").count,
        documents_count: base_scope.where("active_storage_blobs.content_type IN ('application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document')").count,
        videos_count: base_scope.where("active_storage_blobs.content_type LIKE 'video/%'").count,
        this_week: base_scope.where(active_storage_attachments: { created_at: 1.week.ago..Time.current }).count,
        orphaned_files: ActiveStorage::Blob.left_joins(:attachments).where(active_storage_attachments: { id: nil }).count
      }
    end

    def bulk_delete_attachments(attachment_ids)
      deleted_count = 0
      failed_count = 0
      deleted_files = []

      attachment_ids.each do |attachment_id|
        begin
          attachment = ActiveStorage::Attachment.find(attachment_id)
          blob = attachment.blob
          
          deleted_files << {
            filename: blob.filename.to_s,
            record_type: attachment.record_type,
            record_id: attachment.record_id
          }
          
          attachment.purge
          deleted_count += 1
        rescue => e
          Rails.logger.error "Error deleting attachment #{attachment_id}: #{e.message}"
          failed_count += 1
        end
      end

      # Log bulk deletion
      if deleted_count > 0
        AdminAuditLog.log_action(
          action: 'bulk_delete',
          controller: controller_path,
          resource: nil,
          changes: {
            'files_deleted' => [deleted_files, nil],
            'deleted_count' => [nil, deleted_count],
            'failed_count' => [nil, failed_count]
          },
          admin_user: Current.user
        )
      end

      if failed_count > 0
        redirect_to super_admin_admin_files_path, 
                    alert: "Deleted #{deleted_count} files. Failed to delete #{failed_count} files."
      else
        redirect_to super_admin_admin_files_path, 
                    notice: "Successfully deleted #{deleted_count} files."
      end
    end

    def resource_name
      'files'
    end

    def collection_for_export
      @attachments_for_export || @attachments
    end

    def csv_headers
      [
        'ID',
        'Filename',
        'Content Type',
        'File Size (bytes)',
        'File Size (human)',
        'Record Type',
        'Record ID',
        'Attachment Name',
        'Storage Key',
        'Service Name',
        'Checksum',
        'Created At',
        'Record Exists'
      ]
    end

    def csv_row(attachment)
      blob = attachment.blob
      [
        attachment.id,
        blob.filename.to_s,
        blob.content_type,
        blob.byte_size,
        number_to_human_size(blob.byte_size),
        attachment.record_type,
        attachment.record_id,
        attachment.name,
        blob.key,
        blob.service_name,
        blob.checksum,
        attachment.created_at.iso8601,
        attachment.record.present? ? 'Yes' : 'No'
      ]
    end
  end
end
