class SuperAdmin::AdminTalentBookmarksController < SuperAdmin::AdminBaseController
  include SuperAdmin::CsvExportable
  include AdminBulkOperations

  before_action :set_talent_bookmark, only: [:show, :destroy]

  def index
    @talent_bookmarks = TalentBookmark.includes(:user, :talent_profile => :user)
                                     .order(created_at: :desc)

    # Apply search
    if params[:search].present?
      search_term = "%#{params[:search]}%"
      @talent_bookmarks = @talent_bookmarks.joins(:user, talent_profile: :user)
                                          .where(
                                            "users.first_name <PERSON>IKE ? OR users.last_name ILIKE ? OR users.email ILIKE ? OR talent_users.first_name ILIKE ? OR talent_users.last_name ILIKE ? OR talent_users.email ILIKE ? OR talent_profiles.headline ILIKE ?",
                                            search_term, search_term, search_term, search_term, search_term, search_term, search_term
                                          )
                                          .references(:talent_profiles)
    end

    # Apply filters
    if params[:user_id].present? && params[:user_id] != 'all'
      @talent_bookmarks = @talent_bookmarks.where(user_id: params[:user_id])
    end

    if params[:talent_profile_id].present? && params[:talent_profile_id] != 'all'
      @talent_bookmarks = @talent_bookmarks.where(talent_profile_id: params[:talent_profile_id])
    end

    if params[:date_range].present? && params[:date_range] != 'all'
      case params[:date_range]
      when 'today'
        @talent_bookmarks = @talent_bookmarks.where(created_at: Date.current.beginning_of_day..Date.current.end_of_day)
      when 'week'
        @talent_bookmarks = @talent_bookmarks.where(created_at: 1.week.ago..Time.current)
      when 'month'
        @talent_bookmarks = @talent_bookmarks.where(created_at: 1.month.ago..Time.current)
      end
    end

    # Get filter options
    @available_users = User.joins(:talent_bookmarks).distinct.select(:id, :first_name, :last_name, :email).order(:first_name, :last_name)
    @available_talents = TalentProfile.joins(:talent_bookmarks).includes(:user).distinct.order('users.first_name, users.last_name')

    # Paginate results
    @pagy, @talent_bookmarks = pagy(@talent_bookmarks, limit: @page_size)

    # Stats for dashboard
    @stats = {
      total: TalentBookmark.count,
      unique_scouts: TalentBookmark.distinct.count(:user_id),
      unique_talents: TalentBookmark.distinct.count(:talent_profile_id),
      avg_bookmarks_per_scout: calculate_avg_bookmarks_per_scout,
      most_bookmarked_talent: find_most_bookmarked_talent
    }
  end

  def show
  end

  def destroy
    scout_name = @talent_bookmark.user.name.full
    talent_name = @talent_bookmark.talent_profile.user.name.full
    
    @talent_bookmark.destroy
    redirect_to super_admin_admin_talent_bookmarks_path,
                notice: "Removed bookmark: #{scout_name} → #{talent_name}."
  end

  # Bulk operations
  def bulk_update
    bookmark_ids = params[:talent_bookmark_ids]
    action = params[:bulk_action]

    return redirect_to super_admin_admin_talent_bookmarks_path, alert: 'No bookmarks selected.' if bookmark_ids.blank?

    bookmarks = TalentBookmark.where(id: bookmark_ids)

    case action
    when 'delete'
      count = bookmarks.count
      bookmarks.destroy_all
      redirect_to super_admin_admin_talent_bookmarks_path, notice: "#{count} talent bookmarks deleted successfully."
    else
      redirect_to super_admin_admin_talent_bookmarks_path, alert: 'Invalid bulk action.'
    end
  end

  protected

  def resource_name
    'talent_bookmarks'
  end

  private

  def set_talent_bookmark
    @talent_bookmark = TalentBookmark.find(params[:id])
  end

  def collection_for_export
    @talent_bookmarks_for_export || @talent_bookmarks
  end

  def calculate_avg_bookmarks_per_scout
    total_bookmarks = TalentBookmark.count
    unique_scouts = TalentBookmark.distinct.count(:user_id)
    return 0 if unique_scouts.zero?
    (total_bookmarks.to_f / unique_scouts).round(1)
  end

  def find_most_bookmarked_talent
    most_bookmarked = TalentBookmark.group(:talent_profile_id)
                                   .count
                                   .max_by { |_, count| count }
    
    return 'N/A' if most_bookmarked.nil?
    
    talent_profile = TalentProfile.find(most_bookmarked[0])
    "#{talent_profile.user.name.full} (#{most_bookmarked[1]} bookmarks)"
  rescue
    'N/A'
  end

  # CSV Export methods
  def generate_talent_bookmarks_csv
    CSV.generate(headers: true) do |csv|
      csv << [
        'ID',
        'Scout Name',
        'Scout Email',
        'Talent Name',
        'Talent Email',
        'Talent Headline',
        'Talent Skills',
        'Talent Location',
        'Talent Availability',
        'Bookmarked At',
        'Created At',
        'Updated At'
      ]

      collection_for_export.includes(:user, talent_profile: :user).find_each do |bookmark|
        csv << [
          bookmark.id,
          bookmark.user.name.full,
          bookmark.user.email,
          bookmark.talent_profile.user.name.full,
          bookmark.talent_profile.user.email,
          bookmark.talent_profile.headline,
          bookmark.talent_profile.skills&.join(', '),
          bookmark.talent_profile.location,
          bookmark.talent_profile.availability_status&.humanize,
          bookmark.created_at.strftime('%Y-%m-%d %H:%M'),
          bookmark.created_at.strftime('%Y-%m-%d %H:%M'),
          bookmark.updated_at.strftime('%Y-%m-%d %H:%M')
        ]
      end
    end
  end
end
