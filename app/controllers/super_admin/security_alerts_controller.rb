module SuperAdmin
  class SecurityAlertsController < SuperAdmin::BaseController
    before_action :set_security_alert, only: %i[show resolve]

    def index
      @security_alerts = SecurityAlert.includes(:user, :resolved_by).recent

      # Apply filters
      if params[:status].present? && params[:status] != 'all'
        case params[:status]
        when 'unresolved'
          @security_alerts = @security_alerts.unresolved
        when 'resolved'
          @security_alerts = @security_alerts.resolved
        end
      end

      if params[:severity].present? && params[:severity] != 'all'
        @security_alerts = @security_alerts.where(severity: params[:severity])
      end

      if params[:alert_type].present? && params[:alert_type] != 'all'
        @security_alerts = @security_alerts.where(alert_type: params[:alert_type])
      end

      if params[:user_filter].present? && params[:user_filter] != 'all'
        @security_alerts = @security_alerts.where(user_id: params[:user_filter])
      end

      if params[:date_from].present?
        @security_alerts = @security_alerts.where('created_at >= ?', Date.parse(params[:date_from]))
      end

      if params[:date_to].present?
        @security_alerts = @security_alerts.where('created_at <= ?', Date.parse(params[:date_to]).end_of_day)
      end

      # Get filter options
      @available_severities = SecurityAlert::SEVERITIES.map { |severity| [severity.humanize, severity] }
      @available_alert_types = SecurityAlert::ALERT_TYPES.map { |type| [type.humanize.titleize, type] }
      @available_users = User.joins(:security_alerts)
                            .distinct
                            .select(:id, :first_name, :last_name, :email)
                            .order(:first_name, :last_name)

      # Paginate results
      @pagy, @security_alerts = pagy(@security_alerts.order(created_at: :desc), limit: 25)

      # Get summary statistics
      @stats = {
        total: SecurityAlert.count,
        unresolved: SecurityAlert.unresolved.count,
        critical: SecurityAlert.critical.count,
        high_priority: SecurityAlert.high_priority.count,
        recent: SecurityAlert.where('created_at > ?', 24.hours.ago).count
      }
    end

    def show
      @user = @security_alert.user
    end

    def resolve
      @security_alert.resolve!(Current.user)
      
      AdminAuditLog.log_action(
        action: 'resolve_security_alert',
        controller: controller_path,
        resource: @security_alert,
        changes: { 'resolved_at' => [nil, @security_alert.resolved_at] },
        admin_user: Current.user
      )

      redirect_to super_admin_security_alert_path(@security_alert), 
                  notice: 'Security alert has been resolved.'
    end

    private

    def set_security_alert
      @security_alert = SecurityAlert.find(params[:id])
    end
  end
end
