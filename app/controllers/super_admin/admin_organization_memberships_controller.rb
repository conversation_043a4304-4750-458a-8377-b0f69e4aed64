class SuperAdmin::AdminOrganizationMembershipsController < SuperAdmin::AdminBaseController
  include SuperAdmin::CsvExportable
  include AdminBulkOperations

  before_action :set_organization_membership, only: [:show, :edit, :update, :destroy]

  def index
    @organization_memberships = OrganizationMembership.includes(:user, :organization)
                                                     .order(created_at: :desc)

    # Apply search
    if params[:search].present?
      search_term = "%#{params[:search]}%"
      @organization_memberships = @organization_memberships.joins(:user, :organization)
                                                          .where(
                                                            "users.first_name ILIKE ? OR users.last_name ILIKE ? OR users.email ILIKE ? OR organizations.name ILIKE ?",
                                                            search_term, search_term, search_term, search_term
                                                          )
    end

    # Apply filters
    if params[:org_role].present? && params[:org_role] != 'all'
      @organization_memberships = @organization_memberships.where(org_role: params[:org_role])
    end

    if params[:organization_id].present? && params[:organization_id] != 'all'
      @organization_memberships = @organization_memberships.where(organization_id: params[:organization_id])
    end

    if params[:date_range].present? && params[:date_range] != 'all'
      case params[:date_range]
      when 'today'
        @organization_memberships = @organization_memberships.where(created_at: Date.current.beginning_of_day..Date.current.end_of_day)
      when 'week'
        @organization_memberships = @organization_memberships.where(created_at: 1.week.ago..Time.current)
      when 'month'
        @organization_memberships = @organization_memberships.where(created_at: 1.month.ago..Time.current)
      end
    end

    # Get filter options
    @available_roles = OrganizationMembership.org_roles.keys.map { |role| [role.humanize, role] }
    @available_organizations = Organization.joins(:organization_memberships).distinct.select(:id, :name).order(:name)

    # Paginate results
    @pagy, @organization_memberships = pagy(@organization_memberships, limit: @page_size)

    # Stats for dashboard
    @stats = {
      total: OrganizationMembership.count,
      owners: OrganizationMembership.owner.count,
      admins: OrganizationMembership.admin.count,
      members: OrganizationMembership.member.count,
      organizations_with_multiple_members: calculate_multi_member_orgs
    }
  end

  def show
  end

  def edit
  end

  def update
    if @organization_membership.update(organization_membership_params)
      redirect_to super_admin_admin_organization_membership_path(@organization_membership),
                  notice: 'Organization membership was successfully updated.'
    else
      render :edit, status: :unprocessable_entity
    end
  end

  def destroy
    organization_name = @organization_membership.organization.name
    user_name = @organization_membership.user.name.full
    
    @organization_membership.destroy
    redirect_to super_admin_admin_organization_memberships_path,
                notice: "Removed #{user_name} from #{organization_name}."
  end

  # Bulk operations
  def bulk_update
    membership_ids = params[:organization_membership_ids]
    action = params[:bulk_action]

    return redirect_to super_admin_admin_organization_memberships_path, alert: 'No memberships selected.' if membership_ids.blank?

    memberships = OrganizationMembership.where(id: membership_ids)

    case action
    when 'delete'
      count = memberships.count
      memberships.destroy_all
      redirect_to super_admin_admin_organization_memberships_path, notice: "#{count} organization memberships deleted successfully."
    when 'promote_to_admin'
      memberships.update_all(org_role: :admin)
      redirect_to super_admin_admin_organization_memberships_path, notice: "#{memberships.count} members promoted to admin."
    when 'demote_to_member'
      memberships.update_all(org_role: :member)
      redirect_to super_admin_admin_organization_memberships_path, notice: "#{memberships.count} members demoted to member."
    when 'promote_to_owner'
      memberships.update_all(org_role: :owner)
      redirect_to super_admin_admin_organization_memberships_path, notice: "#{memberships.count} members promoted to owner."
    else
      redirect_to super_admin_admin_organization_memberships_path, alert: 'Invalid bulk action.'
    end
  end

  protected

  def resource_name
    'organization_memberships'
  end

  private

  def set_organization_membership
    @organization_membership = OrganizationMembership.find(params[:id])
  end

  def organization_membership_params
    params.require(:organization_membership).permit(:org_role)
  end

  def collection_for_export
    @organization_memberships_for_export || @organization_memberships
  end

  def calculate_multi_member_orgs
    Organization.joins(:organization_memberships)
                .group('organizations.id')
                .having('COUNT(organization_memberships.id) > 1')
                .count
                .size
  end

  # CSV Export methods
  def generate_organization_memberships_csv
    CSV.generate(headers: true) do |csv|
      csv << [
        'ID',
        'User Name',
        'User Email',
        'Organization',
        'Role',
        'Joined At',
        'Created At',
        'Updated At'
      ]

      collection_for_export.includes(:user, :organization).find_each do |membership|
        csv << [
          membership.id,
          membership.user.name.full,
          membership.user.email,
          membership.organization.name,
          membership.org_role.humanize,
          membership.created_at.strftime('%Y-%m-%d %H:%M'),
          membership.created_at.strftime('%Y-%m-%d %H:%M'),
          membership.updated_at.strftime('%Y-%m-%d %H:%M')
        ]
      end
    end
  end
end
