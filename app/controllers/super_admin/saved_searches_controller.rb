class SuperAdmin::SavedSearchesController < SuperAdmin::BaseController
  before_action :set_saved_search, only: [:show, :destroy]

  def index
    @saved_searches = Current.user.saved_searches
                                  .includes(:user)
                                  .order(:resource_type, :name)
    
    # Group by resource type for better organization
    @searches_by_resource = @saved_searches.group_by(&:resource_type)
  end

  def show
    # Redirect to the appropriate admin page with the saved search applied
    redirect_path = case @saved_search.resource_type
                   when 'AdminUser'
                     super_admin_admin_users_path
                   when 'AdminJob'
                     super_admin_admin_jobs_path
                   when 'AdminChatRequest'
                     super_admin_admin_chat_requests_path
                   when 'AdminConversation'
                     super_admin_admin_conversations_path
                   when 'AdminAuditLog'
                     super_admin_admin_audit_logs_path
                   else
                     super_admin_saved_searches_path
                   end

    if redirect_path != super_admin_saved_searches_path
      # Apply the saved search parameters
      params_hash = @saved_search.parsed_params
      params_hash[:saved_search_id] = @saved_search.id
      
      redirect_to redirect_path, params: params_hash
    else
      redirect_to super_admin_saved_searches_path, 
                  alert: "Unknown resource type: #{@saved_search.resource_type}"
    end
  end

  def destroy
    resource_type = @saved_search.resource_display_name
    search_name = @saved_search.name
    
    if @saved_search.destroy
      redirect_to super_admin_saved_searches_path, 
                  notice: "Saved search '#{search_name}' for #{resource_type} was deleted."
    else
      redirect_to super_admin_saved_searches_path, 
                  alert: "Failed to delete saved search."
    end
  end

  private

  def set_saved_search
    @saved_search = Current.user.saved_searches.find(params[:id])
  end
end
