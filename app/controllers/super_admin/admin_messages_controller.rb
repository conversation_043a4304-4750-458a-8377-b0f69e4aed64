require 'csv'

module SuperAdmin
  class AdminMessagesController < SuperAdmin::AdminBaseController
    include Pagy::Backend
    include SuperAdmin::CsvExportable

    def index
      @messages = Message.includes(:user, :conversation)
      
      apply_search
      apply_filters
      apply_sorting

      respond_to do |format|
        format.html do
          @pagy, @messages = pagy(@messages, items: params[:per_page] || 25)
        end
        format.csv do
          @messages_for_export = @messages
          send_csv_data
        end
      end
    end

    def show
      @message = Message.includes(:user, :conversation).find(params[:id])
      @conversation = @message.conversation
      @related_messages = @conversation.messages
                                     .includes(:user)
                                     .where.not(id: @message.id)
                                     .order(:created_at)
                                     .limit(10)
    end

    private

    def apply_search
      return unless params[:search].present?

      search_term = "%#{params[:search]}%"
      @messages = @messages.joins(:user, :conversation)
                          .where(
                            "messages.body ILIKE ? OR users.first_name ILIKE ? OR users.last_name ILIKE ? OR users.email ILIKE ?",
                            search_term, search_term, search_term, search_term
                          )
    end

    def apply_filters
      # Filter by read status
      case params[:read_status]
      when 'read'
        @messages = @messages.where.not(read_at: nil)
      when 'unread'
        @messages = @messages.where(read_at: nil)
      end

      # Filter by conversation type
      case params[:conversation_type]
      when 'with_job'
        @messages = @messages.joins(:conversation).where.not(conversations: { job_id: nil })
      when 'without_job'
        @messages = @messages.joins(:conversation).where(conversations: { job_id: nil })
      end

      # Filter by date range
      if params[:date_from].present?
        @messages = @messages.where('messages.created_at >= ?', Date.parse(params[:date_from]))
      end

      if params[:date_to].present?
        @messages = @messages.where('messages.created_at <= ?', Date.parse(params[:date_to]).end_of_day)
      end
    end

    def apply_sorting
      case params[:sort]
      when 'oldest'
        @messages = @messages.order(:created_at)
      when 'user_name'
        @messages = @messages.joins(:user).order('users.first_name, users.last_name')
      when 'conversation'
        @messages = @messages.joins(:conversation).order('conversations.updated_at DESC')
      else
        @messages = @messages.order(created_at: :desc)
      end
    end

    def collection_for_export
      @messages_for_export || @messages
    end
  end
end
