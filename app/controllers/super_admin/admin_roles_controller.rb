require 'csv'

module SuperAdmin
  class AdminRolesController < SuperAdmin::AdminBaseController
    include SuperAdmin::CsvExportable
    before_action :require_superadmin
    before_action :set_user, only: [:show, :edit, :update, :destroy]

    def index
      @q = params[:q]
      @role_filter = params[:role_filter]
      @sort_by = params[:sort_by] || 'created_at'
      @sort_direction = params[:sort_direction] || 'desc'

      # Start with all users who have admin roles
      users = User.joins(:roles).includes(:roles, :organizations).distinct

      # Apply search filter
      if @q.present?
        users = users.where(
          "first_name ILIKE ? OR last_name ILIKE ? OR email ILIKE ? OR CAST(users.id AS TEXT) ILIKE ?",
          "%#{@q}%", "%#{@q}%", "%#{@q}%", "%#{@q}%"
        )
      end

      # Apply role filter
      if @role_filter.present? && @role_filter != 'all'
        users = users.where(roles: { name: @role_filter })
      end

      # Apply sorting
      case @sort_by
      when 'name'
        users = users.order("first_name #{@sort_direction}, last_name #{@sort_direction}")
      when 'email'
        users = users.order("email #{@sort_direction}")
      when 'created_at'
        users = users.order("users.created_at #{@sort_direction}")
      else
        users = users.order("users.created_at #{@sort_direction}")
      end

      # Get filter options
      @available_roles = Role.pluck(:name).sort
      @admin_roles = User.admin_roles_for_select

      # Paginate results
      respond_to do |format|
        format.html do
          @pagy, @users = pagy(users, limit: 20)
        end
        format.csv do
          @users_for_export = users
          send_csv_data
        end
      end
    end

    def show
      @user_roles = @user.roles.order(:name)
      @available_roles = Role.where.not(id: @user.role_ids).order(:name)
    end

    def edit
      @available_roles = Role.order(:name)
      @user_roles = @user.roles.pluck(:id)
    end

    def update
      role_ids = params[:user][:role_ids]&.reject(&:blank?) || []

      begin
        # Track changes for audit log
        old_roles = @user.roles.pluck(:name).sort
        new_roles = Role.where(id: role_ids).pluck(:name).sort

        ActiveRecord::Base.transaction do
          # Remove all current roles
          @user.user_roles.destroy_all

          # Add new roles
          role_ids.each do |role_id|
            role = Role.find(role_id)
            @user.user_roles.create!(role: role)
          end
        end

        # Log the role changes
        if old_roles != new_roles
          AdminAuditLog.log_action(
            action: 'update',
            controller: controller_path,
            resource: @user,
            changes: {
              'roles' => [old_roles, new_roles],
              'user' => [nil, @user.email]
            },
            admin_user: Current.user
          )
        end

        redirect_to super_admin_admin_role_path(@user),
                    notice: 'User roles updated successfully.'
      rescue ActiveRecord::RecordNotFound
        redirect_to edit_super_admin_admin_role_path(@user), 
                    alert: 'Invalid role selected.'
      rescue => e
        Rails.logger.error "Error updating user roles: #{e.message}"
        redirect_to edit_super_admin_admin_role_path(@user), 
                    alert: 'Failed to update user roles.'
      end
    end

    def new
      @user = User.new
      @available_roles = Role.order(:name)
    end

    def create
      @user = User.find(params[:user_id])
      role_ids = params[:role_ids]&.reject(&:blank?) || []
      
      begin
        assigned_roles = []
        ActiveRecord::Base.transaction do
          role_ids.each do |role_id|
            role = Role.find(role_id)
            user_role = @user.user_roles.find_or_create_by!(role: role)
            assigned_roles << role.name if user_role.persisted?
          end
        end

        # Log the role assignments
        if assigned_roles.any?
          AdminAuditLog.log_action(
            action: 'assign_role',
            controller: controller_path,
            resource: @user,
            changes: {
              'roles_assigned' => [nil, assigned_roles],
              'user' => [nil, @user.email]
            },
            admin_user: Current.user
          )
        end

        redirect_to super_admin_admin_role_path(@user),
                    notice: 'Roles assigned successfully.'
      rescue ActiveRecord::RecordNotFound
        redirect_to super_admin_admin_roles_path, 
                    alert: 'Invalid user or role selected.'
      rescue => e
        Rails.logger.error "Error assigning roles: #{e.message}"
        redirect_to super_admin_admin_roles_path, 
                    alert: 'Failed to assign roles.'
      end
    end

    def destroy
      role = Role.find(params[:role_id])
      user_role = @user.user_roles.find_by(role: role)
      
      if user_role
        user_role.destroy

        # Log the role removal
        AdminAuditLog.log_action(
          action: 'remove_role',
          controller: controller_path,
          resource: @user,
          changes: {
            'role_removed' => [role.name, nil],
            'user' => [nil, @user.email]
          },
          admin_user: Current.user
        )

        redirect_to super_admin_admin_role_path(@user),
                    notice: 'Role removed successfully.'
      else
        redirect_to super_admin_admin_role_path(@user),
                    alert: 'Role not found for this user.'
      end
    rescue ActiveRecord::RecordNotFound
      redirect_to super_admin_admin_roles_path, 
                  alert: 'Role not found.'
    end

    private

    def set_user
      @user = User.find(params[:id])
    end

    def resource_name
      'roles'
    end

    def collection_for_export
      @users_for_export || @users
    end
  end
end
