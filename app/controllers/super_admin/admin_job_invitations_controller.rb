class SuperAdmin::AdminJobInvitationsController < SuperAdmin::AdminBaseController
  include SuperAdmin::CsvExportable
  include AdminBulkOperations

  before_action :set_job_invitation, only: [:show, :edit, :update, :destroy]

  def index
    @job_invitations = JobInvitation.includes(:job, :user, :talent_profile, job: :organization)
                                   .order(created_at: :desc)

    # Apply search
    if params[:search].present?
      search_term = "%#{params[:search]}%"
      @job_invitations = @job_invitations.joins(:job, :user, :talent_profile)
                                        .where(
                                          "jobs.title ILIKE ? OR users.first_name ILIKE ? OR users.last_name ILIKE ? OR users.email ILIKE ? OR job_invitations.invitation_letter ILIKE ?",
                                          search_term, search_term, search_term, search_term, search_term
                                        )
    end

    # Apply filters
    if params[:status].present? && params[:status] != 'all'
      @job_invitations = @job_invitations.where(status: params[:status])
    end

    if params[:job_id].present? && params[:job_id] != 'all'
      @job_invitations = @job_invitations.where(job_id: params[:job_id])
    end

    if params[:organization_id].present? && params[:organization_id] != 'all'
      @job_invitations = @job_invitations.joins(:job).where(jobs: { organization_id: params[:organization_id] })
    end

    if params[:date_range].present? && params[:date_range] != 'all'
      case params[:date_range]
      when 'today'
        @job_invitations = @job_invitations.where(created_at: Date.current.beginning_of_day..Date.current.end_of_day)
      when 'week'
        @job_invitations = @job_invitations.where(created_at: 1.week.ago..Time.current)
      when 'month'
        @job_invitations = @job_invitations.where(created_at: 1.month.ago..Time.current)
      end
    end

    # Get filter options
    @available_statuses = JobInvitation.statuses.keys.map { |status| [status.humanize, status] }
    @available_jobs = Job.joins(:job_invitations).distinct.select(:id, :title).order(:title)
    @available_organizations = Organization.joins(jobs: :job_invitations).distinct.select(:id, :name).order(:name)

    # Paginate results
    @pagy, @job_invitations = pagy(@job_invitations, limit: @page_size)

    # Stats for dashboard
    @stats = {
      total: JobInvitation.count,
      pending: JobInvitation.pending.count,
      accepted: JobInvitation.accepted.count,
      ignored: JobInvitation.ignored.count,
      acceptance_rate: calculate_acceptance_rate
    }
  end

  def show
    @job_application = @job_invitation.job_application
  end

  def edit
  end

  def update
    if @job_invitation.update(job_invitation_params)
      redirect_to super_admin_admin_job_invitation_path(@job_invitation),
                  notice: 'Job invitation was successfully updated.'
    else
      render :edit, status: :unprocessable_entity
    end
  end

  def destroy
    @job_invitation.destroy
    redirect_to super_admin_admin_job_invitations_path,
                notice: 'Job invitation was successfully deleted.'
  end

  # Bulk operations
  def bulk_update
    job_invitation_ids = params[:job_invitation_ids]
    action = params[:bulk_action]

    return redirect_to super_admin_admin_job_invitations_path, alert: 'No invitations selected.' if job_invitation_ids.blank?

    job_invitations = JobInvitation.where(id: job_invitation_ids)

    case action
    when 'delete'
      count = job_invitations.count
      job_invitations.destroy_all
      redirect_to super_admin_admin_job_invitations_path, notice: "#{count} job invitations deleted successfully."
    when 'mark_pending'
      job_invitations.update_all(status: :pending, accepted_at: nil, ignored_at: nil)
      redirect_to super_admin_admin_job_invitations_path, notice: "#{job_invitations.count} job invitations marked as pending."
    when 'mark_accepted'
      job_invitations.update_all(status: :accepted, accepted_at: Time.current)
      redirect_to super_admin_admin_job_invitations_path, notice: "#{job_invitations.count} job invitations marked as accepted."
    when 'mark_ignored'
      job_invitations.update_all(status: :ignored, ignored_at: Time.current)
      redirect_to super_admin_admin_job_invitations_path, notice: "#{job_invitations.count} job invitations marked as ignored."
    else
      redirect_to super_admin_admin_job_invitations_path, alert: 'Invalid bulk action.'
    end
  end

  protected

  def resource_name
    'job_invitations'
  end

  private

  def set_job_invitation
    @job_invitation = JobInvitation.find(params[:id])
  end

  def job_invitation_params
    params.require(:job_invitation).permit(:invitation_letter, :status)
  end

  def collection_for_export
    @job_invitations_for_export || @job_invitations
  end

  def calculate_acceptance_rate
    total = JobInvitation.where.not(status: :pending).count
    return 0 if total.zero?
    
    accepted = JobInvitation.accepted.count
    ((accepted.to_f / total) * 100).round(1)
  end

  # CSV Export methods
  def generate_job_invitations_csv
    CSV.generate(headers: true) do |csv|
      csv << [
        'ID',
        'Job Title',
        'Organization',
        'Talent Name',
        'Talent Email',
        'Status',
        'Invited At',
        'Accepted At',
        'Ignored At',
        'Invitation Letter',
        'Created At'
      ]

      collection_for_export.includes(:job, :user, :talent_profile, job: :organization).find_each do |invitation|
        csv << [
          invitation.id,
          invitation.job.title,
          invitation.job.organization.name,
          invitation.user.name.full,
          invitation.user.email,
          invitation.status.humanize,
          invitation.invited_at&.strftime('%Y-%m-%d %H:%M'),
          invitation.accepted_at&.strftime('%Y-%m-%d %H:%M'),
          invitation.ignored_at&.strftime('%Y-%m-%d %H:%M'),
          invitation.invitation_letter&.truncate(100),
          invitation.created_at.strftime('%Y-%m-%d %H:%M')
        ]
      end
    end
  end
end
