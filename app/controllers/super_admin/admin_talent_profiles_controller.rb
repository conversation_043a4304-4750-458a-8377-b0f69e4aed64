require 'csv'

module SuperAdmin
  class AdminTalentProfilesController < SuperAdmin::AdminBaseController
    include Pagy::Backend
    include SuperAdmin::CsvExportable

    def index
      @talent_profiles = TalentProfile.includes(:user, :talent_bookmarks)
      
      apply_search
      apply_filters
      apply_sorting

      respond_to do |format|
        format.html do
          @pagy, @talent_profiles = pagy(@talent_profiles, items: params[:per_page] || 25)
        end
        format.csv do
          @talent_profiles_for_export = @talent_profiles
          send_csv_data
        end
      end
    end

    def show
      @talent_profile = TalentProfile.includes(:user, :talent_bookmarks, :talent_notes).find(params[:id])
      @user = @talent_profile.user
      @recent_applications = @user.job_applications
                                  .includes(:job)
                                  .order(created_at: :desc)
                                  .limit(5)
      @bookmarked_by_count = @talent_profile.talent_bookmarks.count
      @notes_count = @talent_profile.talent_notes.count
    end

    private

    def apply_search
      return unless params[:search].present?

      search_term = "%#{params[:search]}%"
      @talent_profiles = @talent_profiles.joins(:user)
                                        .where(
                                          "users.first_name ILIKE ? OR users.last_name ILIKE ? OR users.email ILIKE ? OR talent_profiles.bio ILIKE ? OR talent_profiles.headline ILIKE ? OR talent_profiles.location ILIKE ?",
                                          search_term, search_term, search_term, search_term, search_term, search_term
                                        )
    end

    def apply_filters
      # Filter by availability status
      if params[:availability_status].present?
        @talent_profiles = @talent_profiles.where(availability_status: params[:availability_status])
      end

      # Filter by pricing model
      if params[:pricing_model].present?
        @talent_profiles = @talent_profiles.where(pricing_model: params[:pricing_model])
      end

      # Filter by location preference
      if params[:location_preference].present?
        @talent_profiles = @talent_profiles.where(location_preference: params[:location_preference])
      end

      # Filter by agency status
      case params[:agency_status]
      when 'agency'
        @talent_profiles = @talent_profiles.where(is_agency: true)
      when 'individual'
        @talent_profiles = @talent_profiles.where(is_agency: false)
      end

      # Filter by premium status
      case params[:premium_status]
      when 'premium'
        @talent_profiles = @talent_profiles.where(is_premium: true)
      when 'standard'
        @talent_profiles = @talent_profiles.where(is_premium: false)
      end

      # Filter by profile completeness
      case params[:profile_completeness]
      when 'complete'
        @talent_profiles = @talent_profiles.where.not(bio: [nil, ''])
                                          .where.not(headline: [nil, ''])
                                          .where.not(location: [nil, ''])
      when 'incomplete'
        @talent_profiles = @talent_profiles.where(
          'bio IS NULL OR bio = ? OR headline IS NULL OR headline = ? OR location IS NULL OR location = ?',
          '', '', ''
        )
      end

      # Filter by skills (if specific skill is searched)
      if params[:skill].present?
        @talent_profiles = @talent_profiles.where("? = ANY(skills)", params[:skill])
      end

      # Filter by date range
      if params[:date_from].present?
        @talent_profiles = @talent_profiles.where('talent_profiles.created_at >= ?', Date.parse(params[:date_from]))
      end

      if params[:date_to].present?
        @talent_profiles = @talent_profiles.where('talent_profiles.created_at <= ?', Date.parse(params[:date_to]).end_of_day)
      end
    end

    def apply_sorting
      case params[:sort]
      when 'name'
        @talent_profiles = @talent_profiles.joins(:user).order('users.first_name, users.last_name')
      when 'location'
        @talent_profiles = @talent_profiles.order(:location)
      when 'availability'
        @talent_profiles = @talent_profiles.order(:availability_status)
      when 'pricing'
        @talent_profiles = @talent_profiles.order(:price_range_min)
      when 'oldest'
        @talent_profiles = @talent_profiles.order(:created_at)
      else
        @talent_profiles = @talent_profiles.order(created_at: :desc)
      end
    end

    def collection_for_export
      @talent_profiles_for_export || @talent_profiles
    end
  end
end
