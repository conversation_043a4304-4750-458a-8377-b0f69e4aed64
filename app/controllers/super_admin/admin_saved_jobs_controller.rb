class SuperAdmin::AdminSavedJobsController < SuperAdmin::AdminBaseController
  include SuperAdmin::CsvExportable
  include AdminBulkOperations

  before_action :set_saved_job, only: [:show, :destroy]

  def index
    @saved_jobs = SavedJob.includes(:user, :job => :organization)
                          .order(created_at: :desc)

    # Apply search
    if params[:search].present?
      search_term = "%#{params[:search]}%"
      @saved_jobs = @saved_jobs.joins(:user, job: :organization)
                              .where(
                                "users.first_name ILIKE ? OR users.last_name ILIKE ? OR users.email ILIKE ? OR jobs.title ILIKE ? OR organizations.name ILIKE ?",
                                search_term, search_term, search_term, search_term, search_term
                              )
    end

    # Apply filters
    if params[:user_id].present? && params[:user_id] != 'all'
      @saved_jobs = @saved_jobs.where(user_id: params[:user_id])
    end

    if params[:job_id].present? && params[:job_id] != 'all'
      @saved_jobs = @saved_jobs.where(job_id: params[:job_id])
    end

    if params[:job_category].present? && params[:job_category] != 'all'
      @saved_jobs = @saved_jobs.joins(:job).where(jobs: { job_category: params[:job_category] })
    end

    if params[:job_status].present? && params[:job_status] != 'all'
      @saved_jobs = @saved_jobs.joins(:job).where(jobs: { status: params[:job_status] })
    end

    if params[:date_range].present? && params[:date_range] != 'all'
      case params[:date_range]
      when 'today'
        @saved_jobs = @saved_jobs.where(created_at: Date.current.beginning_of_day..Date.current.end_of_day)
      when 'week'
        @saved_jobs = @saved_jobs.where(created_at: 1.week.ago..Time.current)
      when 'month'
        @saved_jobs = @saved_jobs.where(created_at: 1.month.ago..Time.current)
      end
    end

    # Get filter options
    @available_users = User.joins(:saved_jobs).distinct.select(:id, :first_name, :last_name, :email).order(:first_name, :last_name)
    @available_jobs = Job.joins(:saved_jobs).includes(:organization).distinct.order(:title)
    @job_categories = Job.job_categories.keys
    @job_statuses = Job.statuses.keys

    # Paginate results
    @pagy, @saved_jobs = pagy(@saved_jobs, limit: @page_size)

    # Stats for dashboard
    @stats = {
      total: SavedJob.count,
      unique_users: SavedJob.distinct.count(:user_id),
      unique_jobs: SavedJob.distinct.count(:job_id),
      avg_saves_per_user: calculate_avg_saves_per_user,
      most_saved_job: find_most_saved_job,
      saves_this_week: SavedJob.where(created_at: 1.week.ago..Time.current).count
    }
  end

  def show
  end

  def destroy
    user_name = @saved_job.user.name.full
    job_title = @saved_job.job.title
    
    @saved_job.destroy
    redirect_to super_admin_admin_saved_jobs_path,
                notice: "Removed saved job: #{user_name} → #{job_title}."
  end

  # Bulk operations
  def bulk_update
    saved_job_ids = params[:saved_job_ids]
    action = params[:bulk_action]

    return redirect_to super_admin_admin_saved_jobs_path, alert: 'No saved jobs selected.' if saved_job_ids.blank?

    saved_jobs = SavedJob.where(id: saved_job_ids)

    case action
    when 'delete'
      count = saved_jobs.count
      saved_jobs.destroy_all
      redirect_to super_admin_admin_saved_jobs_path, notice: "#{count} saved jobs deleted successfully."
    else
      redirect_to super_admin_admin_saved_jobs_path, alert: 'Invalid bulk action.'
    end
  end

  protected

  def resource_name
    'saved_jobs'
  end

  private

  def set_saved_job
    @saved_job = SavedJob.find(params[:id])
  end

  def collection_for_export
    @saved_jobs_for_export || @saved_jobs
  end

  def calculate_avg_saves_per_user
    total_saves = SavedJob.count
    unique_users = SavedJob.distinct.count(:user_id)
    return 0 if unique_users.zero?
    (total_saves.to_f / unique_users).round(1)
  end

  def find_most_saved_job
    most_saved = SavedJob.group(:job_id)
                         .count
                         .max_by { |_, count| count }
    
    return 'N/A' if most_saved.nil?
    
    job = Job.find(most_saved[0])
    "#{job.title} (#{most_saved[1]} saves)"
  rescue
    'N/A'
  end

  # CSV Export methods
  def generate_saved_jobs_csv
    CSV.generate(headers: true) do |csv|
      csv << [
        'ID',
        'User Name',
        'User Email',
        'Job Title',
        'Job Category',
        'Job Status',
        'Organization',
        'Job Budget Range',
        'Job Platform',
        'Job Outcome',
        'Job Application Deadline',
        'Saved At',
        'Created At',
        'Updated At'
      ]

      collection_for_export.includes(:user, job: :organization).find_each do |saved_job|
        csv << [
          saved_job.id,
          saved_job.user.name.full,
          saved_job.user.email,
          saved_job.job.title,
          saved_job.job.job_category&.humanize,
          saved_job.job.status&.humanize,
          saved_job.job.organization.name,
          saved_job.job.budget_range&.humanize,
          saved_job.job.platform&.humanize,
          saved_job.job.outcome&.humanize,
          saved_job.job.application_deadline&.strftime('%Y-%m-%d'),
          saved_job.created_at.strftime('%Y-%m-%d %H:%M'),
          saved_job.created_at.strftime('%Y-%m-%d %H:%M'),
          saved_job.updated_at.strftime('%Y-%m-%d %H:%M')
        ]
      end
    end
  end
end
