class SuperAdmin::AdminDashboardController < SuperAdmin::AdminBaseController
  # Dashboard doesn't require specific resource permissions, just admin access
  skip_before_action :check_resource_permissions

  def index
    AdminPerformanceMonitorService.monitor_query('Dashboard Load') do
      # Optimized stats using cached service
      @stats = AdminDashboardStatsService.generate_stats

      # Optimized recent activity using cached service with proper includes
      @recent_activity = AdminRecentActivityService.generate_recent_activity

      # Enhanced dashboard data
      @system_health = SystemHealthService.check_all
      @analytics = AdminAnalyticsService.generate_dashboard_analytics
      @recent_admin_actions =
        AdminAuditLog.includes(:admin_user).order(created_at: :desc).limit(10)
    end
  end
end
