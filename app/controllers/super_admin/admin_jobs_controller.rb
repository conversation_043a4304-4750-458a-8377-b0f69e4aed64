require 'csv'

class SuperAdmin::AdminJobsController < SuperAdmin::AdminBaseController
  include SuperAdmin::CsvExportable
  before_action :set_job, only: %i[show edit update]

  def index
    @jobs = Job.includes(:organization, :job_applications)

    # Apply search
    @jobs = apply_search(@jobs, %w[title description])

    # Apply filters
    @jobs = @jobs.where(status: params[:status]) if params[:status].present?
    @jobs = @jobs.where(job_category: params[:job_category]) if params[
      :job_category
    ].present?
    @jobs = @jobs.where(platform: params[:platform]) if params[:platform]
      .present?
    @jobs = @jobs.where(budget_range: params[:budget_range]) if params[
      :budget_range
    ].present?
    @jobs = @jobs.where(is_premium: true) if params[:is_premium] == 'true'
    @jobs = @jobs.where(is_premium: false) if params[:is_premium] == 'false'

    # Date filters
    if params[:created_after].present?
      @jobs = @jobs.where('created_at >= ?', Date.parse(params[:created_after]))
    end
    if params[:created_before].present?
      @jobs =
        @jobs.where('created_at <= ?', Date.parse(params[:created_before]))
    end

    # Apply sorting
    @jobs =
      apply_sorting(
        @jobs,
        %w[
          id
          title
          status
          job_category
          budget_range
          created_at
          published_at
          application_deadline
        ],
      )

    respond_to do |format|
      format.html do
        # Paginate for HTML only
        @pagy, @jobs = pagy(@jobs, items: @page_size)

        # Set up cached filter options for better performance
        @filter_options = AdminFilterOptionsService.job_filter_options
      end
      format.csv do
        # For CSV, we want all filtered results, not paginated
        @jobs_for_export = @jobs
        send_csv_data
      end
    end
  end

  def show
    @applications =
      @job.job_applications.includes(:user).order(created_at: :desc).limit(10)
    @invitations =
      @job
        .job_invitations
        .includes(:user, :talent_profile)
        .order(created_at: :desc)
        .limit(10) if defined?(JobInvitation)
    @stats = {
      applications_count: @job.job_applications.count,
      applications_by_status: @job.job_applications.group(:status).count,
      invitations_count: @job.job_invitations.count,
      invitations_by_status: @job.job_invitations.group(:status).count,
    }
  end

  def edit; end

  def update
    # Handle topics array conversion
    if params[:job][:topics].present?
      params[:job][:topics] =
        params[:job][:topics].split(',').map(&:strip).reject(&:blank?)
    end

    if @job.update(job_params)
      redirect_to super_admin_admin_job_path(@job),
                  notice: 'Job was successfully updated.'
    else
      render :edit, status: :unprocessable_entity
    end
  end

  private

  def set_job
    @job = Job.find(params[:id])
  end

  def job_params
    params
      .require(:job)
      .permit(
        :title,
        :description,
        :status,
        :job_category,
        :platform,
        :budget_range,
        :work_duration,
        :outcome,
        :payment_frequency,
        :involvement_level,
        :application_deadline,
        :expires_at,
        :is_premium,
        :business_challenge,
        :useful_info,
        :offer_summary,
        :target_audience_description,
        :emulated_brands_description,
        :client_count,
        :charge_per_client,
        # Category-specific fields
        :social_media_goal_type,
        :social_media_understands_risk_acknowledged,
        :newsletter_frequency,
        :newsletter_length,
        :lead_magnet_type,
        topics: [],
      )
  end

  def collection_for_export
    @jobs_for_export || @jobs
  end
end
