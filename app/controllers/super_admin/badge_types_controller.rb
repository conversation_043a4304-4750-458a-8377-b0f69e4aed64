# app/controllers/super_admin/badge_types_controller.rb
class SuperAdmin::BadgeTypesController < SuperAdmin::BaseController
  before_action :set_badge_type, only: %i[show edit update destroy]

  def index
    @badge_types = BadgeType.all.order(priority: :asc)
  end

  def show
    @assignments =
      @badge_type
        .badge_assignments
        .includes(:user, :admin)
        .order(assigned_at: :desc)
        .limit(50)
  end

  def new
    @badge_type = BadgeType.new
  end

  def create
    @badge_type = BadgeType.new(badge_type_params)

    if @badge_type.save
      AdminAuditLog.log_action(
        action: 'create',
        controller: 'super_admin/badge_types',
        resource: @badge_type,
        changes: {
          badge_type: @badge_type.attributes,
        },
        admin_user: Current.user,
      )

      redirect_to super_admin_badge_type_path(@badge_type),
                  notice: 'Badge type was successfully created.'
    else
      render :new, status: :unprocessable_entity
    end
  end

  def edit
    # The view will be rendered by default
  end

  def update
    if @badge_type.update(badge_type_params)
      changes = @badge_type.previous_changes.except('updated_at')

      if changes.present?
        AdminAuditLog.log_action(
          action: 'update',
          controller: 'super_admin/badge_types',
          resource: @badge_type,
          changes: changes,
          admin_user: Current.user,
        )
      end

      redirect_to super_admin_badge_type_path(@badge_type),
                  notice: 'Badge type was successfully updated.'
    else
      render :edit, status: :unprocessable_entity
    end
  end

  def destroy
    if @badge_type.badge_assignments.exists?
      redirect_to super_admin_badge_type_path(@badge_type),
                  alert: 'Cannot delete badge type that is assigned to users.'
    else
      @badge_type.destroy

      AdminAuditLog.log_action(
        action: 'destroy',
        controller: 'super_admin/badge_types',
        resource: @badge_type,
        changes: {
          badge_type: @badge_type.attributes,
        },
        admin_user: Current.user,
      )

      redirect_to super_admin_badge_types_path,
                  notice: 'Badge type was successfully deleted.'
    end
  end

  private

  def set_badge_type
    @badge_type = BadgeType.find(params[:id])
  end

  def badge_type_params
    params
      .require(:badge_type)
      .permit(
        :name,
        :description,
        :background_color,
        :text_color,
        :icon,
        :priority,
        :active,
      )
  end
end
