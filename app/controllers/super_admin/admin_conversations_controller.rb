require 'csv'

class SuperAdmin::AdminConversationsController < SuperAdmin::AdminBaseController
  include SuperAdmin::CsvExportable
  before_action :set_conversation, only: %i[show]

  def index
    @conversations = Conversation.includes(:users, :job, :messages)

    # Apply search
    @conversations = apply_search_with_associations(@conversations)

    # Apply filters
    if params[:with_job] == 'true'
      @conversations = @conversations.with_job
    elsif params[:with_job] == 'false'
      @conversations = @conversations.without_job
    end

    @conversations = @conversations.active if params[:active] == 'true'

    @conversations = @conversations.recent if params[:recent] == 'true'

    # Date filters
    if params[:created_after].present?
      @conversations =
        @conversations.where(
          'created_at >= ?',
          Date.parse(params[:created_after]),
        )
    end
    if params[:created_before].present?
      @conversations =
        @conversations.where(
          'created_at <= ?',
          Date.parse(params[:created_before]),
        )
    end

    # Apply sorting
    @conversations = apply_sorting(@conversations, %w[id created_at updated_at])

    respond_to do |format|
      format.html do
        # Paginate
        @pagy, @conversations = pagy(@conversations, items: @page_size)

        # Set up filter options
        @filter_options = {
          with_job: [['With Job', 'true'], ['Without Job', 'false']],
          active: [['Active (24h)', 'true']],
          recent: [['Recent (7 days)', 'true']],
        }
      end
      format.csv do
        @conversations_for_export = @conversations
        send_csv_data
      end
    end
  end

  def show
    @participants = @conversation.conversation_participants.includes(:user)
    @recent_messages =
      @conversation.messages.includes(:user).order(created_at: :desc).limit(20)
    @stats = {
      total_messages: @conversation.messages.count,
      unread_messages: @conversation.messages.unread.count,
      participants_count: @conversation.users.count,
      last_activity:
        @conversation.messages.maximum(:created_at) || @conversation.created_at,
    }
  end

  private

  def set_conversation
    @conversation = Conversation.find(params[:id])
  end

  def apply_search_with_associations(collection)
    return collection unless @search.present?

    search_term = "%#{@search}%"
    collection
      .joins(:users)
      .where(
        'users.first_name ILIKE :search OR users.last_name ILIKE :search OR users.email ILIKE :search',
        search: search_term,
      )
      .distinct
  end

  def collection_for_export
    @conversations_for_export || @conversations
  end
end
