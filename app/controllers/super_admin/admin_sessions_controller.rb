module SuperAdmin
  class AdminSessionsController < SuperAdmin::BaseController
    before_action :set_session, only: %i[show destroy unlock]

    def index
      @sessions = Session.includes(:user, :session_activities)
                         .joins(:user => :roles)
                         .distinct

      # Apply filters
      if params[:status].present? && params[:status] != 'all'
        case params[:status]
        when 'active'
          @sessions = @sessions.active
        when 'inactive'
          @sessions = @sessions.inactive
        when 'locked'
          @sessions = @sessions.locked
        end
      end

      if params[:user_filter].present? && params[:user_filter] != 'all'
        @sessions = @sessions.where(user_id: params[:user_filter])
      end

      if params[:ip_filter].present?
        @sessions = @sessions.where(ip_address: params[:ip_filter])
      end

      # Get filter options
      @available_users = User.joins(:roles, :sessions)
                            .distinct
                            .select(:id, :first_name, :last_name, :email)
                            .order(:first_name, :last_name)
      @available_ips = Session.joins(:user => :roles)
                             .distinct
                             .pluck(:ip_address)
                             .compact
                             .sort

      # Paginate results
      @pagy, @sessions = pagy(@sessions.order(created_at: :desc), limit: 25)

      # Get summary statistics
      @stats = {
        total: Session.joins(:user => :roles).distinct.count,
        active: Session.joins(:user => :roles).active.distinct.count,
        locked: Session.joins(:user => :roles).locked.distinct.count,
        with_warnings: Session.joins(:user => :roles).where('security_warnings_count > 0').distinct.count
      }
    end

    def show
      @user = @session.user
      @session_activities = @session.session_activities
                                   .order(created_at: :desc)
                                   .limit(50)
      @security_alerts = @user.security_alerts
                             .where('created_at >= ?', @session.created_at)
                             .order(created_at: :desc)
                             .limit(10)
    end

    def destroy
      @session.destroy
      
      AdminAuditLog.log_action(
        action: 'terminate_session',
        controller: controller_path,
        resource: @session.user,
        changes: { 'session_terminated' => [false, true] },
        admin_user: Current.user
      )

      redirect_to super_admin_admin_sessions_path, 
                  notice: 'Session has been terminated.'
    end

    def unlock
      @session.unlock!
      
      AdminAuditLog.log_action(
        action: 'unlock_session',
        controller: controller_path,
        resource: @session.user,
        changes: { 'session_unlocked' => [false, true] },
        admin_user: Current.user
      )

      redirect_to super_admin_admin_session_path(@session), 
                  notice: 'Session has been unlocked.'
    end

    private

    def set_session
      @session = Session.find(params[:id])
    end
  end
end
