class TestController < ApplicationController
  # Skip all authentication for test pages in development
  skip_before_action :authenticate, if: -> { Rails.env.development? }
  skip_before_action :check_verification, if: -> { Rails.env.development? }
  skip_before_action :check_impersonation_timeout,
                     if: -> { Rails.env.development? }
  skip_before_action :require_onboarding_completion,
                     if: -> { Rails.env.development? }
  skip_before_action :set_current_organization,
                     if: -> { Rails.env.development? }
  skip_before_action :require_organization_selected,
                     if: -> { Rails.env.development? }
  skip_before_action :check_impersonation_restrictions,
                     if: -> { Rails.env.development? }

  def badge_modal
    # This action renders the badge modal test page
    # No additional logic needed as the view handles everything
  end
end
