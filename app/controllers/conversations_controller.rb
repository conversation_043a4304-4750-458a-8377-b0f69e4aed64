class ConversationsController < ApplicationController
  before_action :authenticate
  before_action :set_conversation, only: [ :show ]

  def index
    @conversations = Current.user.conversations.includes(:users, :job)
  end

  def show
    @conversations = Conversation.joins(:conversation_participants)
                      .where(conversation_participants: { user_id: Current.user.id })
                      .includes(:users, :job)
                      .order(updated_at: :desc)
                      .distinct
    @messages = @conversation.messages.includes(:user)
    @message = Message.new
    mark_messages_as_read
  end

  def create
    @job = Job.find(params[:job_id])
    @conversation = Conversation.find_or_create_by_participants(
      Current.user,
      @job.company.user,
      @job
    )
    redirect_to @conversation
  end

  private

  def set_conversation
    @conversation = Current.user.conversations.find(params[:id])
  end

  def mark_messages_as_read
    @conversation.messages
      .where.not(user: Current.user)
      .unread
      .update_all(read_at: Time.current)
  end
end
