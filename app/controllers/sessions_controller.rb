class SessionsController < ApplicationController
  layout 'auth'

  skip_before_action :authenticate, only: %i[new create]
  skip_before_action :require_onboarding_completion
  skip_before_action :set_current_organization, only: [:destroy_current]
  skip_before_action :require_organization_selected, only: [:destroy_current] # Skip org checks for sign out

  def index
    @sessions = Current.user.sessions.order(created_at: :desc)
  end

  def new; end

  def create
    if user =
         User.authenticate_by(
           email: params[:email],
           password: params[:password],
         )
      @session = user.sessions.create!
      cookies.signed.permanent[:session_token] = {
        value: @session.id,
        httponly: true,
      }

      Current.session = @session
      Current.user = user

      # Set organization context
      set_current_organization

      # Determine redirect path after login
      if Current.user.talent_signup_completed? ||
           Current.user.onboarding_completed?
        # If either Talent or Scout onboarding is done, go to Launchpad
        redirect_to launchpad_path
      else
        # Neither onboarding is complete, go to the current step
        redirect_to(
          if Current.user.onboarding_step == 'personal'
            onboarding_personal_path
          else
            # Assumes step must be 'organization' if not 'personal' and not complete
            onboarding_organization_path
          end,
          # Optional: Add alert: "Please complete your setup."
        )
      end
    else
      redirect_to sign_in_path(email_hint: params[:email]),
                  alert: 'That email or password is incorrect'
    end
  end

  # Logs out the current session based on the cookie
  def destroy_current
    # Save the last organization before logging out
    Current.user&.update(
      last_logged_in_organization_id: Current.organization&.id,
    )

    Current.session&.destroy
    cookies.delete(:session_token)
    session.delete(:organization_id)
    redirect_to sign_in_path, notice: 'You have been signed out.'
  end

  private

  def set_current_organization
    return unless Current.user

    if session[:organization_id]
      # Use the org from session if present (ensure it still exists for the user)
      Current.organization =
        Current.user.organizations.find_by(id: session[:organization_id])

      # If the stored org is not found, clear it
      session.delete(:organization_id) unless Current.organization
    end

    # Only auto-set if the user has exactly one org and none selected
    if Current.organization.nil? && Current.user.organizations.count == 1
      org = Current.user.organizations.first
      Current.organization = org
      session[:organization_id] = org.id
    end
  end

  def destroy
    set_session
    session.delete(:organization_id)
    @session.destroy
    redirect_to(sessions_path, notice: 'That session has been logged out')
  end

  # Logs out a specific session by ID (e.g., from another device)
  def destroy
    set_session
    session.delete(:organization_id) if @session == Current.session # Clear org if logging out current session via this route too
    @session.destroy
    redirect_to(sessions_path, notice: 'That session has been logged out')
  end

  private

  def set_session
    @session = Current.user.sessions.find(params[:id])
  end
end
