class AccountProfilesController < ApplicationController
  layout 'auth'

  # Skip organization checks as profile is user-specific
  skip_before_action :set_current_organization
  skip_before_action :require_organization_selected
  skip_before_action :require_onboarding_completion, only: %i[edit update]

  before_action :set_user

  def edit
    # @user is set by before_action
  end

  def update
    if @user.update(account_profile_params)
      redirect_to launchpad_path, notice: 'Profile updated successfully.'
    else
      render :edit, status: :unprocessable_entity
    end
  end

  private

  def set_user
    @user = Current.user
  end

  def account_profile_params
    params.require(:user).permit(:first_name, :last_name, :avatar)
  end
end
