class OnboardingController < ApplicationController
  layout 'onboarding'
  before_action :authenticate
  before_action :require_email_verification
  before_action :redirect_if_onboarded
  skip_before_action :require_onboarding_completion

  def personal
    @user = Current.user
  end

  def organization
    @user = Current.user
    @organization = Organization.new
  end

  def update_personal
    @user = Current.user

    if @user.update(personal_params)
      # Check signup intent to determine next step
      if @user.signup_intent == 'talent'
        # Mark talent signup complete and redirect to launchpad
        @user.update!(talent_signup_completed: true) # Use update! to raise errors on failure
        redirect_to launchpad_path, notice: 'Personal details saved! Welcome.' # Adjusted notice
      else
        # Proceed to Scout organization setup
        @user.update(onboarding_step: 'organization')
        redirect_to onboarding_organization_path
      end
    else
      render :personal, status: :unprocessable_entity
    end
  end

  def update_organization
    @user = Current.user
    @organization = Organization.new(organization_params)

    if @organization.save
      # Create organization membership
      OrganizationMembership.create(
        user: @user,
        organization: @organization,
        org_role: 'admin',
      )

      @user.update(
        onboarding_step: 'completed',
        onboarding_completed: true,
        scout_signup_completed: true, # Mark scout signup as complete
      )

      redirect_to scout_root_path, notice: 'Welcome to Ghostwrote!'
    else
      render :organization, status: :unprocessable_entity
    end
  end

  private

  def personal_params
    params.require(:user).permit(:first_name, :last_name, :time_zone)
  end

  # Update the organization_params method to include the new fields
  def organization_params
    params
      .require(:organization)
      .permit(:name, :bio, :email, :operating_timezone, :size)
  end

  def require_email_verification
    unless Current.user.verified?
      redirect_to root_path, alert: 'Please verify your email first'
    end
  end

  def redirect_if_onboarded
    # Redirect if general (Scout) onboarding is done OR if Talent signup is done
    if Current.user.onboarding_completed? ||
         Current.user.talent_signup_completed?
      redirect_to launchpad_path # Always go to launchpad if any onboarding is done
    elsif Current.user.onboarding_step == 'organization' &&
          action_name == 'personal'
      redirect_to onboarding_organization_path
    end
  end
end
