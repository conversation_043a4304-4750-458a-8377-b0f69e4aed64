module AdminBulkOperations
  extend ActiveSupport::Concern

  included do
    before_action :check_bulk_permissions, only: [:bulk_update, :bulk_delete, :bulk_status_change]
  end

  # Bulk update multiple records with the same field values
  def bulk_update
    ids = params[:ids]&.split(',')&.map(&:to_i) || []

    # Check if this is a predefined bulk operation
    operation_key = params[:operation_key]
    if operation_key.present?
      operation = get_bulk_update_operations.find { |op| op[:key] == operation_key }
      if operation
        updates = operation[:params] || {}
      else
        render json: { error: 'Invalid bulk operation' }, status: :bad_request
        return
      end
    else
      updates = bulk_update_params
    end

    if ids.empty?
      render json: { error: 'No records selected' }, status: :bad_request
      return
    end

    if updates.empty?
      render json: { error: 'No updates specified' }, status: :bad_request
      return
    end

    result = perform_bulk_update(ids, updates)

    # Log the bulk operation
    log_bulk_action('update', resource_name.singularize, result[:success_count], {
      updated_fields: updates.keys,
      failed_count: result[:failed_count],
      errors: result[:errors].take(5) # Limit error details in log
    })

    render json: result
  end

  # Bulk delete multiple records
  def bulk_delete
    ids = params[:ids]&.split(',')&.map(&:to_i) || []

    if ids.empty?
      render json: { error: 'No records selected' }, status: :bad_request
      return
    end

    result = perform_bulk_delete(ids)
    
    # Log the bulk operation
    log_bulk_action('delete', resource_name.singularize, result[:success_count], {
      failed_count: result[:failed_count],
      errors: result[:errors].take(5)
    })

    render json: result
  end

  # Bulk status change for models with status enums
  def bulk_status_change
    ids = params[:ids]&.split(',')&.map(&:to_i) || []
    new_status = params[:status]

    if ids.empty?
      render json: { error: 'No records selected' }, status: :bad_request
      return
    end

    if new_status.blank?
      render json: { error: 'No status specified' }, status: :bad_request
      return
    end

    result = perform_bulk_status_change(ids, new_status)
    
    # Log the bulk operation
    log_bulk_action('status_change', resource_name.singularize, result[:success_count], {
      new_status: new_status,
      failed_count: result[:failed_count],
      errors: result[:errors].take(5)
    })

    render json: result
  end

  private

  def perform_bulk_update(ids, updates)
    model_class = get_model_class
    success_count = 0
    failed_count = 0
    errors = []

    # Process in batches to avoid memory issues
    ids.each_slice(50) do |batch_ids|
      records = model_class.where(id: batch_ids)
      
      records.each do |record|
        if can_edit_record?(record)
          if record.update(updates)
            success_count += 1
          else
            failed_count += 1
            errors << "#{record.class.name} ##{record.id}: #{record.errors.full_messages.join(', ')}"
          end
        else
          failed_count += 1
          errors << "#{record.class.name} ##{record.id}: Permission denied"
        end
      end
    end

    {
      success_count: success_count,
      failed_count: failed_count,
      errors: errors,
      message: build_result_message('updated', success_count, failed_count)
    }
  end

  def perform_bulk_delete(ids)
    model_class = get_model_class
    success_count = 0
    failed_count = 0
    errors = []

    # Process in batches
    ids.each_slice(50) do |batch_ids|
      records = model_class.where(id: batch_ids)
      
      records.each do |record|
        if can_delete_record?(record)
          begin
            record.destroy!
            success_count += 1
          rescue => e
            failed_count += 1
            errors << "#{record.class.name} ##{record.id}: #{e.message}"
          end
        else
          failed_count += 1
          errors << "#{record.class.name} ##{record.id}: Permission denied"
        end
      end
    end

    {
      success_count: success_count,
      failed_count: failed_count,
      errors: errors,
      message: build_result_message('deleted', success_count, failed_count)
    }
  end

  def perform_bulk_status_change(ids, new_status)
    model_class = get_model_class
    success_count = 0
    failed_count = 0
    errors = []

    # Validate that the model has a status field
    unless model_class.respond_to?(:statuses) || model_class.column_names.include?('status')
      return {
        success_count: 0,
        failed_count: ids.length,
        errors: ["#{model_class.name} does not have a status field"],
        message: "Status change failed: model does not support status"
      }
    end

    # Process in batches
    ids.each_slice(50) do |batch_ids|
      records = model_class.where(id: batch_ids)
      
      records.each do |record|
        if can_edit_record?(record)
          if record.update(status: new_status)
            success_count += 1
          else
            failed_count += 1
            errors << "#{record.class.name} ##{record.id}: #{record.errors.full_messages.join(', ')}"
          end
        else
          failed_count += 1
          errors << "#{record.class.name} ##{record.id}: Permission denied"
        end
      end
    end

    {
      success_count: success_count,
      failed_count: failed_count,
      errors: errors,
      message: build_result_message("changed to #{new_status}", success_count, failed_count)
    }
  end

  def build_result_message(action, success_count, failed_count)
    if failed_count == 0
      "Successfully #{action} #{success_count} record#{'s' if success_count != 1}"
    elsif success_count == 0
      "Failed to #{action.gsub('changed to ', 'change ')} any records"
    else
      "#{action.capitalize} #{success_count} record#{'s' if success_count != 1}, #{failed_count} failed"
    end
  end

  def get_model_class
    # Override in controllers to specify the model class
    # Default implementation tries to infer from controller name
    controller_name.classify.constantize
  rescue NameError
    # Fallback for admin controllers
    controller_name.sub('admin_', '').classify.constantize
  end

  def can_edit_record?(record)
    # Override in controllers for custom permission logic
    # Default implementation checks if user can edit the resource type
    can_perform?(:edit, resource_name)
  end

  def can_delete_record?(record)
    # Override in controllers for custom permission logic
    # Default implementation checks if user can delete the resource type
    can_perform?(:delete, resource_name)
  end

  def check_bulk_permissions
    unless can_perform?(:edit, resource_name) || can_perform?(:delete, resource_name)
      render json: { error: 'Insufficient permissions for bulk operations' }, status: :forbidden
    end
  end

  def bulk_update_params
    # Override in controllers to specify which fields can be bulk updated
    {}
  end

  # Get available bulk operations for the current resource
  def get_bulk_operations
    operations = []
    
    # Add update operations if user can edit
    if can_perform?(:edit, resource_name)
      operations.concat(get_bulk_update_operations)
      operations.concat(get_bulk_status_operations)
    end
    
    # Add delete operation if user can delete
    if can_perform?(:delete, resource_name)
      operations << {
        key: 'delete',
        label: 'Delete Selected',
        action: 'bulk_delete',
        confirm: true,
        confirm_message: 'Are you sure you want to delete the selected records? This action cannot be undone.',
        class: 'text-red-600 hover:text-red-900'
      }
    end
    
    operations
  end

  def get_bulk_update_operations
    # Override in controllers to define available bulk update operations
    []
  end

  def get_bulk_status_operations
    # Override in controllers to define available status change operations
    []
  end
end
