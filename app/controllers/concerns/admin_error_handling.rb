module AdminErrorHandling
  extend ActiveSupport::Concern

  included do
    rescue_from ActiveRecord::RecordNotFound, with: :handle_record_not_found
    rescue_from ActiveRecord::RecordInvalid, with: :handle_record_invalid
    rescue_from ActionController::ParameterMissing, with: :handle_parameter_missing
    rescue_from ActiveRecord::ConnectionTimeoutError, with: :handle_admin_error
    rescue_from ActiveRecord::StatementInvalid, with: :handle_admin_error
    rescue_from Timeout::Error, with: :handle_admin_error
    rescue_from Pundit::NotAuthorizedError, with: :handle_authorization_error if defined?(Pundit)
  end

  private

  def handle_admin_error(exception)
    # Log the error with context
    log_admin_error(exception)
    
    # Send to error tracking service
    capture_admin_exception(exception)
    
    # Provide user-friendly response
    respond_to_admin_error(exception)
  end

  def handle_record_not_found(exception)
    log_admin_error(exception, level: :warn)
    
    respond_to do |format|
      format.html do
        redirect_to admin_fallback_path,
                    alert: "The requested #{resource_name.humanize.downcase} could not be found."
      end
      format.json do
        render json: { 
          error: 'Record not found',
          message: "The requested #{resource_name.humanize.downcase} could not be found."
        }, status: :not_found
      end
    end
  end

  def handle_record_invalid(exception)
    log_admin_error(exception, level: :warn)
    
    @resource = exception.record
    errors = @resource.errors.full_messages.join(', ')
    
    respond_to do |format|
      format.html do
        flash.now[:alert] = "Unable to save #{resource_name.humanize.downcase}: #{errors}"
        render error_template_for_action, status: :unprocessable_entity
      end
      format.json do
        render json: {
          error: 'Validation failed',
          message: errors,
          details: @resource.errors.as_json
        }, status: :unprocessable_entity
      end
    end
  end

  def handle_parameter_missing(exception)
    log_admin_error(exception, level: :warn)
    
    respond_to do |format|
      format.html do
        redirect_to admin_fallback_path,
                    alert: "Required parameter missing: #{exception.param}"
      end
      format.json do
        render json: {
          error: 'Parameter missing',
          message: "Required parameter missing: #{exception.param}"
        }, status: :bad_request
      end
    end
  end

  def handle_authorization_error(exception)
    log_admin_error(exception, level: :warn)
    
    respond_to do |format|
      format.html do
        redirect_to super_admin_root_path,
                    alert: 'Access denied. Insufficient permissions for this action.'
      end
      format.json do
        render json: {
          error: 'Access denied',
          message: 'Insufficient permissions for this action.'
        }, status: :forbidden
      end
    end
  end

  def log_admin_error(exception, level: :error, context: {})
    error_context = {
      controller: controller_name,
      action: action_name,
      user_id: Current.user&.id,
      user_email: Current.user&.email,
      admin_role: Current.user&.admin_role,
      ip_address: request.remote_ip,
      user_agent: request.user_agent,
      request_path: request.fullpath,
      request_method: request.method,
      params: filtered_params,
      session_id: session.id,
      impersonating: Current.impersonating?,
      impersonator_id: Current.impersonator_id
    }.merge(context)

    case level
    when :error
      Rails.logger.error "Admin Error: #{exception.class}: #{exception.message}"
      Rails.logger.error "Context: #{error_context.to_json}"
      Rails.logger.error exception.backtrace.join("\n") if exception.backtrace
    when :warn
      Rails.logger.warn "Admin Warning: #{exception.class}: #{exception.message}"
      Rails.logger.warn "Context: #{error_context.to_json}"
    when :info
      Rails.logger.info "Admin Info: #{exception.class}: #{exception.message}"
      Rails.logger.info "Context: #{error_context.to_json}"
    end

    # Log to admin audit log for serious errors
    if level == :error
      AdminAuditLog.log_action(
        action: 'error',
        controller: controller_path,
        resource: nil,
        changes: {
          'error_class' => [nil, exception.class.to_s],
          'error_message' => [nil, exception.message],
          'error_context' => [nil, error_context]
        },
        admin_user: Current.user
      )
    end
  end

  def capture_admin_exception(exception)
    # Send to Sentry with admin context
    Sentry.with_scope do |scope|
      scope.set_tag('component', 'admin_interface')
      scope.set_tag('controller', controller_name)
      scope.set_tag('action', action_name)
      scope.set_user(
        id: Current.user&.id,
        email: Current.user&.email,
        admin_role: Current.user&.admin_role
      )
      scope.set_context('admin', {
        impersonating: Current.impersonating?,
        impersonator_id: Current.impersonator_id,
        request_path: request.fullpath,
        ip_address: request.remote_ip
      })
      
      Sentry.capture_exception(exception)
    end
  end

  def respond_to_admin_error(exception)
    respond_to do |format|
      format.html do
        flash[:alert] = user_friendly_error_message(exception)
        redirect_to admin_fallback_path
      end
      format.json do
        render json: {
          error: 'Internal server error',
          message: user_friendly_error_message(exception)
        }, status: :internal_server_error
      end
    end
  end

  def user_friendly_error_message(exception)
    case exception
    when ActiveRecord::ConnectionTimeoutError
      "The system is currently experiencing high load. Please try again in a moment."
    when ActiveRecord::StatementInvalid
      "There was a problem processing your request. Please try again or contact support."
    when ActionController::InvalidAuthenticityToken
      "Your session has expired. Please refresh the page and try again."
    when Timeout::Error
      "The request timed out. Please try again with a smaller dataset or contact support."
    else
      if Rails.env.development?
        "Error: #{exception.class}: #{exception.message}"
      else
        "An unexpected error occurred. Please try again or contact support if the problem persists."
      end
    end
  end

  def admin_fallback_path
    case controller_name
    when 'admin_users'
      super_admin_admin_users_path
    when 'admin_jobs'
      super_admin_admin_jobs_path
    when 'admin_organizations'
      super_admin_admin_organizations_path
    when 'admin_chat_requests'
      super_admin_admin_chat_requests_path
    when 'admin_conversations'
      super_admin_admin_conversations_path
    when 'admin_audit_logs'
      super_admin_admin_audit_logs_path
    else
      super_admin_root_path
    end
  end

  def error_template_for_action
    case action_name
    when 'create'
      :new
    when 'update'
      :edit
    else
      :index
    end
  end

  def filtered_params
    # Filter sensitive parameters for logging
    filter = ActiveSupport::ParameterFilter.new(Rails.application.config.filter_parameters)
    filter.filter(params.to_unsafe_h)
  end

  def resource_name
    controller_name.singularize.gsub('admin_', '')
  end
end
