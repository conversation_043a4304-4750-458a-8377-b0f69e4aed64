module SuperAdmin
  module CsvExportable
    extend ActiveSupport::Concern
    include ActionView::Helpers::NumberHelper

    private

    def send_csv_data
      # Check if this should be an async export based on collection size
      collection = collection_for_export

      if should_export_async?(collection)
        # Queue the export job and notify user
        export_id = SecureRandom.uuid
        filters = extract_current_filters

        # Create the tracking record
        csv_export = CsvExport.create!(
          export_id: export_id,
          admin_user: Current.user,
          controller_name: controller_name,
          filters: filters.to_json,
          status: :queued
        )

        CsvExportJob.perform_later(Current.user.id, controller_name, filters, export_id)

        # Log the async export request
        log_export_action(controller_name.humanize, filters.merge(async: true, export_id: export_id))

        redirect_back(fallback_location: request.referer,
                     notice: "Large export queued. You'll receive an email when it's ready (Export ID: #{export_id[0..7]})")
      else
        # Proceed with synchronous export
        csv_data = generate_csv_data
        filename = "#{controller_name}_#{Date.current.strftime('%Y%m%d')}.csv"

        # Log the sync export
        log_export_action(controller_name.humanize, extract_current_filters)

        send_data csv_data,
                  filename: filename,
                  type: 'text/csv',
                  disposition: 'attachment'
      end
    end

    def should_export_async?(collection)
      # Export async if collection has more than 5000 records
      collection.count > 5000
    rescue
      # If count fails, assume it's large and export async
      true
    end

    def extract_current_filters
      # Extract current filter parameters for the job
      filters = {}
      filters[:search] = params[:search] if params[:search].present?
      filters[:sort_by] = params[:sort_by] if params[:sort_by].present?
      filters[:sort_direction] = params[:sort_direction] if params[:sort_direction].present?

      # Add controller-specific filters
      case controller_name
      when 'admin_users'
        filters[:verified] = params[:verified] if params[:verified].present?
        filters[:signup_intent] = params[:signup_intent] if params[:signup_intent].present?
        filters[:role] = params[:role] if params[:role].present?
      when 'admin_jobs'
        filters[:status] = params[:status] if params[:status].present?
        filters[:job_category] = params[:job_category] if params[:job_category].present?
        filters[:platform] = params[:platform] if params[:platform].present?
        filters[:budget_range] = params[:budget_range] if params[:budget_range].present?
      when 'admin_roles'
        filters[:role_filter] = params[:role_filter] if params[:role_filter].present?
      # Add more controller-specific filters as needed
      end

      filters
    end

    def generate_csv_data
      case controller_name
      when 'admin_users'
        generate_users_csv
      when 'admin_jobs'
        generate_jobs_csv
      when 'admin_job_applications'
        generate_job_applications_csv
      when 'admin_messages'
        generate_messages_csv
      when 'admin_talent_profiles'
        generate_talent_profiles_csv
      when 'admin_organizations'
        generate_organizations_csv
      when 'admin_conversations'
        generate_conversations_csv
      when 'admin_chat_requests'
        generate_chat_requests_csv
      when 'admin_roles'
        generate_admin_roles_csv
      when 'admin_audit_logs'
        generate_audit_logs_csv
      when 'admin_files'
        generate_admin_files_csv
      else
        raise "CSV export not implemented for #{controller_name}"
      end
    end

    def generate_users_csv
      CSV.generate(headers: true) do |csv|
        csv << ['ID', 'Name', 'Email', 'Verified', 'Signup Intent', 'Organizations', 'Created At']

        # Preload associations to avoid N+1 queries
        users_with_orgs = collection_for_export.includes(:organizations)

        users_with_orgs.find_each do |user|
          csv << [
            user.id,
            user.full_name,
            user.email,
            user.verified? ? 'Yes' : 'No',
            user.signup_intent&.humanize,
            user.organizations.map(&:name).join(', '),
            user.created_at.strftime('%Y-%m-%d %H:%M:%S')
          ]
        end
      end
    end

    def generate_jobs_csv
      CSV.generate(headers: true) do |csv|
        csv << ['ID', 'Title', 'Category', 'Status', 'Organization', 'Budget Range', 'Platform', 'Applications Count', 'Created At', 'Published At']

        # Preload associations and use counter cache to avoid N+1 queries
        jobs_with_data = collection_for_export.includes(:organization)

        jobs_with_data.find_each do |job|
          csv << [
            job.id,
            job.title,
            job.job_category&.humanize,
            job.status&.humanize,
            job.organization&.name,
            job.budget_range&.humanize&.gsub('_', ' '),
            job.platform&.humanize&.gsub('_', ' '),
            job.job_applications_count, # Use counter cache instead of preloading
            job.created_at.strftime('%Y-%m-%d %H:%M:%S'),
            job.published_at&.strftime('%Y-%m-%d %H:%M:%S')
          ]
        end
      end
    end

    def generate_job_applications_csv
      CSV.generate(headers: true) do |csv|
        csv << ['ID', 'Applicant Name', 'Applicant Email', 'Job Title', 'Status', 'Invitation Status', 'Salary Consideration', 'Applied At']

        # Preload associations to avoid N+1 queries
        applications_with_data = collection_for_export.includes(:user, :job, :job_invitation)

        applications_with_data.find_each do |application|
          csv << [
            application.id,
            application.user.full_name,
            application.user.email,
            application.job.title,
            application.status&.humanize,
            application.job_invitation&.status&.humanize || 'Not Invited',
            application.salary_consideration ? 'Yes' : 'No',
            application.created_at.strftime('%Y-%m-%d %H:%M:%S')
          ]
        end
      end
    end

    def generate_messages_csv
      CSV.generate(headers: true) do |csv|
        csv << ['ID', 'Sender Name', 'Sender Email', 'Conversation ID', 'Message Preview', 'Read Status', 'Created At']

        # Preload associations to avoid N+1 queries
        messages_with_data = collection_for_export.includes(:user, :conversation)

        messages_with_data.find_each do |message|
          csv << [
            message.id,
            message.user.full_name,
            message.user.email,
            message.conversation.id,
            message.body.truncate(100),
            message.read_at ? 'Read' : 'Unread',
            message.created_at.strftime('%Y-%m-%d %H:%M:%S')
          ]
        end
      end
    end

    def generate_talent_profiles_csv
      CSV.generate(headers: true) do |csv|
        csv << ['ID', 'Name', 'Email', 'Headline', 'Location', 'Availability', 'Pricing Model', 'Price Min', 'Price Max', 'Is Agency', 'Is Premium', 'Created At']

        # Preload associations to avoid N+1 queries
        profiles_with_users = collection_for_export.includes(:user)

        profiles_with_users.find_each do |profile|
          csv << [
            profile.id,
            profile.user.full_name,
            profile.user.email,
            profile.headline,
            profile.location,
            profile.availability_status&.humanize,
            profile.pricing_model&.humanize,
            profile.price_range_min,
            profile.price_range_max,
            profile.is_agency ? 'Yes' : 'No',
            profile.is_premium ? 'Yes' : 'No',
            profile.created_at.strftime('%Y-%m-%d %H:%M:%S')
          ]
        end
      end
    end

    def generate_organizations_csv
      CSV.generate(headers: true) do |csv|
        csv << ['ID', 'Name', 'Company Size', 'Members Count', 'Jobs Count', 'Created At']

        # Preload associations and use counter cache to avoid N+1 queries
        orgs_with_data = collection_for_export.includes(:users)

        orgs_with_data.find_each do |org|
          csv << [
            org.id,
            org.name,
            org.size&.humanize,
            org.users.size, # Use size to leverage preloaded data
            org.jobs_count, # Use counter cache
            org.created_at.strftime('%Y-%m-%d %H:%M:%S')
          ]
        end
      end
    end

    def generate_conversations_csv
      CSV.generate(headers: true) do |csv|
        csv << ['ID', 'Participants', 'Messages Count', 'Related Job', 'Last Activity', 'Created At']

        # Preload associations and use counter cache to avoid N+1 queries
        conversations_with_data = collection_for_export.includes(:users, :job)

        conversations_with_data.find_each do |conversation|
          csv << [
            conversation.id,
            conversation.users.map(&:email).join(', '), # Use map instead of pluck on preloaded data
            conversation.messages_count, # Use counter cache
            conversation.job&.title || 'No Job',
            conversation.updated_at.strftime('%Y-%m-%d %H:%M:%S'),
            conversation.created_at.strftime('%Y-%m-%d %H:%M:%S')
          ]
        end
      end
    end

    def generate_chat_requests_csv
      CSV.generate(headers: true) do |csv|
        csv << ['ID', 'Scout Name', 'Scout Email', 'Talent Name', 'Talent Email', 'Status', 'Note', 'Requested At']

        # Preload associations to avoid N+1 queries
        requests_with_users = collection_for_export.includes(:scout, :talent)

        requests_with_users.find_each do |request|
          csv << [
            request.id,
            request.scout.full_name,
            request.scout.email,
            request.talent.full_name,
            request.talent.email,
            request.status&.humanize,
            request.note,
            request.requested_at&.strftime('%Y-%m-%d %H:%M:%S')
          ]
        end
      end
    end

    def generate_admin_roles_csv
      CSV.generate(headers: true) do |csv|
        csv << ['ID', 'Name', 'Email', 'Admin Roles', 'Organizations', 'Verified', 'Created At']

        # Preload associations to avoid N+1 queries
        users_with_data = collection_for_export.includes(:roles, :organizations)

        users_with_data.find_each do |user|
          csv << [
            user.id,
            user.full_name,
            user.email,
            user.roles.map(&:name).join(', '),
            user.organizations.map(&:name).join(', '),
            user.verified? ? 'Yes' : 'No',
            user.created_at.strftime('%Y-%m-%d %H:%M:%S')
          ]
        end
      end
    end

    def generate_audit_logs_csv
      CSV.generate(headers: true) do |csv|
        csv << [
          'ID',
          'Date/Time',
          'Admin User',
          'Admin Email',
          'Action',
          'Controller',
          'Resource Type',
          'Resource ID',
          'Resource Name',
          'Description',
          'IP Address',
          'User Agent',
          'Changes'
        ]

        collection_for_export.find_each do |log|
          csv << [
            log.id,
            log.created_at.strftime('%Y-%m-%d %H:%M:%S %Z'),
            log.respond_to?(:admin_name) ? log.admin_name : log.admin_user&.full_name,
            log.respond_to?(:admin_email) ? log.admin_email : log.admin_user&.email,
            log.action&.humanize,
            log.controller,
            log.resource_type || 'N/A',
            log.resource_id || 'N/A',
            log.respond_to?(:resource_identifier) ? log.resource_identifier : 'N/A',
            log.respond_to?(:description) ? log.description : 'N/A',
            log.ip_address || 'N/A',
            log.user_agent || 'N/A',
            log.changes.present? ? log.changes.to_json : 'N/A'
          ]
        end
      end
    end

    def generate_admin_files_csv
      CSV.generate(headers: true) do |csv|
        csv << [
          'ID',
          'Filename',
          'Content Type',
          'File Size (bytes)',
          'File Size (human)',
          'Record Type',
          'Record ID',
          'Attachment Name',
          'Storage Key',
          'Service Name',
          'Checksum',
          'Created At',
          'Record Exists'
        ]

        # Preload blobs to avoid N+1 queries
        attachments_with_blobs = collection_for_export.includes(:blob)

        attachments_with_blobs.find_each do |attachment|
          blob = attachment.blob
          record_exists = attachment.record.present? rescue false

          csv << [
            attachment.id,
            blob.filename.to_s,
            blob.content_type,
            blob.byte_size,
            number_to_human_size(blob.byte_size),
            attachment.record_type,
            attachment.record_id,
            attachment.name,
            blob.key,
            blob.service_name,
            blob.checksum,
            attachment.created_at.strftime('%Y-%m-%d %H:%M:%S'),
            record_exists ? 'Yes' : 'No'
          ]
        end
      end
    end

    def collection_for_export
      # This should be overridden in each controller to return the filtered collection
      # For now, return the instance variable that matches the controller name
      instance_variable_get("@#{controller_name.sub('admin_', '')}")
    end
  end
end
