module SessionSecurity
  extend ActiveSupport::Concern

  included do
    before_action :track_session_activity, if: :should_track_activity?
    before_action :check_session_security, if: :should_check_security?
    after_action :update_session_activity, if: :should_track_activity?
  end

  private

  def should_track_activity?
    Current.session.present? && Current.user&.admin?
  end

  def should_check_security?
    Current.session.present? && Current.user&.admin? && 
    controller_path.start_with?('super_admin/')
  end

  def track_session_activity
    return unless Current.session

    activity_type = determine_activity_type
    
    SessionActivity.log_activity(
      activity_type: activity_type,
      session: Current.session,
      controller: controller_path,
      action: action_name,
      metadata: {
        request_path: request.fullpath,
        request_method: request.method,
        params: filtered_params
      }
    )
  end

  def update_session_activity
    return unless Current.session
    
    Current.session.touch_activity!
  end

  def check_session_security
    return unless Current.session

    # Check if session is locked
    if Current.session.locked?
      sign_out
      redirect_to sign_in_path, alert: 'Your session has been locked due to security concerns. Please contact an administrator.'
      return
    end

    # Check for session timeout (admin sessions have shorter timeout)
    if admin_session_expired?
      Current.session.destroy
      sign_out
      redirect_to sign_in_path, alert: 'Your admin session has expired for security reasons.'
      return
    end

    # Check for suspicious activity
    check_for_suspicious_activity
  end

  def admin_session_expired?
    return false unless Current.user&.admin?
    return false unless Current.session.last_activity_at
    
    # Admin sessions expire after 2 hours of inactivity
    Current.session.last_activity_at < 2.hours.ago
  end

  def check_for_suspicious_activity
    # Check for IP address changes
    if request.ip != Current.session.ip_address
      SecurityAlert.create_alert(
        user: Current.user,
        alert_type: 'suspicious_login',
        severity: 'high',
        description: "IP address changed during session from #{Current.session.ip_address} to #{request.ip}",
        metadata: {
          old_ip: Current.session.ip_address,
          new_ip: request.ip,
          user_agent: request.user_agent,
          session_id: Current.session.id,
          controller: controller_path,
          action: action_name
        }
      )
      
      Current.session.increment_security_warnings!
    end

    # Check for user agent changes (potential session hijacking)
    if request.user_agent != Current.session.user_agent
      SecurityAlert.create_alert(
        user: Current.user,
        alert_type: 'session_hijacking',
        severity: 'critical',
        description: "User agent changed during session - potential session hijacking",
        metadata: {
          old_user_agent: Current.session.user_agent,
          new_user_agent: request.user_agent,
          ip_address: request.ip,
          session_id: Current.session.id,
          controller: controller_path,
          action: action_name
        }
      )
      
      Current.session.increment_security_warnings!
      
      # Lock session immediately for critical security events
      Current.session.lock!(24.hours)
    end

    # Check for unusual admin activity patterns
    if unusual_admin_activity?
      SecurityAlert.create_alert(
        user: Current.user,
        alert_type: 'unusual_activity',
        severity: 'medium',
        description: "Unusual admin activity pattern detected",
        metadata: {
          controller: controller_path,
          action: action_name,
          ip_address: request.ip,
          user_agent: request.user_agent,
          session_id: Current.session.id
        }
      )
    end
  end

  def unusual_admin_activity?
    return false unless Current.session

    # Check for rapid successive admin actions (potential automation/bot)
    recent_activities = Current.session.session_activities
                                      .where('created_at > ?', 1.minute.ago)
                                      .where(activity_type: 'admin_action')
                                      .count

    recent_activities > 10 # More than 10 admin actions in 1 minute
  end

  def determine_activity_type
    if controller_path.start_with?('super_admin/')
      'admin_action'
    elsif action_name == 'create' && controller_name == 'sessions'
      'login'
    elsif action_name == 'destroy' && controller_name == 'sessions'
      'logout'
    elsif request.xhr? || request.format.json?
      'api_request'
    else
      'page_view'
    end
  end

  def filtered_params
    # Filter out sensitive parameters
    params.except(:password, :password_confirmation, :authenticity_token)
          .to_unsafe_h
          .slice(*safe_param_keys)
  end

  def safe_param_keys
    %w[id search filter page per_page sort order format controller action]
  end

  # Helper method to log security events
  def log_security_event(event_type, description, metadata = {})
    SessionActivity.log_activity(
      activity_type: 'security_event',
      session: Current.session,
      controller: controller_path,
      action: action_name,
      metadata: metadata.merge(
        event_type: event_type,
        description: description,
        request_path: request.fullpath
      )
    )
  end
end
