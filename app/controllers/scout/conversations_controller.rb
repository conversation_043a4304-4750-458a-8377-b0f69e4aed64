module Scout
  class ConversationsController < Scout::BaseController
    before_action :set_conversation, only: [:show]
    before_action :set_modal_conversation, only: [:show_modal]

    def index
      if params[:query].present?
        search_params = {
          where: {
            user_ids: Current.user.id,
            archived: [false, nil],
          },
          fields: %w[participant_names^5 last_message^3 messages],
          match: :word_start,
          order: {
            updated_at: :desc,
          },
          includes: [:users, :job, messages: :user],
          debug: true,
        }

        Rails
          .logger.info "Searching conversations with params: #{search_params}"
        @conversations = Conversation.search(params[:query], **search_params)
        Rails.logger.info "Search results: #{@conversations.to_json}"
      else
        @conversations =
          Conversation
            .active_for(Current.user)
            .includes(:users, :job, messages: :user)
            .order(updated_at: :desc)
      end
    end

    def show
      @conversations =
        Conversation
          .active_for(Current.user)
          .includes(:users, :job, messages: :user)
          .order(updated_at: :desc)
      @messages = @conversation.messages.includes(:user)
      @message = Message.new
      mark_messages_as_read

      # Set counts for the sidebar
      @all_count = @conversations.count
      @unread_count =
        @conversations
          .joins(:messages)
          .where(messages: { read_at: nil })
          .where.not(messages: { user_id: Current.user.id })
          .distinct
          .count
    end

    def create
      @job = Job.find(params[:job_id])
      @conversation =
        Conversation.find_or_create_by_participants(
          Current.user,
          @job.company.user,
          @job,
        )
      redirect_to @conversation
    end

    def archive
      @conversation = Current.user.conversations.find(params[:id])
      @participant =
        @conversation.conversation_participants.find_by(user: Current.user)
      @participant.archive!

      redirect_to scout_archives_path, notice: 'Conversation archived'
    end

    def unarchive
      @conversation = Current.user.conversations.find(params[:id])
      @participant =
        @conversation.conversation_participants.find_by(user: Current.user)
      @participant.unarchive!

      redirect_to scout_conversations_path, notice: 'Conversation unarchived'
    end

    # Action for displaying a conversation in a modal
    def show_modal
      @messages = @conversation.messages.includes(:user)
      @message = Message.new
      mark_messages_as_read

      respond_to do |format|
        format.turbo_stream do
          render turbo_stream: [
                   turbo_stream.update(
                     'message-modal-content',
                     partial: 'scout/conversations/modal_chat',
                   ),
                   turbo_stream.append(
                     'modal_actions',
                     html:
                       "<script>document.querySelector('[data-controller=\"modal\"]').dispatchEvent(new CustomEvent('show-modal'))</script>",
                   ),
                 ]
        end
        format.html do
          render partial: 'scout/conversations/modal_chat', layout: false
        end
        format.any do
          render partial: 'scout/conversations/modal_chat', layout: false
        end
      end
    end

    private

    def set_conversation
      @conversation = Current.user.conversations.find(params[:id])
    end

    def set_modal_conversation
      applicant_user = User.find(params[:applicant_user_id])
      job = Job.find(params[:job_id])

      @conversation =
        Conversation.find_or_create_by_participants(
          Current.user,
          applicant_user,
          job,
        )
    end

    def mark_messages_as_read
      @conversation
        .messages
        .where.not(user: Current.user)
        .unread
        .update_all(read_at: Time.current)
    end
  end
end
