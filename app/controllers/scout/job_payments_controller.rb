# frozen_string_literal: true

module Scout
  class JobPaymentsController < Scout::BaseController
    # Ensure user is authenticated and part of an organization if BaseController doesn't handle it
    # before_action :authenticate_user!
    # before_action :set_organization # Assuming BaseController provides current_organization
    before_action :set_job

    def create
      price_id = params[:plan]
      payment_type = params[:type] == 'premium_upgrade' ? 'premium_listing' : 'standard_listing'

      unless price_id.present?
        redirect_to scout_job_path(@job), alert: 'Payment plan not specified.'
        return
      end

      begin
        # Ensure user is set up with Stripe processor
        Current.user.set_payment_processor(:stripe)

        # Use Stripe Checkout for one-time payments (recommended for SCA)
        checkout_session = Current.user.payment_processor.checkout(
          mode: 'payment',
          line_items: [{ price: price_id, quantity: 1 }],
          # Add metadata directly to the session AND payment intent for robustness
          metadata: { job_id: @job.id, type: payment_type },
          payment_intent_data: {
            metadata: { job_id: @job.id, type: payment_type }
          },
          # Use the new dedicated success/cancel routes
          success_url: success_scout_job_payment_url(@job, session_id: '{CHECKOUT_SESSION_ID}'), # Stripe appends session_id automatically
          cancel_url: cancel_scout_job_payment_url(@job)
        )

        # Redirect to Stripe Checkout page
        redirect_to checkout_session.url, allow_other_host: true, status: :see_other

      # Note: The actual job update logic (setting status, expiry, premium)
      # should now happen in a webhook handler for 'checkout.session.completed'
      # or 'checkout.session.async_payment_succeeded' because the payment is asynchronous.
      # The controller only initiates the checkout.

      # rescue Pay::Error, Stripe::CardError => e # CardError less likely with Checkout
      rescue Pay::Error => e
        # Catch Pay gem errors during checkout session creation
        redirect_to scout_job_path(@job), alert: "Payment initiation failed: #{e.message}"
      rescue StandardError => e
        # Catch other potential errors
        Rails.logger.error("Job Payment Checkout Error: #{e.message}\n#{e.backtrace.join("\n")}")
        redirect_to scout_job_path(@job), alert: 'An unexpected error occurred initiating payment.'
      end
    end

    def success
      # Retrieve the session to potentially display job info or confirm status
      # Note: Actual fulfillment happens via webhook
      session_id = params[:session_id]
      begin
        checkout_session = Stripe::Checkout::Session.retrieve(session_id)
        @job_id = checkout_session.metadata.job_id
        @job = current_organization.jobs.find_by(id: @job_id) # Find job safely
        flash.now[:notice] = "Payment processing initiated! Your job posting is pending confirmation."
        # Add confetti effect here if desired (e.g., via JS)
      rescue Stripe::InvalidRequestError => e
        Rails.logger.error "Could not retrieve checkout session: #{e.message}"
        flash.now[:alert] = "Could not verify payment session."
      rescue ActiveRecord::RecordNotFound
        flash.now[:alert] = "Job associated with this payment could not be found."
      rescue StandardError => e
        Rails.logger.error "Error in success action: #{e.message}"
        flash.now[:alert] = "An unexpected error occurred."
      end
      # Render app/views/scout/job_payments/success.html.erb
    end

    def cancel
      @job = current_organization.jobs.find_by(id: params[:job_id]) # Find job safely
      flash.now[:alert] = "Payment was cancelled. Your job has not been published."
      # Render app/views/scout/job_payments/cancel.html.erb
    end

    private

    def set_job
      # Ensure the job belongs to the current user's organization
      # Assumes current_organization is available from BaseController or similar
      @job = current_organization.jobs.find(params[:job_id])
    rescue ActiveRecord::RecordNotFound
      redirect_to scout_dashboard_path, alert: 'Job not found.' # Or appropriate path
    end

    # Define scout_job_path/url if not already available globally
    # Remove these helpers if they are defined globally or in BaseController
    # def scout_job_url(job, options = {})
    #   # Your url helper - needs to generate full URL for Stripe redirect
    #   super(job, options) # Assuming standard Rails URL helpers exist
    # end
    # def scout_dashboard_path(options = {})
    #   # Your path helper
    # end
  end
end
