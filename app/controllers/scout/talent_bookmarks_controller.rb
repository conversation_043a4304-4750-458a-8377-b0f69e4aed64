module Scout
  class TalentBookmarksController < Scout::BaseController
    before_action :set_talent_profile

    def create
      @bookmark =
        Current.user.talent_bookmarks.build(talent_profile: @talent_profile)

      if @bookmark.save
        render turbo_stream: [
                 turbo_stream.replace(
                   "bookmark_button_#{@talent_profile.id}",
                   partial: 'scout/talent/bookmark_button',
                   locals: {
                     talent_profile: @talent_profile,
                   },
                 ),
                 turbo_stream.replace(
                   'bookmarked_talents_filter_count',
                   partial: 'scout/talent/filters/bookmarked_count',
                   locals: {
                     count: Current.user.talent_bookmarks.count,
                   },
                 ),
               ]
      end
    end

    def destroy
      @bookmark =
        Current.user.talent_bookmarks.find_by!(talent_profile: @talent_profile)
      @bookmark.destroy

      render turbo_stream: [
               turbo_stream.replace(
                 "bookmark_button_#{@talent_profile.id}",
                 partial: 'scout/talent/bookmark_button',
                 locals: {
                   talent_profile: @talent_profile,
                 },
               ),
               turbo_stream.replace(
                 'bookmarked_talents_filter_count',
                 partial: 'scout/talent/filters/bookmarked_count',
                 locals: {
                   count: Current.user.talent_bookmarks.count,
                 },
               ),
             ]
    end

    private

    def set_talent_profile
      @talent_profile = TalentProfile.find(params[:id])
    end
  end
end
