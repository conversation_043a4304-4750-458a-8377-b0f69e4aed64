module Scout
  class MessagesController < Scout::BaseController
    before_action :set_conversation
    
    def create
      @message = @conversation.messages.build(message_params) do |m|
        m.user = Current.user
      end

      if @message.save
        @conversation.touch
        respond_to do |format|
          format.turbo_stream
        end
        broadcast_to_others
      else
        render turbo_stream: turbo_stream.replace(:new_message, partial: "scout/messages/form", 
          locals: { conversation: @conversation, message: @message })
      end
    end

    private

    def set_conversation
      @conversation = Current.user.conversations.find(params[:conversation_id])
    end

    def message_params
      params.require(:message).permit(:body)
    end

    def broadcast_to_others
      @conversation.recipients_of(Current.user).each do |recipient|
        Turbo::StreamsChannel.broadcast_append_to(
          recipient,
          target: :messages,
          partial: "scout/messages/message",
          locals: { message: @message }
        )
      end
    end
  end
end