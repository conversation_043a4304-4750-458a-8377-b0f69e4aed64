module Scout
  class ApplicantNotesController < Scout::BaseController
    before_action :set_application

    def new
      @note = TalentNote.new

      respond_to do |format|
        format.turbo_stream
      end
    end

    def create
      @note = TalentNote.new(note_params)
      @note.talent_profile = @application.user.talent_profile
      @note.user = Current.user
      @note.organization = Current.user.organizations.first

      if @note.save
        # Ensure we're explicitly rendering the turbo_stream template
        # which will handle removing the form and showing the new note
        respond_to do |format|
          format.turbo_stream { render :create }
        end
      else
        respond_to do |format|
          format.turbo_stream { render turbo_stream: turbo_stream.replace("new_note_form", partial: "scout/applicant_notes/form", locals: { application: @application, note: @note }) }
        end
      end
    end

    private

    def set_application
      @application = JobApplication.find(params[:applicant_id])
    end

    def note_params
      params.require(:talent_note).permit(:content, :category, :pinned)
    end
  end
end
