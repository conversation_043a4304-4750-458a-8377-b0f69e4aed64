module Scout
  class ChatRequestsController < Scout::BaseController
    def new
      @talent = User.find(params[:talent_user_id])
      @chat_request = ChatRequest.new

      # Check for existing pending request
      existing_request =
        Current.user.sent_chat_requests.pending.find_by(talent: @talent)

      if existing_request
        render turbo_stream:
                 turbo_stream.replace(
                   'message-modal-content',
                   partial: 'scout/chat_requests/existing_request_error',
                   locals: {
                     talent: @talent,
                   },
                 )
        return
      end

      render turbo_stream:
               turbo_stream.replace(
                 'message-modal-content',
                 partial: 'scout/chat_requests/modal_form',
                 locals: {
                   talent: @talent,
                   chat_request: @chat_request,
                 },
               )
    end

    def create
      @talent = User.find(params[:talent_user_id])

      # Check for existing pending request
      existing_request =
        Current.user.sent_chat_requests.pending.find_by(talent: @talent)

      if existing_request
        respond_to do |format|
          format.html do
            redirect_back(
              fallback_location: scout_talent_index_path,
              alert:
                "You already have a pending chat request with #{@talent.name}.",
            )
          end
          format.turbo_stream do
            render turbo_stream:
                     turbo_stream.replace(
                       'message-modal-content',
                       partial: 'scout/chat_requests/existing_request_error',
                       locals: {
                         talent: @talent,
                       },
                     )
          end
        end
        return
      end

      # Create new chat request
      @chat_request =
        Current.user.sent_chat_requests.build(
          chat_request_params.merge(talent: @talent, status: :pending),
        )

      if @chat_request.save
        respond_to do |format|
          format.html do
            redirect_back(
              fallback_location: scout_talent_index_path,
              notice: "Chat request sent to #{@talent.name}!",
            )
          end
          format.turbo_stream do
            render turbo_stream: [
                     turbo_stream.replace(
                       'message-modal-content',
                       partial: 'scout/chat_requests/success',
                       locals: {
                         talent: @talent,
                       },
                     ),
                     turbo_stream.replace(
                       "talent_card_#{@talent.id}",
                       partial: 'scout/talent_profile_card_wrapper',
                       locals: {
                         talent_profile: @talent.talent_profile,
                       },
                     ),
                     turbo_stream.append(
                       'modal_actions',
                       "<script>
                         setTimeout(() => {
                           const chatModalController = document.querySelector('[data-controller*=\"chat-request-modal\"]');
                           if (chatModalController && chatModalController.chatRequestModalController) {
                             chatModalController.chatRequestModalController.handleSuccessFromServer('#{@talent.name.gsub("'", "\\'")}');
                           } else {
                             document.dispatchEvent(new CustomEvent('chat-request-success', {
                               detail: { talentName: '#{@talent.name.gsub("'", "\\'")}' }
                             }));
                           }
                         }, 10);
                       </script>".html_safe,
                     ),
                   ]
          end
        end
      else
        respond_to do |format|
          format.html do
            redirect_back(
              fallback_location: scout_talent_index_path,
              alert: 'Unable to send chat request. Please try again.',
            )
          end
          format.turbo_stream do
            render turbo_stream:
                     turbo_stream.replace(
                       'message-modal-content',
                       partial: 'scout/chat_requests/modal_form',
                       locals: {
                         talent: @talent,
                         chat_request: @chat_request,
                       },
                     )
          end
        end
      end
    end

    private

    def chat_request_params
      params.require(:chat_request).permit(:pitch)
    end
  end
end
