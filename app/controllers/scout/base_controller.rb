module Scout
  class BaseController < ApplicationController
    layout 'scout'

    before_action :set_current_request_details
    before_action :authenticate
    before_action :check_verification
    before_action :check_impersonation_timeout
    before_action :require_onboarding_completion
    before_action :set_current_organization
    before_action :require_organization_selected # Remove :logout since it doesn't exist
    before_action :check_impersonation_restrictions
    before_action :require_scout_signup_completed # Add check for scout access

    def current_organization
      Current.organization
    end

    private

    def require_scout_signup_completed
      unless Current.user&.scout_signup_completed?
        redirect_to launchpad_path,
                    alert:
                      'You need to complete Scout signup to access this area.'
      end
    end
  end
end
