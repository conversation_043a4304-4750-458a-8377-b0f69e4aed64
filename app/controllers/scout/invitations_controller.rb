module Scout
  class InvitationsController < Scout::BaseController
    before_action :set_talent_profile, only: %i[new create]

    def new
      # Renders a form (modal) that includes:
      # - pitch (text area)
      # - select job from current_organization jobs
      @talent_profile_id = params[:talent_profile_id]
      @jobs = current_organization.jobs.published

      respond_to do |format|
        format.turbo_stream
        format.html
      end
    end

    def create
      # We assume "pitch" param goes into job_application.application_letter
      # job_id param => job_id
      # user => @talent_profile.user
      # invited: true, invited_at: now, status: :applied

      @jobs = current_organization.jobs
      job_id = params[:job_id]
      pitch = params[:pitch].presence || ''

      if job_id.blank?
        flash[:alert] = 'Please select a Job.'
        render :new, status: :unprocessable_entity
        return
      end

      # Check if an application already exists for this talent and job
      existing_application =
        JobApplication.find_by(user: @talent_profile.user, job_id: job_id)
      if existing_application
        error_message = 'This talent has already applied for this job.'

        respond_to do |format|
          format.turbo_stream do
            render turbo_stream: [
                     turbo_stream.append(
                       'body',
                       "<div data-controller='sonner' 
                     data-action='connect->sonner#error' 
                     data-sonner-message-value='#{error_message}'>
                </div>".html_safe,
                     ),
                   ]
          end
          format.html do
            flash[:alert] = error_message
            render :new, status: :unprocessable_entity
          end
        end
        return
      end

      @application =
        JobApplication.new(
          user: @talent_profile.user,
          job_id: job_id,
          invited: true,
          invited_at: Time.current,
          status: :applied,
          application_letter: pitch,
        )

      if @application.save
        # Optionally, create or find a conversation to chat with them
        # conversation = Conversation.find_or_create_by_participants(
        #   Current.user,
        #   @talent_profile.user,
        #   @application.job
        # )

        # For turbo_stream requests, respond with a stream that dismisses the modal
        # and shows a Sonner toast notification
        respond_to do |format|
          format.turbo_stream do
            render turbo_stream: [
                     turbo_stream.replace('invite-modal', ''),
                     turbo_stream.append(
                       'body',
                       '<div data-controller="sonner" 
                     data-action="connect->sonner#success" 
                     data-sonner-message-value="Invitation sent successfully">
                  </div>'.html_safe,
                     ),
                   ]
          end
          format.html do
            flash[:notice] = 'Invitation sent successfully.'
            redirect_to scout_talent_index_path
          end
        end
      else
        error_message = @application.errors.full_messages.join(', ')

        respond_to do |format|
          format.turbo_stream do
            render turbo_stream: [
                     turbo_stream.append(
                       'body',
                       "<div data-controller='sonner' 
                     data-action='connect->sonner#error' 
                     data-sonner-message-value='#{error_message}'>
                </div>".html_safe,
                     ),
                     turbo_stream.replace(
                       'invitation-form-container',
                       render_to_string(
                         partial: 'form',
                         locals: {
                           talent_profile_id: params[:talent_profile_id],
                           jobs: @jobs,
                         },
                       ),
                     ),
                   ]
          end
          format.html do
            flash.now[:alert] = error_message
            render :new, status: :unprocessable_entity
          end
        end
      end
    end

    private

    def set_talent_profile
      @talent_profile =
        TalentProfile.find(params[:talent_profile_id]) if params[
        :talent_profile_id
      ].present?
      # or you might pass :id => for a single invite flow
    end
  end
end
