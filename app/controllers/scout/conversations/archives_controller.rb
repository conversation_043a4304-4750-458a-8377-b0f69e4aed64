module Scout
  module Conversations
    class ArchivesController < Scout::BaseController
      before_action :authenticate
      before_action :set_conversation, only: [ :show ]

      def show
        @messages = @conversation.messages.includes(:user)
        @message = Message.new
        mark_messages_as_read

        # Set counts for the sidebar
        @conversations = Conversation.archived_for(Current.user)
                          .includes(:users, :job, messages: :user)
                          .order(updated_at: :desc)
        @all_count = @conversations.count
        @unread_count = @conversations.joins(:messages)
                          .where(messages: { read_at: nil })
                          .where.not(messages: { user_id: Current.user.id })
                          .distinct
                          .count
      end

      def index
        if params[:query].present?
          @conversations = Conversation.search(
            params[:query],
            where: {
              user_ids: Current.user.id,
              archived: true
            },
          fields: [
            "participant_names^5",
            "last_message^3",
            "messages"
          ],
            match: :word_start,
            order: { updated_at: :desc },
            includes: [ :users, :job, messages: :user ]
          )
        else
          @conversations = Conversation.archived_for(Current.user)
                            .includes(:users, :job, messages: :user)
                            .order(updated_at: :desc)
        end
      end

      private

      def set_conversation
        @conversation = Conversation.archived_for(Current.user)
                          .find(params[:id])
      end

      def mark_messages_as_read
        @conversation.messages.where.not(user: Current.user).update_all(read_at: Time.current)
      end
    end
  end
end
