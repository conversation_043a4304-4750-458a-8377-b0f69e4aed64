module Scout
  module Jobs
    class ApplicantsController < Scout::BaseController
      before_action :set_job, if: -> { params[:job_id].present? }

      def index
        @jobs = Organization.first.jobs
        @job_ids = @jobs.pluck(:id)

        # Build search conditions
        search_conditions = {}

        search_conditions[:job_id] = params[:job_id] if params[:job_id].present?

        # Add status filter if provided
        search_conditions[:status] = params[:status] if params[:status].present?

        # Execute search with conditions
        if params[:query].present?
          @applications =
            JobApplication.search(
              params[:query],
              where: search_conditions,
              order: {
                updated_at: :desc,
              },
              page: params[:page],
              per_page: 20,
            )
        else
          @applications =
            JobApplication.search(
              '*',
              where: search_conditions,
              order: {
                updated_at: :desc,
              },
              aggs: {
                'status' => {
                  limit: 20,
                },
                'job_id' => {
                  limit: 20,
                },
              },
              page: params[:page],
              per_page: 20,
            )
        end

        @applications = @applications.results

        # Keep the original grouping for status filters
        @grouped_applications = @applications.group_by(&:status)

        # Add nested grouping by job and then by status
        @job_grouped_applications =
          @applications
            .group_by(&:job)
            .transform_values { |job_apps| job_apps.group_by(&:status) }
      end

      private

      def set_job
        @job = current_organization.jobs.find(params[:job_id])
      end
    end
  end
end
