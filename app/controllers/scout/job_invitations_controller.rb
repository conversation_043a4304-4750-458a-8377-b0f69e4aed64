module Scout
  class JobInvitationsController < ApplicationController
    before_action :authenticate_user!
    before_action :require_scout_permissions

    def create
      job = Job.find(params[:job_id])
      talent = User.find(params[:user_id])
      
      invitation = JobInvitation.create!(
        job: job,
        user: talent,
        talent_profile: talent.talent_profile,
        invitation_letter: params[:message]
      )

      # Create the associated job application
      JobApplication.create!(
        job: job,
        user: talent,
        job_invitation: invitation,
        status: :offered
      )

      render json: { invitation: invitation }, status: :created
    rescue ActiveRecord::RecordInvalid => e
      render json: { errors: e.record.errors.full_messages }, status: :unprocessable_entity
    end

    private

    def require_scout_permissions
      unless current_user.scout?
        render json: { error: 'Unauthorized' }, status: :unauthorized
      end
    end
  end
end
