# frozen_string_literal: true

class AdminFilterOptionsService
  CACHE_EXPIRY = 1.hour

  def self.job_filter_options
    Rails.cache.fetch('admin_job_filter_options', expires_in: CACHE_EXPIRY) do
      {
        status: Job.statuses.map { |key, value| [key.humanize, key] },
        job_category: Job.job_categories.map { |key, value| [key.humanize, key] },
        platform: Job.platforms.map do |key, value|
          [key.humanize.gsub('_', ' '), key]
        end,
        budget_range: Job.budget_ranges.map do |key, value|
          [key.humanize.gsub('_', ' '), key]
        end,
        is_premium: [%w[Premium true], %w[Regular false]]
      }
    end
  end

  def self.user_filter_options
    Rails.cache.fetch('admin_user_filter_options', expires_in: CACHE_EXPIRY) do
      {
        verified: [%w[Verified true], %w[Unverified false]],
        onboarding_completed: [%w[Completed true], %w[Incomplete false]],
        signup_intent: User.distinct.pluck(:signup_intent).compact.map { |intent| [intent.humanize, intent] }
      }
    end
  end

  def self.role_filter_options
    Rails.cache.fetch('admin_role_filter_options', expires_in: CACHE_EXPIRY) do
      Role.pluck(:name).map { |name| [name.humanize, name] }
    end
  end

  def self.audit_log_filter_options
    Rails.cache.fetch('admin_audit_log_filter_options', expires_in: CACHE_EXPIRY) do
      {
        actions: AdminAuditLog.distinct.pluck(:action).compact.sort,
        resource_types: AdminAuditLog.distinct.pluck(:resource_type).compact.sort,
        admins: User.joins(:admin_audit_logs)
                   .distinct
                   .select(:id, :first_name, :last_name, :email)
                   .order(:first_name, :last_name)
      }
    end
  end

  def self.clear_all_caches
    Rails.cache.delete('admin_job_filter_options')
    Rails.cache.delete('admin_user_filter_options')
    Rails.cache.delete('admin_role_filter_options')
    Rails.cache.delete('admin_audit_log_filter_options')
  end

  def self.clear_cache(type)
    Rails.cache.delete("admin_#{type}_filter_options")
  end
end
