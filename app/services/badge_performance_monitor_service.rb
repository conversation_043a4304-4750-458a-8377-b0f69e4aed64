# frozen_string_literal: true

# Service for monitoring badge system performance
# Tracks render times, query performance, and system health
# Provides alerts and reporting for performance issues
class BadgePerformanceMonitorService
  include ActionView::Helpers::NumberHelper

  # In-memory storage for performance data (consider Redis for production)
  @@performance_data = {
    page_renders: [],
    queries: [],
    components: [],
    middleware: [],
    operations: [],
    alerts: [],
    health_checks: []
  }

  @@alert_cooldowns = {}

  class << self
    # Monitor a specific query or operation
    def monitor_query(operation_name)
      return yield unless enabled?

      start_time = Time.current
      memory_before = get_memory_usage if Rails.application.config.badge_performance_settings[:track_memory]

      result = yield

      end_time = Time.current
      duration = (end_time - start_time) * 1000 # Convert to milliseconds
      memory_after = get_memory_usage if Rails.application.config.badge_performance_settings[:track_memory]

      # Record the operation performance
      operation_data = {
        timestamp: Time.current,
        operation: operation_name,
        duration: duration.round(2),
        memory_before: memory_before&.round(2),
        memory_after: memory_after&.round(2),
        memory_delta: memory_after && memory_before ? (memory_after - memory_before).round(2) : nil
      }

      add_performance_record(:operations, operation_data)

      log_performance_data('OPERATION', operation_data) if detailed_logging?

      # Check for performance issues
      thresholds = Rails.application.config.badge_performance_thresholds
      if duration > thresholds[:analytics_critical]
        alert_performance_issue(
          type: 'critical_operation',
          operation: operation_name,
          duration: duration,
          threshold: thresholds[:analytics_critical]
        )
      elsif duration > thresholds[:analytics_warning]
        alert_performance_issue(
          type: 'warning_operation',
          operation: operation_name,
          duration: duration,
          threshold: thresholds[:analytics_warning]
        )
      end

      result
    end

    # Record page performance data
    def record_page_performance(controller:, action:, duration:, status:, method:, path:, params: {}, db_runtime: nil, view_runtime: nil, allocations: nil)
      return unless enabled?

      data = {
        timestamp: Time.current,
        controller: controller,
        action: action,
        controller_action: "#{controller}##{action}",
        duration: duration.round(2),
        status: status,
        method: method,
        path: path,
        params: sanitize_params(params),
        db_runtime: db_runtime&.round(2),
        view_runtime: view_runtime&.round(2),
        allocations: allocations,
        memory_usage: get_memory_usage
      }

      add_performance_record(:page_renders, data)
      
      log_performance_data('PAGE_RENDER', data) if detailed_logging?
    end

    # Record database query performance
    def record_query_performance(sql:, duration:, name: nil, connection_id: nil)
      return unless enabled?

      data = {
        timestamp: Time.current,
        sql: sanitize_sql(sql),
        duration: duration.round(2),
        name: name,
        connection_id: connection_id,
        query_type: extract_query_type(sql),
        table_name: extract_table_name(sql)
      }

      add_performance_record(:queries, data)
      
      log_performance_data('QUERY', data) if detailed_logging?
    end

    # Record component rendering performance
    def record_component_performance(partial:, duration:, count: 1)
      return unless enabled?

      data = {
        timestamp: Time.current,
        partial: partial,
        duration: duration.round(2),
        count: count,
        avg_duration_per_render: count > 0 ? (duration / count).round(2) : 0,
        component_name: extract_component_name(partial)
      }

      add_performance_record(:components, data)
      
      log_performance_data('COMPONENT', data) if detailed_logging?
    end

    # Record middleware performance
    def record_middleware_performance(path:, method:, duration:, status:, memory_before: nil, memory_after: nil, memory_delta: nil)
      return unless enabled?

      data = {
        timestamp: Time.current,
        path: path,
        method: method,
        duration: duration.round(2),
        status: status,
        memory_before: memory_before&.round(2),
        memory_after: memory_after&.round(2),
        memory_delta: memory_delta&.round(2)
      }

      add_performance_record(:middleware, data)
      
      log_performance_data('MIDDLEWARE', data) if detailed_logging?
    end

    # Alert on performance issues
    def alert_performance_issue(type:, **details)
      return unless enabled?

      # Check cooldown to prevent spam
      cooldown_key = "#{type}:#{details.values.join(':')}"
      return if in_cooldown?(cooldown_key)

      alert_data = {
        timestamp: Time.current,
        type: type,
        severity: determine_severity(type),
        details: details,
        environment: Rails.env
      }

      add_performance_record(:alerts, alert_data)
      
      # Set cooldown
      set_cooldown(cooldown_key)
      
      # Log alert
      log_performance_alert(alert_data)
      
      # Send notification if configured
      send_performance_notification(alert_data) if should_send_notification?(alert_data)
    end

    # Perform health check
    def perform_health_check
      return unless enabled?

      health_data = {
        timestamp: Time.current,
        memory_usage: get_memory_usage,
        performance_summary: generate_performance_summary,
        system_health: assess_system_health
      }

      add_performance_record(:health_checks, health_data)
      
      log_performance_data('HEALTH_CHECK', health_data) if detailed_logging?
      
      # Check for health issues
      check_health_thresholds(health_data)
    end

    # Get performance summary
    def get_performance_summary(timeframe: 1.hour)
      return {} unless enabled?

      cutoff_time = timeframe.ago
      
      {
        page_renders: summarize_page_renders(cutoff_time),
        queries: summarize_queries(cutoff_time),
        components: summarize_components(cutoff_time),
        operations: summarize_operations(cutoff_time),
        alerts: summarize_alerts(cutoff_time),
        health_status: get_current_health_status
      }
    end

    # Export performance data
    def export_performance_data(format: :json, timeframe: 24.hours)
      return {} unless enabled?

      cutoff_time = timeframe.ago
      
      data = {
        exported_at: Time.current,
        timeframe: "#{timeframe.to_i} seconds",
        page_renders: filter_by_time(:page_renders, cutoff_time),
        queries: filter_by_time(:queries, cutoff_time),
        components: filter_by_time(:components, cutoff_time),
        middleware: filter_by_time(:middleware, cutoff_time),
        operations: filter_by_time(:operations, cutoff_time),
        alerts: filter_by_time(:alerts, cutoff_time),
        health_checks: filter_by_time(:health_checks, cutoff_time)
      }

      case format
      when :json
        data.to_json
      when :csv
        export_to_csv(data)
      else
        data
      end
    end

    # Clear old performance data
    def cleanup_old_data(older_than: 24.hours)
      return unless enabled?

      cutoff_time = older_than.ago
      
      @@performance_data.each do |key, records|
        @@performance_data[key] = records.select { |record| record[:timestamp] > cutoff_time }
      end
      
      Rails.logger.info "[BADGE_PERFORMANCE] Cleaned up performance data older than #{older_than}"
    end

    private

    def enabled?
      Rails.application.config.badge_performance_monitoring_enabled
    end

    def detailed_logging?
      Rails.application.config.badge_performance_settings[:detailed_logging]
    end

    def sample_rate
      Rails.application.config.badge_performance_settings[:sample_rate]
    end

    def should_sample?
      rand <= sample_rate
    end

    def add_performance_record(type, data)
      return unless should_sample?

      @@performance_data[type] << data
      
      # Limit memory usage
      max_records = Rails.application.config.badge_performance_settings[:max_memory_records]
      if @@performance_data[type].size > max_records
        @@performance_data[type] = @@performance_data[type].last(max_records)
      end
    end

    def sanitize_params(params)
      return {} unless params.is_a?(Hash)
      
      params.except(:password, :password_confirmation, :authenticity_token)
            .transform_values { |v| v.is_a?(String) && v.length > 100 ? v.truncate(100) : v }
    end

    def sanitize_sql(sql)
      # Remove sensitive data and limit length
      sql.gsub(/('.*?'|".*?")/, "'[FILTERED]'").truncate(500)
    end

    def extract_query_type(sql)
      case sql.upcase
      when /^SELECT/
        'SELECT'
      when /^INSERT/
        'INSERT'
      when /^UPDATE/
        'UPDATE'
      when /^DELETE/
        'DELETE'
      else
        'OTHER'
      end
    end

    def extract_table_name(sql)
      # Simple table name extraction
      match = sql.match(/(?:FROM|INTO|UPDATE|JOIN)\s+`?(\w+)`?/i)
      match ? match[1] : 'unknown'
    end

    def extract_component_name(partial)
      File.basename(partial, '.*') if partial
    end

    def get_memory_usage
      return nil unless Rails.application.config.badge_performance_settings[:track_memory]
      
      begin
        `ps -o rss= -p #{Process.pid}`.to_i / 1024.0 # Convert to MB
      rescue
        nil
      end
    end

    def determine_severity(type)
      case type
      when /critical/
        'critical'
      when /warning/
        'warning'
      else
        'info'
      end
    end

    def in_cooldown?(key)
      cooldown_time = Rails.application.config.badge_performance_settings[:alert_cooldown]
      last_alert = @@alert_cooldowns[key]
      
      last_alert && (Time.current - last_alert) < cooldown_time
    end

    def set_cooldown(key)
      @@alert_cooldowns[key] = Time.current
    end

    def log_performance_data(type, data)
      Rails.logger.info "[BADGE_PERFORMANCE] #{type}: #{data.to_json}"
    end

    def log_performance_alert(alert_data)
      severity = alert_data[:severity].upcase
      Rails.logger.warn "[BADGE_PERFORMANCE] #{severity} ALERT: #{alert_data[:type]} - #{alert_data[:details]}"
    end

    def should_send_notification?(alert_data)
      # Only send notifications for critical alerts in production
      Rails.env.production? && alert_data[:severity] == 'critical'
    end

    def send_performance_notification(alert_data)
      # Placeholder for notification system integration
      # Could integrate with Slack, email, PagerDuty, etc.
      Rails.logger.error "[BADGE_PERFORMANCE] CRITICAL ALERT NOTIFICATION: #{alert_data}"
    end

    def generate_performance_summary
      {
        total_page_renders: @@performance_data[:page_renders].size,
        total_queries: @@performance_data[:queries].size,
        total_components: @@performance_data[:components].size,
        total_operations: @@performance_data[:operations].size,
        total_alerts: @@performance_data[:alerts].size,
        avg_page_render_time: calculate_average(:page_renders, :duration),
        avg_query_time: calculate_average(:queries, :duration),
        avg_component_render_time: calculate_average(:components, :duration),
        avg_operation_time: calculate_average(:operations, :duration)
      }
    end

    def assess_system_health
      recent_alerts = @@performance_data[:alerts].select { |a| a[:timestamp] > 1.hour.ago }
      critical_alerts = recent_alerts.count { |a| a[:severity] == 'critical' }
      warning_alerts = recent_alerts.count { |a| a[:severity] == 'warning' }
      
      if critical_alerts > 0
        'critical'
      elsif warning_alerts > 5
        'warning'
      else
        'healthy'
      end
    end

    def check_health_thresholds(health_data)
      if health_data[:system_health] == 'critical'
        alert_performance_issue(
          type: 'system_health_critical',
          health_status: health_data[:system_health],
          memory_usage: health_data[:memory_usage]
        )
      end
    end

    def calculate_average(type, field)
      records = @@performance_data[type]
      return 0 if records.empty?
      
      values = records.map { |r| r[field] }.compact
      return 0 if values.empty?
      
      (values.sum / values.size).round(2)
    end

    def summarize_page_renders(cutoff_time)
      records = filter_by_time(:page_renders, cutoff_time)
      
      {
        total_renders: records.size,
        avg_duration: calculate_average_for_records(records, :duration),
        slowest_render: records.max_by { |r| r[:duration] },
        by_controller: group_and_summarize(records, :controller_action)
      }
    end

    def summarize_queries(cutoff_time)
      records = filter_by_time(:queries, cutoff_time)
      
      {
        total_queries: records.size,
        avg_duration: calculate_average_for_records(records, :duration),
        slowest_query: records.max_by { |r| r[:duration] },
        by_type: group_and_summarize(records, :query_type),
        by_table: group_and_summarize(records, :table_name)
      }
    end

    def summarize_components(cutoff_time)
      records = filter_by_time(:components, cutoff_time)

      {
        total_renders: records.size,
        avg_duration: calculate_average_for_records(records, :duration),
        slowest_component: records.max_by { |r| r[:duration] },
        by_component: group_and_summarize(records, :component_name)
      }
    end

    def summarize_operations(cutoff_time)
      records = filter_by_time(:operations, cutoff_time)

      {
        total_operations: records.size,
        avg_duration: calculate_average_for_records(records, :duration),
        slowest_operation: records.max_by { |r| r[:duration] },
        by_operation: group_and_summarize(records, :operation)
      }
    end

    def summarize_alerts(cutoff_time)
      records = filter_by_time(:alerts, cutoff_time)
      
      {
        total_alerts: records.size,
        by_severity: records.group_by { |r| r[:severity] }.transform_values(&:size),
        by_type: records.group_by { |r| r[:type] }.transform_values(&:size),
        recent_critical: records.select { |r| r[:severity] == 'critical' }.last(5)
      }
    end

    def get_current_health_status
      recent_health_checks = @@performance_data[:health_checks].last(5)
      return 'unknown' if recent_health_checks.empty?
      
      recent_health_checks.last[:system_health]
    end

    def filter_by_time(type, cutoff_time)
      @@performance_data[type].select { |record| record[:timestamp] > cutoff_time }
    end

    def calculate_average_for_records(records, field)
      return 0 if records.empty?
      
      values = records.map { |r| r[field] }.compact
      return 0 if values.empty?
      
      (values.sum / values.size).round(2)
    end

    def group_and_summarize(records, field)
      records.group_by { |r| r[field] }
             .transform_values { |group| 
               {
                 count: group.size,
                 avg_duration: calculate_average_for_records(group, :duration)
               }
             }
    end

    def export_to_csv(data)
      require 'csv'
      
      CSV.generate(headers: true) do |csv|
        csv << ['Badge Performance Export', "Generated: #{Time.current}"]
        csv << ['Timeframe', data[:timeframe]]
        csv << []
        
        # Export page renders
        csv << ['PAGE RENDERS']
        csv << ['Timestamp', 'Controller', 'Action', 'Duration (ms)', 'Status', 'Method', 'Path']
        data[:page_renders].each do |render|
          csv << [
            render[:timestamp],
            render[:controller],
            render[:action],
            render[:duration],
            render[:status],
            render[:method],
            render[:path]
          ]
        end
        
        csv << []
        
        # Export queries
        csv << ['QUERIES']
        csv << ['Timestamp', 'Query Type', 'Table', 'Duration (ms)', 'SQL']
        data[:queries].each do |query|
          csv << [
            query[:timestamp],
            query[:query_type],
            query[:table_name],
            query[:duration],
            query[:sql]
          ]
        end
        
        csv << []

        # Export operations
        csv << ['OPERATIONS']
        csv << ['Timestamp', 'Operation', 'Duration (ms)', 'Memory Before (MB)', 'Memory After (MB)', 'Memory Delta (MB)']
        data[:operations].each do |operation|
          csv << [
            operation[:timestamp],
            operation[:operation],
            operation[:duration],
            operation[:memory_before],
            operation[:memory_after],
            operation[:memory_delta]
          ]
        end

        csv << []

        # Export alerts
        csv << ['ALERTS']
        csv << ['Timestamp', 'Type', 'Severity', 'Details']
        data[:alerts].each do |alert|
          csv << [
            alert[:timestamp],
            alert[:type],
            alert[:severity],
            alert[:details].to_json
          ]
        end
      end
    end
  end
end
