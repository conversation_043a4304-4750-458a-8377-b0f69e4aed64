class AdminNotificationService
  include Singleton

  # Notification types
  ERROR_NOTIFICATION = 'error'.freeze
  SECURITY_NOTIFICATION = 'security'.freeze
  PERFORMANCE_NOTIFICATION = 'performance'.freeze
  SYSTEM_NOTIFICATION = 'system'.freeze
  USER_NOTIFICATION = 'user'.freeze

  # Severity levels
  LOW = 'low'.freeze
  MEDIUM = 'medium'.freeze
  HIGH = 'high'.freeze
  CRITICAL = 'critical'.freeze

  class << self
    delegate :notify_error, :notify_security_event, :notify_performance_issue,
             :notify_system_event, :notify_user_action, :send_admin_alert,
             :get_recent_notifications, :mark_notification_read,
             to: :instance
  end

  def notify_error(error, context = {})
    severity = determine_error_severity(error, context)
    
    notification = create_notification(
      type: ERROR_NOTIFICATION,
      severity: severity,
      title: "Admin Error: #{error.class}",
      message: error.message,
      details: {
        error_class: error.class.to_s,
        error_message: error.message,
        backtrace: error.backtrace&.first(5),
        context: context
      }
    )

    # Send immediate alerts for critical errors
    if severity == CRIT<PERSON>AL
      send_immediate_alert(notification)
    end

    notification
  end

  def notify_security_event(event_type, details = {})
    severity = determine_security_severity(event_type, details)
    
    notification = create_notification(
      type: SECURITY_NOTIFICATION,
      severity: severity,
      title: "Security Event: #{event_type.humanize}",
      message: build_security_message(event_type, details),
      details: details.merge(event_type: event_type)
    )

    # Send immediate alerts for high/critical security events
    if [HIGH, CRITICAL].include?(severity)
      send_immediate_alert(notification)
    end

    notification
  end

  def notify_performance_issue(operation, duration, details = {})
    severity = determine_performance_severity(duration, details)
    
    notification = create_notification(
      type: PERFORMANCE_NOTIFICATION,
      severity: severity,
      title: "Performance Issue: #{operation}",
      message: "Operation took #{duration}ms (threshold exceeded)",
      details: details.merge(
        operation: operation,
        duration: duration,
        threshold_type: determine_threshold_type(duration)
      )
    )

    notification
  end

  def notify_system_event(event_type, details = {})
    severity = details[:severity] || MEDIUM
    
    notification = create_notification(
      type: SYSTEM_NOTIFICATION,
      severity: severity,
      title: "System Event: #{event_type.humanize}",
      message: details[:message] || "System event occurred: #{event_type}",
      details: details.merge(event_type: event_type)
    )

    notification
  end

  def notify_user_action(action, user, resource = nil, details = {})
    severity = determine_user_action_severity(action, details)
    
    notification = create_notification(
      type: USER_NOTIFICATION,
      severity: severity,
      title: "Admin Action: #{action.humanize}",
      message: build_user_action_message(action, user, resource),
      details: details.merge(
        action: action,
        user_id: user&.id,
        user_email: user&.email,
        resource_type: resource&.class&.name,
        resource_id: resource&.id
      )
    )

    notification
  end

  def send_admin_alert(notification)
    # Send email alerts to superadmins for critical notifications
    if notification[:severity] == CRITICAL
      superadmins = User.where(admin_role: 'superadmin')
      superadmins.find_each do |admin|
        AdminMailer.critical_alert(admin, notification).deliver_later
      end
    end

    # Send Slack/Discord webhook if configured
    send_webhook_alert(notification) if webhook_configured?

    # Log to external monitoring
    send_to_monitoring(notification)
  end

  def get_recent_notifications(limit: 50, user: nil)
    # Get notifications relevant to the user's admin role
    notifications = AdminAuditLog.where(action: 'admin_notification')
                                .order(created_at: :desc)
                                .limit(limit)

    # Filter by user's permission level if specified
    if user && !user.superadmin?
      notifications = notifications.where(
        "change_data->>'severity' NOT IN (?)", 
        [CRITICAL]
      )
    end

    notifications.map { |log| parse_notification(log) }
  end

  def mark_notification_read(notification_id, user)
    notification = AdminAuditLog.find(notification_id)
    
    read_by = notification.change_data['read_by'] || []
    read_by << {
      user_id: user.id,
      user_email: user.email,
      read_at: Time.current.iso8601
    }

    notification.update!(
      change_data: notification.change_data.merge('read_by' => read_by)
    )
  end

  private

  def create_notification(type:, severity:, title:, message:, details: {})
    notification_data = {
      id: SecureRandom.uuid,
      type: type,
      severity: severity,
      title: title,
      message: message,
      details: details,
      created_at: Time.current.iso8601,
      read_by: []
    }

    # Store in AdminAuditLog
    AdminAuditLog.create!(
      action: 'admin_notification',
      controller: 'admin_notification_service',
      admin_user: Current.user,
      change_data: notification_data
    )

    notification_data
  end

  def determine_error_severity(error, context)
    case error
    when ActiveRecord::ConnectionTimeoutError, 
         ActiveRecord::ConnectionNotEstablished
      CRITICAL
    when ActiveRecord::RecordNotFound,
         ActionController::ParameterMissing
      LOW
    when ActiveRecord::RecordInvalid,
         ActionController::InvalidAuthenticityToken
      MEDIUM
    else
      # Check context for severity hints
      if context[:controller]&.include?('admin') && 
         context[:action]&.in?(%w[destroy update create])
        HIGH
      else
        MEDIUM
      end
    end
  end

  def determine_security_severity(event_type, details)
    case event_type.to_s
    when 'unauthorized_access', 'privilege_escalation', 'data_breach'
      CRITICAL
    when 'failed_login_attempts', 'suspicious_activity', 'permission_violation'
      HIGH
    when 'password_reset', 'role_change', 'impersonation_start'
      MEDIUM
    else
      LOW
    end
  end

  def determine_performance_severity(duration, details)
    if duration > 5000 # 5 seconds
      CRITICAL
    elsif duration > 2000 # 2 seconds
      HIGH
    elsif duration > 1000 # 1 second
      MEDIUM
    else
      LOW
    end
  end

  def determine_user_action_severity(action, details)
    case action.to_s
    when 'user_deleted', 'role_changed', 'bulk_operation'
      HIGH
    when 'user_updated', 'organization_modified', 'job_deleted'
      MEDIUM
    else
      LOW
    end
  end

  def build_security_message(event_type, details)
    case event_type.to_s
    when 'unauthorized_access'
      "Unauthorized access attempt from #{details[:ip_address]} for #{details[:resource]}"
    when 'failed_login_attempts'
      "Multiple failed login attempts for #{details[:email]} from #{details[:ip_address]}"
    when 'privilege_escalation'
      "Privilege escalation attempt by #{details[:user_email]}"
    when 'impersonation_start'
      "#{details[:admin_email]} started impersonating #{details[:target_email]}"
    else
      "Security event: #{event_type.humanize}"
    end
  end

  def build_user_action_message(action, user, resource)
    user_info = user ? "#{user.email}" : "Unknown user"
    resource_info = resource ? "#{resource.class.name} ##{resource.id}" : "system"
    
    "#{user_info} performed #{action.humanize.downcase} on #{resource_info}"
  end

  def determine_threshold_type(duration)
    if duration > AdminPerformanceMonitorService::SLOW_REQUEST_THRESHOLD
      'request_threshold'
    elsif duration > AdminPerformanceMonitorService::SLOW_QUERY_THRESHOLD
      'query_threshold'
    else
      'custom_threshold'
    end
  end

  def send_immediate_alert(notification)
    # Send to all superadmins immediately
    send_admin_alert(notification)
    
    # Log the alert
    Rails.logger.error "CRITICAL ADMIN ALERT: #{notification[:title]} - #{notification[:message]}"
  end

  def webhook_configured?
    ENV['ADMIN_WEBHOOK_URL'].present?
  end

  def send_webhook_alert(notification)
    return unless webhook_configured?

    webhook_payload = {
      text: "Admin Alert: #{notification[:title]}",
      attachments: [{
        color: severity_color(notification[:severity]),
        fields: [
          { title: "Severity", value: notification[:severity].upcase, short: true },
          { title: "Type", value: notification[:type].humanize, short: true },
          { title: "Message", value: notification[:message], short: false }
        ],
        timestamp: notification[:created_at]
      }]
    }

    # Send webhook (implement based on your webhook service)
    # HTTParty.post(ENV['ADMIN_WEBHOOK_URL'], body: webhook_payload.to_json, headers: { 'Content-Type' => 'application/json' })
  rescue => e
    Rails.logger.error "Failed to send webhook alert: #{e.message}"
  end

  def send_to_monitoring(notification)
    # Send to Sentry for critical notifications
    if notification[:severity] == CRITICAL
      Sentry.capture_message(
        "Critical Admin Notification: #{notification[:title]}",
        level: :error,
        extra: notification
      )
    end
  end

  def severity_color(severity)
    case severity
    when CRITICAL then 'danger'
    when HIGH then 'warning'
    when MEDIUM then 'good'
    else 'good'
    end
  end

  def parse_notification(audit_log)
    audit_log.change_data.symbolize_keys.merge(
      id: audit_log.id,
      created_at: audit_log.created_at
    )
  end
end
