# frozen_string_literal: true

class AdminPerformanceMonitorService
  include Singleton

  # Performance thresholds (in milliseconds)
  SLOW_QUERY_THRESHOLD = 1000
  SLOW_REQUEST_THRESHOLD = 2000
  MEMORY_WARNING_THRESHOLD = 500 # MB

  class << self
    delegate :monitor_query, :monitor_request, :monitor_memory_usage,
             :log_slow_operation, :get_performance_stats, :check_system_health,
             to: :instance
  end

  def monitor_query(description, &block)
    start_time = Time.current
    memory_before = get_memory_usage
    sql_count_before = get_sql_count

    result = yield

    end_time = Time.current
    sql_count_after = get_sql_count
    memory_after = get_memory_usage

    duration = ((end_time - start_time) * 1000).round(2)
    query_count = sql_count_after - sql_count_before
    memory_used = memory_after - memory_before

    log_performance(description, duration, query_count, memory_used)

    # Log to structured logging service for production monitoring
    if duration > SLOW_QUERY_THRESHOLD || query_count > 20
      AdminLoggingService.log_performance_event(
        operation: description,
        duration: duration,
        details: {
          query_count: query_count,
          memory_used_mb: memory_used,
          slow_operation: duration > SLOW_QUERY_THRESHOLD,
          potential_n_plus_one: query_count > 20
        }
      )
    end

    result
  rescue => e
    AdminLoggingService.log_error_event(
      error: e,
      context: { operation: description, type: 'database_query' }
    )
    raise
  end

  def monitor_request(controller, action, &block)
    start_time = Time.current
    memory_before = get_memory_usage

    result = yield

    duration = ((Time.current - start_time) * 1000).round(2)
    memory_after = get_memory_usage
    memory_used = memory_after - memory_before

    if duration > SLOW_REQUEST_THRESHOLD
      AdminLoggingService.log_performance_event(
        operation: "#{controller}##{action}",
        duration: duration,
        details: {
          memory_used_mb: memory_used,
          slow_request: true,
          controller: controller,
          action: action
        }
      )
    end

    result
  rescue => e
    AdminLoggingService.log_error_event(
      error: e,
      context: { controller: controller, action: action, type: 'http_request' }
    )
    raise
  end

  def monitor_memory_usage
    current_usage = get_memory_usage

    if current_usage > MEMORY_WARNING_THRESHOLD
      AdminLoggingService.log_system_event(
        event: 'high_memory_usage',
        details: {
          current_usage_mb: current_usage,
          threshold_mb: MEMORY_WARNING_THRESHOLD,
          severity: 'warning'
        }
      )
    end

    current_usage
  end

  def log_slow_operation(operation_name, duration, details = {})
    AdminLoggingService.log_performance_event(
      operation: operation_name,
      duration: duration,
      details: details.merge(
        slow_operation: true,
        threshold_exceeded: duration > SLOW_REQUEST_THRESHOLD
      )
    )
  end

  def get_performance_stats(time_range = 24.hours)
    # Implementation for getting performance statistics
    {
      slow_queries: 0, # Placeholder - implement based on your logging
      slow_requests: 0,
      average_response_time: 0,
      memory_usage_trend: 'stable',
      error_rate: 0
    }
  end

  def check_system_health
    {
      database: check_database_health,
      memory: check_memory_health,
      overall: 'healthy'
    }
  end

  def self.log_slow_queries
    return unless Rails.env.development?

    ActiveSupport::Notifications.subscribe('sql.active_record') do |name, start, finish, id, payload|
      duration = (finish - start) * 1000

      if duration > 100 # Log queries taking more than 100ms
        Rails.logger.warn "SLOW QUERY (#{duration.round(2)}ms): #{payload[:sql]}"
      end
    end
  end

  private

  def get_memory_usage
    # Get current memory usage in MB
    if defined?(GC.stat)
      # Ruby memory usage
      gc_stat = GC.stat
      heap_pages = gc_stat[:heap_allocated_pages]
      page_size = gc_stat[:heap_page_size]

      if heap_pages && page_size
        (heap_pages * page_size) / (1024 * 1024)
      else
        # Fallback to system memory if GC stats are incomplete
        get_system_memory_usage
      end
    else
      # Fallback - try to get system memory
      get_system_memory_usage
    end
  end

  def get_system_memory_usage
    `ps -o rss= -p #{Process.pid}`.to_i / 1024 rescue 0
  end

  def check_database_health
    begin
      start_time = Time.current
      ActiveRecord::Base.connection.execute('SELECT 1')
      response_time = ((Time.current - start_time) * 1000).round(2)

      if response_time > 1000
        { status: 'critical', response_time: response_time }
      elsif response_time > 500
        { status: 'warning', response_time: response_time }
      else
        { status: 'healthy', response_time: response_time }
      end
    rescue => e
      { status: 'critical', error: e.message }
    end
  end

  def check_memory_health
    current_usage = get_memory_usage

    if current_usage > MEMORY_WARNING_THRESHOLD * 1.5
      { status: 'critical', usage_mb: current_usage }
    elsif current_usage > MEMORY_WARNING_THRESHOLD
      { status: 'warning', usage_mb: current_usage }
    else
      { status: 'healthy', usage_mb: current_usage }
    end
  end

  def get_sql_count
    # Simple approximation - in a real implementation you might want to track this more precisely
    # For now, return 0 as we're focusing on memory and timing metrics
    0
  end

  def log_performance(description, duration, query_count, memory_used = 0)
    status = case
             when duration > 1000
               'SLOW'
             when query_count > 10
               'N+1?'
             else
               'OK'
             end

    Rails.logger.info "ADMIN PERF [#{status}] #{description}: #{duration}ms, #{query_count} queries, #{memory_used}MB"
  end
end

# Initialize slow query logging in development
AdminPerformanceMonitorService.log_slow_queries if Rails.env.development?
