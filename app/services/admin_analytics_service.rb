# frozen_string_literal: true

class AdminAnalyticsService
  def self.generate_dashboard_analytics
    new.generate_dashboard_analytics
  end

  def generate_dashboard_analytics
    {
      growth_trends: calculate_growth_trends,
      user_engagement: calculate_user_engagement,
      job_metrics: calculate_job_metrics,
      conversion_rates: calculate_conversion_rates,
      recent_activity_summary: calculate_recent_activity_summary
    }
  end

  private

  def calculate_growth_trends
    {
      users: {
        daily: daily_growth(User),
        weekly: weekly_growth(User),
        monthly: monthly_growth(User)
      },
      jobs: {
        daily: daily_growth(Job),
        weekly: weekly_growth(Job),
        monthly: monthly_growth(Job)
      },
      organizations: {
        daily: daily_growth(Organization),
        weekly: weekly_growth(Organization),
        monthly: monthly_growth(Organization)
      }
    }
  end

  def calculate_user_engagement
    total_users = User.count
    active_users_7d = User.where('updated_at > ?', 7.days.ago).count
    active_users_30d = User.where('updated_at > ?', 30.days.ago).count
    
    verified_users = User.where(verified: true).count
    users_with_profiles = User.joins(:talent_profile).count
    users_with_organizations = User.joins(:organization_memberships).count

    {
      total_users: total_users,
      active_7_days: active_users_7d,
      active_30_days: active_users_30d,
      engagement_rate_7d: total_users > 0 ? (active_users_7d.to_f / total_users * 100).round(2) : 0,
      engagement_rate_30d: total_users > 0 ? (active_users_30d.to_f / total_users * 100).round(2) : 0,
      verification_rate: total_users > 0 ? (verified_users.to_f / total_users * 100).round(2) : 0,
      profile_completion_rate: total_users > 0 ? (users_with_profiles.to_f / total_users * 100).round(2) : 0,
      organization_join_rate: total_users > 0 ? (users_with_organizations.to_f / total_users * 100).round(2) : 0
    }
  end

  def calculate_job_metrics
    total_jobs = Job.count
    published_jobs = Job.published.count
    draft_jobs = Job.draft.count
    expired_jobs = Job.expired.count
    
    jobs_with_applications = Job.joins(:job_applications).distinct.count
    avg_applications_per_job = total_jobs > 0 ? (JobApplication.count.to_f / total_jobs).round(2) : 0
    
    recent_jobs = Job.where('created_at > ?', 7.days.ago).count
    jobs_published_this_week = Job.published.where('created_at > ?', 7.days.ago).count

    {
      total_jobs: total_jobs,
      published: published_jobs,
      draft: draft_jobs,
      expired: expired_jobs,
      publish_rate: total_jobs > 0 ? (published_jobs.to_f / total_jobs * 100).round(2) : 0,
      jobs_with_applications: jobs_with_applications,
      application_rate: total_jobs > 0 ? (jobs_with_applications.to_f / total_jobs * 100).round(2) : 0,
      avg_applications_per_job: avg_applications_per_job,
      recent_activity: {
        new_jobs_7d: recent_jobs,
        published_jobs_7d: jobs_published_this_week
      }
    }
  end

  def calculate_conversion_rates
    total_applications = JobApplication.count
    accepted_applications = JobApplication.where(status: 'accepted').count
    pending_applications = JobApplication.where(status: 'pending').count
    
    total_chat_requests = ChatRequest.count
    accepted_chat_requests = ChatRequest.where(status: 'accepted').count
    
    total_conversations = Conversation.count
    active_conversations = Conversation.joins(:messages).where('messages.created_at > ?', 7.days.ago).distinct.count

    {
      job_applications: {
        total: total_applications,
        accepted: accepted_applications,
        pending: pending_applications,
        acceptance_rate: total_applications > 0 ? (accepted_applications.to_f / total_applications * 100).round(2) : 0
      },
      chat_requests: {
        total: total_chat_requests,
        accepted: accepted_chat_requests,
        acceptance_rate: total_chat_requests > 0 ? (accepted_chat_requests.to_f / total_chat_requests * 100).round(2) : 0
      },
      conversations: {
        total: total_conversations,
        active_7d: active_conversations,
        activity_rate: total_conversations > 0 ? (active_conversations.to_f / total_conversations * 100).round(2) : 0
      }
    }
  end

  def calculate_recent_activity_summary
    {
      new_users_today: User.where('created_at > ?', 1.day.ago).count,
      new_jobs_today: Job.where('created_at > ?', 1.day.ago).count,
      new_applications_today: JobApplication.where('created_at > ?', 1.day.ago).count,
      new_messages_today: Message.where('created_at > ?', 1.day.ago).count,
      admin_actions_today: AdminAuditLog.where('created_at > ?', 1.day.ago).count,
      peak_activity_hour: calculate_peak_activity_hour
    }
  end

  def daily_growth(model)
    today = model.where('created_at > ?', 1.day.ago).count
    yesterday = model.where('created_at BETWEEN ? AND ?', 2.days.ago, 1.day.ago).count
    
    {
      today: today,
      yesterday: yesterday,
      change: yesterday > 0 ? ((today - yesterday).to_f / yesterday * 100).round(2) : (today > 0 ? 100 : 0)
    }
  end

  def weekly_growth(model)
    this_week = model.where('created_at > ?', 1.week.ago).count
    last_week = model.where('created_at BETWEEN ? AND ?', 2.weeks.ago, 1.week.ago).count
    
    {
      this_week: this_week,
      last_week: last_week,
      change: last_week > 0 ? ((this_week - last_week).to_f / last_week * 100).round(2) : (this_week > 0 ? 100 : 0)
    }
  end

  def monthly_growth(model)
    this_month = model.where('created_at > ?', 1.month.ago).count
    last_month = model.where('created_at BETWEEN ? AND ?', 2.months.ago, 1.month.ago).count
    
    {
      this_month: this_month,
      last_month: last_month,
      change: last_month > 0 ? ((this_month - last_month).to_f / last_month * 100).round(2) : (this_month > 0 ? 100 : 0)
    }
  end

  def calculate_peak_activity_hour
    # Find the hour with most user registrations in the last 7 days
    User.where('created_at > ?', 7.days.ago)
        .group("EXTRACT(hour FROM created_at)")
        .count
        .max_by { |hour, count| count }
        &.first&.to_i || 12 # Default to noon if no data
  end
end
