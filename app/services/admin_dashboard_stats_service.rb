# frozen_string_literal: true

class AdminDashboardStatsService
  CACHE_EXPIRY = 15.minutes

  def self.generate_stats
    Rails
      .cache
      .fetch('admin_dashboard_stats', expires_in: CACHE_EXPIRY) do
        new.generate_stats
      end
  end

  def self.clear_cache
    Rails.cache.delete('admin_dashboard_stats')
  end

  def generate_stats
    {
      users: user_stats,
      jobs: job_stats,
      organizations: organization_stats,
      communication: communication_stats,
      applications: application_stats,
    }
  end

  private

  def user_stats
    # Use a single query with conditional aggregation for better performance
    user_counts =
      User
        .joins('LEFT JOIN user_roles ON users.id = user_roles.user_id')
        .joins('LEFT JOIN roles ON user_roles.role_id = roles.id')
        .group('roles.name')
        .count

    {
      total: User.count,
      verified: User.where(verified: true).count,
      scouts: user_counts['scout'] || 0,
      talents: user_counts['talent'] || 0,
    }
  end

  def job_stats
    # Use enum values directly for better performance with indexes
    job_counts = Job.group(:status).count

    {
      total: Job.count,
      published: job_counts['published'] || 0,
      draft: job_counts['draft'] || 0,
      expired: job_counts['expired'] || 0,
    }
  end

  def organization_stats
    {
      total: Organization.count,
      # Use counter cache for better performance
      with_jobs: Organization.where('jobs_count > 0').count,
      total_jobs: Organization.sum(:jobs_count),
    }
  end

  def communication_stats
    {
      chat_requests: ChatRequest.count,
      conversations: Conversation.count,
      # Use counter cache for better performance
      messages: Conversation.sum(:messages_count),
    }
  end

  def application_stats
    return { total: 0, pending: 0, accepted: 0 } unless defined?(JobApplication)

    app_counts = JobApplication.group(:status).count

    {
      # Use counter cache for better performance
      total: Job.sum(:job_applications_count),
      pending: app_counts['applied'] || 0, # 'applied' is the enum value for pending
      accepted: app_counts['accepted'] || 0,
    }
  end
end
