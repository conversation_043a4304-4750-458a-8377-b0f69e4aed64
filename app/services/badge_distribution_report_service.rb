# frozen_string_literal: true

# Service for generating badge distribution reports and analytics
# Provides insights into badge distribution patterns, assignment trends, and usage statistics
class BadgeDistributionReportService
  include ActionView::Helpers::NumberHelper

  # Generate comprehensive badge distribution report
  # @param date_range [ActiveSupport::Duration] Time period to analyze (default: 30 days)
  # @param badge_type_id [Integer, nil] Optional filter for specific badge type
  # @return [Hash] Comprehensive distribution report data
  def self.generate_distribution_report(date_range: 30.days, badge_type_id: nil)
    new(date_range: date_range, badge_type_id: badge_type_id).generate_report
  end

  # Export distribution report as CSV
  # @param date_range [ActiveSupport::Duration] Time period to analyze
  # @param badge_type_id [Integer, nil] Optional filter for specific badge type
  # @return [String] CSV formatted report data
  def self.export_to_csv(date_range: 30.days, badge_type_id: nil)
    new(date_range: date_range, badge_type_id: badge_type_id).export_csv
  end

  def initialize(date_range: 30.days, badge_type_id: nil)
    @date_range = date_range
    @badge_type_id = badge_type_id
    @start_date = date_range.ago
    @end_date = Time.current
  end

  def generate_report
    Rails
      .cache
      .fetch(cache_key, expires_in: 30.minutes) do
        {
          summary: generate_summary_metrics,
          distribution_by_badge_type: distribution_by_badge_type,
          distribution_by_user_role: distribution_by_user_role,
          assignment_trends: assignment_trends_over_time,
          geographic_distribution: geographic_distribution,
          user_badge_counts: user_badge_count_distribution,
          assignment_velocity: assignment_velocity_metrics,
          badge_lifecycle: badge_lifecycle_analysis,
          admin_activity: admin_assignment_activity,
          expiration_analysis: expiration_analysis,
        }
      end
  end

  def export_csv
    require 'csv'

    report_data = generate_report

    CSV.generate(headers: true) do |csv|
      # Header information
      csv << [
        'Badge Distribution Report',
        "Generated: #{Time.current.strftime('%Y-%m-%d %H:%M:%S')}",
      ]
      csv << [
        'Date Range',
        "#{@date_range.to_i} days (#{@start_date.strftime('%Y-%m-%d')} to #{@end_date.strftime('%Y-%m-%d')})",
      ]
      csv << ['Badge Filter', badge_filter_description]
      csv << []

      # Summary metrics
      export_summary_section(csv, report_data[:summary])

      # Distribution by badge type
      export_badge_type_distribution(
        csv,
        report_data[:distribution_by_badge_type],
      )

      # Distribution by user role
      export_user_role_distribution(
        csv,
        report_data[:distribution_by_user_role],
      )

      # Assignment trends
      export_assignment_trends(csv, report_data[:assignment_trends])

      # User badge counts
      export_user_badge_counts(csv, report_data[:user_badge_counts])

      # Assignment velocity
      export_assignment_velocity(csv, report_data[:assignment_velocity])

      # Admin activity
      export_admin_activity(csv, report_data[:admin_activity])

      # Expiration analysis
      export_expiration_analysis(csv, report_data[:expiration_analysis])
    end
  end

  private

  attr_reader :date_range, :badge_type_id, :start_date, :end_date

  def cache_key
    "badge_distribution_report:#{@date_range.to_i}:#{@badge_type_id}:#{Date.current}"
  end

  def badge_filter_description
    @badge_type_id ? BadgeType.find(@badge_type_id).name : 'All Badge Types'
  end

  def base_assignments_scope
    scope =
      BadgeAssignment
        .includes(:badge_type, :user, :admin)
        .where('badge_assignments.created_at >= ?', @start_date)

    scope = scope.where(badge_type_id: @badge_type_id) if @badge_type_id
    scope
  end

  def generate_summary_metrics
    assignments = base_assignments_scope

    {
      total_assignments: assignments.count,
      unique_users_with_badges: assignments.select(:user_id).distinct.count,
      unique_badge_types_assigned:
        assignments.select(:badge_type_id).distinct.count,
      unique_admins_assigning: assignments.select(:admin_id).distinct.count,
      assignments_with_expiration: assignments.where.not(expires_at: nil).count,
      expired_assignments:
        assignments.where('expires_at < ?', Time.current).count,
      active_assignments:
        assignments.where('expires_at IS NULL OR expires_at > ?', Time.current)
          .count,
      avg_assignments_per_day: calculate_avg_assignments_per_day(assignments),
      most_active_assignment_day: find_most_active_assignment_day(assignments),
    }
  end

  def distribution_by_badge_type
    assignments = base_assignments_scope

    # Get badge type distribution with additional metrics
    # Use separate queries to avoid PostgreSQL GROUP BY issues
    badge_stats = []

    # Get all badge types that have assignments
    badge_type_ids = assignments.distinct.pluck(:badge_type_id)

    badge_type_ids.each do |badge_type_id|
      badge_type = BadgeType.find(badge_type_id)

      # Get assignment count for this badge type
      assignment_count = assignments.where(badge_type_id: badge_type_id).count

      # Get unique users for this badge type
      unique_users =
        assignments.where(badge_type_id: badge_type_id).distinct.count(:user_id)

      # Get unique admins for this badge type
      unique_admins =
        assignments
          .where(badge_type_id: badge_type_id)
          .distinct
          .count(:admin_id)

      badge_stats <<
        OpenStruct.new(
          id: badge_type.id,
          name: badge_type.name,
          background_color: badge_type.background_color,
          assignment_count: assignment_count,
          unique_users: unique_users,
          unique_admins: unique_admins,
        )
    end

    # Sort by assignment count descending
    badge_stats.sort_by! { |stat| -stat.assignment_count }

    badge_stats.map do |stat|
      badge_type = BadgeType.find(stat.id)

      {
        badge_type: {
          id: stat.id,
          name: stat.name,
          background_color: stat.background_color,
          icon: badge_type.icon,
        },
        assignment_count: stat.assignment_count,
        unique_users: stat.unique_users,
        unique_admins: stat.unique_admins,
        percentage_of_total:
          calculate_percentage(stat.assignment_count, assignments.count),
        avg_assignments_per_user:
          if stat.unique_users > 0
            (stat.assignment_count.to_f / stat.unique_users).round(2)
          else
            0
          end,
        recent_assignments:
          assignments
            .where(badge_type_id: stat.id)
            .where('badge_assignments.created_at >= ?', 7.days.ago)
            .count,
      }
    end
  end

  def distribution_by_user_role
    # This assumes users have roles - adjust based on your user role system
    assignments = base_assignments_scope

    # Use completely separate queries to avoid PostgreSQL GROUP BY issues
    role_stats = []

    # Get all user IDs that have assignments
    user_ids_with_assignments = assignments.distinct.pluck(:user_id)

    # Group users by their roles
    users_by_role = {}

    user_ids_with_assignments.each do |user_id|
      user = User.find(user_id)
      role_names = user.roles.pluck(:name)

      if role_names.empty?
        role_name = 'No Role'
      else
        # For users with multiple roles, we'll count them for each role
        role_names.each do |role_name|
          users_by_role[role_name] ||= []
          users_by_role[role_name] << user_id
        end
        next # Skip the 'No Role' assignment for users with roles
      end

      users_by_role[role_name] ||= []
      users_by_role[role_name] << user_id
    end

    users_by_role.each do |role_name, user_ids|
      # Count assignments for users with this role
      assignment_count = assignments.where(user_id: user_ids).count
      unique_users = user_ids.uniq.count

      role_stats <<
        OpenStruct.new(
          role_name: role_name,
          assignment_count: assignment_count,
          unique_users: unique_users,
        )
    end

    # Sort by assignment count descending
    role_distribution = role_stats.sort_by { |stat| -stat.assignment_count }

    role_distribution.map do |stat|
      {
        role_name: stat.role_name,
        assignment_count: stat.assignment_count,
        unique_users: stat.unique_users,
        percentage_of_total:
          calculate_percentage(stat.assignment_count, assignments.count),
        avg_badges_per_user:
          if stat.unique_users > 0
            (stat.assignment_count.to_f / stat.unique_users).round(2)
          else
            0
          end,
      }
    end
  end

  def assignment_trends_over_time
    assignments = base_assignments_scope

    # Daily assignment counts
    daily_counts =
      assignments.group_by_day(:created_at, range: @start_date..@end_date).count

    # Weekly assignment counts for longer periods
    weekly_counts =
      assignments.group_by_week(:created_at, range: @start_date..@end_date)
        .count if @date_range > 30.days

    {
      daily_assignments:
        daily_counts.map do |date, count|
          { date: date.strftime('%Y-%m-%d'), count: count }
        end,
      weekly_assignments:
        weekly_counts&.map do |date, count|
          { week_start: date.strftime('%Y-%m-%d'), count: count }
        end,
      peak_assignment_day:
        daily_counts.max_by { |_, count| count }&.first&.strftime('%Y-%m-%d'),
      lowest_assignment_day:
        daily_counts.min_by { |_, count| count }&.first&.strftime('%Y-%m-%d'),
      trend_direction: calculate_trend_direction(daily_counts),
    }
  end

  def geographic_distribution
    # This is a placeholder - implement based on your user location data
    # For now, we'll return a simple distribution by user creation patterns
    assignments = base_assignments_scope

    # Group by user creation date patterns as a proxy for geographic distribution
    time_zone_distribution =
      assignments
        .joins(:user)
        .group('EXTRACT(hour FROM users.created_at)')
        .count

    {
      note: 'Geographic distribution requires user location data',
      user_creation_hour_distribution: time_zone_distribution,
      total_countries: 'N/A - Requires location data',
      top_regions: [],
    }
  end

  def user_badge_count_distribution
    # Analyze how many badges users have
    user_badge_counts = base_assignments_scope.group(:user_id).count

    distribution_hash =
      user_badge_counts.values.group_by(&:itself).transform_values(&:count)

    distribution = distribution_hash.sort

    {
      distribution:
        distribution.map do |badge_count, user_count|
          {
            badge_count: badge_count,
            user_count: user_count,
            percentage:
              calculate_percentage(user_count, user_badge_counts.count),
          }
        end,
      avg_badges_per_user:
        distribution.sum { |badge_count, user_count| badge_count * user_count }
          .to_f / user_badge_counts.count,
      max_badges_per_user: distribution_hash.keys.max || 0,
      users_with_multiple_badges:
        distribution_hash.select { |badge_count, _| badge_count > 1 }.values.sum,
    }
  end

  def assignment_velocity_metrics
    assignments = base_assignments_scope.order(:created_at)

    if assignments.count < 2
      return { note: 'Insufficient data for velocity analysis' }
    end

    # Calculate time between assignments
    assignment_times = assignments.pluck(:created_at)
    intervals = assignment_times.each_cons(2).map { |prev, curr| curr - prev }

    {
      avg_time_between_assignments: intervals.sum / intervals.count,
      median_time_between_assignments: intervals.sort[intervals.count / 2],
      fastest_assignment_interval: intervals.min,
      slowest_assignment_interval: intervals.max,
      assignment_frequency_trend: calculate_frequency_trend(assignment_times),
    }
  end

  def badge_lifecycle_analysis
    all_assignments = BadgeAssignment.includes(:badge_type)

    lifecycle_data =
      all_assignments
        .group(:badge_type_id)
        .select(
          :badge_type_id,
          'MIN(created_at) as first_assignment',
          'MAX(created_at) as latest_assignment',
          'COUNT(*) as total_assignments',
        )

    lifecycle_data.map do |data|
      badge_type = BadgeType.find(data.badge_type_id)
      days_active = (data.latest_assignment - data.first_assignment) / 1.day

      {
        badge_type: {
          id: badge_type.id,
          name: badge_type.name,
        },
        first_assignment: data.first_assignment,
        latest_assignment: data.latest_assignment,
        days_active: days_active.round(1),
        total_assignments: data.total_assignments,
        avg_assignments_per_day:
          days_active > 0 ? (data.total_assignments / days_active).round(2) : 0,
      }
    end
  end

  def admin_assignment_activity
    assignments = base_assignments_scope

    # Use completely separate queries to avoid PostgreSQL GROUP BY issues
    admin_data = []

    # Get all admin IDs that have made assignments
    admin_ids_with_assignments = assignments.distinct.pluck(:admin_id).compact

    admin_ids_with_assignments.each do |admin_id|
      admin = User.find(admin_id)
      assignment_count = assignments.where(admin_id: admin_id).count

      # Get unique badge types for this admin
      unique_badge_types = assignments.where(admin_id: admin_id).distinct.count(:badge_type_id)

      # Get unique users assigned by this admin
      unique_users_assigned = assignments.where(admin_id: admin_id).distinct.count(:user_id)

      admin_data <<
        OpenStruct.new(
          id: admin_id,
          name: admin.name,
          email: admin.email,
          assignment_count: assignment_count,
          unique_badge_types: unique_badge_types,
          unique_users_assigned: unique_users_assigned,
        )
    end

    # Sort by assignment count descending
    admin_stats = admin_data.sort_by { |stat| -stat.assignment_count }

    admin_stats.map do |stat|
      {
        admin: {
          id: stat.id,
          name: stat.name,
          email: stat.email,
        },
        assignment_count: stat.assignment_count,
        unique_badge_types: stat.unique_badge_types,
        unique_users_assigned: stat.unique_users_assigned,
        percentage_of_total:
          calculate_percentage(stat.assignment_count, assignments.count),
        avg_assignments_per_day:
          calculate_admin_daily_average(stat.id, assignments),
      }
    end
  end

  def expiration_analysis
    assignments = base_assignments_scope

    with_expiration = assignments.where.not(expires_at: nil)
    expired = with_expiration.where('expires_at < ?', Time.current)
    expiring_soon =
      with_expiration.where(expires_at: Time.current..30.days.from_now)

    {
      total_with_expiration: with_expiration.count,
      expired_count: expired.count,
      expiring_soon_count: expiring_soon.count,
      never_expire_count: assignments.where(expires_at: nil).count,
      expiration_rate:
        calculate_percentage(expired.count, with_expiration.count),
      avg_days_to_expiration: calculate_avg_days_to_expiration(with_expiration),
      expiration_by_badge_type: expiration_by_badge_type_breakdown(assignments),
    }
  end

  # Helper methods for calculations
  def calculate_percentage(part, total)
    return 0 if total.zero?
    ((part.to_f / total) * 100).round(2)
  end

  def calculate_avg_assignments_per_day(assignments)
    return 0 if assignments.empty?

    days = (@end_date - @start_date) / 1.day
    (assignments.count.to_f / days).round(2)
  end

  def find_most_active_assignment_day(assignments)
    daily_counts = assignments.group_by_day(:created_at).count
    most_active = daily_counts.max_by { |_, count| count }
    most_active&.first&.strftime('%Y-%m-%d')
  end

  def calculate_trend_direction(daily_counts)
    return 'insufficient_data' if daily_counts.count < 7

    recent_avg = daily_counts.values.last(7).sum / 7.0
    earlier_avg = daily_counts.values.first(7).sum / 7.0

    if recent_avg > earlier_avg * 1.1
      'increasing'
    elsif recent_avg < earlier_avg * 0.9
      'decreasing'
    else
      'stable'
    end
  end

  def calculate_frequency_trend(assignment_times)
    return 'insufficient_data' if assignment_times.count < 10

    # Compare first half vs second half frequency
    midpoint = assignment_times.count / 2
    first_half = assignment_times.first(midpoint)
    second_half = assignment_times.last(midpoint)

    first_half_duration = first_half.last - first_half.first
    second_half_duration = second_half.last - second_half.first

    first_half_rate = first_half.count / (first_half_duration / 1.day)
    second_half_rate = second_half.count / (second_half_duration / 1.day)

    if second_half_rate > first_half_rate * 1.2
      'accelerating'
    elsif second_half_rate < first_half_rate * 0.8
      'decelerating'
    else
      'steady'
    end
  end

  def calculate_admin_daily_average(admin_id, assignments)
    admin_assignments = assignments.where(admin_id: admin_id)
    return 0 if admin_assignments.empty?

    first_assignment = admin_assignments.minimum(:created_at)
    days_active = (Time.current - first_assignment) / 1.day

    (admin_assignments.count.to_f / days_active).round(2)
  end

  def calculate_avg_days_to_expiration(assignments_with_expiration)
    return 0 if assignments_with_expiration.empty?

    days_to_expiration =
      assignments_with_expiration.map do |assignment|
        (assignment.expires_at - assignment.created_at) / 1.day
      end

    (days_to_expiration.sum / days_to_expiration.count).round(1)
  end

  def expiration_by_badge_type_breakdown(assignments)
    # Fix PostgreSQL GROUP BY error by using separate queries instead of complex joins
    current_time = Time.current

    # Get all badge type IDs that have assignments with expiration dates
    badge_type_ids = assignments.where.not(expires_at: nil).distinct.pluck(:badge_type_id)

    badge_type_ids.map do |badge_type_id|
      badge_type = BadgeType.find(badge_type_id)
      assignments_with_expiration = assignments.where(badge_type_id: badge_type_id).where.not(expires_at: nil)
      expired_assignments = assignments_with_expiration.where('expires_at < ?', current_time)

      total_with_expiration = assignments_with_expiration.count
      expired_count = expired_assignments.count

      {
        badge_type_name: badge_type.name,
        total_with_expiration: total_with_expiration,
        expired_count: expired_count,
        expiration_rate: calculate_percentage(expired_count, total_with_expiration),
      }
    end
  end

  # CSV Export Helper Methods
  def export_summary_section(csv, summary)
    csv << ['SUMMARY METRICS']
    csv << ['Total Assignments', summary[:total_assignments]]
    csv << ['Unique Users with Badges', summary[:unique_users_with_badges]]
    csv << [
      'Unique Badge Types Assigned',
      summary[:unique_badge_types_assigned],
    ]
    csv << ['Unique Admins Assigning', summary[:unique_admins_assigning]]
    csv << [
      'Assignments with Expiration',
      summary[:assignments_with_expiration],
    ]
    csv << ['Expired Assignments', summary[:expired_assignments]]
    csv << ['Active Assignments', summary[:active_assignments]]
    csv << ['Avg Assignments per Day', summary[:avg_assignments_per_day]]
    csv << ['Most Active Assignment Day', summary[:most_active_assignment_day]]
    csv << []
  end

  def export_badge_type_distribution(csv, distribution)
    csv << ['DISTRIBUTION BY BADGE TYPE']
    csv << [
      'Badge Name',
      'Assignment Count',
      'Unique Users',
      'Unique Admins',
      'Percentage of Total',
      'Avg Assignments per User',
      'Recent Assignments (7 days)',
    ]

    distribution.each do |badge_data|
      csv << [
        badge_data[:badge_type][:name],
        badge_data[:assignment_count],
        badge_data[:unique_users],
        badge_data[:unique_admins],
        "#{badge_data[:percentage_of_total]}%",
        badge_data[:avg_assignments_per_user],
        badge_data[:recent_assignments],
      ]
    end
    csv << []
  end

  def export_user_role_distribution(csv, distribution)
    csv << ['DISTRIBUTION BY USER ROLE']
    csv << [
      'Role Name',
      'Assignment Count',
      'Unique Users',
      'Percentage of Total',
      'Avg Badges per User',
    ]

    distribution.each do |role_data|
      csv << [
        role_data[:role_name],
        role_data[:assignment_count],
        role_data[:unique_users],
        "#{role_data[:percentage_of_total]}%",
        role_data[:avg_badges_per_user],
      ]
    end
    csv << []
  end

  def export_assignment_trends(csv, trends)
    csv << ['ASSIGNMENT TRENDS']
    csv << ['Peak Assignment Day', trends[:peak_assignment_day]]
    csv << ['Lowest Assignment Day', trends[:lowest_assignment_day]]
    csv << ['Trend Direction', trends[:trend_direction]]
    csv << []

    csv << ['DAILY ASSIGNMENT COUNTS']
    csv << ['Date', 'Assignment Count']
    trends[:daily_assignments].each do |day_data|
      csv << [day_data[:date], day_data[:count]]
    end
    csv << []
  end

  def export_user_badge_counts(csv, user_badge_counts)
    csv << ['USER BADGE COUNT DISTRIBUTION']
    csv << ['Number of Badges', 'Number of Users', 'Percentage']

    user_badge_counts[:distribution].each do |count_data|
      csv << [
        count_data[:badge_count],
        count_data[:user_count],
        "#{count_data[:percentage]}%",
      ]
    end

    csv << []
    csv << ['Avg Badges per User', user_badge_counts[:avg_badges_per_user]]
    csv << ['Max Badges per User', user_badge_counts[:max_badges_per_user]]
    csv << [
      'Users with Multiple Badges',
      user_badge_counts[:users_with_multiple_badges],
    ]
    csv << []
  end

  def export_assignment_velocity(csv, velocity)
    return if velocity[:note]

    csv << ['ASSIGNMENT VELOCITY METRICS']
    csv << [
      'Avg Time Between Assignments (seconds)',
      velocity[:avg_time_between_assignments],
    ]
    csv << [
      'Median Time Between Assignments (seconds)',
      velocity[:median_time_between_assignments],
    ]
    csv << [
      'Fastest Assignment Interval (seconds)',
      velocity[:fastest_assignment_interval],
    ]
    csv << [
      'Slowest Assignment Interval (seconds)',
      velocity[:slowest_assignment_interval],
    ]
    csv << ['Assignment Frequency Trend', velocity[:assignment_frequency_trend]]
    csv << []
  end

  def export_admin_activity(csv, admin_activity)
    csv << ['ADMIN ASSIGNMENT ACTIVITY']
    csv << [
      'Admin Name',
      'Admin Email',
      'Assignment Count',
      'Unique Badge Types',
      'Unique Users Assigned',
      'Percentage of Total',
      'Avg Assignments per Day',
    ]

    admin_activity.each do |admin_data|
      csv << [
        admin_data[:admin][:name],
        admin_data[:admin][:email],
        admin_data[:assignment_count],
        admin_data[:unique_badge_types],
        admin_data[:unique_users_assigned],
        "#{admin_data[:percentage_of_total]}%",
        admin_data[:avg_assignments_per_day],
      ]
    end
    csv << []
  end

  def export_expiration_analysis(csv, expiration)
    csv << ['EXPIRATION ANALYSIS']
    csv << ['Total with Expiration', expiration[:total_with_expiration]]
    csv << ['Expired Count', expiration[:expired_count]]
    csv << ['Expiring Soon Count (30 days)', expiration[:expiring_soon_count]]
    csv << ['Never Expire Count', expiration[:never_expire_count]]
    csv << ['Expiration Rate', "#{expiration[:expiration_rate]}%"]
    csv << ['Avg Days to Expiration', expiration[:avg_days_to_expiration]]
    csv << []

    csv << ['EXPIRATION BY BADGE TYPE']
    csv << [
      'Badge Type',
      'Total with Expiration',
      'Expired Count',
      'Expiration Rate',
    ]
    expiration[:expiration_by_badge_type].each do |badge_exp|
      csv << [
        badge_exp[:badge_type_name],
        badge_exp[:total_with_expiration],
        badge_exp[:expired_count],
        "#{badge_exp[:expiration_rate]}%",
      ]
    end
    csv << []
  end
end
