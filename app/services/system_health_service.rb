# frozen_string_literal: true

class SystemHealthService
  def self.check_all
    new.check_all
  end

  def check_all
    {
      database: check_database,
      search: check_search_indices,
      background_jobs: check_background_jobs,
      cache: check_cache,
      storage: check_storage,
      overall_status: :healthy # Will be calculated based on individual checks
    }.tap do |health|
      health[:overall_status] = calculate_overall_status(health)
    end
  end

  private

  def check_database
    start_time = Time.current

    begin
      # Test basic connectivity
      ActiveRecord::Base.connection.execute('SELECT 1')

      # Test query performance
      query_time = Benchmark.realtime do
        User.limit(1).count
      end

      response_time = (Time.current - start_time).round(3)

      # Get connection pool stats safely
      pool = ActiveRecord::Base.connection_pool
      pool_stats = begin
        {
          size: pool.size,
          checked_out: pool.respond_to?(:checked_out) ? pool.checked_out.size : 0,
          available: pool.respond_to?(:available) ? pool.available.size : pool.size
        }
      rescue => e
        Rails.logger.warn "Could not get connection pool stats: #{e.message}"
        { size: pool.size, checked_out: 0, available: pool.size }
      end

      {
        status: :healthy,
        response_time: response_time,
        query_performance: query_time.round(3),
        connection_pool: pool_stats
      }
    rescue => e
      Rails.logger.error "Database health check failed: #{e.message}"
      {
        status: :unhealthy,
        error: e.message,
        response_time: nil
      }
    end
  end

  def check_search_indices
    searchable_models = [TalentProfile, Job, JobApplication, Conversation]
    indices_status = {}
    overall_healthy = true

    searchable_models.each do |model|
      begin
        db_count = model.count
        search_result = model.search('*', limit: 0)
        search_count = search_result.total_count
        
        in_sync = db_count == search_count
        overall_healthy = false unless in_sync
        
        indices_status[model.name.underscore] = {
          status: in_sync ? :healthy : :warning,
          database_count: db_count,
          search_count: search_count,
          difference: db_count - search_count
        }
      rescue => e
        overall_healthy = false
        indices_status[model.name.underscore] = {
          status: :unhealthy,
          error: e.message
        }
      end
    end

    {
      status: overall_healthy ? :healthy : :warning,
      indices: indices_status
    }
  end

  def check_background_jobs
    begin
      # Check if Solid Queue is available and tables exist
      unless defined?(SolidQueue::Job) && ActiveRecord::Base.connection.table_exists?('solid_queue_jobs')
        return {
          status: :warning,
          message: "Solid Queue not configured",
          total_jobs: 0,
          pending_jobs: 0,
          failed_jobs: 0
        }
      end

      # Check Solid Queue status
      total_jobs = SolidQueue::Job.count
      pending_jobs = SolidQueue::Job.where(finished_at: nil).count

      # Try to get failed jobs, but handle if failed_execution table doesn't exist
      failed_jobs = begin
        if ActiveRecord::Base.connection.table_exists?('solid_queue_failed_executions')
          SolidQueue::Job.joins(:failed_execution).count
        else
          0
        end
      rescue => e
        Rails.logger.warn "Could not check failed jobs: #{e.message}"
        0
      end

      # Consider unhealthy if too many failed jobs or too many pending
      status = if failed_jobs > 100 || pending_jobs > 1000
                 :warning
               elsif failed_jobs > 500 || pending_jobs > 5000
                 :unhealthy
               else
                 :healthy
               end

      {
        status: status,
        total_jobs: total_jobs,
        pending_jobs: pending_jobs,
        failed_jobs: failed_jobs,
        success_rate: total_jobs > 0 ? ((total_jobs - failed_jobs).to_f / total_jobs * 100).round(2) : 100
      }
    rescue => e
      Rails.logger.error "Background jobs health check failed: #{e.message}"
      {
        status: :warning,
        message: "Background jobs unavailable",
        error: e.message
      }
    end
  end

  def check_cache
    begin
      start_time = Time.current
      test_key = "health_check_#{SecureRandom.hex(8)}"
      test_value = "test_value_#{Time.current.to_i}"
      
      # Test write
      Rails.cache.write(test_key, test_value, expires_in: 1.minute)
      
      # Test read
      cached_value = Rails.cache.read(test_key)
      
      # Test delete
      Rails.cache.delete(test_key)
      
      response_time = (Time.current - start_time).round(3)
      
      if cached_value == test_value
        {
          status: :healthy,
          response_time: response_time,
          cache_store: Rails.cache.class.name
        }
      else
        {
          status: :warning,
          error: "Cache read/write mismatch",
          response_time: response_time
        }
      end
    rescue => e
      {
        status: :unhealthy,
        error: e.message
      }
    end
  end

  def check_storage
    begin
      # Check if Active Storage is working
      start_time = Time.current
      
      # Test blob creation (without actual file)
      test_successful = ActiveStorage::Blob.service.respond_to?(:exist?)
      
      {
        status: test_successful ? :healthy : :warning,
        response_time: (Time.current - start_time).round(3),
        service: ActiveStorage::Blob.service.class.name
      }
    rescue => e
      {
        status: :unhealthy,
        error: e.message
      }
    end
  end

  def calculate_overall_status(health_data)
    statuses = health_data.values.map do |check|
      check.is_a?(Hash) ? check[:status] : nil
    end.compact

    if statuses.include?(:unhealthy)
      :unhealthy
    elsif statuses.include?(:warning)
      :warning
    else
      :healthy
    end
  end
end
