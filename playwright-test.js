#!/usr/bin/env node

/**
 * Playwright MCP Test Script
 * 
 * This script demonstrates how to interact with the Playwright MCP server
 * using HTTP requests to automate web browsing.
 */

const http = require('http');

class PlaywrightMCPClient {
  constructor(baseUrl = 'http://localhost:8931') {
    this.baseUrl = baseUrl;
  }

  async makeRequest(tool, args = {}) {
    return new Promise((resolve, reject) => {
      const postData = JSON.stringify({
        method: 'tools/call',
        params: {
          name: tool,
          arguments: args
        }
      });

      const options = {
        hostname: 'localhost',
        port: 8931,
        path: '/mcp',
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Content-Length': Buffer.byteLength(postData)
        }
      };

      const req = http.request(options, (res) => {
        let data = '';
        res.on('data', (chunk) => {
          data += chunk;
        });
        res.on('end', () => {
          try {
            const response = JSON.parse(data);
            resolve(response);
          } catch (error) {
            reject(error);
          }
        });
      });

      req.on('error', (error) => {
        reject(error);
      });

      req.write(postData);
      req.end();
    });
  }

  async navigate(url) {
    console.log(`🌐 Navigating to: ${url}`);
    return await this.makeRequest('browser_navigate', { url });
  }

  async takeScreenshot(filename) {
    console.log(`📸 Taking screenshot: ${filename || 'default'}`);
    return await this.makeRequest('browser_take_screenshot', { filename });
  }

  async getSnapshot() {
    console.log(`📋 Getting page snapshot...`);
    return await this.makeRequest('browser_snapshot');
  }

  async click(element, ref) {
    console.log(`👆 Clicking: ${element}`);
    return await this.makeRequest('browser_click', { element, ref });
  }

  async type(element, ref, text) {
    console.log(`⌨️  Typing "${text}" into: ${element}`);
    return await this.makeRequest('browser_type', { element, ref, text });
  }
}

async function runDemo() {
  console.log('🎭 Playwright MCP Test Demo');
  console.log('============================');
  console.log('');

  const client = new PlaywrightMCPClient();

  try {
    // Test 1: Navigate to a website
    console.log('📍 Test 1: Navigation');
    const navResult = await client.navigate('https://example.com');
    console.log('✅ Navigation result:', navResult.result ? 'Success' : 'Failed');
    console.log('');

    // Wait a moment for the page to load
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Test 2: Take a screenshot
    console.log('📍 Test 2: Screenshot');
    const screenshotResult = await client.takeScreenshot('example-page.png');
    console.log('✅ Screenshot result:', screenshotResult.result ? 'Success' : 'Failed');
    console.log('');

    // Test 3: Get page snapshot
    console.log('📍 Test 3: Page Snapshot');
    const snapshotResult = await client.getSnapshot();
    console.log('✅ Snapshot result:', snapshotResult.result ? 'Success' : 'Failed');
    if (snapshotResult.result) {
      console.log('📄 Page title found in snapshot:', 
        snapshotResult.result.includes('Example Domain') ? 'Yes' : 'No');
    }
    console.log('');

    console.log('🎉 Demo completed successfully!');
    console.log('');
    console.log('💡 You can now:');
    console.log('  • Navigate to any website');
    console.log('  • Take screenshots');
    console.log('  • Get accessibility snapshots');
    console.log('  • Click elements');
    console.log('  • Type text');
    console.log('  • And much more!');

  } catch (error) {
    console.error('❌ Demo failed:', error.message);
    console.log('');
    console.log('🔧 Make sure the Playwright MCP server is running:');
    console.log('   yarn playwright:mcp');
    console.log('   or');
    console.log('   node node_modules/@playwright/mcp/cli.js --port 8931');
  }
}

// Run the demo if this script is executed directly
if (require.main === module) {
  runDemo().catch(console.error);
}

module.exports = PlaywrightMCPClient;
