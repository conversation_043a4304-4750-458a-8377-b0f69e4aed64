Start application

```
bin/dev
```

## Path

Mimic Closify then improve from there.

## TODO

Learn Caching

Timezone on signup https://chatgpt.com/share/676df047-40e8-8010-845f-d5a73c55ccab https://www.youtube.com/watch?v=yMkO69MMzwg

User roles (Global - user vs superadmin) local vs global https://chatgpt.com/share/676df021-7020-8010-8304-46388adf7113

profile's

Job Applications and Job's -> Listing

Ghostwiring Profile -> Profile

Job Application -> Application

add sticky asides

Notifcation -> https://www.youtube.com/watch?v=SzX-aBEqnAc

Reputation Behavior with Merit
add Multimedia via ActionText

Add ActiveStorage
Style the messages
Maintain seeds

Screens

- Login
- Sign up
- password reset

- talent / jobs
- talent / profile
- talent / applications
- talent / messages
- talent / settings / (general)
- talent / settings / password
- talent / settings / notifications (Leave f:sor now)
- talent / settings / privacy (leave for now)

WIP

- scout / messages

- scout / jobs / new
- scout / jobs / edit
- scout / jobs / index
- scout / jobs / show

- scout / applicants / index


Add tags to jobs and search


scout / jobs / index

- CHange the budget to ranges.

Find Talents

- Platforms
- Topics
- Outcome
- Budget ranges
- Payment Frequency



Tag line instead of BIO
My Message (Everywhere)
Time to add descriptions

Messages activate on application accepted.


Noticifcations
Email's
Feed's
Test's
Hos ting

