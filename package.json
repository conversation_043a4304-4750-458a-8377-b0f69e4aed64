{"name": "<PERSON>wrote", "private": true, "packageManager": "yarn@1.22.22", "devDependencies": {"@playwright/mcp": "^0.0.28", "@playwright/test": "^1.52.0", "@pmmmwh/react-refresh-webpack-plugin": "^0.5.15", "@prettier/plugin-ruby": "^1.6.1", "playwright-core": "^1.52.0", "prettier": "^2.2.1", "prettier-plugin-erb": "^0.4.0", "react-refresh": "^0.16.0", "webpack-dev-server": "4"}, "dependencies": {"@babel/core": "7", "@babel/plugin-transform-runtime": "7", "@babel/preset-env": "7", "@babel/preset-react": "^7.26.3", "@babel/runtime": "7", "@hotwired/stimulus": "^3.2.2", "@hotwired/stimulus-webpack-helpers": "^1.0.1", "@hotwired/turbo-rails": "^8.0.12", "@rails/actiontext": "^8.0.100", "@rails/request.js": "^0.0.9", "@stimulus-components/dropdown": "^3.0.0", "@stimulus-components/reveal": "^5.0.0", "@tailwindcss/container-queries": "^0.1.1", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@types/babel__core": "7", "@types/webpack": "5", "autoprefixer": "^10.4.20", "babel-loader": "8", "babel-plugin-macros": "^3.1.0", "babel-plugin-transform-react-remove-prop-types": "^0.4.24", "compression-webpack-plugin": "9", "css-loader": "^7.1.2", "css-minimizer-webpack-plugin": "^7.0.0", "mini-css-extract-plugin": "^2.9.2", "postcss": "^8.4.49", "postcss-import": "^16.1.0", "postcss-loader": "^8.1.1", "prop-types": "^15.8.1", "react": "^18.0.0", "react-dom": "^18.0.0", "react-on-rails": "14.2.0", "shakapacker": "8.2.0", "style-loader": "^4.0.0", "tailwindcss": "^3.4.17", "terser-webpack-plugin": "5", "trix": "^2.1.13", "webpack": "5", "webpack-assets-manifest": "5", "webpack-cli": "4", "webpack-merge": "5"}, "version": "0.1.0", "babel": {"presets": ["./node_modules/shakapacker/package/babel/preset.js"]}, "browserslist": ["defaults"], "scripts": {"build:css": "tailwindcss -i ./app/assets/stylesheets/application.tailwind.css -o ./app/assets/builds/application.css --minify", "playwright:demo": "node playwright-demo.js", "playwright:browser": "node playwright-browser.js", "playwright:test": "node playwright-test.js", "playwright:login": "node login-automation.js", "playwright:direct-login": "node direct-login.js", "playwright:mcp": "node node_modules/@playwright/mcp/cli.js --config playwright-mcp-config.json"}}