# Playwright MCP Web Browsing Guide

This guide explains how to use Playwright MCP for web browsing automation in your project.

## What is Playwright MCP?

Playwright MCP (Model Context Protocol) is a server that provides browser automation capabilities using Playwright. It enables LLMs to interact with web pages through structured accessibility snapshots, making it fast, lightweight, and deterministic.

## Setup

You already have Playwright MCP installed in your project! The following packages are available:

- `@playwright/mcp@^0.0.28`
- `@playwright/test@^1.52.0`
- `playwright-core@^1.52.0`

## Quick Start

### Option 1: Run MCP Server Directly

```bash
yarn playwright:mcp
```

This starts the Playwright MCP server on `http://localhost:8931` using the configuration file.

### Option 2: Run with Custom Port

```bash
node node_modules/@playwright/mcp/cli.js --port 8931
```

This starts the server with a custom port.

### Option 3: Run the Demo Script

```bash
yarn playwright:demo
```

This will start a Playwright MCP server and show usage instructions.

### Option 4: Test the Setup

```bash
yarn playwright:test
```

This runs a simple test to verify the MCP server is working.

## Configuration

The `playwright-mcp-config.json` file contains the browser configuration:

```json
{
  "browser": {
    "browserName": "chromium",
    "isolated": false,
    "launchOptions": {
      "headless": false,
      "channel": "chrome"
    },
    "contextOptions": {
      "viewport": {
        "width": 1280,
        "height": 720
      }
    }
  },
  "capabilities": ["core", "tabs", "pdf", "history", "wait", "files"]
}
```

## Available Tools

### Navigation

- `browser_navigate(url)` - Navigate to a URL
- `browser_navigate_back()` - Go back
- `browser_navigate_forward()` - Go forward

### Interaction

- `browser_snapshot()` - Get accessibility snapshot of the page
- `browser_click(element, ref)` - Click an element
- `browser_type(element, ref, text)` - Type text into an element
- `browser_hover(element, ref)` - Hover over an element
- `browser_press_key(key)` - Press a keyboard key

### Screenshots & Media

- `browser_take_screenshot()` - Take a screenshot
- `browser_pdf_save()` - Save page as PDF

### Tabs

- `browser_tab_new(url?)` - Open new tab
- `browser_tab_list()` - List all tabs
- `browser_tab_select(index)` - Switch to tab
- `browser_tab_close(index?)` - Close tab

### Utilities

- `browser_wait_for(time?, text?, textGone?)` - Wait for conditions
- `browser_resize(width, height)` - Resize browser window
- `browser_close()` - Close browser

## Usage Examples

### Basic Web Browsing

1. Start the MCP server: `yarn playwright:demo`
2. Navigate to a website: `browser_navigate("https://example.com")`
3. Take a snapshot: `browser_snapshot()`
4. Take a screenshot: `browser_take_screenshot()`

### Interacting with Elements

1. Get page snapshot to find element references
2. Click elements: `browser_click("Submit button", "button[type=submit]")`
3. Type text: `browser_type("Search input", "#search", "hello world")`

### Working with Multiple Tabs

1. Open new tab: `browser_tab_new("https://github.com")`
2. List tabs: `browser_tab_list()`
3. Switch tabs: `browser_tab_select(1)`

## Key Features

- **Fast & Lightweight**: Uses accessibility tree instead of screenshots
- **LLM-Friendly**: Structured data output, no vision models needed
- **Deterministic**: Avoids ambiguity of screenshot-based approaches
- **Full Browser Support**: Chromium, Firefox, and WebKit
- **Persistent Sessions**: Maintains login state between sessions

## Tips

1. **Use Accessibility Snapshots**: Always call `browser_snapshot()` first to understand the page structure
2. **Element References**: Use the `ref` values from snapshots for precise element targeting
3. **Wait for Content**: Use `browser_wait_for()` when pages load dynamically
4. **Multiple Tabs**: Great for comparing content or managing multiple workflows

## Troubleshooting

### Browser Not Found

If you get browser installation errors:

```bash
npx playwright install chromium
```

### Permission Issues

Make sure the scripts are executable:

```bash
chmod +x playwright-browser.js playwright-demo.js
```

### Port Already in Use

Change the port in `playwright-mcp-config.json` if 8931 is busy.

## Integration with Your Rails App

You can use Playwright MCP to:

- Test your application's frontend
- Automate user workflows
- Generate screenshots for documentation
- Scrape competitor websites
- Validate form submissions
- Test responsive design

The MCP server runs independently of your Rails application, so you can browse any website while your Rails app runs on its own port.
