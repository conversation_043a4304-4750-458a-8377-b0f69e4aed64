# frozen_string_literal: true

# Cache invalidation for admin interface performance optimizations
Rails.application.config.after_initialize do
  # Clear dashboard stats cache when relevant models are updated
  [User, Job, Organization, ChatRequest, Conversation, Message].each do |model|
    next unless defined?(model)
    
    model.class_eval do
      after_create :clear_admin_dashboard_cache
      after_update :clear_admin_dashboard_cache
      after_destroy :clear_admin_dashboard_cache

      private

      def clear_admin_dashboard_cache
        AdminDashboardStatsService.clear_cache
        AdminRecentActivityService.clear_cache
      end
    end
  end

  # Clear filter options cache when relevant models are updated
  if defined?(Job)
    Job.class_eval do
      after_update :clear_job_filter_cache, if: :saved_change_to_status?

      private

      def clear_job_filter_cache
        AdminFilterOptionsService.clear_cache('job')
      end
    end
  end

  if defined?(Role)
    Role.class_eval do
      after_create :clear_role_filter_cache
      after_update :clear_role_filter_cache
      after_destroy :clear_role_filter_cache

      private

      def clear_role_filter_cache
        AdminFilterOptionsService.clear_cache('role')
      end
    end
  end

  if defined?(AdminAuditLog)
    AdminAuditLog.class_eval do
      after_create :clear_audit_log_filter_cache

      private

      def clear_audit_log_filter_cache
        AdminFilterOptionsService.clear_cache('audit_log')
      end
    end
  end

  # Clear user filter cache when user signup intent changes
  if defined?(User)
    User.class_eval do
      after_update :clear_user_filter_cache, if: :saved_change_to_signup_intent?

      private

      def clear_user_filter_cache
        AdminFilterOptionsService.clear_cache('user')
      end
    end
  end
end
