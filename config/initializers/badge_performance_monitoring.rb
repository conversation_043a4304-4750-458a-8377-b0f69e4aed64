# frozen_string_literal: true

# Badge Performance Monitoring Configuration
# This initializer sets up performance monitoring for the badge system
# to track render times, query performance, and system health

Rails.application.configure do
  # Enable badge performance monitoring in all environments except test
  config.badge_performance_monitoring_enabled = !Rails.env.test?
  
  # Performance thresholds (in milliseconds)
  config.badge_performance_thresholds = {
    # Page render time thresholds
    page_render_warning: 1000,    # 1 second
    page_render_critical: 2000,   # 2 seconds
    
    # Database query thresholds
    query_warning: 500,           # 500ms
    query_critical: 1000,         # 1 second
    
    # Badge component render thresholds
    badge_render_warning: 100,    # 100ms
    badge_render_critical: 250,   # 250ms
    
    # Badge analytics thresholds
    analytics_warning: 2000,      # 2 seconds
    analytics_critical: 5000      # 5 seconds
  }
  
  # Performance monitoring settings
  config.badge_performance_settings = {
    # Enable detailed logging
    detailed_logging: Rails.env.development?,
    
    # Sample rate for performance tracking (0.0 to 1.0)
    sample_rate: Rails.env.production? ? 0.1 : 1.0,
    
    # Enable memory usage tracking
    track_memory: Rails.env.development?,
    
    # Enable SQL query analysis
    track_sql_queries: true,
    
    # Maximum number of performance records to keep in memory
    max_memory_records: 1000,
    
    # Performance alert cooldown (seconds)
    alert_cooldown: 300, # 5 minutes
    
    # Enable performance metrics export
    enable_metrics_export: true
  }
  
  # Performance monitoring targets
  config.badge_performance_targets = [
    # Pages that render badges
    'profiles#show',
    'search#index',
    'search#show',
    'super_admin/badge_analytics#index',
    'super_admin/badge_analytics#distribution',
    'super_admin/badge_types#index',
    'super_admin/badge_types#show',
    'super_admin/users#show'
  ]
  
  # Initialize performance monitoring if enabled
  if config.badge_performance_monitoring_enabled
    # Set up ActiveSupport::Notifications subscribers
    ActiveSupport::Notifications.subscribe('process_action.action_controller') do |name, started, finished, unique_id, data|
      # Only monitor badge-related actions
      controller_action = "#{data[:controller]}##{data[:action]}"
      
      if config.badge_performance_targets.include?(controller_action)
        duration = (finished - started) * 1000 # Convert to milliseconds
        
        # Log performance data
        BadgePerformanceMonitorService.record_page_performance(
          controller: data[:controller],
          action: data[:action],
          duration: duration,
          status: data[:status],
          method: data[:method],
          path: data[:path],
          params: data[:params]&.except(:password, :password_confirmation, :authenticity_token),
          db_runtime: data[:db_runtime],
          view_runtime: data[:view_runtime],
          allocations: data[:allocations]
        )
        
        # Check for performance issues
        if duration > config.badge_performance_thresholds[:page_render_critical]
          BadgePerformanceMonitorService.alert_performance_issue(
            type: 'critical_page_render',
            controller_action: controller_action,
            duration: duration,
            threshold: config.badge_performance_thresholds[:page_render_critical]
          )
        elsif duration > config.badge_performance_thresholds[:page_render_warning]
          BadgePerformanceMonitorService.alert_performance_issue(
            type: 'warning_page_render',
            controller_action: controller_action,
            duration: duration,
            threshold: config.badge_performance_thresholds[:page_render_warning]
          )
        end
      end
    end
    
    # Monitor SQL queries for badge-related operations
    ActiveSupport::Notifications.subscribe('sql.active_record') do |name, started, finished, unique_id, data|
      # Only monitor badge-related queries
      sql = data[:sql]
      
      if sql.match?(/badge_types|badge_assignments|badge_clicks|badge_views/i)
        duration = (finished - started) * 1000 # Convert to milliseconds
        
        # Record query performance
        BadgePerformanceMonitorService.record_query_performance(
          sql: sql,
          duration: duration,
          name: data[:name],
          connection_id: data[:connection_id]
        )
        
        # Check for slow queries
        if duration > config.badge_performance_thresholds[:query_critical]
          BadgePerformanceMonitorService.alert_performance_issue(
            type: 'critical_query',
            sql: sql.truncate(200),
            duration: duration,
            threshold: config.badge_performance_thresholds[:query_critical]
          )
        elsif duration > config.badge_performance_thresholds[:query_warning]
          BadgePerformanceMonitorService.alert_performance_issue(
            type: 'warning_query',
            sql: sql.truncate(200),
            duration: duration,
            threshold: config.badge_performance_thresholds[:query_warning]
          )
        end
      end
    end
    
    # Monitor badge component rendering
    ActiveSupport::Notifications.subscribe('render_partial.action_view') do |name, started, finished, unique_id, data|
      # Only monitor badge-related partials
      identifier = data[:identifier]
      
      if identifier&.include?('badge') || identifier&.include?('_badge')
        duration = (finished - started) * 1000 # Convert to milliseconds
        
        # Record component performance
        BadgePerformanceMonitorService.record_component_performance(
          partial: identifier,
          duration: duration,
          count: data[:count] || 1
        )
        
        # Check for slow component rendering
        if duration > config.badge_performance_thresholds[:badge_render_critical]
          BadgePerformanceMonitorService.alert_performance_issue(
            type: 'critical_component_render',
            component: identifier,
            duration: duration,
            threshold: config.badge_performance_thresholds[:badge_render_critical]
          )
        elsif duration > config.badge_performance_thresholds[:badge_render_warning]
          BadgePerformanceMonitorService.alert_performance_issue(
            type: 'warning_component_render',
            component: identifier,
            duration: duration,
            threshold: config.badge_performance_thresholds[:badge_render_warning]
          )
        end
      end
    end
    
    # Set up periodic performance health checks
    if defined?(Rails::Server)
      Thread.new do
        loop do
          sleep 60 # Check every minute
          BadgePerformanceMonitorService.perform_health_check
        rescue => e
          Rails.logger.error "[BADGE_PERFORMANCE] Health check error: #{e.message}"
        end
      end
    end
    
    Rails.logger.info "[BADGE_PERFORMANCE] Performance monitoring initialized"
  else
    Rails.logger.info "[BADGE_PERFORMANCE] Performance monitoring disabled"
  end
end

# Performance monitoring middleware for badge-specific requests
class BadgePerformanceMiddleware
  def initialize(app)
    @app = app
  end

  def call(env)
    return @app.call(env) unless Rails.application.config.badge_performance_monitoring_enabled
    
    request = Rack::Request.new(env)
    
    # Check if this is a badge-related request
    if badge_related_request?(request)
      start_time = Time.current
      memory_before = get_memory_usage if Rails.application.config.badge_performance_settings[:track_memory]
      
      status, headers, response = @app.call(env)
      
      end_time = Time.current
      duration = (end_time - start_time) * 1000 # Convert to milliseconds
      memory_after = get_memory_usage if Rails.application.config.badge_performance_settings[:track_memory]
      
      # Record middleware performance
      BadgePerformanceMonitorService.record_middleware_performance(
        path: request.path,
        method: request.request_method,
        duration: duration,
        status: status,
        memory_before: memory_before,
        memory_after: memory_after,
        memory_delta: memory_after && memory_before ? memory_after - memory_before : nil
      )
      
      [status, headers, response]
    else
      @app.call(env)
    end
  end

  private

  def badge_related_request?(request)
    path = request.path
    
    # Check for badge-related paths
    path.include?('/badge') ||
    path.include?('/profiles/') ||
    path.include?('/search') ||
    path.include?('/super_admin/badge')
  end

  def get_memory_usage
    # Get current memory usage in MB
    `ps -o rss= -p #{Process.pid}`.to_i / 1024.0
  rescue
    nil
  end
end

# Add the middleware to the Rails application
Rails.application.config.middleware.use BadgePerformanceMiddleware if Rails.application.config.badge_performance_monitoring_enabled
