<!DOCTYPE html>
<html>
<head>
    <title>Form Wizard Test</title>
    <script src="http://localhost:3035/js/runtime.js"></script>
    <script src="http://localhost:3035/js/vendors-node_modules_hotwired_stimulus_dist_stimulus_js-node_modules_hotwired_turbo-rails_app-4b86c5.js"></script>
    <script src="http://localhost:3035/js/application.js"></script>
    <style>
        .hidden { display: none; }
        body { font-family: Arial, sans-serif; padding: 20px; }
        .form-container { max-width: 600px; margin: 0 auto; }
        .step { padding: 20px; border: 1px solid #ccc; margin: 10px 0; }
        .debug { background: yellow; padding: 10px; margin: 10px 0; }
        button { padding: 10px 20px; margin: 5px; }
        select, input { padding: 5px; margin: 5px 0; width: 200px; }
    </style>
</head>
<body>
    <div class="form-container">
        <h1>Form Wizard Test</h1>
        
        <div class="debug">
            <p>🔧 Debug: Testing form wizard JavaScript</p>
            <button onclick="testFormWizard()">Test Form Wizard</button>
            <button onclick="console.log('Form element:', document.querySelector('[data-controller=form-wizard]'))">Check Form Element</button>
        </div>

        <form data-controller="form-wizard" data-form-wizard-current-step-value="0">
            <!-- Progress Bar -->
            <div style="background: #f0f0f0; height: 10px; margin: 20px 0;">
                <div data-form-wizard-target="progressBar" style="background: purple; height: 10px; width: 0%; transition: width 0.3s;"></div>
            </div>
            <div>
                <span data-form-wizard-target="stepTitle">Getting Started</span>
            </div>

            <!-- Step 1: Category Selection -->
            <div data-step-name="category_selection" data-form-wizard-target="step" class="step">
                <h3>Tell us about the ghostwriter you need</h3>
                <div>
                    <label>What type of ghostwriter are you looking to hire?</label><br>
                    <select name="job[job_category]" data-action="change->form-wizard#handleCategoryChange">
                        <option value="">Select a category</option>
                        <option value="social_media">Social Media</option>
                        <option value="newsletter">Newsletter</option>
                        <option value="lead_magnet">Lead Magnet</option>
                    </select>
                </div>
            </div>

            <!-- Step 2: Newsletter Steps (hidden) -->
            <div data-section-name="newsletter" class="hidden">
                <div data-step-name="newsletter_goal" data-step-id="goal" data-form-wizard-target="step" class="step hidden">
                    <h3>Newsletter Goal</h3>
                    <select name="job[outcome]">
                        <option value="">Select goal</option>
                        <option value="build_brand">Build brand</option>
                        <option value="drive_traffic">Drive traffic</option>
                    </select>
                </div>
            </div>

            <!-- Navigation -->
            <div style="margin-top: 20px;">
                <button type="button" data-form-wizard-target="previousButton" data-action="click->form-wizard#previousStep" class="hidden">
                    Previous
                </button>
                <button type="button" data-form-wizard-target="nextButton" data-action="click->form-wizard#nextStep">
                    Next
                </button>
                <button type="submit" data-form-wizard-target="submitButton" class="hidden">
                    Submit
                </button>
            </div>
        </form>
    </div>

    <script>
        function testFormWizard() {
            const form = document.querySelector('[data-controller="form-wizard"]');
            console.log('Form element:', form);
            
            if (form && form.stimulus) {
                const controller = form.stimulus.controller;
                console.log('Controller:', controller);
                console.log('Step manager:', controller?.stepManager);
            } else {
                console.log('No stimulus controller found');
            }
        }

        // Wait for page load
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Page loaded, testing form wizard...');
            setTimeout(testFormWizard, 1000);
        });
    </script>
</body>
</html>
