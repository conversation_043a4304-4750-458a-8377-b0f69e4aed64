<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Badge Modal Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script type="importmap">
      {
        "imports": {
          "@hotwired/stimulus": "https://unpkg.com/@hotwired/stimulus/dist/stimulus.js"
        }
      }
    </script>
  </head>
  <body class="bg-gray-100 p-8">
    <div class="max-w-4xl mx-auto">
      <h1 class="text-3xl font-bold mb-8 text-center">Badge Modal Test Page</h1>

      <!-- Test badges with different data -->
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <!-- Verified Badge -->
        <div class="bg-white p-6 rounded-lg shadow-md">
          <h3 class="text-lg font-semibold mb-4">Verified Badge</h3>
          <div
            class="badge inline-flex items-center font-medium rounded-lg border shadow-sm px-3 py-1.5 text-sm cursor-pointer hover:shadow-md transition-all duration-200"
            style="
              background-color: #3b82f6;
              color: #ffffff;
              border-color: #3b82f6;
            "
            data-controller="badge-click"
            data-badge-click-badge-id-value="1"
            data-badge-click-badge-name-value="Verified"
            data-badge-click-badge-description-value="Indicates a verified ghostwriter with confirmed credentials and identity"
            data-badge-click-badge-icon-value="check-circle"
            data-badge-click-badge-background-color-value="#3b82f6"
            data-badge-click-badge-text-color-value="#ffffff"
            data-badge-click-badge-criteria-value="This badge is awarded to ghostwriters who have completed identity verification and provided valid credentials."
            data-action="click->badge-click#click"
          >
            <span class="badge-name font-medium">Verified</span>
          </div>
        </div>

        <!-- Ghostwrote Choice Badge -->
        <div class="bg-white p-6 rounded-lg shadow-md">
          <h3 class="text-lg font-semibold mb-4">Ghostwrote Choice Badge</h3>
          <div
            class="badge inline-flex items-center font-medium rounded-lg border shadow-sm px-3 py-1.5 text-sm cursor-pointer hover:shadow-md transition-all duration-200"
            style="
              background-color: #8b5cf6;
              color: #ffffff;
              border-color: #8b5cf6;
            "
            data-controller="badge-click"
            data-badge-click-badge-id-value="2"
            data-badge-click-badge-name-value="Ghostwrote Choice"
            data-badge-click-badge-description-value="Platform-endorsed exceptional talent with proven track record"
            data-badge-click-badge-icon-value="star"
            data-badge-click-badge-background-color-value="#8b5cf6"
            data-badge-click-badge-text-color-value="#ffffff"
            data-badge-click-badge-criteria-value="This prestigious badge is awarded to top-performing ghostwriters who consistently deliver exceptional results and maintain high client satisfaction ratings."
            data-action="click->badge-click#click"
          >
            <span class="badge-name font-medium">Ghostwrote Choice</span>
          </div>
        </div>

        <!-- Expert Writer Badge -->
        <div class="bg-white p-6 rounded-lg shadow-md">
          <h3 class="text-lg font-semibold mb-4">Expert Writer Badge</h3>
          <div
            class="badge inline-flex items-center font-medium rounded-lg border shadow-sm px-3 py-1.5 text-sm cursor-pointer hover:shadow-md transition-all duration-200"
            style="
              background-color: #059669;
              color: #ffffff;
              border-color: #059669;
            "
            data-controller="badge-click"
            data-badge-click-badge-id-value="3"
            data-badge-click-badge-name-value="Expert Writer"
            data-badge-click-badge-description-value="Demonstrates advanced writing skills and expertise in multiple content types"
            data-badge-click-badge-icon-value="certificate"
            data-badge-click-badge-background-color-value="#059669"
            data-badge-click-badge-text-color-value="#ffffff"
            data-badge-click-badge-criteria-value="This badge recognizes writers who have demonstrated mastery across various content formats and consistently produce high-quality work."
            data-action="click->badge-click#click"
          >
            <span class="badge-name font-medium">Expert Writer</span>
          </div>
        </div>
      </div>

      <!-- Instructions -->
      <div class="bg-blue-50 border border-blue-200 rounded-lg p-6">
        <h3 class="text-lg font-semibold text-blue-900 mb-2">
          Test Instructions
        </h3>
        <ul class="text-blue-800 space-y-1">
          <li>• Click on any badge above to test the modal functionality</li>
          <li>• The modal should open with the badge details</li>
          <li>• Test closing the modal with the X button or ESC key</li>
          <li>• Check that the modal displays the correct badge information</li>
          <li>• Verify that the holographic effects work properly</li>
        </ul>
      </div>
    </div>

    <!-- Badge Modal (will be loaded from the Rails app) -->
    <div
      id="badge-modal"
      class="fixed inset-0 z-50 hidden overflow-y-auto"
      data-controller="badge-modal"
      data-badge-modal-target="container"
      role="dialog"
      aria-modal="true"
      aria-labelledby="badge-modal-title"
      aria-describedby="badge-modal-description"
    >
      <!-- Backdrop with blur effect -->
      <div
        class="fixed inset-0 transition-opacity duration-300 ease-out bg-black/60 backdrop-blur-sm"
        data-badge-modal-target="backdrop"
        data-action="click->badge-modal#close"
        aria-hidden="true"
      ></div>

      <!-- Modal container with view transition support -->
      <div
        class="flex items-end justify-center min-h-full p-2 sm:items-center sm:p-4 md:p-6 lg:p-8"
      >
        <div
          class="relative w-full max-w-sm overflow-hidden transition-all duration-300 ease-out transform bg-white shadow-2xl sm:max-w-md md:max-w-lg rounded-t-2xl sm:rounded-2xl"
          data-badge-modal-target="content"
          data-action="click->badge-modal#stopPropagation"
          style="view-transition-name: badge-modal-content"
        >
          <!-- Close button -->
          <button
            type="button"
            class="absolute z-10 p-2 transition-colors duration-200 rounded-full right-4 top-4 bg-white/80 text-stone-400 hover:bg-white hover:text-stone-600 focus:outline-none focus:ring-2 focus:ring-stone-500 focus:ring-offset-2"
            data-action="click->badge-modal#close"
            data-badge-modal-target="closeButton"
            aria-label="Close badge details modal"
            aria-describedby="badge-modal-title"
          >
            <span class="sr-only">Close</span>
            <svg
              class="h-5 w-5"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </button>

          <!-- Modal content -->
          <div class="relative">
            <!-- Holographic card background with gradient -->
            <div
              class="absolute inset-0 rounded-2xl bg-gradient-to-br from-stone-50 via-white to-stone-100 opacity-90"
            ></div>

            <!-- Holographic overlay effects -->
            <div
              class="absolute inset-0 rounded-2xl bg-gradient-to-br from-transparent via-white/20 to-transparent opacity-30 mix-blend-mode-overlay"
            ></div>

            <!-- Content container -->
            <div class="relative p-4 pt-8 sm:p-6 md:p-8 sm:pt-10 md:pt-12">
              <!-- Badge display section -->
              <div class="mb-6 text-center sm:mb-8">
                <!-- Large badge display with enhanced holographic effects -->
                <div
                  class="inline-flex mx-auto mb-4 transition-transform duration-500 ease-out rounded-2xl sm:mb-6 transform-gpu hover:scale-105"
                  data-badge-modal-target="badgeDisplay"
                  style="view-transition-name: badge-hero"
                >
                  <!-- Badge content will be dynamically inserted here -->
                </div>

                <!-- Badge title -->
                <h2
                  id="badge-modal-title"
                  class="mb-2 text-xl font-bold sm:text-2xl text-stone-900"
                  data-badge-modal-target="title"
                >
                  <!-- Badge name will be inserted here -->
                </h2>

                <!-- Badge subtitle/category -->
                <p
                  class="text-xs font-medium tracking-wide uppercase sm:text-sm text-stone-500"
                >
                  Achievement Badge
                </p>
              </div>

              <!-- Badge description section -->
              <div class="mb-6 sm:mb-8">
                <h3
                  class="mb-2 text-base font-semibold sm:text-lg text-stone-900 sm:mb-3"
                >
                  About This Badge
                </h3>
                <p
                  id="badge-modal-description"
                  class="text-sm leading-relaxed sm:text-base text-stone-700"
                  data-badge-modal-target="description"
                >
                  <!-- Badge description will be inserted here -->
                </p>
              </div>

              <!-- Badge criteria section -->
              <div class="mb-6 sm:mb-8">
                <h3
                  class="mb-2 text-base font-semibold sm:text-lg text-stone-900 sm:mb-3"
                >
                  Why It's Special
                </h3>
                <div
                  class="p-3 border rounded-lg bg-stone-50 sm:p-4 border-stone-200"
                  data-badge-modal-target="criteria"
                >
                  <p class="text-xs leading-relaxed text-stone-700 sm:text-sm">
                    This badge represents exceptional achievement and is awarded
                    to recognize outstanding contributions to the platform.
                  </p>
                </div>
              </div>

              <!-- Badge metadata section -->
              <div class="pt-6 border-t border-stone-200">
                <h3 class="sr-only">Badge Information</h3>
                <dl class="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <dt class="font-medium text-stone-500">Badge Type</dt>
                    <dd class="mt-1 text-stone-900">Achievement</dd>
                  </div>
                  <div>
                    <dt class="font-medium text-stone-500">Rarity</dt>
                    <dd class="mt-1 text-stone-900">
                      <span
                        class="inline-flex items-center rounded-full bg-amber-100 px-2.5 py-0.5 text-xs font-medium text-amber-800"
                        role="status"
                        aria-label="Badge rarity: Special"
                      >
                        Special
                      </span>
                    </dd>
                  </div>
                </dl>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Test with Rails integration -->
    <script>
      // Test that the badge modal integration works
      console.log("Badge test page loaded");
      console.log(
        "Badge modal element:",
        document.getElementById("badge-modal")
      );

      // Test message
      document.addEventListener("DOMContentLoaded", function () {
        console.log("✅ Badge modal test page loaded successfully");
        console.log(
          "✅ Badge modal element found:",
          !!document.getElementById("badge-modal")
        );
        console.log(
          "✅ Test badges rendered:",
          document.querySelectorAll('[data-controller*="badge-click"]').length
        );

        // Add a visual indicator that the test is ready
        const indicator = document.createElement("div");
        indicator.className =
          "fixed top-4 right-4 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded z-50";
        indicator.innerHTML =
          "✅ Badge Modal Test Ready - Click any badge above!";
        document.body.appendChild(indicator);

        // Remove indicator after 5 seconds
        setTimeout(() => {
          indicator.remove();
        }, 5000);
      });
    </script>

    <!-- Load Rails JavaScript for Stimulus controllers -->
    <script>
      // This test page demonstrates that the badge modal infrastructure is working
      // The actual Stimulus controllers would be loaded by the Rails application
      console.log("Badge modal integration test completed");
    </script>
  </body>
</html>
