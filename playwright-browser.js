#!/usr/bin/env node

/**
 * Simple Playwright MCP Browser Script
 *
 * This script demonstrates how to use the Playwright MCP for web browsing.
 * It starts a Playwright MCP server and provides a simple interface for web automation.
 */

const { spawn } = require("child_process");
const readline = require("readline");

class PlaywrightBrowser {
  constructor() {
    this.mcpProcess = null;
    this.rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout,
    });
  }

  async start() {
    console.log("🚀 Starting Playwright MCP Browser...");
    console.log(
      "📖 This will start a browser automation session using Playwright MCP"
    );

    // Start the Playwright MCP server
    this.mcpProcess = spawn(
      "node",
      ["node_modules/@playwright/mcp/cli.js", "--port", "8931"],
      {
        stdio: "inherit",
      }
    );

    this.mcpProcess.on("error", (error) => {
      console.error("❌ Error starting Playwright MCP:", error);
    });

    this.mcpProcess.on("exit", (code) => {
      console.log(`🔚 Playwright MCP exited with code ${code}`);
    });

    console.log("✅ Playwright MCP server started!");
    console.log("");
    console.log(
      "🌐 You can now use the browser automation tools through your MCP client."
    );
    console.log("");
    console.log("Available commands:");
    console.log("  - Navigate to URLs");
    console.log("  - Take screenshots");
    console.log("  - Click elements");
    console.log("  - Type text");
    console.log("  - And much more!");
    console.log("");
    console.log("Press Ctrl+C to stop the browser session.");

    // Keep the process running
    process.on("SIGINT", () => {
      this.stop();
    });
  }

  stop() {
    console.log("\n🛑 Stopping Playwright MCP Browser...");
    if (this.mcpProcess) {
      this.mcpProcess.kill();
    }
    this.rl.close();
    process.exit(0);
  }
}

// Start the browser if this script is run directly
if (require.main === module) {
  const browser = new PlaywrightBrowser();
  browser.start().catch(console.error);
}

module.exports = PlaywrightBrowser;
