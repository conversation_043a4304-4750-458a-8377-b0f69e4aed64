# frozen_string_literal: true

require 'test_helper'

class AdminBadgeAnalyticsServiceTest < ActiveSupport::TestCase
  def setup
    # Clear caches before each test
    Rails.cache.clear

    # Create superadmin role and admin user
    @superadmin_role = Role.find_or_create_by!(name: 'superadmin')
    @admin =
      User.create!(
        first_name: 'Admin',
        last_name: 'User',
        email: '<EMAIL>',
        password: 'password123456',
        verified: true,
      )
    UserRole.create!(user: @admin, role: @superadmin_role)

    @user1 =
      User.create!(
        first_name: 'Test',
        last_name: 'User1',
        email: '<EMAIL>',
        password: 'password123456',
        verified: true,
      )

    @user2 =
      User.create!(
        first_name: 'Test',
        last_name: 'User2',
        email: '<EMAIL>',
        password: 'password123456',
        verified: true,
      )

    @badge_type1 =
      BadgeType.create!(
        name: 'Expert Badge',
        description: 'For expert users',
        background_color: '#000000',
        text_color: '#FFFFFF',
        icon: 'star',
        priority: 1,
        active: true,
      )

    @badge_type2 =
      BadgeType.create!(
        name: 'Contributor Badge',
        description: 'For active contributors',
        background_color: '#000000',
        text_color: '#FFFFFF',
        icon: 'heart',
        priority: 2,
        active: true,
      )

    # Create badge assignments
    @assignment1 =
      BadgeAssignment.create!(
        user: @user1,
        badge_type: @badge_type1,
        admin: @admin,
        assigned_at: 2.days.ago,
        notes: 'Test assignment 1',
      )

    @assignment2 =
      BadgeAssignment.create!(
        user: @user2,
        badge_type: @badge_type2,
        admin: @admin,
        assigned_at: 1.day.ago,
        notes: 'Test assignment 2',
      )

    # Create badge clicks
    @click1 =
      BadgeClick.create!(
        badge_type: @badge_type1,
        badge_owner: @user1,
        clicker_user: @user2,
        click_context: 'profile',
        ip_address: '***********',
        created_at: 1.day.ago,
      )

    @click2 =
      BadgeClick.create!(
        badge_type: @badge_type1,
        badge_owner: @user1,
        clicker_user: nil,
        click_context: 'profile',
        ip_address: '***********',
        created_at: 6.hours.ago,
      )

    # Create badge views
    @view1 =
      BadgeView.create!(
        viewed_user: @user1,
        viewer_user: @user2,
        badge_types_displayed: [@badge_type1.id],
        ip_address: '***********',
        created_at: 1.day.ago,
      )

    @view2 =
      BadgeView.create!(
        viewed_user: @user2,
        viewer_user: nil,
        badge_types_displayed: [@badge_type2.id],
        ip_address: '***********',
        created_at: 12.hours.ago,
      )
  end

  test 'generate_badge_analytics returns complete analytics data' do
    analytics =
      AdminBadgeAnalyticsService.generate_badge_analytics(date_range: 7.days)

    assert_not_nil analytics
    assert_includes analytics.keys, :overview
    assert_includes analytics.keys, :badge_performance
    assert_includes analytics.keys, :user_engagement
    assert_includes analytics.keys, :click_analytics
    assert_includes analytics.keys, :view_analytics
    assert_includes analytics.keys, :trends
    assert_includes analytics.keys, :top_performers
  end

  test 'overview metrics calculation' do
    analytics =
      AdminBadgeAnalyticsService.generate_badge_analytics(date_range: 7.days)
    overview = analytics[:overview]

    assert_equal 2, overview[:total_badges_assigned]
    assert_equal 2, overview[:total_badge_clicks]
    assert_equal 2, overview[:total_badge_views]
    assert overview[:unique_badge_clickers] >= 1
    assert overview[:unique_badge_viewers] >= 1
    assert_equal 2, overview[:active_badge_types]
    assert_equal 2, overview[:users_with_badges]
  end

  test 'badge performance calculation' do
    analytics =
      AdminBadgeAnalyticsService.generate_badge_analytics(date_range: 7.days)
    performance = analytics[:badge_performance]

    assert_equal 2, performance.length

    # Check first badge performance
    badge1_perf =
      performance.find { |p| p[:badge_type][:id] == @badge_type1.id }
    assert_not_nil badge1_perf
    assert_equal 1, badge1_perf[:metrics][:total_assignments]
    assert_equal 2, badge1_perf[:metrics][:total_clicks]
    assert_equal 1, badge1_perf[:metrics][:total_views]
  end

  test 'user engagement metrics calculation' do
    analytics =
      AdminBadgeAnalyticsService.generate_badge_analytics(date_range: 7.days)
    engagement = analytics[:user_engagement]

    assert_equal 2, engagement[:total_users_with_badges]
    assert engagement[:users_with_badge_clicks] >= 0
    assert engagement[:users_with_badge_views] >= 0
    assert engagement[:click_engagement_rate] >= 0
    assert engagement[:view_engagement_rate] >= 0
    assert engagement[:avg_badges_per_user] > 0
  end

  test 'click analytics calculation' do
    analytics =
      AdminBadgeAnalyticsService.generate_badge_analytics(date_range: 7.days)
    clicks = analytics[:click_analytics]

    assert_equal 2, clicks[:total_clicks]
    assert_includes clicks[:clicks_by_context].keys, 'profile'
    assert_includes clicks[:clicks_by_context].keys, 'search_results'
    assert_equal 1, clicks[:authenticated_clicks]
    assert_equal 1, clicks[:anonymous_clicks]
    assert clicks[:clicks_by_day].is_a?(Hash)
  end

  test 'view analytics calculation' do
    analytics =
      AdminBadgeAnalyticsService.generate_badge_analytics(date_range: 7.days)
    views = analytics[:view_analytics]

    assert_equal 2, views[:total_views]
    assert_equal 1, views[:authenticated_views]
    assert_equal 1, views[:anonymous_views]
    assert views[:views_by_day].is_a?(Hash)
    assert views[:top_viewed_users].is_a?(Array)
  end

  test 'filtering by badge type' do
    analytics =
      AdminBadgeAnalyticsService.generate_badge_analytics(
        date_range: 7.days,
        badge_type_id: @badge_type1.id,
      )

    overview = analytics[:overview]
    assert_equal 1, overview[:total_badges_assigned]
    assert_equal 2, overview[:total_badge_clicks]
    assert_equal 1, overview[:total_badge_views]

    performance = analytics[:badge_performance]
    assert_equal 1, performance.length
    assert_equal @badge_type1.id, performance.first[:badge_type][:id]
  end

  test 'caching functionality' do
    # First call should cache the result
    analytics1 =
      AdminBadgeAnalyticsService.generate_badge_analytics(date_range: 7.days)

    # Second call should return cached result
    analytics2 =
      AdminBadgeAnalyticsService.generate_badge_analytics(date_range: 7.days)

    assert_equal analytics1, analytics2
  end

  test 'daily counts calculation' do
    service = AdminBadgeAnalyticsService.new
    scope = BadgeClick.where('created_at >= ?', 7.days.ago)
    daily_counts = service.send(:calculate_daily_counts, scope, 7.days)

    assert daily_counts.is_a?(Hash)
    assert daily_counts.keys.all? { |key| key.is_a?(String) }
    assert daily_counts.values.all? { |value| value.is_a?(Integer) }
  end

  test 'percentage change calculation' do
    service = AdminBadgeAnalyticsService.new

    assert_equal 0, service.send(:calculate_percentage_change, 10, 10)
    assert_equal 100, service.send(:calculate_percentage_change, 20, 10)
    assert_equal -50, service.send(:calculate_percentage_change, 5, 10)
    assert_equal 0, service.send(:calculate_percentage_change, 0, 0)
    assert_equal 100, service.send(:calculate_percentage_change, 10, 0)
  end

  test 'handles empty data gracefully' do
    # Clear all test data
    BadgeClick.delete_all
    BadgeView.delete_all
    BadgeAssignment.delete_all

    analytics =
      AdminBadgeAnalyticsService.generate_badge_analytics(date_range: 7.days)

    overview = analytics[:overview]
    assert_equal 0, overview[:total_badges_assigned]
    assert_equal 0, overview[:total_badge_clicks]
    assert_equal 0, overview[:total_badge_views]
    assert_equal 0, overview[:unique_badge_clickers]
    assert_equal 0, overview[:unique_badge_viewers]
  end

  test 'trends calculation with previous period' do
    analytics =
      AdminBadgeAnalyticsService.generate_badge_analytics(date_range: 7.days)
    trends = analytics[:trends]

    assert_includes trends.keys, :assignments_change
    assert_includes trends.keys, :clicks_change
    assert_includes trends.keys, :views_change
    assert_includes trends.keys, :engagement_change
  end

  test 'top performers calculation' do
    analytics =
      AdminBadgeAnalyticsService.generate_badge_analytics(date_range: 7.days)
    top_performers = analytics[:top_performers]

    assert_includes top_performers.keys, :top_clicked_users
    assert_includes top_performers.keys, :top_viewed_users
    assert top_performers[:top_clicked_users].is_a?(Array)
    assert top_performers[:top_viewed_users].is_a?(Array)
  end
end
