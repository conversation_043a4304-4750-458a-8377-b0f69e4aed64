# frozen_string_literal: true

require 'test_helper'

class AdminCounterCacheTest < ActiveSupport::TestCase
  def setup
    # Clear caches before each test
    Rails.cache.clear
  end

  test "counter cache columns exist on models" do
    # Test that counter cache columns exist
    assert Job.column_names.include?('job_applications_count')
    assert Conversation.column_names.include?('messages_count')
    assert Organization.column_names.include?('jobs_count')
    assert User.column_names.include?('conversations_count')
    assert User.column_names.include?('sent_chat_requests_count')
    assert User.column_names.include?('received_chat_requests_count')
  end

  test "dashboard stats service methods exist and return hashes" do
    service = AdminDashboardStatsService.new
    
    # Test that all methods exist and return hashes
    assert service.respond_to?(:user_stats)
    assert service.respond_to?(:job_stats)
    assert service.respond_to?(:organization_stats)
    assert service.respond_to?(:communication_stats)
    assert service.respond_to?(:application_stats)
    
    # Test that methods return hashes (even if empty)
    assert service.user_stats.is_a?(Hash)
    assert service.job_stats.is_a?(Hash)
    assert service.organization_stats.is_a?(Hash)
    assert service.communication_stats.is_a?(Hash)
    assert service.application_stats.is_a?(Hash)
  end

  test "filter options service methods exist" do
    # Test that service methods exist
    assert AdminFilterOptionsService.respond_to?(:job_filter_options)
    assert AdminFilterOptionsService.respond_to?(:user_filter_options)
    assert AdminFilterOptionsService.respond_to?(:audit_log_filter_options)
    assert AdminFilterOptionsService.respond_to?(:clear_cache)
    assert AdminFilterOptionsService.respond_to?(:clear_all_caches)
  end

  test "performance monitor service methods exist" do
    # Test that service methods exist
    assert AdminPerformanceMonitorService.respond_to?(:monitor_query)
    assert AdminPerformanceMonitorService.respond_to?(:log_slow_query)
  end

  test "recent activity service methods exist" do
    # Test that service methods exist
    assert AdminRecentActivityService.respond_to?(:generate_recent_activity)
    assert AdminRecentActivityService.respond_to?(:clear_cache)
  end

  test "counter cache associations are configured" do
    # Test JobApplication counter cache
    job_association = JobApplication.reflect_on_association(:job)
    assert job_association.options[:counter_cache]
    
    # Test Message counter cache
    message_association = Message.reflect_on_association(:conversation)
    assert message_association.options[:counter_cache]
    
    # Test Job counter cache
    job_org_association = Job.reflect_on_association(:organization)
    assert job_org_association.options[:counter_cache]
    
    # Test ChatRequest counter caches
    scout_association = ChatRequest.reflect_on_association(:scout)
    talent_association = ChatRequest.reflect_on_association(:talent)
    assert_equal :sent_chat_requests_count, scout_association.options[:counter_cache]
    assert_equal :received_chat_requests_count, talent_association.options[:counter_cache]
    
    # Test ConversationParticipant counter cache
    participant_association = ConversationParticipant.reflect_on_association(:user)
    assert_equal :conversations_count, participant_association.options[:counter_cache]
  end
end
