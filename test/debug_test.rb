require "test_helper"

class DebugTest < ActiveSupport::TestCase
  test "admin user has superadmin role" do
    admin = users(:admin)
    puts "Admin: #{admin.inspect}"
    puts "Admin roles: #{admin.roles.pluck(:name)}"
    puts "Admin superadmin?: #{admin.superadmin?}"
    
    scout = users(:scout)
    puts "Scout: #{scout.inspect}"
    puts "Scout roles: #{scout.roles.pluck(:name)}"
    puts "Scout superadmin?: #{scout.superadmin?}"
    
    # Test impersonation log creation
    log = ImpersonationLog.new(
      admin: admin,
      user: scout,
      started_at: Time.current,
      ip_address: "127.0.0.1",
      user_agent: "Test Browser"
    )
    
    puts "Log valid?: #{log.valid?}"
    puts "Log errors: #{log.errors.full_messages}" unless log.valid?
    
    assert admin.superadmin?
  end
end
