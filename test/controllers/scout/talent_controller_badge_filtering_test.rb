require 'test_helper'

class Scout::TalentControllerBadgeFilteringTest < ActionDispatch::IntegrationTest
  def setup
    # Create scout user and organization
    @scout =
      User.create!(
        email: '<EMAIL>',
        password: 'password123456',
        first_name: 'Scout',
        last_name: 'Test',
        verified: true,
        scout_signup_completed: true,
      )

    @organization =
      Organization.create!(name: 'Test Organization', slug: 'test-org')
    OrganizationMembership.create!(
      user: @scout,
      organization: @organization,
      org_role: 'admin',
    )
    @scout.update!(last_logged_in_organization_id: @organization.id)

    # Create talent users with different badge combinations
    @talent_with_choice_badge =
      User.create!(
        email: '<EMAIL>',
        password: 'password123456',
        first_name: 'Choice',
        last_name: 'Talent',
        verified: true,
        talent_signup_completed: true,
      )

    @talent_with_expert_badge =
      User.create!(
        email: '<EMAIL>',
        password: 'password123456',
        first_name: 'Expert',
        last_name: 'Talent',
        verified: true,
        talent_signup_completed: true,
      )

    @talent_without_badges =
      User.create!(
        email: '<EMAIL>',
        password: 'password123456',
        first_name: 'Unbadged',
        last_name: 'Talent',
        verified: true,
        talent_signup_completed: true,
      )

    # Create talent profiles
    @profile_choice =
      TalentProfile.create!(
        user: @talent_with_choice_badge,
        headline: 'Ghostwrote Choice Writer',
        about: 'Top-rated ghostwriter',
        availability_status: 'available',
      )

    @profile_expert =
      TalentProfile.create!(
        user: @talent_with_expert_badge,
        headline: 'Expert Writer',
        about: 'Proven expertise',
        availability_status: 'available',
      )

    @profile_unbadged =
      TalentProfile.create!(
        user: @talent_without_badges,
        headline: 'New Writer',
        about: 'Starting out',
        availability_status: 'available',
      )

    # Create badge types
    @ghostwrote_choice_badge =
      BadgeType.create!(
        name: 'Ghostwrote Choice',
        description: 'Top-rated ghostwriter',
        icon: 'star',
        background_color: '#FFD700',
        text_color: '#000000',
        priority: 1,
      )

    @expert_badge =
      BadgeType.create!(
        name: 'Expert Writer',
        description: 'Proven expertise',
        icon: 'certificate',
        background_color: '#4F46E5',
        text_color: '#FFFFFF',
        priority: 2,
      )

    # Assign badges
    BadgeAssignment.create!(
      user: @talent_with_choice_badge,
      badge_type: @ghostwrote_choice_badge,
      assigned_by: @scout,
      assigned_at: 1.day.ago,
    )

    BadgeAssignment.create!(
      user: @talent_with_expert_badge,
      badge_type: @expert_badge,
      assigned_by: @scout,
      assigned_at: 1.day.ago,
    )

    # Sign in as scout
    post sign_in_url,
         params: {
           email: @scout.email,
           password: 'password123456',
         }
  end

  def teardown
    BadgeAssignment.destroy_all
    BadgeType.destroy_all
    TalentProfile.destroy_all
    OrganizationMembership.destroy_all
    Organization.destroy_all
    User.destroy_all
  end

  test 'index without badge filter returns all talent profiles' do
    get scout_talent_index_path

    assert_response :success
    assert_includes response.body, 'Choice Talent'
    assert_includes response.body, 'Expert Talent'
    assert_includes response.body, 'Unbadged Talent'
  end

  test 'index with single badge filter returns only matching profiles' do
    get scout_talent_index_path, params: { badge_types: 'Ghostwrote Choice' }

    assert_response :success
    assert_includes response.body, 'Choice Talent'
    assert_not_includes response.body, 'Expert Talent'
    assert_not_includes response.body, 'Unbadged Talent'
  end

  test 'index with multiple badge filters returns profiles with any of the badges' do
    get scout_talent_index_path,
        params: {
          badge_types: 'Ghostwrote Choice,Expert Writer',
        }

    assert_response :success
    assert_includes response.body, 'Choice Talent'
    assert_includes response.body, 'Expert Talent'
    assert_not_includes response.body, 'Unbadged Talent'
  end

  test 'index with non-existent badge filter returns no results' do
    get scout_talent_index_path, params: { badge_types: 'Non-existent Badge' }

    assert_response :success
    assert_not_includes response.body, 'Choice Talent'
    assert_not_includes response.body, 'Expert Talent'
    assert_not_includes response.body, 'Unbadged Talent'
  end

  test 'badge filter displays active filter tag' do
    get scout_talent_index_path, params: { badge_types: 'Ghostwrote Choice' }

    assert_response :success
    assert_includes response.body, 'Ghostwrote Choice'
    assert_select '[data-filter-type="badge_types"]'
    assert_select 'button[data-action*="talent-filter-tags#removeFilter"]'
  end

  test 'badge filter combines with other filters' do
    get scout_talent_index_path,
        params: {
          badge_types: 'Ghostwrote Choice',
          availability_status: 'available',
        }

    assert_response :success
    assert_includes response.body, 'Choice Talent'
    assert_not_includes response.body, 'Expert Talent'
    assert_not_includes response.body, 'Unbadged Talent'

    # Should show both filter tags
    assert_includes response.body, 'Ghostwrote Choice'
    assert_includes response.body, 'Available'
  end

  test 'badge aggregations are included in search response' do
    get scout_talent_index_path

    assert_response :success

    # Check that badge aggregations are available for the filter dropdown
    # This would be tested by checking the @aggregations instance variable
    # but we can verify the UI shows the badge filter dropdown
    assert_includes response.body, 'Badges'
    assert_select 'button', text: 'Badges'
  end

  test 'empty badge_types parameter is handled gracefully' do
    get scout_talent_index_path, params: { badge_types: '' }

    assert_response :success

    # Should return all profiles when badge_types is empty
    assert_includes response.body, 'Choice Talent'
    assert_includes response.body, 'Expert Talent'
    assert_includes response.body, 'Unbadged Talent'
  end
end
