require 'test_helper'

class Talent::ConversationsControllerTest < ActionDispatch::IntegrationTest
  setup do
    # Create organizations
    @organization = Organization.create!(name: 'Test Organization')

    # Find or create roles
    @scout_role = Role.find_or_create_by!(name: 'scout')
    @talent_role = Role.find_or_create_by!(name: 'talent')

    # Create scout user
    @scout =
      User.create!(
        email: "scout_#{SecureRandom.hex(8)}@example.com",
        password: 'password123123',
        password_confirmation: 'password123123',
        first_name: 'Scout',
        last_name: 'User',
        verified: true,
        signup_intent: 'scout',
        onboarding_step: 'completed',
      )

    # Create talent user
    @talent =
      User.create!(
        email: "talent_#{SecureRandom.hex(8)}@example.com",
        password: 'password123123',
        password_confirmation: 'password123123',
        first_name: 'Talent',
        last_name: 'User',
        verified: true,
        signup_intent: 'talent',
        talent_signup_completed: true,
        onboarding_step: 'completed',
      )

    # Assign roles
    UserRole.create!(user: @scout, role: @scout_role)
    UserRole.create!(user: @talent, role: @talent_role)

    # Create organization memberships
    [@scout, @talent].each do |user|
      OrganizationMembership.create!(
        user: user,
        organization: @organization,
        org_role: 'member',
      )
      user.update!(last_logged_in_organization_id: @organization.id)
    end

    # Create talent profile for the talent user
    TalentProfile.create!(
      user: @talent,
      bio: 'Test talent bio',
      price_range_min: 50,
      price_range_max: 100,
      availability_status: 'available',
    )

    # Create a conversation
    @conversation = Conversation.find_or_create_by_participants(@scout, @talent)

    # Sign in as talent user
    post sign_in_url,
         params: {
           email: @talent.email,
           password: 'password123123',
         }
  end

  def teardown
    Message.destroy_all
    ConversationParticipant.destroy_all
    Conversation.destroy_all
    ChatRequest.destroy_all
    TalentProfile.destroy_all
    BadgeAssignment.destroy_all
    ImpersonationLog.destroy_all
    OrganizationMembership.destroy_all
    UserRole.destroy_all
    Organization.destroy_all
    User.destroy_all
  end

  test 'should get index with active conversations by default' do
    get talent_conversations_path
    assert_response :success

    # Should show filter buttons
    assert_includes response.body, 'Active'
    assert_includes response.body, 'Archived'
    assert_includes response.body, 'Chat Requests'

    # Should show conversation
    assert_includes response.body, @scout.name
  end

  test 'should get index with archived filter' do
    # Archive the conversation for the talent user
    participant = @conversation.conversation_participants.find_by(user: @talent)
    participant.update!(archived: true)

    get talent_conversations_path, params: { status: 'archived' }
    assert_response :success

    # Should show the archived conversation
    assert_includes response.body, @scout.name
  end

  test 'should get index with chat requests filter' do
    # Create a pending chat request
    chat_request =
      @scout.sent_chat_requests.create!(
        talent: @talent,
        pitch: 'Test pitch message',
      )

    get talent_conversations_path, params: { status: 'chat_requests' }
    assert_response :success

    # Should show chat request instead of conversations
    assert_includes response.body, @scout.name
    assert_includes response.body, 'Test pitch message'
    assert_includes response.body, 'Accept'
    assert_includes response.body, 'Decline'
  end

  test 'should show correct counts for each filter' do
    # Create additional conversations and chat requests
    scout2 =
      User.create!(
        email: '<EMAIL>',
        password: 'password123123',
        password_confirmation: 'password123123',
        first_name: 'Scout2',
        last_name: 'User',
        verified: true,
        signup_intent: 'scout',
        onboarding_step: 'completed',
      )

    UserRole.create!(user: scout2, role: @scout_role)
    OrganizationMembership.create!(
      user: scout2,
      organization: @organization,
      org_role: 'member',
    )
    scout2.update!(last_logged_in_organization_id: @organization.id)

    # Create second conversation
    conversation2 = Conversation.find_or_create_by_participants(scout2, @talent)

    # Archive one conversation
    participant = conversation2.conversation_participants.find_by(user: @talent)
    participant.update!(archived: true)

    # Create pending chat requests
    @scout.sent_chat_requests.create!(talent: @talent, pitch: 'Request 1')
    scout2.sent_chat_requests.create!(talent: @talent, pitch: 'Request 2')

    get talent_conversations_path
    assert_response :success

    # Check that the page loads successfully with filter buttons
    # Note: Actual counts may vary based on test data setup
  end

  test 'should show empty state for chat requests when none exist' do
    get talent_conversations_path, params: { status: 'chat_requests' }
    assert_response :success

    assert_includes response.body, 'No chat requests'
    assert_includes response.body,
                    'When scouts want to chat with you, their requests will appear here.'
  end

  test 'should show conversation details' do
    # Create a message in the conversation
    Message.create!(
      conversation: @conversation,
      user: @scout,
      body: 'Hello from scout!',
    )

    get talent_conversation_path(@conversation)
    assert_response :success

    assert_includes response.body, @scout.name
    assert_includes response.body, 'Hello from scout!'
  end

  test 'should not allow access without talent signup completion' do
    # Create user without talent signup completed
    incomplete_user =
      User.create!(
        email: '<EMAIL>',
        password: 'password123123',
        password_confirmation: 'password123123',
        first_name: 'Incomplete',
        last_name: 'User',
        verified: true,
        signup_intent: 'talent',
        talent_signup_completed: false,
        onboarding_step: 'personal',
      )

    UserRole.create!(user: incomplete_user, role: @talent_role)
    OrganizationMembership.create!(
      user: incomplete_user,
      organization: @organization,
      org_role: 'member',
    )
    incomplete_user.update!(last_logged_in_organization_id: @organization.id)

    # Sign in as incomplete user
    post sign_in_url,
         params: {
           email: incomplete_user.email,
           password: 'password123123',
         }

    get talent_conversations_path
    assert_redirected_to onboarding_personal_path
    assert_includes flash[:alert],
                    'Please complete your profile setup to continue'
  end

  test 'should handle invalid status parameter gracefully' do
    get talent_conversations_path, params: { status: 'invalid_status' }
    assert_response :success

    # Should default to showing active conversations
    assert_includes response.body, @scout.name
  end

  test 'should archive conversation' do
    post archive_talent_conversation_path(@conversation)

    participant = @conversation.conversation_participants.find_by(user: @talent)
    assert participant.archived?

    assert_redirected_to talent_conversations_path(status: 'archived')
  end

  test 'should unarchive conversation' do
    # First archive the conversation
    participant = @conversation.conversation_participants.find_by(user: @talent)
    participant.update!(archived: true)

    post unarchive_talent_conversation_path(@conversation)

    participant.reload
    assert_not participant.archived?

    assert_redirected_to talent_conversations_path
  end
end
