# frozen_string_literal: true

require 'test_helper'

class CsvExportPerformanceTest < ActiveSupport::TestCase
  include SuperAdmin::CsvExportable

  def setup
    @controller_name = 'admin_jobs'
  end

  def controller_name
    @controller_name
  end

  def params
    {}
  end

  test "CSV export methods exist and return valid CSV" do
    # Test that all CSV generation methods exist
    assert respond_to?(:generate_users_csv)
    assert respond_to?(:generate_jobs_csv)
    assert respond_to?(:generate_job_applications_csv)
    assert respond_to?(:generate_messages_csv)
    assert respond_to?(:generate_talent_profiles_csv)
    assert respond_to?(:generate_organizations_csv)
    assert respond_to?(:generate_conversations_csv)
    assert respond_to?(:generate_chat_requests_csv)
    assert respond_to?(:generate_admin_roles_csv)
    assert respond_to?(:generate_audit_logs_csv)
  end

  test "CSV export uses proper includes for associations" do
    # Test that the CSV export methods use proper includes
    # This is a code inspection test to ensure we're avoiding N+1 queries
    
    csv_methods = [
      :generate_users_csv,
      :generate_jobs_csv,
      :generate_job_applications_csv,
      :generate_messages_csv,
      :generate_talent_profiles_csv,
      :generate_organizations_csv,
      :generate_conversations_csv,
      :generate_chat_requests_csv,
      :generate_admin_roles_csv
    ]

    csv_methods.each do |method|
      method_source = method(method).source_location
      assert method_source, "Method #{method} should have source location"
      
      # Read the method source to check for includes
      file_path = method_source[0]
      line_number = method_source[1]
      
      if File.exist?(file_path)
        file_content = File.read(file_path)
        method_lines = file_content.lines[line_number-1..-1]
        
        # Find the method definition and its end
        method_content = []
        indent_level = nil
        in_method = false
        
        method_lines.each do |line|
          if line.include?("def #{method}")
            in_method = true
            indent_level = line.index('def')
            method_content << line
          elsif in_method
            current_indent = line.strip.empty? ? Float::INFINITY : (line.index(/\S/) || 0)
            
            if current_indent <= indent_level && line.strip.start_with?('def ', 'end', 'private', 'protected')
              break if line.strip == 'end' || line.strip.start_with?('def ')
            end
            
            method_content << line
          end
        end
        
        method_text = method_content.join
        
        # Check that methods with associations use includes
        case method
        when :generate_users_csv
          assert method_text.include?('includes(:organizations)'), 
                 "#{method} should include organizations association"
        when :generate_jobs_csv
          assert method_text.include?('includes(:organization)'), 
                 "#{method} should include organization association"
        when :generate_job_applications_csv
          assert method_text.include?('includes(:user, :job, :job_invitation)'), 
                 "#{method} should include user, job, and job_invitation associations"
        when :generate_messages_csv
          assert method_text.include?('includes(:user, :conversation)'), 
                 "#{method} should include user and conversation associations"
        when :generate_talent_profiles_csv
          assert method_text.include?('includes(:user)'), 
                 "#{method} should include user association"
        when :generate_organizations_csv
          assert method_text.include?('includes(:users)'), 
                 "#{method} should include users association"
        when :generate_conversations_csv
          assert method_text.include?('includes(:users, :job)'), 
                 "#{method} should include users and job associations"
        when :generate_chat_requests_csv
          assert method_text.include?('includes(:scout, :talent)'), 
                 "#{method} should include scout and talent associations"
        when :generate_admin_roles_csv
          assert method_text.include?('includes(:roles, :organizations)'), 
                 "#{method} should include roles and organizations associations"
        end
      end
    end
  end

  test "CSV export uses counter caches where appropriate" do
    # Test that CSV exports use counter cache columns instead of count queries
    file_path = File.join(Rails.root, 'app/controllers/concerns/super_admin/csv_exportable.rb')
    file_content = File.read(file_path)

    # Check that jobs CSV uses job_applications_count
    assert file_content.include?('job.job_applications_count'), 
           "Jobs CSV should use job_applications_count counter cache"

    # Check that organizations CSV uses jobs_count
    assert file_content.include?('org.jobs_count'), 
           "Organizations CSV should use jobs_count counter cache"

    # Check that conversations CSV uses messages_count
    assert file_content.include?('conversation.messages_count'), 
           "Conversations CSV should use messages_count counter cache"

    # Check that we're not using inefficient count queries
    refute file_content.include?('job_applications.count'), 
           "Should not use job_applications.count in CSV exports"
    refute file_content.include?('messages.count'), 
           "Should not use messages.count in CSV exports"
  end

  test "CSV export methods use efficient collection iteration" do
    # Test that CSV exports use find_each for memory efficiency
    file_path = File.join(Rails.root, 'app/controllers/concerns/super_admin/csv_exportable.rb')
    file_content = File.read(file_path)

    # Count occurrences of find_each vs each
    find_each_count = file_content.scan(/\.find_each/).length
    each_count = file_content.scan(/\.each(?!\w)/).length - file_content.scan(/find_each/).length

    # Should use find_each for database iteration
    assert find_each_count > 0, "Should use find_each for database iteration"
    
    # find_each should be used more than regular each for collection processing
    # (allowing some each for array operations like map(&:email).join)
    assert find_each_count >= 8, "Should use find_each for all main CSV generation loops"
  end

  test "async export threshold is reasonable" do
    # Test that async export threshold is set to a reasonable value
    assert_equal 5000, should_export_async?(double_collection(5001))
    refute should_export_async?(double_collection(4999))
  end

  private

  def double_collection(count)
    # Create a mock collection that responds to count
    mock_collection = Object.new
    mock_collection.define_singleton_method(:count) { count }
    mock_collection
  end

  def collection_for_export
    # Return empty relation for testing
    Job.none
  end
end
