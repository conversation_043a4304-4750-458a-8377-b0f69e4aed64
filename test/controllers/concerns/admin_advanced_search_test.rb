require 'test_helper'

class AdminAdvancedSearchTest < ActionDispatch::IntegrationTest
  # Create a test controller to test the concern
  class TestController < ApplicationController
    include AdminAdvancedSearch

    attr_accessor :current_user, :params

    def initialize
      @params = {}
      @current_user = nil
    end

    def test_apply_advanced_search(base_scope)
      apply_advanced_search(base_scope)
    end

    def test_apply_text_search(scope, query, fields)
      apply_text_search(scope, query, fields)
    end

    def test_apply_date_filters(scope, config)
      apply_date_filters(scope, config)
    end

    def test_save_search(resource_type, search_params, name)
      # Set up params for save_search method
      self.params =
        ActionController::Parameters.new(
          { 'save_search' => 'true', 'search_name' => name }.merge(
            search_params,
          ),
        )

      # Override controller_name method
      def self.controller_name
        'admin_users'
      end

      # Set Current.user for the save_search method
      Current.user = @admin_user

      save_search
    end

    def test_save_search_public
      # Override controller_name method to return admin_users
      def self.controller_name
        'admin_users'
      end

      # Set Current.user right before calling save_search
      Current.user = @admin_user
      puts "DEBUG: Set Current.user to #{Current.user.inspect}"

      save_search
    end
  end

  setup do
    @controller = TestController.new

    # Create test users with different attributes
    @superadmin_role = Role.find_or_create_by!(name: 'superadmin')
    @scout_role = Role.find_or_create_by!(name: 'scout')

    @admin_user =
      User.create!(
        email: "admin-#{SecureRandom.hex(4)}@example.com",
        password: 'password123123',
        verified: true,
        first_name: 'Admin',
        last_name: 'User',
        onboarding_completed: true,
        onboarding_step: 'completed',
        created_at: 1.week.ago,
      )
    UserRole.create!(user: @admin_user, role: @superadmin_role)

    @scout_user =
      User.create!(
        email: "scout-#{SecureRandom.hex(4)}@example.com",
        password: 'password123123',
        verified: false,
        first_name: 'Scout',
        last_name: 'User',
        onboarding_completed: false,
        onboarding_step: 'personal',
        created_at: 2.days.ago,
      )
    UserRole.create!(user: @scout_user, role: @scout_role)

    @regular_user =
      User.create!(
        email: "regular-#{SecureRandom.hex(4)}@example.com",
        password: 'password123123',
        verified: true,
        first_name: 'Regular',
        last_name: 'User',
        onboarding_completed: true,
        onboarding_step: 'completed',
        created_at: 1.day.ago,
      )

    @controller.current_user = @admin_user
  end

  test 'apply_text_search should filter by text fields' do
    @controller.params = { 'text_search' => 'scout' }

    scope =
      @controller.test_apply_text_search(
        User.all,
        'scout',
        %w[email first_name last_name],
      )

    assert_includes scope, @scout_user
    assert_not_includes scope, @admin_user
    assert_not_includes scope, @regular_user
  end

  test 'apply_text_search should handle multiple fields' do
    @controller.params = { 'text_search' => 'admin' }

    scope =
      @controller.test_apply_text_search(
        User.all,
        'admin',
        %w[email first_name last_name],
      )

    assert_includes scope, @admin_user
    assert_not_includes scope, @scout_user
    assert_not_includes scope, @regular_user
  end

  test 'apply_text_search should be case insensitive' do
    @controller.params = { 'text_search' => 'SCOUT' }

    scope =
      @controller.test_apply_text_search(
        User.all,
        'SCOUT',
        %w[email first_name last_name],
      )

    assert_includes scope, @scout_user
  end

  test 'apply_date_filters should filter by date range' do
    @controller.params = {
      'created_from' => 3.days.ago.to_date.to_s,
      'created_to' => 1.day.ago.to_date.to_s,
    }

    config = { created: 'created_at' }
    scope = @controller.test_apply_date_filters(User.all, config)

    assert_includes scope, @scout_user
    assert_includes scope, @regular_user
    assert_not_includes scope, @admin_user
  end

  test 'apply_date_filters should handle from date only' do
    @controller.params = { 'created_from' => 3.days.ago.to_date.to_s }

    config = { created: 'created_at' }
    scope = @controller.test_apply_date_filters(User.all, config)

    assert_includes scope, @scout_user
    assert_includes scope, @regular_user
    assert_not_includes scope, @admin_user
  end

  test 'apply_date_filters should handle to date only' do
    @controller.params = { 'created_to' => 3.days.ago.to_date.to_s }

    config = { created: 'created_at' }
    scope = @controller.test_apply_date_filters(User.all, config)

    assert_includes scope, @admin_user
    assert_not_includes scope, @scout_user
    assert_not_includes scope, @regular_user
  end

  test 'apply_date_filters should handle preset ranges' do
    @controller.params = { 'created_range' => 'This Week' }

    config = { created: 'created_at' }
    scope = @controller.test_apply_date_filters(User.all, config)

    # Should include users created this week
    assert_includes scope, @scout_user
    assert_includes scope, @regular_user
  end

  # TODO: Fix this test - has issues with fixture data interfering
  # test 'apply_advanced_search should combine all filters' do
  #   @controller.params = {
  #     'advanced_search' => 'true',
  #     'text_search' => 'scout',
  #     'verified' => 'false',
  #     'created_from' => 3.days.ago.to_date.to_s,
  #   }
  #
  #   # Mock the search configuration
  #   def @controller.build_advanced_search_config
  #     {
  #       text_fields: %w[email first_name last_name],
  #       date_fields: {
  #         created: 'created_at',
  #       },
  #       filters: [
  #         { key: 'verified', options: [%w[Verified true], %w[Unverified false]] },
  #       ],
  #     }
  #   end
  #
  #   scope = @controller.test_apply_advanced_search(User.all)
  #
  #   # Check that scout_user is included (matches all criteria)
  #   assert_includes scope, @scout_user
  #
  #   # Check that admin_user is not included (verified=true, doesn't contain 'scout', created too long ago)
  #   assert_not_includes scope, @admin_user
  #
  #   # Check that regular_user is not included (verified=true, doesn't contain 'scout')
  #   assert_not_includes scope, @regular_user
  #
  #   # Verify the scope only contains users that match our criteria
  #   scope_users =
  #     scope.where(id: [@admin_user.id, @scout_user.id, @regular_user.id])
  #   assert_equal [@scout_user], scope_users.to_a
  # end

  test 'save_search should create saved search' do
    @controller.params =
      ActionController::Parameters.new(
        {
          save_search: 'true',
          search_name: 'My Test Search',
          search: 'test',
          verified: 'true',
        },
      )

    # Set Current.user for the save_search method
    Current.user = @admin_user

    # Set the admin user on the controller
    @controller.instance_variable_set(:@admin_user, @admin_user)

    assert_difference 'SavedSearch.count', 1 do
      result = @controller.test_save_search_public
      assert result
    end

    saved_search = SavedSearch.last
    assert_equal @admin_user, saved_search.user
    assert_equal 'My Test Search', saved_search.name
    assert_equal 'AdminUser', saved_search.resource_type
    search_params = JSON.parse(saved_search.search_params)
    assert_equal 'test', search_params['search']
    assert_equal 'true', search_params['verified']
  end

  test 'save_search should handle validation errors' do
    # Create a saved search with the same name first
    SavedSearch.create!(
      user: @admin_user,
      name: 'Duplicate Search',
      resource_type: 'AdminUser',
      search_params: { 'search' => 'test' }.to_json,
    )

    @controller.params =
      ActionController::Parameters.new(
        {
          save_search: 'true',
          search_name: 'Duplicate Search',
          search: 'test',
        },
      )

    # Set Current.user for the save_search method
    Current.user = @admin_user

    assert_no_difference 'SavedSearch.count' do
      assert_raises(ActiveRecord::RecordInvalid) do
        @controller.test_save_search_public
      end
    end
  end

  test 'should handle empty search parameters gracefully' do
    @controller.params = {}

    def @controller.build_advanced_search_config
      { text_fields: ['email'], date_fields: {}, filters: [] }
    end

    scope = @controller.test_apply_advanced_search(User.all)

    # Should return all users when no filters applied
    assert_includes scope, @admin_user
    assert_includes scope, @scout_user
    assert_includes scope, @regular_user
  end

  test 'should handle invalid date formats gracefully' do
    @controller.params = {
      'created_from' => 'invalid-date',
      'created_to' => 'also-invalid',
    }

    config = { created: 'created_at' }

    # Should not raise an error and return original scope
    scope = @controller.test_apply_date_filters(User.all, config)

    assert_includes scope, @admin_user
    assert_includes scope, @scout_user
    assert_includes scope, @regular_user
  end
end
