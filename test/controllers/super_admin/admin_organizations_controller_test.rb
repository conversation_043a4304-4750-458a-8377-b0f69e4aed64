require 'test_helper'

class SuperAdmin::AdminOrganizationsControllerTest < ActionDispatch::IntegrationTest
  setup do
    # Create roles
    @superadmin_role = Role.find_or_create_by!(name: 'superadmin')
    @support_role = Role.find_or_create_by!(name: 'support')
    @readonly_role = Role.find_or_create_by!(name: 'readonly')
    @scout_role = Role.find_or_create_by!(name: 'scout')

    # Create admin users
    @superadmin = User.create!(
      email: '<EMAIL>',
      password: 'password123123',
      verified: true,
      first_name: 'Super',
      last_name: 'Admin',
      onboarding_completed: true,
      onboarding_step: 'completed'
    )
    UserRole.create!(user: @superadmin, role: @superadmin_role)

    @support_admin = User.create!(
      email: '<EMAIL>',
      password: 'password123123',
      verified: true,
      first_name: 'Support',
      last_name: 'Admin',
      onboarding_completed: true,
      onboarding_step: 'completed'
    )
    UserRole.create!(user: @support_admin, role: @support_role)

    @readonly_admin = User.create!(
      email: '<EMAIL>',
      password: 'password123123',
      verified: true,
      first_name: 'ReadOnly',
      last_name: 'Admin',
      onboarding_completed: true,
      onboarding_step: 'completed'
    )
    UserRole.create!(user: @readonly_admin, role: @readonly_role)

    @regular_user = User.create!(
      email: '<EMAIL>',
      password: 'password123123',
      verified: true,
      first_name: 'Regular',
      last_name: 'User',
      onboarding_completed: true,
      onboarding_step: 'completed'
    )
    UserRole.create!(user: @regular_user, role: @scout_role)

    # Create test organization
    @organization = Organization.create!(
      name: 'Test Organization',
      description: 'A test organization for admin testing'
    )

    # Create organization memberships
    [@superadmin, @support_admin, @readonly_admin, @regular_user].each do |user|
      OrganizationMembership.create!(
        user: user,
        organization: @organization,
        org_role: 'member'
      )
      user.update!(last_logged_in_organization_id: @organization.id)
    end
  end

  test 'superadmin can access organizations index' do
    sign_in_as(@superadmin)
    get super_admin_admin_organizations_path
    assert_response :success
    assert_select 'h1', text: 'Organizations'
  end

  test 'support admin can access organizations index' do
    sign_in_as(@support_admin)
    get super_admin_admin_organizations_path
    assert_response :success
    assert_select 'h1', text: 'Organizations'
  end

  test 'readonly admin can access organizations index' do
    sign_in_as(@readonly_admin)
    get super_admin_admin_organizations_path
    assert_response :success
    assert_select 'h1', text: 'Organizations'
  end

  test 'regular user cannot access organizations index' do
    sign_in_as(@regular_user)
    get super_admin_admin_organizations_path
    assert_redirected_to root_path
  end

  test 'unauthenticated user cannot access organizations index' do
    get super_admin_admin_organizations_path
    assert_redirected_to sign_in_path
  end

  test 'superadmin can view organization details' do
    sign_in_as(@superadmin)
    get super_admin_admin_organization_path(@organization)
    assert_response :success
    assert_select 'h1', text: @organization.name
  end

  test 'superadmin can edit organization' do
    sign_in_as(@superadmin)
    get edit_super_admin_admin_organization_path(@organization)
    assert_response :success
    assert_select 'form'
  end

  test 'support admin cannot edit organization' do
    sign_in_as(@support_admin)
    get edit_super_admin_admin_organization_path(@organization)
    assert_response :forbidden
  end

  test 'readonly admin cannot edit organization' do
    sign_in_as(@readonly_admin)
    get edit_super_admin_admin_organization_path(@organization)
    assert_response :forbidden
  end

  test 'organizations index displays search and filter options' do
    sign_in_as(@superadmin)
    get super_admin_admin_organizations_path
    assert_response :success
    
    assert_select 'input[name="search"]'
    assert_select 'button', text: 'Search'
    assert_select 'button', text: 'Clear'
  end

  test 'organizations index search functionality' do
    sign_in_as(@superadmin)
    get super_admin_admin_organizations_path, params: { search: 'Test Organization' }
    assert_response :success
    assert_select 'td', text: @organization.name
  end

  test 'organizations index displays empty state when no organizations' do
    Organization.destroy_all
    sign_in_as(@superadmin)
    get super_admin_admin_organizations_path
    assert_response :success
    assert_select '.empty-state', text: /No organizations found/
  end

  test 'organizations index pagination works' do
    # Create more organizations to test pagination
    25.times do |i|
      Organization.create!(
        name: "Organization #{i}",
        description: "Description #{i}"
      )
    end

    sign_in_as(@superadmin)
    get super_admin_admin_organizations_path
    assert_response :success
    assert_select '.pagination'
  end

  test 'superadmin can update organization' do
    sign_in_as(@superadmin)
    patch super_admin_admin_organization_path(@organization), params: {
      organization: { name: 'Updated Organization Name' }
    }
    assert_redirected_to super_admin_admin_organization_path(@organization)
    @organization.reload
    assert_equal 'Updated Organization Name', @organization.name
  end

  test 'support admin cannot update organization' do
    sign_in_as(@support_admin)
    patch super_admin_admin_organization_path(@organization), params: {
      organization: { name: 'Should not update' }
    }
    assert_response :forbidden
  end

  test 'readonly admin cannot update organization' do
    sign_in_as(@readonly_admin)
    patch super_admin_admin_organization_path(@organization), params: {
      organization: { name: 'Should not update' }
    }
    assert_response :forbidden
  end

  test 'export functionality for superadmin' do
    sign_in_as(@superadmin)
    get super_admin_admin_organizations_path, params: { format: 'csv' }
    assert_response :success
    assert_equal 'text/csv', response.content_type
  end

  test 'export functionality for support admin' do
    sign_in_as(@support_admin)
    get super_admin_admin_organizations_path, params: { format: 'csv' }
    assert_response :success
    assert_equal 'text/csv', response.content_type
  end

  test 'export functionality for readonly admin' do
    sign_in_as(@readonly_admin)
    get super_admin_admin_organizations_path, params: { format: 'csv' }
    assert_response :success
    assert_equal 'text/csv', response.content_type
  end

  test 'organization show page displays all relevant information' do
    sign_in_as(@superadmin)
    get super_admin_admin_organization_path(@organization)
    assert_response :success
    
    assert_select 'dt', text: 'Name'
    assert_select 'dd', text: @organization.name
    assert_select 'dt', text: 'Description'
    assert_select 'dd', text: @organization.description
    assert_select 'dt', text: 'Created At'
    assert_select 'dt', text: 'Updated At'
  end

  test 'organization show page displays member count' do
    sign_in_as(@superadmin)
    get super_admin_admin_organization_path(@organization)
    assert_response :success
    
    assert_select 'dt', text: 'Members Count'
    assert_select 'dd', text: @organization.organization_memberships.count.to_s
  end

  test 'organization show page displays recent members' do
    sign_in_as(@superadmin)
    get super_admin_admin_organization_path(@organization)
    assert_response :success
    
    assert_select 'h2', text: 'Recent Members'
    assert_select 'a', text: @regular_user.full_name
  end

  test 'organizations index shows member counts' do
    sign_in_as(@superadmin)
    get super_admin_admin_organizations_path
    assert_response :success
    
    assert_select 'th', text: 'Members'
    assert_select 'td', text: @organization.organization_memberships.count.to_s
  end

  test 'organizations index shows creation dates' do
    sign_in_as(@superadmin)
    get super_admin_admin_organizations_path
    assert_response :success
    
    assert_select 'th', text: 'Created'
  end

  test 'date range filtering works' do
    sign_in_as(@superadmin)
    get super_admin_admin_organizations_path, params: {
      created_from: 1.day.ago.strftime('%Y-%m-%d'),
      created_to: 1.day.from_now.strftime('%Y-%m-%d')
    }
    assert_response :success
    assert_select 'td', text: @organization.name
  end

  test 'organization show page has link to members admin' do
    sign_in_as(@superadmin)
    get super_admin_admin_organization_path(@organization)
    assert_response :success
    
    assert_select 'a[href*="admin_organization_memberships"]', text: /View All Members/
  end

  test 'organization show page has link to jobs admin' do
    sign_in_as(@superadmin)
    get super_admin_admin_organization_path(@organization)
    assert_response :success
    
    assert_select 'a[href*="admin_jobs"]', text: /View Organization Jobs/
  end

  test 'superadmin can create new organization' do
    sign_in_as(@superadmin)
    get new_super_admin_admin_organization_path
    assert_response :success
    assert_select 'form'
  end

  test 'support admin cannot create new organization' do
    sign_in_as(@support_admin)
    get new_super_admin_admin_organization_path
    assert_response :forbidden
  end

  test 'superadmin can delete organization' do
    sign_in_as(@superadmin)
    assert_difference 'Organization.count', -1 do
      delete super_admin_admin_organization_path(@organization)
    end
    assert_redirected_to super_admin_admin_organizations_path
  end

  test 'support admin cannot delete organization' do
    sign_in_as(@support_admin)
    assert_no_difference 'Organization.count' do
      delete super_admin_admin_organization_path(@organization)
    end
    assert_response :forbidden
  end

  private

  def sign_in_as(user)
    post sign_in_path, params: { email: user.email, password: 'password123123' }
    follow_redirect!
  end
end
