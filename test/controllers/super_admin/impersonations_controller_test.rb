require 'test_helper'
require 'minitest/mock'

class SuperAdmin::ImpersonationsControllerTest < ActionDispatch::IntegrationTest
  def setup
    @admin = users(:admin)
    @scout = users(:scout)
    @super_admin = users(:super_admin)

    # Sign in as admin
    sign_in_as(@admin)

    # Set Current attributes for impersonation
    Current.user_agent = 'Test Browser'
    Current.ip_address = '127.0.0.1'
  end

  test 'should create impersonation successfully' do
    assert_difference 'ImpersonationLog.count', 1 do
      post super_admin_masquerades_path,
           params: {
             user_id: @scout.id,
           },
           headers: {
             'HTTP_USER_AGENT' => 'Test Browser',
           }
    end

    assert_redirected_to launchpad_path
    assert_equal "Successfully started impersonating #{@scout.name}. You can exit impersonation using the banner above.",
                 flash[:notice]

    # Check that impersonation state is set
    log = ImpersonationLog.last
    assert_equal @admin, log.admin
    assert_equal @scout, log.user
    assert_not_nil log.started_at
    assert_nil log.ended_at
  end

  test 'should prevent self impersonation' do
    assert_no_difference 'ImpersonationLog.count' do
      post super_admin_masquerades_path,
           params: {
             user_id: @admin.id,
           },
           headers: {
             'HTTP_USER_AGENT' => 'Test Browser',
           }
    end

    assert_redirected_to super_admin_users_path
    assert_equal 'Cannot impersonate yourself', flash[:alert]
  end

  test 'should prevent impersonating super admin' do
    assert_no_difference 'ImpersonationLog.count' do
      post super_admin_masquerades_path,
           params: {
             user_id: @super_admin.id,
           },
           headers: {
             'HTTP_USER_AGENT' => 'Test Browser',
           }
    end

    assert_redirected_to super_admin_users_path
    assert_equal 'Cannot impersonate another super admin', flash[:alert]
  end

  test 'should prevent impersonating non-existent user' do
    assert_no_difference 'ImpersonationLog.count' do
      post super_admin_masquerades_path,
           params: {
             user_id: 999_999,
           },
           headers: {
             'HTTP_USER_AGENT' => 'Test Browser',
           }
    end

    assert_redirected_to super_admin_users_path
    assert_match /User not found/, flash[:alert]
  end

  test 'should end impersonation successfully' do
    # Start impersonation first using Pretender
    post super_admin_masquerades_path,
         params: {
           user_id: @scout.id,
         },
         headers: {
           'HTTP_USER_AGENT' => 'Test Browser',
         }

    # Verify impersonation started
    assert_redirected_to launchpad_path

    # Now end the impersonation (don't follow redirect to maintain session)
    delete super_admin_masquerades_path

    assert_redirected_to super_admin_users_path
    assert_equal 'Successfully ended impersonation', flash[:notice]

    # Check that log is ended
    log = ImpersonationLog.last
    assert_not_nil log.ended_at
  end

  test 'should handle ending impersonation when not impersonating' do
    delete super_admin_masquerades_path

    assert_redirected_to super_admin_root_path
    assert_equal 'Not currently impersonating anyone', flash[:alert]
  end

  test 'should require super admin access for create' do
    sign_in_as(@scout) # Regular user

    post super_admin_masquerades_path,
         params: {
           user_id: @scout.id,
         },
         headers: {
           'HTTP_USER_AGENT' => 'Test Browser',
         }

    assert_redirected_to root_path
    assert_equal 'Access denied. Super admin privileges required.',
                 flash[:alert]
  end

  test 'should require super admin access for destroy' do
    sign_in_as(@scout) # Regular user

    delete super_admin_masquerades_path

    assert_redirected_to root_path
    assert_equal 'Access denied. Super admin privileges required.',
                 flash[:alert]
  end

  test 'should log impersonation details' do
    post super_admin_masquerades_path,
         params: {
           user_id: @scout.id,
         },
         headers: {
           'HTTP_USER_AGENT' => 'Test Browser',
         }

    log = ImpersonationLog.last
    assert_equal @admin, log.admin
    assert_equal @scout, log.user
    assert_not_nil log.ip_address
    assert_not_nil log.user_agent
    assert_not_nil log.started_at
    assert_nil log.ended_at
  end

  test 'should redirect to appropriate landing page for scout' do
    scout = users(:scout)
    scout.update!(onboarding_completed: true)

    post super_admin_masquerades_path,
         params: {
           user_id: scout.id,
         },
         headers: {
           'HTTP_USER_AGENT' => 'Test Browser',
         }

    assert_redirected_to launchpad_path
  end

  test 'should redirect to onboarding for incomplete user' do
    incomplete_user = users(:scout)
    incomplete_user.update!(
      onboarding_completed: false,
      onboarding_step: 'personal',
    )

    post super_admin_masquerades_path,
         params: {
           user_id: incomplete_user.id,
         },
         headers: {
           'HTTP_USER_AGENT' => 'Test Browser',
         }

    assert_redirected_to onboarding_personal_path
  end

  test 'should handle errors gracefully' do
    # Mock an error in the create process
    mock = Minitest::Mock.new
    mock.expect :call, nil do |args|
      raise StandardError.new('Test error')
    end

    ImpersonationLog.stub :create!, mock do
      post super_admin_masquerades_path,
           params: {
             user_id: @scout.id,
           },
           headers: {
             'HTTP_USER_AGENT' => 'Test Browser',
           }

      assert_redirected_to super_admin_users_path
      assert_match /Failed to impersonate user/, flash[:alert]
    end
  end
end
