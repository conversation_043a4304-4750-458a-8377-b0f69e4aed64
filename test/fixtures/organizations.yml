# Read about fixtures at https://api.rubyonrails.org/classes/ActiveRecord/FixtureSet.html

# == Schema Information
#
# Table name: organizations
#
#  id                 :bigint           not null, primary key
#  jobs_count         :integer          default(0), not null
#  name               :string           not null
#  operating_timezone :string
#  size               :string
#  created_at         :datetime         not null
#  updated_at         :datetime         not null
#
# Indexes
#
#  index_organizations_on_created_at  (created_at)
#  index_organizations_on_name        (name)
#

one:
  id: <%= ActiveRecord::FixtureSet.identify(:one) %>
  name: Test Organization One

two:
  id: <%= ActiveRecord::FixtureSet.identify(:two) %>
  name: Test Organization Two
