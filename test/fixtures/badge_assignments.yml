# Badge Assignment Fixtures
# These fixtures represent badge assignments for testing purposes

# Active permanent badge assignment
# == Schema Information
#
# Table name: badge_assignments
#
#  id            :bigint           not null, primary key
#  assigned_at   :datetime         not null
#  expires_at    :datetime
#  notes         :text
#  created_at    :datetime         not null
#  updated_at    :datetime         not null
#  admin_id      :bigint           not null
#  badge_type_id :bigint           not null
#  user_id       :bigint           not null
#
# Indexes
#
#  index_badge_assignments_active_by_type                    (badge_type_id) WHERE (expires_at IS NULL)
#  index_badge_assignments_active_only                       (user_id,badge_type_id,assigned_at) WHERE (expires_at IS NULL)
#  index_badge_assignments_on_admin_and_date                 (admin_id,assigned_at)
#  index_badge_assignments_on_admin_id                       (admin_id)
#  index_badge_assignments_on_assigned_at                    (assigned_at)
#  index_badge_assignments_on_assigning_admin                (admin_id)
#  index_badge_assignments_on_badge_type_id                  (badge_type_id)
#  index_badge_assignments_on_badge_type_id_and_assigned_at  (badge_type_id,assigned_at)
#  index_badge_assignments_on_badge_type_id_and_user_id      (badge_type_id,user_id) UNIQUE
#  index_badge_assignments_on_expires_at                     (expires_at)
#  index_badge_assignments_on_type_date_expiry               (badge_type_id,assigned_at,expires_at)
#  index_badge_assignments_on_user_active_priority           (user_id,assigned_at,expires_at)
#  index_badge_assignments_on_user_id                        (user_id)
#  index_badge_assignments_on_user_id_and_expires_at         (user_id,expires_at)
#
# Foreign Keys
#
#  fk_rails_...  (admin_id => users.id)
#  fk_rails_...  (badge_type_id => badge_types.id)
#  fk_rails_...  (user_id => users.id)
#
verified_talent_badge:
  badge_type: verified
  user: talent
  admin: super_admin
  assigned_at: <%= 1.week.ago %>
  expires_at:
  notes: "Verified talent profile with excellent portfolio"

# Active temporary badge assignment
premium_scout_badge:
  badge_type: premium
  user: scout
  admin: super_admin
  assigned_at: <%= 3.days.ago %>
  expires_at: <%= 1.month.from_now %>
  notes: "Premium subscription active"

# Expired badge assignment
expired_rising_star:
  badge_type: rising_star
  user: talent
  admin: admin
  assigned_at: <%= 2.months.ago %>
  expires_at: <%= 1.week.ago %>
  notes: "Rising star period has ended"

# Recent badge assignment by admin
expert_badge_assignment:
  badge_type: expert
  user: talent
  admin: admin
  assigned_at: <%= 2.days.ago %>
  expires_at:
  notes: "Demonstrated expertise in technical writing"

# Badge assignment for different user
choice_badge_scout:
  badge_type: ghostwrote_choice
  user: scout
  admin: super_admin
  assigned_at: <%= 5.days.ago %>
  expires_at:
  notes: "Consistently high-quality job postings"

# Temporary badge expiring soon
expiring_soon_badge:
  badge_type: premium
  user: talent
  admin: super_admin
  assigned_at: <%= 1.month.ago %>
  expires_at: <%= 2.days.from_now %>
  notes: "Premium trial period ending soon"
