# Read about fixtures at https://api.rubyonrails.org/classes/ActiveRecord/FixtureSet.html

# == Schema Information
#
# Table name: impersonation_logs
#
#  id         :bigint           not null, primary key
#  ended_at   :datetime
#  ip_address :string
#  started_at :datetime         not null
#  user_agent :string
#  created_at :datetime         not null
#  updated_at :datetime         not null
#  admin_id   :bigint           not null
#  user_id    :bigint           not null
#
# Indexes
#
#  index_impersonation_logs_on_admin_id  (admin_id)
#  index_impersonation_logs_on_user_id   (user_id)
#
# Foreign Keys
#
#  fk_rails_...  (admin_id => users.id)
#  fk_rails_...  (user_id => users.id)
#

active_session:
  admin: admin
  user: scout
  started_at: <%= 1.hour.ago %>
  ended_at: 
  ip_address: "*************"
  user_agent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36"

ended_session:
  admin: admin
  user: talent
  started_at: <%= 3.hours.ago %>
  ended_at: <%= 2.hours.ago %>
  ip_address: "*************"
  user_agent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"

expired_session:
  admin: admin
  user: two
  started_at: <%= 4.hours.ago %>
  ended_at: 
  ip_address: "*************"
  user_agent: "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36"
