# Read about fixtures at https://api.rubyonrails.org/classes/ActiveRecord/FixtureSet.html

# == Schema Information
#
# Table name: security_alerts
#
#  id             :bigint           not null, primary key
#  alert_type     :string           not null
#  description    :text             not null
#  metadata       :json
#  resolved_at    :datetime
#  severity       :string           not null
#  created_at     :datetime         not null
#  updated_at     :datetime         not null
#  resolved_by_id :bigint
#  user_id        :bigint           not null
#
# Indexes
#
#  index_security_alerts_on_alert_type              (alert_type)
#  index_security_alerts_on_created_at              (created_at)
#  index_security_alerts_on_resolved_at             (resolved_at)
#  index_security_alerts_on_resolved_by_id          (resolved_by_id)
#  index_security_alerts_on_severity                (severity)
#  index_security_alerts_on_user_id                 (user_id)
#  index_security_alerts_on_user_id_and_created_at  (user_id,created_at)
#
# Foreign Keys
#
#  fk_rails_...  (resolved_by_id => users.id)
#  fk_rails_...  (user_id => users.id)
#
one:
  user: one
  alert_type: MyString
  severity: MyString
  description: MyText
  metadata: 
  resolved_at: 2025-06-29 11:37:49
  resolved_by: one

two:
  user: two
  alert_type: MyString
  severity: MyString
  description: MyText
  metadata: 
  resolved_at: 2025-06-29 11:37:49
  resolved_by: two
