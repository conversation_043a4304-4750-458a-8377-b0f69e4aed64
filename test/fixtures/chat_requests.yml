# Read about fixtures at https://api.rubyonrails.org/classes/ActiveRecord/FixtureSet.html

# == Schema Information
#
# Table name: chat_requests
#
#  id           :bigint           not null, primary key
#  accepted_at  :datetime
#  declined_at  :datetime
#  pitch        :text
#  requested_at :datetime
#  status       :integer          default("pending"), not null
#  created_at   :datetime         not null
#  updated_at   :datetime         not null
#  scout_id     :bigint           not null
#  talent_id    :bigint           not null
#
# Indexes
#
#  index_chat_requests_on_created_at              (created_at)
#  index_chat_requests_on_scout_id                (scout_id)
#  index_chat_requests_on_scout_id_and_status     (scout_id,status)
#  index_chat_requests_on_scout_id_and_talent_id  (scout_id,talent_id) UNIQUE
#  index_chat_requests_on_status                  (status)
#  index_chat_requests_on_talent_id               (talent_id)
#  index_chat_requests_on_talent_id_and_status    (talent_id,status)
#
# Foreign Keys
#
#  fk_rails_...  (scout_id => users.id)
#  fk_rails_...  (talent_id => users.id)
#

# Empty fixtures - tests create their own data
