# Read about fixtures at https://api.rubyonrails.org/classes/ActiveRecord/FixtureSet.html

# == Schema Information
#
# Table name: roles
#
#  id         :bigint           not null, primary key
#  name       :string
#  created_at :datetime         not null
#  updated_at :datetime         not null
#
# Indexes
#
#  index_roles_on_created_at  (created_at)
#  index_roles_on_name        (name) UNIQUE
#

superadmin:
  id: <%= ActiveRecord::FixtureSet.identify(:superadmin) %>
  name: "superadmin"

admin:
  id: <%= ActiveRecord::FixtureSet.identify(:admin) %>
  name: "admin"

scout:
  id: <%= ActiveRecord::FixtureSet.identify(:scout) %>
  name: "scout"

talent:
  id: <%= ActiveRecord::FixtureSet.identify(:talent) %>
  name: "talent"
