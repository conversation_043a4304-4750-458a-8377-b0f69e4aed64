# Read about fixtures at https://api.rubyonrails.org/classes/ActiveRecord/FixtureSet.html

# == Schema Information
#
# Table name: sessions
#
#  id                      :bigint           not null, primary key
#  ip_address              :string
#  last_activity_at        :datetime
#  locked_until            :datetime
#  login_attempts          :integer          default(0), not null
#  security_warnings_count :integer          default(0), not null
#  user_agent              :string
#  created_at              :datetime         not null
#  updated_at              :datetime         not null
#  user_id                 :bigint           not null
#
# Indexes
#
#  index_sessions_on_last_activity_at  (last_activity_at)
#  index_sessions_on_locked_until      (locked_until)
#  index_sessions_on_user_id           (user_id)
#
# Foreign Keys
#
#  fk_rails_...  (user_id => users.id)
#

one:
  user: one
  user_agent: "Mozilla/5.0 Test Browser"
  ip_address: "127.0.0.1"
  last_activity_at: <%= 1.hour.ago %>
  security_warnings_count: 0
  login_attempts: 0

two:
  user: two
  user_agent: "Mozilla/5.0 Test Browser 2"
  ip_address: "*********"
  last_activity_at: <%= 2.hours.ago %>
  security_warnings_count: 0
  login_attempts: 0
