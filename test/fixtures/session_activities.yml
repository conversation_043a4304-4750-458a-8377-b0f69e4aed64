# Read about fixtures at https://api.rubyonrails.org/classes/ActiveRecord/FixtureSet.html

# == Schema Information
#
# Table name: session_activities
#
#  id            :bigint           not null, primary key
#  action        :string
#  activity_type :string           not null
#  controller    :string
#  ip_address    :string
#  metadata      :json
#  request_path  :string
#  user_agent    :string
#  created_at    :datetime         not null
#  updated_at    :datetime         not null
#  session_id    :bigint           not null
#
# Indexes
#
#  index_session_activities_on_activity_type              (activity_type)
#  index_session_activities_on_created_at                 (created_at)
#  index_session_activities_on_ip_address                 (ip_address)
#  index_session_activities_on_session_id                 (session_id)
#  index_session_activities_on_session_id_and_created_at  (session_id,created_at)
#
# Foreign Keys
#
#  fk_rails_...  (session_id => sessions.id)
#
one:
  session: one
  activity_type: MyString
  controller: MyString
  action: MyString
  ip_address: MyString
  user_agent: MyString
  request_path: MyString

two:
  session: two
  activity_type: MyString
  controller: MyString
  action: MyString
  ip_address: MyString
  user_agent: MyString
  request_path: MyString
