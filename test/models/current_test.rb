require 'test_helper'

class CurrentTest < ActiveSupport::TestCase
  def setup
    @admin = users(:admin)
    @user = users(:scout)
    Current.reset
  end

  def teardown
    Current.reset
  end

  test 'should detect when not impersonating' do
    Current.user = @admin
    Current.impersonator_id = nil
    Current.impersonation_log_id = nil

    assert_not Current.impersonating?
    assert_nil Current.impersonator
    assert_nil Current.impersonation_log
  end

  test 'should detect when impersonating' do
    log =
      ImpersonationLog.create!(
        admin: @admin,
        user: @user,
        started_at: Time.current,
        ip_address: '***********',
        user_agent: 'Test Browser',
      )

    Current.user = @user
    Current.impersonator_id = @admin.id
    Current.impersonation_log_id = log.id

    assert Current.impersonating?
    assert_equal @admin, Current.impersonator
    assert_equal log, Current.impersonation_log
  end

  test 'should detect expired impersonation' do
    # Create an old impersonation log (25 hours ago - past the 24 hour limit)
    log =
      ImpersonationLog.create!(
        admin: @admin,
        user: @user,
        started_at: 25.hours.ago,
        ip_address: '***********',
        user_agent: 'Test Browser',
      )

    Current.user = @user
    Current.impersonator_id = @admin.id
    Current.impersonation_log_id = log.id

    assert Current.impersonating?
    assert Current.impersonation_expired?
  end

  test 'should not detect expired impersonation for recent session' do
    # Create a recent impersonation log (30 minutes ago)
    log =
      ImpersonationLog.create!(
        admin: @admin,
        user: @user,
        started_at: 30.minutes.ago,
        ip_address: '***********',
        user_agent: 'Test Browser',
      )

    Current.user = @user
    Current.impersonator_id = @admin.id
    Current.impersonation_log_id = log.id

    assert Current.impersonating?
    assert_not Current.impersonation_expired?
  end

  test 'should not detect expired impersonation when not impersonating' do
    Current.user = @admin
    Current.impersonator_id = nil
    Current.impersonation_log_id = nil

    assert_not Current.impersonating?
    assert_not Current.impersonation_expired?
  end

  test 'should handle missing impersonation log gracefully' do
    Current.user = @user
    Current.impersonator_id = @admin.id
    Current.impersonation_log_id = 999_999 # Non-existent ID

    assert Current.impersonating?
    assert_not Current.impersonation_expired? # Should return false when log is missing
  end

  test 'should return nil for impersonator when not impersonating' do
    Current.user = @admin
    Current.impersonator_id = nil

    assert_nil Current.impersonator
  end

  test 'should return nil for impersonation_log when not impersonating' do
    Current.user = @admin
    Current.impersonation_log_id = nil

    assert_nil Current.impersonation_log
  end

  test 'should handle invalid impersonator_id gracefully' do
    Current.user = @user
    Current.impersonator_id = 999_999 # Non-existent ID

    assert_nil Current.impersonator
  end

  test 'should handle invalid impersonation_log_id gracefully' do
    Current.user = @user
    Current.impersonation_log_id = 999_999 # Non-existent ID

    assert_nil Current.impersonation_log
  end
end
