# == Schema Information
#
# Table name: chat_requests
#
#  id           :bigint           not null, primary key
#  accepted_at  :datetime
#  declined_at  :datetime
#  pitch        :text
#  requested_at :datetime
#  status       :integer          default("pending"), not null
#  created_at   :datetime         not null
#  updated_at   :datetime         not null
#  scout_id     :bigint           not null
#  talent_id    :bigint           not null
#
# Indexes
#
#  index_chat_requests_on_created_at              (created_at)
#  index_chat_requests_on_scout_id                (scout_id)
#  index_chat_requests_on_scout_id_and_status     (scout_id,status)
#  index_chat_requests_on_scout_id_and_talent_id  (scout_id,talent_id) UNIQUE
#  index_chat_requests_on_status                  (status)
#  index_chat_requests_on_talent_id               (talent_id)
#  index_chat_requests_on_talent_id_and_status    (talent_id,status)
#
# Foreign Keys
#
#  fk_rails_...  (scout_id => users.id)
#  fk_rails_...  (talent_id => users.id)
#
require 'test_helper'

class ChatRequestTest < ActiveSupport::TestCase
  # Disable fixtures for this test to avoid foreign key issues
  self.use_transactional_tests = false
  def setup
    # Clean up any existing data
    ImpersonationLog.destroy_all # Ensure impersonation logs are cleared first
    ChatRequest.destroy_all
    User.destroy_all # Use destroy_all to trigger callbacks

    @scout =
      User.create!(
        email: '<EMAIL>',
        password: 'password123456',
        first_name: 'Scout',
        last_name: 'Test',
        scout_signup_completed: true,
      )

    @talent =
      User.create!(
        email: '<EMAIL>',
        password: 'password123456',
        first_name: 'Talent',
        last_name: 'Test',
        talent_signup_completed: true,
      )
  end

  def teardown
    # Clean up after each test
    ChatRequest.destroy_all
    ImpersonationLog.destroy_all # Ensure impersonation logs are cleared first
    User.destroy_all # Use destroy_all to trigger callbacks
  end

  test 'should create chat request' do
    chat_request = @scout.sent_chat_requests.create!(talent: @talent)

    assert chat_request.persisted?
    assert_equal 'pending', chat_request.status
    assert_not_nil chat_request.requested_at
    assert_nil chat_request.accepted_at
    assert_nil chat_request.declined_at
  end

  test 'should prevent duplicate pending requests' do
    @scout.sent_chat_requests.create!(talent: @talent)

    assert_raises(ActiveRecord::RecordInvalid) do
      @scout.sent_chat_requests.create!(talent: @talent)
    end
  end

  test 'should accept chat request' do
    chat_request = @scout.sent_chat_requests.create!(talent: @talent)

    chat_request.accept!

    assert_equal 'accepted', chat_request.status
    assert_not_nil chat_request.accepted_at
  end

  test 'should decline chat request' do
    chat_request = @scout.sent_chat_requests.create!(talent: @talent)

    chat_request.decline!

    assert_equal 'declined', chat_request.status
    assert_not_nil chat_request.declined_at
  end

  test 'scout should have pending request helper method' do
    assert_not @scout.has_pending_chat_request_with?(@talent)

    @scout.sent_chat_requests.create!(talent: @talent)

    assert @scout.has_pending_chat_request_with?(@talent)
  end

  test 'talent should receive chat requests' do
    chat_request = @scout.sent_chat_requests.create!(talent: @talent)

    assert_includes @talent.received_chat_requests, chat_request
    assert_equal 1, @talent.received_chat_requests.pending.count
  end

  test 'should create chat request with pitch' do
    pitch_message = "Hi! I'm interested in working with you on a project."
    chat_request =
      @scout.sent_chat_requests.create!(talent: @talent, pitch: pitch_message)

    assert chat_request.persisted?
    assert_equal pitch_message, chat_request.pitch
    assert_equal 'pending', chat_request.status
  end

  test 'should create chat request without pitch' do
    chat_request = @scout.sent_chat_requests.create!(talent: @talent)

    assert chat_request.persisted?
    assert_nil chat_request.pitch
    assert_equal 'pending', chat_request.status
  end
end
