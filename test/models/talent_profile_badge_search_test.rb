require 'test_helper'

class TalentProfileBadgeSearchTest < ActiveSupport::TestCase
  def setup_environment
    # Disable color contrast validation for tests
    ENV['VALIDATE_BADGE_COLOR_CONTRAST'] = 'false'
  end

  def teardown_environment
    # Re-enable color contrast validation after tests
    ENV['VALIDATE_BADGE_COLOR_CONTRAST'] = 'true'
  end

  def setup
    setup_environment

    # Create admin user for badge assignments
    @superadmin_role = Role.find_or_create_by!(name: 'superadmin')
    @admin_user =
      User.create!(
        email: "admin#{Time.current.to_i}@example.com",
        password: 'password123456',
        verified: true,
        first_name: 'Admin',
        last_name: 'User',
      )
    UserRole.create!(user: @admin_user, role: @superadmin_role)

    # Create test users
    @user_with_badges =
      User.create!(
        email: "badged_talent#{Time.current.to_i}@example.com",
        password: 'password123456',
        first_name: 'Badged',
        last_name: 'Talent',
        verified: true,
        talent_signup_completed: true,
      )

    @user_without_badges =
      User.create!(
        email: "unbadged_talent#{Time.current.to_i}@example.com",
        password: 'password123456',
        first_name: 'Unbadged',
        last_name: 'Talent',
        verified: true,
        talent_signup_completed: true,
      )

    # Create talent profiles
    @profile_with_badges =
      TalentProfile.create!(
        user: @user_with_badges,
        headline: 'Expert Ghostwriter with Badges',
        about: 'Experienced writer with multiple achievements',
        availability_status: 'available',
      )

    @profile_without_badges =
      TalentProfile.create!(
        user: @user_without_badges,
        headline: 'New Ghostwriter',
        about: 'Starting my ghostwriting journey',
        availability_status: 'available',
      )

    # Create badge types with unique names for this test
    @ghostwrote_choice_badge =
      BadgeType.create!(
        name: "Ghostwrote Choice #{Time.current.to_i}",
        description: 'Top-rated ghostwriter',
        icon: 'star',
        background_color: '#FFD700',
        text_color: '#000000',
        priority: 1,
      )

    @expert_badge =
      BadgeType.create!(
        name: "Expert Writer #{Time.current.to_i}",
        description: 'Proven expertise in writing',
        icon: 'certificate',
        background_color: '#4F46E5',
        text_color: '#FFFFFF',
        priority: 2,
      )

    # Assign badges to user
    BadgeAssignment.create!(
      user: @user_with_badges,
      badge_type: @ghostwrote_choice_badge,
      admin: @admin_user,
      assigned_at: 1.day.ago,
    )

    BadgeAssignment.create!(
      user: @user_with_badges,
      badge_type: @expert_badge,
      admin: @admin_user,
      assigned_at: 1.day.ago,
    )
  end

  def teardown
    # Clean up badge assignments to avoid foreign key constraint issues
    begin
      BadgeAssignment.delete_all
      BadgeType.delete_all
      TalentProfile.delete_all
      ImpersonationLog.delete_all  # Clear impersonation logs first
      User.delete_all
    rescue ActiveRecord::StatementInvalid
      # If we're in a failed transaction, rollback and try again
      ActiveRecord::Base.connection.rollback_transaction rescue nil
      BadgeAssignment.delete_all rescue nil
      BadgeType.delete_all rescue nil
      TalentProfile.delete_all rescue nil
      ImpersonationLog.delete_all rescue nil
      User.delete_all rescue nil
    end
    teardown_environment
  end

  test 'search_data includes badge_types for users with badges' do
    search_data = @profile_with_badges.search_data

    assert_includes search_data, :badge_types
    expected_badges = [@ghostwrote_choice_badge.name, @expert_badge.name].sort
    assert_equal expected_badges, search_data[:badge_types].sort
  end

  test 'search_data includes empty badge_types for users without badges' do
    search_data = @profile_without_badges.search_data

    assert_includes search_data, :badge_types
    assert_equal [], search_data[:badge_types]
  end

  test 'search_data excludes expired badges' do
    # Create an expired badge assignment
    expired_badge =
      BadgeType.create!(
        name: "Expired Badge #{Time.current.to_i}",
        description: 'This badge has expired',
        icon: 'clock',
        background_color: '#999999',
        text_color: '#FFFFFF',
        priority: 10,
      )

    expired_assignment = BadgeAssignment.new(
      user: @user_with_badges,
      badge_type: expired_badge,
      admin: @admin_user,
      assigned_at: 10.days.ago,
      expires_at: 1.day.ago, # Expired yesterday
    )
    expired_assignment.instance_variable_set(:@allow_expired_for_testing, true)
    expired_assignment.save!

    search_data = @profile_with_badges.search_data

    # Should not include expired badge
    expected_badges = [@ghostwrote_choice_badge.name, @expert_badge.name].sort
    assert_equal expected_badges, search_data[:badge_types].sort
    assert_not_includes search_data[:badge_types], expired_badge.name
  end

  test 'search_data handles user without active_badges gracefully' do
    # Create a profile with a user that has no badge associations
    user_no_associations =
      User.create!(
        email: '<EMAIL>',
        password: 'password123456',
        first_name: 'No',
        last_name: 'Associations',
        verified: true,
        talent_signup_completed: true,
      )

    profile_no_associations =
      TalentProfile.create!(
        user: user_no_associations,
        headline: 'Test Profile',
        about: 'Test about',
        availability_status: 'available',
      )

    search_data = profile_no_associations.search_data

    assert_includes search_data, :badge_types
    assert_equal [], search_data[:badge_types]
  end

  test 'badge_types is included in searchkick filterable fields' do
    # Check that badge_types is configured as a filterable field
    searchkick_options = TalentProfile.searchkick_options

    assert_includes searchkick_options[:filterable], :badge_types
  end
end
