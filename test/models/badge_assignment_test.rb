# == Schema Information
#
# Table name: badge_assignments
#
#  id            :bigint           not null, primary key
#  assigned_at   :datetime         not null
#  expires_at    :datetime
#  notes         :text
#  created_at    :datetime         not null
#  updated_at    :datetime         not null
#  admin_id      :bigint           not null
#  badge_type_id :bigint           not null
#  user_id       :bigint           not null
#
# Indexes
#
#  index_badge_assignments_active_by_type                    (badge_type_id) WHERE (expires_at IS NULL)
#  index_badge_assignments_active_only                       (user_id,badge_type_id,assigned_at) WHERE (expires_at IS NULL)
#  index_badge_assignments_on_admin_and_date                 (admin_id,assigned_at)
#  index_badge_assignments_on_admin_id                       (admin_id)
#  index_badge_assignments_on_assigned_at                    (assigned_at)
#  index_badge_assignments_on_assigning_admin                (admin_id)
#  index_badge_assignments_on_badge_type_id                  (badge_type_id)
#  index_badge_assignments_on_badge_type_id_and_assigned_at  (badge_type_id,assigned_at)
#  index_badge_assignments_on_badge_type_id_and_user_id      (badge_type_id,user_id) UNIQUE
#  index_badge_assignments_on_expires_at                     (expires_at)
#  index_badge_assignments_on_type_date_expiry               (badge_type_id,assigned_at,expires_at)
#  index_badge_assignments_on_user_active_priority           (user_id,assigned_at,expires_at)
#  index_badge_assignments_on_user_id                        (user_id)
#  index_badge_assignments_on_user_id_and_expires_at         (user_id,expires_at)
#
# Foreign Keys
#
#  fk_rails_...  (admin_id => users.id)
#  fk_rails_...  (badge_type_id => badge_types.id)
#  fk_rails_...  (user_id => users.id)
#
require 'test_helper'

class BadgeAssignmentTest < ActiveSupport::TestCase
  def setup
    # Clear existing data to avoid conflicts
    BadgeAssignment.delete_all
    BadgeType.delete_all

    # Create test badge type
    @badge_type =
      BadgeType.create!(
        name: 'Test Badge',
        description: 'A test badge for testing',
        background_color: '#000000',
        text_color: '#ffffff',
        icon: 'star',
        priority: 1,
        active: true,
      )

    @premium_badge_type =
      BadgeType.create!(
        name: 'Premium Badge',
        description: 'A premium test badge for testing purposes',
        background_color: '#000000',
        text_color: '#ffffff',
        icon: 'crown',
        priority: 2,
        active: true,
      )

    # Create test users
    @user = users(:talent)
    @admin = users(:super_admin)
    @regular_user = users(:scout)

    # Create a proper support admin user
    @support_admin =
      User.create!(
        email: '<EMAIL>',
        password: 'password123123',
        verified: true,
        first_name: 'Support',
        last_name: 'Admin',
        onboarding_completed: true,
        onboarding_step: 'completed',
      )

    # Create support role if it doesn't exist and assign it
    support_role = Role.find_or_create_by!(name: 'support')
    UserRole.create!(user: @support_admin, role: support_role)

    # Create test badge assignment
    @badge_assignment =
      BadgeAssignment.new(
        badge_type: @badge_type,
        user: @user,
        admin: @admin,
        assigned_at: Time.current,
        notes: 'Test assignment',
      )
  end

  # Basic validations
  test 'should be valid with valid attributes' do
    assert @badge_assignment.valid?
  end

  test 'should require badge_type' do
    @badge_assignment.badge_type = nil
    assert_not @badge_assignment.valid?
    assert_includes @badge_assignment.errors[:badge_type], 'must exist'
  end

  test 'should require user' do
    @badge_assignment.user = nil
    assert_not @badge_assignment.valid?
    assert_includes @badge_assignment.errors[:user], 'must exist'
  end

  test 'should require admin' do
    @badge_assignment.admin = nil
    assert_not @badge_assignment.valid?
    assert_includes @badge_assignment.errors[:admin], 'must exist'
  end

  test 'should require assigned_at' do
    # Create a new assignment and skip the callback
    assignment =
      BadgeAssignment.new(badge_type: @badge_type, user: @user, admin: @admin)

    # Temporarily disable the callback
    BadgeAssignment.skip_callback(:validation, :before, :set_assigned_at)

    begin
      assignment.assigned_at = nil
      assignment.valid?
      assert_includes assignment.errors[:assigned_at], "can't be blank"
    ensure
      # Re-enable the callback
      BadgeAssignment.set_callback(:validation, :before, :set_assigned_at)
    end
  end

  test 'should enforce uniqueness of badge_type per user' do
    @badge_assignment.save!

    duplicate_assignment =
      BadgeAssignment.new(
        badge_type: @badge_type,
        user: @user,
        admin: @admin,
        assigned_at: Time.current,
      )

    assert_not duplicate_assignment.valid?
    assert_includes duplicate_assignment.errors[:badge_type_id],
                    'has already been assigned to this user'
  end

  test 'should allow same badge_type for different users' do
    @badge_assignment.save!

    other_user = users(:scout)
    other_assignment =
      BadgeAssignment.new(
        badge_type: @badge_type,
        user: other_user,
        admin: @admin,
        assigned_at: Time.current,
      )

    assert other_assignment.valid?
  end

  test 'should validate notes length' do
    @badge_assignment.notes = 'a' * 1001
    assert_not @badge_assignment.valid?
    assert_includes @badge_assignment.errors[:notes],
                    'is too long (maximum is 1000 characters)'
  end

  # Admin permission validations
  test 'should require admin to have administrative privileges' do
    @badge_assignment.admin = @regular_user
    assert_not @badge_assignment.valid?
    assert_includes @badge_assignment.errors[:admin],
                    'must have administrative privileges to assign badges'
  end

  test 'should allow superadmin to assign badges' do
    @badge_assignment.admin = @admin
    assert @badge_assignment.valid?
  end

  test 'should allow support admin to assign badges' do
    @badge_assignment.admin = @support_admin
    assert @badge_assignment.valid?
  end

  # Date validations
  test 'should not allow expires_at in the past' do
    @badge_assignment.expires_at = 1.day.ago
    assert_not @badge_assignment.valid?
    assert_includes @badge_assignment.errors[:expires_at],
                    'must be in the future'
  end

  test 'should allow expires_at in the future' do
    @badge_assignment.expires_at = 1.day.from_now
    assert @badge_assignment.valid?
  end

  test 'should not allow assigned_at in the future' do
    @badge_assignment.assigned_at = 1.day.from_now
    assert_not @badge_assignment.valid?
    assert_includes @badge_assignment.errors[:assigned_at],
                    'cannot be in the future'
  end

  # Callbacks
  test 'should set assigned_at on create if not provided' do
    assignment =
      BadgeAssignment.new(badge_type: @badge_type, user: @user, admin: @admin)

    assert_nil assignment.assigned_at
    assignment.save!
    assert_not_nil assignment.assigned_at
    assert_in_delta Time.current, assignment.assigned_at, 1.second
  end

  test 'should not override assigned_at if provided' do
    specific_time = 1.hour.ago
    assignment =
      BadgeAssignment.new(
        badge_type: @badge_type,
        user: @user,
        admin: @admin,
        assigned_at: specific_time,
      )

    assignment.save!
    assert_equal specific_time.to_i, assignment.assigned_at.to_i
  end

  # Scopes
  test 'active scope should return only active assignments' do
    # Clear existing data
    BadgeAssignment.delete_all

    active_assignment =
      BadgeAssignment.create!(
        badge_type: @badge_type,
        user: @user,
        admin: @admin,
        assigned_at: Time.current,
      )

    expired_assignment =
      BadgeAssignment.new(
        badge_type: @badge_type,
        user: users(:scout),
        admin: @admin,
        assigned_at: 2.days.ago,
        expires_at: 1.day.ago,
      ).allow_expired_for_testing!
    expired_assignment.save!

    active_assignments = BadgeAssignment.active
    assert_includes active_assignments, active_assignment
    assert_not_includes active_assignments, expired_assignment
  end

  test 'expired scope should return only expired assignments' do
    # Clear existing data
    BadgeAssignment.delete_all

    active_assignment =
      BadgeAssignment.create!(
        badge_type: @badge_type,
        user: @user,
        admin: @admin,
        assigned_at: Time.current,
      )

    expired_assignment =
      BadgeAssignment.new(
        badge_type: @badge_type,
        user: users(:scout),
        admin: @admin,
        assigned_at: 2.days.ago,
        expires_at: 1.day.ago,
      ).allow_expired_for_testing!
    expired_assignment.save!

    expired_assignments = BadgeAssignment.expired
    assert_includes expired_assignments, expired_assignment
    assert_not_includes expired_assignments, active_assignment
  end

  test 'permanent scope should return assignments without expiration' do
    # Clear existing data
    BadgeAssignment.delete_all

    permanent_assignment =
      BadgeAssignment.create!(
        badge_type: @badge_type,
        user: @user,
        admin: @admin,
        assigned_at: Time.current,
      )

    temporary_assignment =
      BadgeAssignment.create!(
        badge_type: @badge_type,
        user: users(:scout),
        admin: @admin,
        assigned_at: Time.current,
        expires_at: 1.week.from_now,
      )

    permanent_assignments = BadgeAssignment.permanent
    assert_includes permanent_assignments, permanent_assignment
    assert_not_includes permanent_assignments, temporary_assignment
  end

  # Instance methods
  test 'active? should return true for non-expired assignments' do
    @badge_assignment.expires_at = nil
    assert @badge_assignment.active?

    @badge_assignment.expires_at = 1.day.from_now
    assert @badge_assignment.active?
  end

  test 'active? should return false for expired assignments' do
    @badge_assignment.expires_at = 1.day.ago
    assert_not @badge_assignment.active?
  end

  test 'permanent? should return true when expires_at is nil' do
    @badge_assignment.expires_at = nil
    assert @badge_assignment.permanent?
  end

  test 'permanent? should return false when expires_at is set' do
    @badge_assignment.expires_at = 1.week.from_now
    assert_not @badge_assignment.permanent?
  end

  test 'expires_in_days should return nil for permanent assignments' do
    @badge_assignment.expires_at = nil
    assert_nil @badge_assignment.expires_in_days
  end

  test 'expires_in_days should return 0 for expired assignments' do
    @badge_assignment.expires_at = 1.day.ago
    assert_equal 0, @badge_assignment.expires_in_days
  end

  test 'expires_in_days should return correct days for future expiration' do
    @badge_assignment.expires_at = 5.days.from_now
    assert_equal 5, @badge_assignment.expires_in_days
  end

  test 'days_since_assigned should return correct number of days' do
    @badge_assignment.assigned_at = 3.days.ago
    assert_equal 3, @badge_assignment.days_since_assigned
  end

  test 'assignment_summary should return descriptive text' do
    @badge_assignment.save!
    summary = @badge_assignment.assignment_summary

    assert_includes summary, @badge_type.name
    assert_includes summary, @user.full_name
    assert_includes summary, @admin.full_name
    assert_includes summary, 'permanent'
  end

  test 'assignment_summary should include expiration for temporary badges' do
    @badge_assignment.expires_at = 1.week.from_now
    @badge_assignment.save!
    summary = @badge_assignment.assignment_summary

    assert_includes summary, 'expires'
    assert_not_includes summary, 'permanent'
  end

  # Permission methods
  test 'can_be_removed_by? should allow superadmin to remove any badge' do
    @badge_assignment.save!
    assert @badge_assignment.can_be_removed_by?(@admin)
  end

  test 'can_be_removed_by? should allow support admin to remove their own assignments' do
    @badge_assignment.admin = @support_admin
    @badge_assignment.save!
    assert @badge_assignment.can_be_removed_by?(@support_admin)
  end

  test 'can_be_removed_by? should not allow support admin to remove superadmin assignments' do
    @badge_assignment.admin = @admin
    @badge_assignment.save!
    assert_not @badge_assignment.can_be_removed_by?(@support_admin)
  end

  test 'can_be_removed_by? should not allow regular users to remove badges' do
    @badge_assignment.save!
    assert_not @badge_assignment.can_be_removed_by?(@regular_user)
  end

  test 'can_be_removed_by? should return false for nil admin' do
    @badge_assignment.save!
    assert_not @badge_assignment.can_be_removed_by?(nil)
  end

  # Extension methods
  test 'extend_expiration should work for admin users' do
    @badge_assignment.save!
    new_expiration = 1.month.from_now

    result = @badge_assignment.extend_expiration(new_expiration, @admin)
    assert result
    assert_equal new_expiration.to_i, @badge_assignment.reload.expires_at.to_i
  end

  test 'extend_expiration should not work for non-admin users' do
    @badge_assignment.save!
    new_expiration = 1.month.from_now

    result = @badge_assignment.extend_expiration(new_expiration, @regular_user)
    assert_not result
  end

  test 'extend_expiration should not allow past dates' do
    @badge_assignment.save!
    past_date = 1.day.ago

    result = @badge_assignment.extend_expiration(past_date, @admin)
    assert_not result
  end

  # Class methods
  test 'active_for_user should return active assignments for specific user' do
    # Clear existing data
    BadgeAssignment.delete_all

    active_assignment =
      BadgeAssignment.create!(
        badge_type: @badge_type,
        user: @user,
        admin: @admin,
        assigned_at: Time.current,
      )

    expired_assignment =
      BadgeAssignment.new(
        badge_type: @premium_badge_type,
        user: @user,
        admin: @admin,
        assigned_at: 2.days.ago,
        expires_at: 1.day.ago,
      ).allow_expired_for_testing!
    expired_assignment.save!

    other_user_assignment =
      BadgeAssignment.create!(
        badge_type: @badge_type,
        user: users(:scout),
        admin: @admin,
        assigned_at: Time.current,
      )

    user_assignments = BadgeAssignment.active_for_user(@user)
    assert_includes user_assignments, active_assignment
    assert_not_includes user_assignments, expired_assignment
    assert_not_includes user_assignments, other_user_assignment
  end

  test 'cleanup_expired_assignments should remove expired assignments' do
    # Clear existing data
    BadgeAssignment.delete_all

    active_assignment =
      BadgeAssignment.create!(
        badge_type: @badge_type,
        user: @user,
        admin: @admin,
        assigned_at: Time.current,
      )

    expired_assignment =
      BadgeAssignment.new(
        badge_type: @badge_type,
        user: users(:scout),
        admin: @admin,
        assigned_at: 2.days.ago,
        expires_at: 1.day.ago,
      ).allow_expired_for_testing!
    expired_assignment.save!

    assert_difference 'BadgeAssignment.count', -1 do
      BadgeAssignment.cleanup_expired_assignments
    end

    assert BadgeAssignment.exists?(active_assignment.id)
    assert_not BadgeAssignment.exists?(expired_assignment.id)
  end

  test 'assignments_by_admin should return assignments by specific admin' do
    # Clear existing data
    BadgeAssignment.delete_all

    admin_assignment =
      BadgeAssignment.create!(
        badge_type: @badge_type,
        user: @user,
        admin: @admin,
        assigned_at: Time.current,
      )

    support_assignment =
      BadgeAssignment.create!(
        badge_type: @badge_type,
        user: users(:scout),
        admin: @support_admin,
        assigned_at: Time.current,
      )

    admin_assignments = BadgeAssignment.assignments_by_admin(@admin)
    assert_includes admin_assignments, admin_assignment
    assert_not_includes admin_assignments, support_assignment
  end
end
