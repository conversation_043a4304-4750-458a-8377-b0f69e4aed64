require 'test_helper'

class AdminPermissionsTest < ActiveSupport::TestCase
  setup do
    # Find or create roles
    @superadmin_role = Role.find_or_create_by!(name: 'superadmin')
    @support_role = Role.find_or_create_by!(name: 'support')
    @readonly_role = Role.find_or_create_by!(name: 'readonly')
    @scout_role = Role.find_or_create_by!(name: 'scout')

    # Create test users with different admin roles
    @superadmin =
      User.create!(
        email: '<EMAIL>',
        password: 'password123123',
        verified: true,
        first_name: '<PERSON>',
        last_name: 'Admin',
        onboarding_completed: true,
        onboarding_step: 'completed',
      )
    UserRole.create!(user: @superadmin, role: @superadmin_role)

    @support_admin =
      User.create!(
        email: '<EMAIL>',
        password: 'password123123',
        verified: true,
        first_name: 'Support',
        last_name: 'Admin',
        onboarding_completed: true,
        onboarding_step: 'completed',
      )
    UserRole.create!(user: @support_admin, role: @support_role)

    @readonly_admin =
      User.create!(
        email: '<EMAIL>',
        password: 'password123123',
        verified: true,
        first_name: 'ReadOnly',
        last_name: 'Admin',
        onboarding_completed: true,
        onboarding_step: 'completed',
      )
    UserRole.create!(user: @readonly_admin, role: @readonly_role)

    @regular_user =
      User.create!(
        email: '<EMAIL>',
        password: 'password123123',
        verified: true,
        first_name: 'Regular',
        last_name: 'User',
        onboarding_completed: true,
        onboarding_step: 'completed',
      )
    UserRole.create!(user: @regular_user, role: @scout_role)
  end

  test 'superadmin has all permissions' do
    assert_equal :superadmin, @superadmin.admin_role
    assert_equal 'Super Admin', @superadmin.admin_role_name
    assert @superadmin.can_access_admin?
    assert @superadmin.can?(:users_read)
    assert @superadmin.can?(:users_edit)
    assert @superadmin.can?(:users_create)
    assert @superadmin.can?(:users_delete)
    assert @superadmin.can?(:export_data)
    assert @superadmin.can_manage_roles?
    assert_equal :all, @superadmin.admin_permissions
  end

  test 'support admin has limited permissions' do
    assert_equal :support, @support_admin.admin_role
    assert_equal 'Support Agent', @support_admin.admin_role_name
    assert @support_admin.can_access_admin?
    assert @support_admin.can?(:users_read)
    assert @support_admin.can?(:users_edit)
    assert @support_admin.can?(:export_data)
    assert_not @support_admin.can?(:users_delete)
    assert_not @support_admin.can_manage_roles?

    expected_permissions = %w[
      users_read
      users_edit
      users_search
      jobs_read
      jobs_edit
      jobs_search
      organizations_read
      organizations_search
      conversations_read
      messages_read
      chat_requests_read
      talent_profiles_read
      talent_profiles_search
      export_data
    ]
    assert_equal expected_permissions, @support_admin.admin_permissions
  end

  test 'readonly admin has read-only permissions' do
    assert_equal :readonly, @readonly_admin.admin_role
    assert_equal 'Read-Only Admin', @readonly_admin.admin_role_name
    assert @readonly_admin.can_access_admin?
    assert @readonly_admin.can?(:users_read)
    assert @readonly_admin.can?(:users_search)
    assert @readonly_admin.can?(:export_data)
    assert_not @readonly_admin.can?(:users_edit)
    assert_not @readonly_admin.can?(:users_create)
    assert_not @readonly_admin.can?(:users_delete)
    assert_not @readonly_admin.can_manage_roles?

    expected_permissions = %w[
      users_read
      users_search
      jobs_read
      jobs_search
      organizations_read
      organizations_search
      conversations_read
      messages_read
      chat_requests_read
      talent_profiles_read
      talent_profiles_search
      job_applications_read
      export_data
    ]
    assert_equal expected_permissions, @readonly_admin.admin_permissions
  end

  test 'regular user has no admin permissions' do
    assert_nil @regular_user.admin_role
    assert_nil @regular_user.admin_role_name
    assert_not @regular_user.can_access_admin?
    assert_not @regular_user.can?(:users_read)
    assert_not @regular_user.can?(:users_edit)
    assert_not @regular_user.can?(:export_data)
    assert_not @regular_user.can_manage_roles?
    assert_equal [], @regular_user.admin_permissions
  end

  test 'admin role constants are properly defined' do
    assert_equal 3, User::ADMIN_ROLES.size
    assert User::ADMIN_ROLES.key?(:superadmin)
    assert User::ADMIN_ROLES.key?(:support)
    assert User::ADMIN_ROLES.key?(:readonly)

    assert_equal 'Super Admin', User::ADMIN_ROLES[:superadmin][:name]
    assert_equal 'Support Agent', User::ADMIN_ROLES[:support][:name]
    assert_equal 'Read-Only Admin', User::ADMIN_ROLES[:readonly][:name]

    assert_equal :all, User::ADMIN_ROLES[:superadmin][:permissions]
    assert User::ADMIN_ROLES[:support][:permissions].is_a?(Array)
    assert User::ADMIN_ROLES[:readonly][:permissions].is_a?(Array)
  end

  test 'permission checking works with string and symbol arguments' do
    assert @superadmin.can?('users_read')
    assert @superadmin.can?(:users_read)

    assert @readonly_admin.can?('users_read')
    assert @readonly_admin.can?(:users_read)
    assert_not @readonly_admin.can?('users_edit')
    assert_not @readonly_admin.can?(:users_edit)
  end
end
