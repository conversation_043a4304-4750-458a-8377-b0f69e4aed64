# == Schema Information
#
# Table name: impersonation_logs
#
#  id         :bigint           not null, primary key
#  ended_at   :datetime
#  ip_address :string
#  started_at :datetime         not null
#  user_agent :string
#  created_at :datetime         not null
#  updated_at :datetime         not null
#  admin_id   :bigint           not null
#  user_id    :bigint           not null
#
# Indexes
#
#  index_impersonation_logs_on_admin_id  (admin_id)
#  index_impersonation_logs_on_user_id   (user_id)
#
# Foreign Keys
#
#  fk_rails_...  (admin_id => users.id)
#  fk_rails_...  (user_id => users.id)
#
require "test_helper"

class ImpersonationLogTest < ActiveSupport::TestCase
  def setup
    @admin = users(:admin)
    @user = users(:scout)
  end

  test "should create impersonation log with valid attributes" do
    log = ImpersonationLog.new(
      admin: @admin,
      user: @user,
      started_at: Time.current,
      ip_address: "***********",
      user_agent: "Test Browser"
    )
    
    assert log.valid?
    assert log.save
  end

  test "should require admin" do
    log = ImpersonationLog.new(
      user: @user,
      started_at: Time.current,
      ip_address: "***********",
      user_agent: "Test Browser"
    )
    
    assert_not log.valid?
    assert_includes log.errors[:admin], "must exist"
  end

  test "should require user" do
    log = ImpersonationLog.new(
      admin: @admin,
      started_at: Time.current,
      ip_address: "***********",
      user_agent: "Test Browser"
    )
    
    assert_not log.valid?
    assert_includes log.errors[:user], "must exist"
  end

  test "should require started_at" do
    log = ImpersonationLog.new(
      admin: @admin,
      user: @user,
      ip_address: "***********",
      user_agent: "Test Browser"
    )
    
    assert_not log.valid?
    assert_includes log.errors[:started_at], "can't be blank"
  end

  test "should require ip_address" do
    log = ImpersonationLog.new(
      admin: @admin,
      user: @user,
      started_at: Time.current,
      user_agent: "Test Browser"
    )
    
    assert_not log.valid?
    assert_includes log.errors[:ip_address], "can't be blank"
  end

  test "should require user_agent" do
    log = ImpersonationLog.new(
      admin: @admin,
      user: @user,
      started_at: Time.current,
      ip_address: "***********"
    )
    
    assert_not log.valid?
    assert_includes log.errors[:user_agent], "can't be blank"
  end

  test "should prevent admin from impersonating themselves" do
    log = ImpersonationLog.new(
      admin: @admin,
      user: @admin,
      started_at: Time.current,
      ip_address: "***********",
      user_agent: "Test Browser"
    )
    
    assert_not log.valid?
    assert_includes log.errors[:user], "cannot impersonate yourself"
  end

  test "should prevent impersonating super admins" do
    super_admin = users(:super_admin)
    log = ImpersonationLog.new(
      admin: @admin,
      user: super_admin,
      started_at: Time.current,
      ip_address: "***********",
      user_agent: "Test Browser"
    )
    
    assert_not log.valid?
    assert_includes log.errors[:user], "cannot impersonate super admins"
  end

  test "should end impersonation" do
    log = ImpersonationLog.create!(
      admin: @admin,
      user: @user,
      started_at: 1.hour.ago,
      ip_address: "***********",
      user_agent: "Test Browser"
    )
    
    assert_nil log.ended_at
    
    log.end_impersonation!
    
    assert_not_nil log.ended_at
    assert log.ended_at <= Time.current
  end

  test "should calculate duration" do
    started_time = 2.hours.ago
    ended_time = 1.hour.ago
    
    log = ImpersonationLog.create!(
      admin: @admin,
      user: @user,
      started_at: started_time,
      ended_at: ended_time,
      ip_address: "***********",
      user_agent: "Test Browser"
    )
    
    expected_duration = ended_time - started_time
    assert_equal expected_duration, log.duration
  end

  test "should return nil duration for ongoing impersonation" do
    log = ImpersonationLog.create!(
      admin: @admin,
      user: @user,
      started_at: 1.hour.ago,
      ip_address: "***********",
      user_agent: "Test Browser"
    )
    
    assert_nil log.duration
  end

  test "should scope active impersonations" do
    # Create an active impersonation
    active_log = ImpersonationLog.create!(
      admin: @admin,
      user: @user,
      started_at: 1.hour.ago,
      ip_address: "***********",
      user_agent: "Test Browser"
    )
    
    # Create an ended impersonation
    ended_log = ImpersonationLog.create!(
      admin: @admin,
      user: users(:talent),
      started_at: 2.hours.ago,
      ended_at: 1.hour.ago,
      ip_address: "***********",
      user_agent: "Test Browser"
    )
    
    active_logs = ImpersonationLog.active
    
    assert_includes active_logs, active_log
    assert_not_includes active_logs, ended_log
  end

  test "should scope ended impersonations" do
    # Create an active impersonation
    active_log = ImpersonationLog.create!(
      admin: @admin,
      user: @user,
      started_at: 1.hour.ago,
      ip_address: "***********",
      user_agent: "Test Browser"
    )
    
    # Create an ended impersonation
    ended_log = ImpersonationLog.create!(
      admin: @admin,
      user: users(:talent),
      started_at: 2.hours.ago,
      ended_at: 1.hour.ago,
      ip_address: "***********",
      user_agent: "Test Browser"
    )
    
    ended_logs = ImpersonationLog.ended
    
    assert_not_includes ended_logs, active_log
    assert_includes ended_logs, ended_log
  end
end
