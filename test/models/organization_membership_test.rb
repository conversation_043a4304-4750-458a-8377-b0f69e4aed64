# == Schema Information
#
# Table name: organization_memberships
#
#  id              :bigint           not null, primary key
#  org_role        :integer          default("owner"), not null
#  created_at      :datetime         not null
#  updated_at      :datetime         not null
#  organization_id :bigint           not null
#  user_id         :bigint           not null
#
# Indexes
#
#  index_organization_memberships_on_organization_id              (organization_id)
#  index_organization_memberships_on_user_id                      (user_id)
#  index_organization_memberships_on_user_id_and_organization_id  (user_id,organization_id) UNIQUE
#
# Foreign Keys
#
#  fk_rails_...  (organization_id => organizations.id)
#  fk_rails_...  (user_id => users.id)
#
require "test_helper"

class OrganizationMembershipTest < ActiveSupport::TestCase
  # test "the truth" do
  #   assert true
  # end
end
