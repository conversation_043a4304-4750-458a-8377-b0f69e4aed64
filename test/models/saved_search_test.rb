# == Schema Information
#
# Table name: saved_searches
#
#  id            :bigint           not null, primary key
#  name          :string(100)      not null
#  resource_type :string(50)       not null
#  search_params :text             not null
#  created_at    :datetime         not null
#  updated_at    :datetime         not null
#  user_id       :bigint           not null
#
# Indexes
#
#  index_saved_searches_on_resource_type                       (resource_type)
#  index_saved_searches_on_user_id                             (user_id)
#  index_saved_searches_on_user_id_and_name_and_resource_type  (user_id,name,resource_type) UNIQUE
#  index_saved_searches_on_user_id_and_resource_type           (user_id,resource_type)
#
# Foreign Keys
#
#  fk_rails_...  (user_id => users.id)
#
require 'test_helper'

class SavedSearchTest < ActiveSupport::TestCase
  setup do
    @superadmin_role = Role.find_or_create_by!(name: 'superadmin')
    @user =
      User.create!(
        email: '<EMAIL>',
        password: 'password123123',
        verified: true,
        first_name: 'Test',
        last_name: 'User',
        onboarding_completed: true,
        onboarding_step: 'completed',
      )
    UserRole.create!(user: @user, role: @superadmin_role)

    @search_params = {
      'search' => 'test',
      'verified' => 'true',
      'role' => 'scout',
      'created_from' => '2024-01-01',
      'created_to' => '2024-12-31',
    }.to_json
  end

  test 'should create saved search with valid attributes' do
    saved_search =
      SavedSearch.new(
        user: @user,
        name: 'Test Search',
        resource_type: 'AdminUser',
        search_params: @search_params,
      )

    assert saved_search.valid?
    assert saved_search.save
  end

  test 'should require user' do
    saved_search =
      SavedSearch.new(
        name: 'Test Search',
        resource_type: 'AdminUser',
        search_params: @search_params,
      )

    assert_not saved_search.valid?
    assert_includes saved_search.errors[:user], 'must exist'
  end

  test 'should require name' do
    saved_search =
      SavedSearch.new(
        user: @user,
        resource_type: 'AdminUser',
        search_params: @search_params,
      )

    assert_not saved_search.valid?
    assert_includes saved_search.errors[:name], "can't be blank"
  end

  test 'should require resource_type' do
    saved_search =
      SavedSearch.new(
        user: @user,
        name: 'Test Search',
        search_params: @search_params,
      )

    assert_not saved_search.valid?
    assert_includes saved_search.errors[:resource_type], "can't be blank"
  end

  test 'should require search_params' do
    saved_search =
      SavedSearch.new(
        user: @user,
        name: 'Test Search',
        resource_type: 'AdminUser',
      )

    assert_not saved_search.valid?
    assert_includes saved_search.errors[:search_params], "can't be blank"
  end

  test 'should enforce uniqueness of name per user and resource type' do
    SavedSearch.create!(
      user: @user,
      name: 'Test Search',
      resource_type: 'AdminUser',
      search_params: @search_params,
    )

    duplicate_search =
      SavedSearch.new(
        user: @user,
        name: 'Test Search',
        resource_type: 'AdminUser',
        search_params: @search_params,
      )

    assert_not duplicate_search.valid?
    assert_includes duplicate_search.errors[:name], 'has already been taken'
  end

  test 'should allow same name for different users' do
    other_user =
      User.create!(
        email: '<EMAIL>',
        password: 'password123123',
        verified: true,
        first_name: 'Other',
        last_name: 'User',
        onboarding_completed: true,
        onboarding_step: 'completed',
      )
    UserRole.create!(user: other_user, role: @superadmin_role)

    SavedSearch.create!(
      user: @user,
      name: 'Test Search',
      resource_type: 'AdminUser',
      search_params: @search_params,
    )

    other_search =
      SavedSearch.new(
        user: other_user,
        name: 'Test Search',
        resource_type: 'AdminUser',
        search_params: @search_params,
      )

    assert other_search.valid?
    assert other_search.save
  end

  test 'should allow same name for different resource types' do
    SavedSearch.create!(
      user: @user,
      name: 'Test Search',
      resource_type: 'AdminUser',
      search_params: @search_params,
    )

    job_search =
      SavedSearch.new(
        user: @user,
        name: 'Test Search',
        resource_type: 'AdminJob',
        search_params: { 'search' => 'developer' }.to_json,
      )

    assert job_search.valid?
    assert job_search.save
  end

  test 'should parse search parameters correctly' do
    saved_search =
      SavedSearch.create!(
        user: @user,
        name: 'Test Search',
        resource_type: 'AdminUser',
        search_params: @search_params,
      )

    parsed_params = saved_search.parsed_params
    assert_equal 'test', parsed_params['search']
    assert_equal 'true', parsed_params['verified']
    assert_equal 'scout', parsed_params['role']
    assert_equal '2024-01-01', parsed_params['created_from']
    assert_equal '2024-12-31', parsed_params['created_to']
  end

  test 'should generate description from search parameters' do
    saved_search =
      SavedSearch.create!(
        user: @user,
        name: 'Test Search',
        resource_type: 'AdminUser',
        search_params: @search_params,
      )

    description = saved_search.description
    assert_includes description, "Search: 'test'"
    assert_includes description, 'Verified: True'
    assert_includes description, 'Role: Scout'
    assert_includes description, 'Created from 2024-01-01'
    assert_includes description, 'Created to 2024-12-31'
  end

  test 'should handle empty search parameters in description' do
    saved_search =
      SavedSearch.create!(
        user: @user,
        name: 'Empty Search',
        resource_type: 'AdminUser',
        search_params: {}.to_json,
      )

    description = saved_search.description
    assert_equal 'No search criteria', description
  end

  test 'should filter out empty values in description' do
    base_params = JSON.parse(@search_params)
    params_with_empty =
      base_params.merge('empty_field' => '', 'nil_field' => nil)
    saved_search =
      SavedSearch.create!(
        user: @user,
        name: 'Test Search',
        resource_type: 'AdminUser',
        search_params: params_with_empty.to_json,
      )

    description = saved_search.description
    assert_not_includes description, 'empty_field'
    assert_not_includes description, 'nil_field'
  end

  test 'should belong to user' do
    saved_search =
      SavedSearch.create!(
        user: @user,
        name: 'Test Search',
        resource_type: 'AdminUser',
        search_params: @search_params,
      )

    assert_equal @user, saved_search.user
  end

  test 'should be destroyed when user is destroyed' do
    saved_search =
      SavedSearch.create!(
        user: @user,
        name: 'Test Search',
        resource_type: 'AdminUser',
        search_params: @search_params,
      )

    assert_difference 'SavedSearch.count', -1 do
      @user.destroy
    end
  end
end
