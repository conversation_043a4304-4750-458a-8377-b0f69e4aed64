/**
 * Badge Click Controller Unit Tests
 * 
 * These tests verify the functionality of the badge click Stimulus controller.
 */

// Mock DOM environment (reusing from badge_modal_controller_test.js)
class MockElement {
  constructor(tagName = 'div') {
    this.tagName = tagName;
    this.classList = new MockClassList();
    this.style = {};
    this.dataset = {};
    this.attributes = {};
    this.children = [];
    this.parentElement = null;
    this.innerHTML = '';
    this.textContent = '';
    this.eventListeners = {};
  }

  querySelector(selector) {
    return this.children.find(child => child.matches && child.matches(selector));
  }

  addEventListener(event, handler) {
    if (!this.eventListeners[event]) {
      this.eventListeners[event] = [];
    }
    this.eventListeners[event].push(handler);
  }

  getAttribute(name) {
    return this.attributes[name];
  }

  setAttribute(name, value) {
    this.attributes[name] = value;
  }
}

class MockClassList {
  constructor() {
    this.classes = [];
  }

  add(className) {
    if (!this.classes.includes(className)) {
      this.classes.push(className);
    }
  }

  contains(className) {
    return this.classes.includes(className);
  }
}

// Mock Stimulus Controller base class
class Controller {
  constructor() {
    this.targets = {};
    this.data = {};
    this.element = new MockElement();
  }

  dispatch(eventName, detail = {}) {
    console.log(`Dispatched event: ${eventName}`, detail);
    return { detail };
  }
}

// Badge Click Controller Test Suite
class BadgeClickControllerTest {
  constructor() {
    this.setupMocks();
  }

  setupMocks() {
    // Mock global objects
    global.document = {
      querySelector: function(selector) {
        if (selector === '#badge-modal') {
          const modal = new MockElement();
          modal.id = 'badge-modal';
          modal.dataset.controller = 'badge-modal';
          return modal;
        }
        return null;
      }
    };

    global.console = {
      log: function(...args) {
        // Silent console for tests, or implement if needed
      },
      error: function(...args) {
        console.error(...args);
      }
    };
  }

  // Test: Controller initialization
  testControllerInitialization() {
    console.log('Testing badge click controller initialization...');
    
    const controller = new Controller();
    
    // Mock the connect method
    controller.connect = function() {
      console.log('Badge click controller connected');
    };

    // Test initialization
    controller.connect();
    
    console.log('✅ Badge click controller initialization test passed');
  }

  // Test: Badge data extraction
  testBadgeDataExtraction() {
    console.log('Testing badge data extraction...');
    
    const controller = new Controller();
    
    // Set up mock element with badge data
    controller.element.dataset = {
      badgeClickBadgeIdValue: '1',
      badgeClickBadgeNameValue: 'Expert Writer',
      badgeClickBadgeDescriptionValue: 'Recognized expert in their field',
      badgeClickBadgeCriteriaValue: 'Demonstrate exceptional writing skills',
      badgeClickBadgeBackgroundColorValue: '#8B5CF6',
      badgeClickBadgeTextColorValue: '#FFFFFF',
      badgeClickBadgeIconValue: 'star'
    };

    // Mock the extractBadgeData method
    controller.extractBadgeData = function() {
      return {
        id: this.element.dataset.badgeClickBadgeIdValue,
        name: this.element.dataset.badgeClickBadgeNameValue,
        description: this.element.dataset.badgeClickBadgeDescriptionValue,
        criteria: this.element.dataset.badgeClickBadgeCriteriaValue,
        backgroundColor: this.element.dataset.badgeClickBadgeBackgroundColorValue,
        textColor: this.element.dataset.badgeClickBadgeTextColorValue,
        icon: this.element.dataset.badgeClickBadgeIconValue
      };
    };

    // Test data extraction
    const badgeData = controller.extractBadgeData();
    
    console.assert(badgeData.id === '1', 'Badge ID should be extracted correctly');
    console.assert(badgeData.name === 'Expert Writer', 'Badge name should be extracted correctly');
    console.assert(badgeData.description === 'Recognized expert in their field', 'Badge description should be extracted correctly');
    console.assert(badgeData.backgroundColor === '#8B5CF6', 'Badge background color should be extracted correctly');
    
    console.log('✅ Badge data extraction test passed');
  }

  // Test: Badge click handling
  testBadgeClickHandling() {
    console.log('Testing badge click handling...');
    
    const controller = new Controller();
    let modalOpened = false;
    let badgeDataPassed = null;
    
    // Mock element data
    controller.element.dataset = {
      badgeClickBadgeIdValue: '2',
      badgeClickBadgeNameValue: 'Verified',
      badgeClickBadgeDescriptionValue: 'This user has been verified'
    };

    // Mock the click method
    controller.click = function(event) {
      event.preventDefault();
      
      const badgeData = this.extractBadgeData();
      
      if (!badgeData.name) {
        console.error('Badge data is incomplete');
        return;
      }
      
      this.openBadgeModal(badgeData);
    };

    controller.extractBadgeData = function() {
      return {
        id: this.element.dataset.badgeClickBadgeIdValue,
        name: this.element.dataset.badgeClickBadgeNameValue,
        description: this.element.dataset.badgeClickBadgeDescriptionValue
      };
    };

    controller.openBadgeModal = function(badgeData) {
      modalOpened = true;
      badgeDataPassed = badgeData;
      
      // Mock finding and calling badge modal
      const badgeModal = document.querySelector('#badge-modal');
      if (badgeModal && badgeModal.dataset.controller === 'badge-modal') {
        // Simulate calling the modal controller
        console.log('Badge modal opened with data:', badgeData);
      }
    };

    // Test click handling
    const mockEvent = { preventDefault: () => {} };
    controller.click(mockEvent);
    
    console.assert(modalOpened, 'Modal should be opened on badge click');
    console.assert(badgeDataPassed !== null, 'Badge data should be passed to modal');
    console.assert(badgeDataPassed.name === 'Verified', 'Correct badge data should be passed');
    
    console.log('✅ Badge click handling test passed');
  }

  // Test: Error handling for incomplete badge data
  testErrorHandling() {
    console.log('Testing error handling for incomplete badge data...');
    
    const controller = new Controller();
    let errorLogged = false;
    
    // Mock element with incomplete data
    controller.element.dataset = {
      badgeClickBadgeIdValue: '3'
      // Missing required fields like name
    };

    // Mock console.error to capture errors
    const originalError = console.error;
    console.error = function(...args) {
      errorLogged = true;
      originalError.apply(console, args);
    };

    controller.click = function(event) {
      event.preventDefault();
      
      const badgeData = this.extractBadgeData();
      
      if (!badgeData.name) {
        console.error('Badge data is incomplete - missing required name field');
        return;
      }
      
      this.openBadgeModal(badgeData);
    };

    controller.extractBadgeData = function() {
      return {
        id: this.element.dataset.badgeClickBadgeIdValue,
        name: this.element.dataset.badgeClickBadgeNameValue,
        description: this.element.dataset.badgeClickBadgeDescriptionValue
      };
    };

    // Test error handling
    const mockEvent = { preventDefault: () => {} };
    controller.click(mockEvent);
    
    console.assert(errorLogged, 'Error should be logged for incomplete badge data');
    
    // Restore original console.error
    console.error = originalError;
    
    console.log('✅ Error handling test passed');
  }

  // Test: Modal controller integration
  testModalControllerIntegration() {
    console.log('Testing modal controller integration...');
    
    const controller = new Controller();
    let modalControllerCalled = false;
    
    // Mock badge modal element with controller
    const mockBadgeModal = new MockElement();
    mockBadgeModal.id = 'badge-modal';
    mockBadgeModal.dataset.controller = 'badge-modal';
    
    // Mock application registry for Stimulus controllers
    mockBadgeModal.application = {
      getControllerForElementAndIdentifier: function(element, identifier) {
        if (identifier === 'badge-modal') {
          return {
            open: function(badgeData) {
              modalControllerCalled = true;
              console.log('Modal controller open method called with:', badgeData);
            }
          };
        }
        return null;
      }
    };

    controller.openBadgeModal = function(badgeData) {
      const badgeModal = document.querySelector('#badge-modal');
      if (badgeModal) {
        // In real implementation, this would use Stimulus application
        // For test, we'll simulate the controller call
        const modalController = badgeModal.application.getControllerForElementAndIdentifier(badgeModal, 'badge-modal');
        if (modalController) {
          modalController.open(badgeData);
        }
      }
    };

    // Override document.querySelector for this test
    const originalQuerySelector = global.document.querySelector;
    global.document.querySelector = function(selector) {
      if (selector === '#badge-modal') {
        return mockBadgeModal;
      }
      return originalQuerySelector.call(this, selector);
    };

    // Test integration
    const testBadgeData = { name: 'Test Badge', description: 'Test description' };
    controller.openBadgeModal(testBadgeData);
    
    console.assert(modalControllerCalled, 'Modal controller should be called');
    
    // Restore original querySelector
    global.document.querySelector = originalQuerySelector;
    
    console.log('✅ Modal controller integration test passed');
  }

  // Test: Event prevention
  testEventPrevention() {
    console.log('Testing event prevention...');
    
    const controller = new Controller();
    let preventDefaultCalled = false;
    
    controller.click = function(event) {
      event.preventDefault();
      // Rest of click handling...
    };

    // Test event prevention
    const mockEvent = {
      preventDefault: function() {
        preventDefaultCalled = true;
      }
    };
    
    controller.click(mockEvent);
    
    console.assert(preventDefaultCalled, 'preventDefault should be called on click event');
    
    console.log('✅ Event prevention test passed');
  }

  // Run all tests
  runAllTests() {
    console.log('🧪 Starting Badge Click Controller Tests...\n');
    
    try {
      this.testControllerInitialization();
      this.testBadgeDataExtraction();
      this.testBadgeClickHandling();
      this.testErrorHandling();
      this.testModalControllerIntegration();
      this.testEventPrevention();
      
      console.log('\n🎉 All Badge Click Controller tests passed!');
    } catch (error) {
      console.error('❌ Test failed:', error);
    }
  }
}

// Run tests if in Node.js environment
if (typeof module !== 'undefined' && module.exports) {
  const testSuite = new BadgeClickControllerTest();
  testSuite.runAllTests();
  module.exports = BadgeClickControllerTest;
} else if (typeof window !== 'undefined') {
  // Run tests in browser environment
  window.BadgeClickControllerTest = BadgeClickControllerTest;
  const testSuite = new BadgeClickControllerTest();
  testSuite.runAllTests();
}
