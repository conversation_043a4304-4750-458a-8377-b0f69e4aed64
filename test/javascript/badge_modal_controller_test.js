/**
 * Badge Modal Controller Unit Tests
 * 
 * These tests verify the functionality of the badge modal Stimulus controller.
 * They can be run with a JavaScript testing framework like Jest or in a browser environment.
 */

// Mock DOM environment for testing
class MockElement {
  constructor(tagName = 'div') {
    this.tagName = tagName;
    this.classList = new MockClassList();
    this.style = {};
    this.dataset = {};
    this.attributes = {};
    this.children = [];
    this.parentElement = null;
    this.innerHTML = '';
    this.textContent = '';
    this.eventListeners = {};
  }

  querySelector(selector) {
    // Simple mock implementation
    return this.children.find(child => child.matches && child.matches(selector));
  }

  querySelectorAll(selector) {
    // Simple mock implementation
    return this.children.filter(child => child.matches && child.matches(selector));
  }

  addEventListener(event, handler) {
    if (!this.eventListeners[event]) {
      this.eventListeners[event] = [];
    }
    this.eventListeners[event].push(handler);
  }

  removeEventListener(event, handler) {
    if (this.eventListeners[event]) {
      const index = this.eventListeners[event].indexOf(handler);
      if (index > -1) {
        this.eventListeners[event].splice(index, 1);
      }
    }
  }

  focus() {
    this.focused = true;
  }

  matches(selector) {
    // Simple implementation for basic selectors
    if (selector.startsWith('#')) {
      return this.id === selector.slice(1);
    }
    if (selector.startsWith('.')) {
      return this.classList.contains(selector.slice(1));
    }
    return this.tagName.toLowerCase() === selector.toLowerCase();
  }

  setAttribute(name, value) {
    this.attributes[name] = value;
  }

  getAttribute(name) {
    return this.attributes[name];
  }
}

class MockClassList {
  constructor() {
    this.classes = [];
  }

  add(className) {
    if (!this.classes.includes(className)) {
      this.classes.push(className);
    }
  }

  remove(className) {
    const index = this.classes.indexOf(className);
    if (index > -1) {
      this.classes.splice(index, 1);
    }
  }

  contains(className) {
    return this.classes.includes(className);
  }

  toggle(className) {
    if (this.contains(className)) {
      this.remove(className);
    } else {
      this.add(className);
    }
  }
}

// Mock Stimulus Controller base class
class Controller {
  constructor() {
    this.targets = {};
    this.data = {};
  }

  dispatch(eventName, detail = {}) {
    console.log(`Dispatched event: ${eventName}`, detail);
  }
}

// Badge Modal Controller Test Suite
class BadgeModalControllerTest {
  constructor() {
    this.setupMocks();
  }

  setupMocks() {
    // Mock global objects
    global.document = {
      body: new MockElement('body'),
      activeElement: new MockElement('input'),
      addEventListener: function(event, handler) {
        this.eventListeners = this.eventListeners || {};
        this.eventListeners[event] = this.eventListeners[event] || [];
        this.eventListeners[event].push(handler);
      },
      removeEventListener: function(event, handler) {
        if (this.eventListeners && this.eventListeners[event]) {
          const index = this.eventListeners[event].indexOf(handler);
          if (index > -1) {
            this.eventListeners[event].splice(index, 1);
          }
        }
      },
      createElement: function(tagName) {
        return new MockElement(tagName);
      },
      getElementById: function(id) {
        const element = new MockElement();
        element.id = id;
        return element;
      }
    };

    global.window = {
      addEventListener: function() {},
      removeEventListener: function() {},
      matchMedia: function() {
        return { matches: false };
      }
    };

    global.navigator = {
      maxTouchPoints: 0
    };
  }

  // Test: Controller initialization
  testControllerInitialization() {
    console.log('Testing controller initialization...');
    
    // Create mock controller instance
    const controller = new Controller();
    
    // Mock the connect method behavior
    controller.connect = function() {
      this.boundHandleKeydown = this.handleKeydown.bind(this);
      this.boundHandleResize = this.handleResize.bind(this);
      this.previousActiveElement = null;
      this.reducedMotion = false;
      this.isTouchDevice = false;
    };

    controller.handleKeydown = function(event) {
      switch (event.key) {
        case 'Escape':
          this.close();
          break;
        case 'Tab':
          this.handleTabNavigation(event);
          break;
      }
    };

    controller.handleResize = function() {
      // Mock resize handler
    };

    controller.close = function() {
      console.log('Modal closed');
    };

    controller.handleTabNavigation = function(event) {
      console.log('Tab navigation handled');
    };

    // Test initialization
    controller.connect();
    
    console.assert(typeof controller.boundHandleKeydown === 'function', 'boundHandleKeydown should be a function');
    console.assert(typeof controller.boundHandleResize === 'function', 'boundHandleResize should be a function');
    console.assert(controller.previousActiveElement === null, 'previousActiveElement should be null initially');
    
    console.log('✅ Controller initialization test passed');
  }

  // Test: Modal opening functionality
  testModalOpening() {
    console.log('Testing modal opening...');
    
    const controller = new Controller();
    
    // Mock targets
    controller.containerTarget = new MockElement();
    controller.containerTarget.classList.add('hidden');
    
    // Mock open method
    controller.open = function(badgeData) {
      this.previousActiveElement = document.activeElement;
      this.populateModal(badgeData);
      this.showModal();
      this.manageFocus();
    };

    controller.populateModal = function(badgeData) {
      console.log('Modal populated with:', badgeData);
    };

    controller.showModal = function() {
      this.containerTarget.classList.remove('hidden');
    };

    controller.manageFocus = function() {
      console.log('Focus managed');
    };

    // Test opening
    const testBadgeData = {
      id: 1,
      name: 'Test Badge',
      description: 'Test description'
    };

    controller.open(testBadgeData);
    
    console.assert(!controller.containerTarget.classList.contains('hidden'), 'Modal should be visible after opening');
    console.assert(controller.previousActiveElement !== null, 'Previous active element should be stored');
    
    console.log('✅ Modal opening test passed');
  }

  // Test: Modal closing functionality
  testModalClosing() {
    console.log('Testing modal closing...');
    
    const controller = new Controller();
    
    // Mock targets
    controller.containerTarget = new MockElement();
    controller.previousActiveElement = new MockElement();
    
    // Mock close method
    controller.close = function() {
      this.hideModal();
      if (this.previousActiveElement) {
        this.previousActiveElement.focus();
        this.previousActiveElement = null;
      }
    };

    controller.hideModal = function() {
      this.containerTarget.classList.add('hidden');
    };

    // Test closing
    controller.close();
    
    console.assert(controller.containerTarget.classList.contains('hidden'), 'Modal should be hidden after closing');
    console.assert(controller.previousActiveElement === null, 'Previous active element should be cleared');
    
    console.log('✅ Modal closing test passed');
  }

  // Test: Keyboard navigation
  testKeyboardNavigation() {
    console.log('Testing keyboard navigation...');
    
    const controller = new Controller();
    let modalClosed = false;
    
    controller.close = function() {
      modalClosed = true;
    };

    controller.handleTabNavigation = function(event) {
      console.log('Tab navigation handled for event:', event);
    };

    controller.handleKeydown = function(event) {
      switch (event.key) {
        case 'Escape':
          event.preventDefault();
          this.close();
          break;
        case 'Tab':
          this.handleTabNavigation(event);
          break;
      }
    };

    // Test ESC key
    const escapeEvent = { key: 'Escape', preventDefault: () => {} };
    controller.handleKeydown(escapeEvent);
    
    console.assert(modalClosed, 'Modal should close on ESC key');
    
    // Test Tab key
    const tabEvent = { key: 'Tab' };
    controller.handleKeydown(tabEvent); // Should not throw error
    
    console.log('✅ Keyboard navigation test passed');
  }

  // Test: Badge data population
  testBadgeDataPopulation() {
    console.log('Testing badge data population...');
    
    const controller = new Controller();
    
    // Mock targets
    controller.titleTarget = new MockElement();
    controller.descriptionTarget = new MockElement();
    controller.badgeDisplayTarget = new MockElement();
    
    controller.populateModal = function(badgeData) {
      if (this.titleTarget) {
        this.titleTarget.textContent = badgeData.name;
      }
      if (this.descriptionTarget) {
        this.descriptionTarget.textContent = badgeData.description;
      }
      if (this.badgeDisplayTarget) {
        this.createBadgeDisplay(badgeData);
      }
    };

    controller.createBadgeDisplay = function(badgeData) {
      this.badgeDisplayTarget.innerHTML = `<div class="badge">${badgeData.name}</div>`;
    };

    // Test data population
    const testBadgeData = {
      name: 'Expert Writer',
      description: 'Recognized expert in their field',
      backgroundColor: '#8B5CF6',
      textColor: '#FFFFFF'
    };

    controller.populateModal(testBadgeData);
    
    console.assert(controller.titleTarget.textContent === testBadgeData.name, 'Title should be populated');
    console.assert(controller.descriptionTarget.textContent === testBadgeData.description, 'Description should be populated');
    console.assert(controller.badgeDisplayTarget.innerHTML.includes(testBadgeData.name), 'Badge display should contain badge name');
    
    console.log('✅ Badge data population test passed');
  }

  // Test: Focus management
  testFocusManagement() {
    console.log('Testing focus management...');
    
    const controller = new Controller();
    
    // Mock targets
    controller.closeButtonTarget = new MockElement();
    controller.containerTarget = new MockElement();
    
    controller.manageFocus = function() {
      if (this.closeButtonTarget) {
        this.closeButtonTarget.focus();
      }
    };

    // Test focus management
    controller.manageFocus();
    
    console.assert(controller.closeButtonTarget.focused, 'Close button should receive focus');
    
    console.log('✅ Focus management test passed');
  }

  // Run all tests
  runAllTests() {
    console.log('🧪 Starting Badge Modal Controller Tests...\n');
    
    try {
      this.testControllerInitialization();
      this.testModalOpening();
      this.testModalClosing();
      this.testKeyboardNavigation();
      this.testBadgeDataPopulation();
      this.testFocusManagement();
      
      console.log('\n🎉 All Badge Modal Controller tests passed!');
    } catch (error) {
      console.error('❌ Test failed:', error);
    }
  }
}

// Run tests if in Node.js environment
if (typeof module !== 'undefined' && module.exports) {
  const testSuite = new BadgeModalControllerTest();
  testSuite.runAllTests();
  module.exports = BadgeModalControllerTest;
} else if (typeof window !== 'undefined') {
  // Run tests in browser environment
  window.BadgeModalControllerTest = BadgeModalControllerTest;
  const testSuite = new BadgeModalControllerTest();
  testSuite.runAllTests();
}
