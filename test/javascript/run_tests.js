#!/usr/bin/env node

/**
 * JavaScript Test Runner for Badge Modal System
 * 
 * This script runs all JavaScript unit tests for the badge modal functionality.
 * It can be executed with Node.js to verify the Stimulus controllers work correctly.
 */

const fs = require('fs');
const path = require('path');

class TestRunner {
  constructor() {
    this.testFiles = [
      'badge_modal_controller_test.js',
      'badge_click_controller_test.js'
    ];
    this.testResults = [];
  }

  async runAllTests() {
    console.log('🚀 Starting Badge Modal JavaScript Test Suite\n');
    console.log('=' * 60);
    
    for (const testFile of this.testFiles) {
      await this.runTestFile(testFile);
    }
    
    this.printSummary();
  }

  async runTestFile(testFile) {
    const testPath = path.join(__dirname, testFile);
    
    if (!fs.existsSync(testPath)) {
      console.error(`❌ Test file not found: ${testFile}`);
      this.testResults.push({ file: testFile, status: 'failed', error: 'File not found' });
      return;
    }

    console.log(`\n📋 Running tests from: ${testFile}`);
    console.log('-' * 40);

    try {
      // Capture console output
      const originalLog = console.log;
      const originalError = console.error;
      let output = [];
      let errors = [];

      console.log = (...args) => {
        output.push(args.join(' '));
        originalLog.apply(console, args);
      };

      console.error = (...args) => {
        errors.push(args.join(' '));
        originalError.apply(console, args);
      };

      // Clear require cache to ensure fresh test run
      delete require.cache[require.resolve(testPath)];
      
      // Run the test
      require(testPath);

      // Restore console
      console.log = originalLog;
      console.error = originalError;

      // Determine test result
      const hasErrors = errors.length > 0 || output.some(line => line.includes('❌'));
      const status = hasErrors ? 'failed' : 'passed';
      
      this.testResults.push({
        file: testFile,
        status: status,
        output: output,
        errors: errors
      });

    } catch (error) {
      console.error(`❌ Error running ${testFile}:`, error.message);
      this.testResults.push({
        file: testFile,
        status: 'failed',
        error: error.message
      });
    }
  }

  printSummary() {
    console.log('\n' + '=' * 60);
    console.log('📊 TEST SUMMARY');
    console.log('=' * 60);

    const passed = this.testResults.filter(r => r.status === 'passed').length;
    const failed = this.testResults.filter(r => r.status === 'failed').length;
    const total = this.testResults.length;

    console.log(`\nTotal test files: ${total}`);
    console.log(`✅ Passed: ${passed}`);
    console.log(`❌ Failed: ${failed}`);

    if (failed > 0) {
      console.log('\n🔍 FAILED TESTS:');
      this.testResults
        .filter(r => r.status === 'failed')
        .forEach(result => {
          console.log(`\n📁 ${result.file}:`);
          if (result.error) {
            console.log(`   Error: ${result.error}`);
          }
          if (result.errors && result.errors.length > 0) {
            result.errors.forEach(error => {
              console.log(`   ${error}`);
            });
          }
        });
    }

    console.log('\n' + '=' * 60);
    
    if (failed === 0) {
      console.log('🎉 All tests passed! Badge modal system is working correctly.');
    } else {
      console.log('⚠️  Some tests failed. Please review the errors above.');
      process.exit(1);
    }
  }
}

// Additional test utilities
class BadgeModalTestUtils {
  static createMockBadgeData() {
    return {
      id: 1,
      name: 'Test Badge',
      description: 'A test badge for unit testing',
      criteria: 'Complete the test successfully',
      backgroundColor: '#10B981',
      textColor: '#FFFFFF',
      icon: 'check-circle'
    };
  }

  static createMockElement(tagName = 'div', attributes = {}) {
    const element = {
      tagName: tagName.toUpperCase(),
      classList: {
        classes: [],
        add: function(className) {
          if (!this.classes.includes(className)) {
            this.classes.push(className);
          }
        },
        remove: function(className) {
          const index = this.classes.indexOf(className);
          if (index > -1) {
            this.classes.splice(index, 1);
          }
        },
        contains: function(className) {
          return this.classes.includes(className);
        },
        toggle: function(className) {
          if (this.contains(className)) {
            this.remove(className);
          } else {
            this.add(className);
          }
        }
      },
      dataset: {},
      style: {},
      attributes: {},
      innerHTML: '',
      textContent: '',
      eventListeners: {},
      focused: false
    };

    // Set attributes
    Object.keys(attributes).forEach(key => {
      element.attributes[key] = attributes[key];
    });

    // Add methods
    element.addEventListener = function(event, handler) {
      if (!this.eventListeners[event]) {
        this.eventListeners[event] = [];
      }
      this.eventListeners[event].push(handler);
    };

    element.removeEventListener = function(event, handler) {
      if (this.eventListeners[event]) {
        const index = this.eventListeners[event].indexOf(handler);
        if (index > -1) {
          this.eventListeners[event].splice(index, 1);
        }
      }
    };

    element.focus = function() {
      this.focused = true;
    };

    element.getAttribute = function(name) {
      return this.attributes[name];
    };

    element.setAttribute = function(name, value) {
      this.attributes[name] = value;
    };

    return element;
  }

  static simulateKeyboardEvent(key, options = {}) {
    return {
      key: key,
      preventDefault: options.preventDefault || function() {},
      stopPropagation: options.stopPropagation || function() {},
      shiftKey: options.shiftKey || false,
      ctrlKey: options.ctrlKey || false,
      altKey: options.altKey || false,
      metaKey: options.metaKey || false
    };
  }

  static simulateClickEvent(options = {}) {
    return {
      type: 'click',
      preventDefault: options.preventDefault || function() {},
      stopPropagation: options.stopPropagation || function() {},
      target: options.target || null,
      currentTarget: options.currentTarget || null
    };
  }
}

// Export utilities for use in tests
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { TestRunner, BadgeModalTestUtils };
}

// Run tests if this file is executed directly
if (require.main === module) {
  const runner = new TestRunner();
  runner.runAllTests().catch(error => {
    console.error('Test runner failed:', error);
    process.exit(1);
  });
}
