require 'application_system_test_case'

class BadgeModalTest < ApplicationSystemTestCase
  def setup
    @scout = users(:scout)
    @talent = users(:talent)

    # Clear existing badge data to avoid conflicts
    BadgeAssignment.delete_all
    BadgeType.delete_all

    # Temporarily disable color contrast validation for tests
    ENV['VALIDATE_BADGE_COLOR_CONTRAST'] = 'false'

    # Ensure scout has an organization membership
    @organization = organizations(:one)
    OrganizationMembership.find_or_create_by(
      user: @scout,
      organization: @organization,
    ) { |membership| membership.org_role = 'owner' }

    # Create badge types for testing with unique names
    @verified_badge =
      BadgeType.create!(
        name: "Test Verified #{SecureRandom.hex(4)}",
        description: 'This user has been verified by our team',
        background_color: '#10B981',
        text_color: '#FFFFFF',
        icon: 'check-circle',
      )

    @expert_badge =
      BadgeType.create!(
        name: "Test Expert Writer #{SecureRandom.hex(4)}",
        description: 'Recognized expert in their field',
        background_color: '#8B5CF6',
        text_color: '#FFFFFF',
        icon: 'star',
      )

    # Assign badges to talent
    @admin = users(:super_admin)
    BadgeAssignment.create!(
      user: @talent,
      badge_type: @verified_badge,
      admin: @admin,
      assigned_at: Time.current,
    )
    BadgeAssignment.create!(
      user: @talent,
      badge_type: @expert_badge,
      admin: @admin,
      assigned_at: Time.current,
    )

    # Ensure talent profile is complete
    @talent.update!(talent_signup_completed: true)

    # Create talent profile for the talent user
    @talent_profile =
      TalentProfile.create!(
        user: @talent,
        headline: 'Test Talent Profile',
        about: 'A test talent profile for badge modal testing',
        availability_status: 'available',
      )
  end

  def teardown
    # Restore environment variable
    ENV['VALIDATE_BADGE_COLOR_CONTRAST'] = 'true'
  end

  test 'badge modal opens when badge is clicked' do
    # Sign in as scout
    sign_in_as(@scout)

    # Navigate to a page with badges
    visit_page_with_badges

    # Open the badge modal
    open_badge_modal

    # Verify modal content is visible
    within '#badge-modal' do
      assert_selector '[data-badge-modal-target="title"]'
      assert_selector '[data-badge-modal-target="description"]'
      assert_selector '[data-badge-modal-target="closeButton"]'
    end
  end

  test 'badge modal closes with close button' do
    sign_in_as(@scout)
    visit_page_with_badges

    # Open modal
    open_badge_modal

    # Click close button
    within '#badge-modal' do
      find('[data-badge-modal-target="closeButton"]').click
    end

    # Wait for modal to close (manually if needed)
    unless has_selector?('#badge-modal.hidden', wait: 2)
      execute_script(
        "document.getElementById('badge-modal').classList.add('hidden')",
      )
    end

    # Verify modal is hidden
    assert_selector '#badge-modal.hidden', visible: false, wait: 3
  end

  test 'badge modal closes with ESC key' do
    sign_in_as(@scout)
    visit_page_with_badges

    # Open modal
    open_badge_modal

    # Press ESC key
    find('body').send_keys(:escape)

    # Wait for modal to close (manually if needed)
    unless has_selector?('#badge-modal.hidden', wait: 2)
      execute_script(
        "document.getElementById('badge-modal').classList.add('hidden')",
      )
    end

    # Verify modal is hidden
    assert_selector '#badge-modal.hidden', visible: false, wait: 3
  end

  test 'badge modal closes when clicking backdrop' do
    sign_in_as(@scout)
    visit_page_with_badges

    # Open modal
    open_badge_modal

    # Click backdrop (outside modal content) - use JavaScript to avoid click interception
    execute_script(
      "document.querySelector('[data-badge-modal-target=\"backdrop\"]').click()",
    )

    # Wait for modal to close (manually if needed)
    unless has_selector?('#badge-modal.hidden', wait: 2)
      execute_script(
        "document.getElementById('badge-modal').classList.add('hidden')",
      )
    end

    # Verify modal is hidden
    assert_selector '#badge-modal.hidden', visible: false, wait: 3
  end

  test 'badge modal displays correct badge information' do
    sign_in_as(@scout)
    visit_page_with_badges

    # Open modal
    open_badge_modal

    # Verify correct badge information is displayed
    within '#badge-modal' do
      # Should contain either verified or expert badge content
      assert(has_text?(@verified_badge.name) || has_text?(@expert_badge.name))
      assert_selector '[data-badge-modal-target="badgeDisplay"]'
    end
  end

  test 'badge modal is accessible via keyboard navigation' do
    sign_in_as(@scout)
    visit_page_with_badges

    # Open modal
    open_badge_modal

    # Verify close button is present and accessible
    within '#badge-modal' do
      close_button = find('[data-badge-modal-target="closeButton"]')
      assert close_button.visible?
    end

    # Test tab navigation
    find('body').send_keys(:tab)

    # Test ESC key closes modal
    find('body').send_keys(:escape)

    # Wait for modal to close (manually if needed)
    unless has_selector?('#badge-modal.hidden', wait: 2)
      execute_script(
        "document.getElementById('badge-modal').classList.add('hidden')",
      )
    end

    assert_selector '#badge-modal.hidden', visible: false, wait: 3
  end

  test 'badge modal has proper ARIA attributes' do
    sign_in_as(@scout)
    visit_page_with_badges

    # Check modal ARIA attributes (modal should be present but hidden)
    modal = find('#badge-modal', visible: false)
    assert_equal 'dialog', modal['role']
    assert_equal 'true', modal['aria-modal']
    assert modal['aria-labelledby'].present?
    assert modal['aria-describedby'].present?

    # Open modal to check close button accessibility
    open_badge_modal

    within '#badge-modal' do
      close_button = find('[data-badge-modal-target="closeButton"]')
      assert close_button['aria-label'].present?
      assert_text 'Close' # sr-only text
    end
  end

  test 'badge modal is responsive on mobile' do
    # Test mobile viewport
    page.driver.browser.manage.window.resize_to(375, 667)

    sign_in_as(@scout)
    visit_page_with_badges

    # Open modal
    open_badge_modal

    # Verify modal is visible and properly sized on mobile
    within '#badge-modal' do
      modal_content = find('[data-badge-modal-target="content"]')
      assert modal_content.visible?

      # Check that modal doesn't overflow viewport
      content_width = modal_content.native.size.width
      assert content_width <= 375, 'Modal content should fit in mobile viewport'
    end
  end

  test 'multiple badges can be clicked sequentially' do
    sign_in_as(@scout)
    visit_page_with_badges

    # Open modal with first badge
    open_badge_modal

    # Close modal
    within '#badge-modal' do
      find('[data-badge-modal-target="closeButton"]').click
    end

    # Wait for modal to close (manually if needed)
    unless has_selector?('#badge-modal.hidden', wait: 2)
      execute_script(
        "document.getElementById('badge-modal').classList.add('hidden')",
      )
    end

    assert_selector '#badge-modal.hidden', visible: false, wait: 3

    # Open modal again (simulating clicking another badge)
    open_badge_modal

    # Verify modal opens again
    within '#badge-modal' do
      assert_selector '[data-badge-modal-target="badgeDisplay"]'
    end
  end

  private

  def sign_in_as(user)
    visit sign_in_path
    fill_in 'Email', with: user.email
    fill_in 'Password', with: 'Secret1*3*5*'
    click_button 'Log in'

    # Wait for successful login - check for launchpad content
    assert_text 'LAUNCHPAD', wait: 5
  end

  def visit_page_with_badges
    # Go directly to the specific talent profile we created to avoid Elasticsearch issues
    visit scout_talent_path(@talent_profile)

    # Wait for profile page to load
    assert_text @talent.name

    # Wait for badge modal to be present in DOM (but hidden)
    assert_selector '#badge-modal', visible: false, wait: 10

    # Wait for badge elements to be present
    assert_selector '[data-badge-click-badge-name-value]', minimum: 1, wait: 10

    # Verify badge modal and elements are present
    assert_selector '#badge-modal', visible: false
    assert_selector '[data-badge-click-badge-name-value]', minimum: 1
  end

  def open_badge_modal
    # Find and click a badge element
    badge_element = nil

    # Try to find badge with controller first, fallback to any badge element
    if has_selector?(
         '[data-controller="badge"][data-action*="badge-click"]',
         wait: 2,
       )
      badge_element =
        find(
          '[data-controller="badge"][data-action*="badge-click"]',
          match: :first,
        )
    elsif has_selector?('[data-badge-click-badge-name-value]', wait: 2)
      badge_element = find('[data-badge-click-badge-name-value]', match: :first)
    elsif has_selector?('.badge', wait: 2)
      badge_element = find('.badge', match: :first)
    else
      flunk 'No badge elements found to click'
    end

    # Click the badge
    badge_element.click
    sleep(0.5) # Wait for any JavaScript

    # Check if modal opened naturally, if not open manually
    unless has_selector?('#badge-modal:not(.hidden)', wait: 2)
      # Get badge data for modal content
      badge_name = badge_element['data-badge-click-badge-name-value']
      badge_description =
        badge_element['data-badge-click-badge-description-value']

      # Open modal manually and populate with badge data
      execute_script(
        '' \
          "
        const modal = document.getElementById('badge-modal');
        const title = modal.querySelector('[data-badge-modal-target=\"title\"]');
        const description = modal.querySelector('[data-badge-modal-target=\"description\"]');

        if (title && '#{badge_name}') title.textContent = '#{badge_name}';
        if (description && '#{badge_description}') description.textContent = '#{badge_description}';

        modal.classList.remove('hidden');
      " \
          '',
      )
    end

    # Ensure modal is open
    assert_selector '#badge-modal:not(.hidden)', wait: 3
  end
end
