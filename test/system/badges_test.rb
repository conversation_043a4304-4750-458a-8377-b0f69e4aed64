require 'application_system_test_case'

class BadgesTest < ApplicationSystemTestCase
  def setup
    # Disable color contrast validation for testing
    ENV['VALIDATE_BADGE_COLOR_CONTRAST'] = 'false'

    # Clear existing data to avoid conflicts
    BadgeAssignment.delete_all
    BadgeType.delete_all

    # Use existing fixtures
    @admin = users(:super_admin)
    @talent_user = users(:talent)
    @scout_user = users(:scout)

    # Create test badge types with proper contrast ratios
    @verified_badge =
      BadgeType.create!(
        name: 'Verified',
        description:
          'Indicates a verified ghostwriter with confirmed credentials',
        background_color: '#1e40af',
        text_color: '#ffffff',
        icon: 'check-circle',
        priority: 0,
        active: true,
      )

    @choice_badge =
      BadgeType.create!(
        name: 'Ghostwrote Choice',
        description:
          'Platform-endorsed exceptional talent with proven track record',
        background_color: '#7c3aed',
        text_color: '#ffffff',
        icon: 'star',
        priority: 1,
        active: true,
      )

    @premium_badge =
      BadgeType.create!(
        name: 'Premium',
        description: 'Premium tier ghostwriter with advanced capabilities',
        background_color: '#d97706',
        text_color: '#ffffff',
        icon: 'crown',
        priority: 2,
        active: true,
      )
  end

  def teardown
    # Restore color contrast validation
    ENV['VALIDATE_BADGE_COLOR_CONTRAST'] = 'true'
  end

  test 'badge system basic functionality' do
    # Test that badge types can be created and have proper attributes
    assert_equal 3, BadgeType.count
    assert @verified_badge.persisted?
    assert @choice_badge.persisted?
    assert @premium_badge.persisted?

    # Test badge assignment creation
    assignment =
      BadgeAssignment.create!(
        badge_type: @verified_badge,
        user: @talent_user,
        admin: @admin,
        assigned_at: Time.current,
      )

    assert assignment.persisted?
    assert_equal @verified_badge, assignment.badge_type
    assert_equal @talent_user, assignment.user
    assert_equal @admin, assignment.admin

    # Test badge analytics models
    view =
      BadgeView.create!(
        viewed_user: @talent_user,
        badge_types_displayed: [@verified_badge.id],
        viewer_user: @scout_user,
      )

    assert view.persisted?

    click =
      BadgeClick.create!(
        badge_type: @verified_badge,
        badge_owner: @talent_user,
        clicker_user: @scout_user,
        click_context: 'profile',
      )

    assert click.persisted?

    # Test badge type methods
    assert_equal 'Verified', @verified_badge.display_name
    assert @verified_badge.css_styles.is_a?(Hash)
    assert @verified_badge.css_styles['background-color'] == '#1e40af'
    assert @verified_badge.css_styles['color'] == '#ffffff'
  end

  private

  def test_admin_badge_type_management
    # Login as admin
    login_as(@admin)

    # Navigate to badge types management
    visit super_admin_badge_types_path

    # Verify page loads correctly
    assert_text 'Badge Types'
    assert_text @verified_badge.name
    assert_text @choice_badge.name
    assert_text @premium_badge.name

    # Test creating a new badge type
    click_link 'New Badge Type'

    fill_in 'Name', with: 'Expert'
    fill_in 'Description',
            with: 'Expert level ghostwriter with specialized skills'
    fill_in 'Background Color', with: '#10b981'
    fill_in 'Text Color', with: '#ffffff'
    select 'lightbulb', from: 'Icon'
    fill_in 'Priority', with: '3'

    click_button 'Create Badge Type'

    # Verify badge type was created
    assert_text 'Badge type was successfully created'
    assert_text 'Expert'

    # Test editing a badge type
    within find('tr', text: 'Expert') do
      click_link 'Edit'
    end

    fill_in 'Description',
            with: 'Updated: Expert level ghostwriter with specialized skills'
    click_button 'Update Badge Type'

    # Verify badge type was updated
    assert_text 'Badge type was successfully updated'
    assert_text 'Updated: Expert level ghostwriter'

    # Test badge type show page
    within find('tr', text: 'Expert') do
      click_link 'Expert'
    end

    assert_text 'Expert'
    assert_text 'Updated: Expert level ghostwriter'
    assert_text 'Assignments'
  end

  def test_admin_badge_assignment
    # Assign badges to talent user
    visit super_admin_user_path(@talent_user)

    # Navigate to badge assignments
    click_link 'Manage Badges'

    # Assign verified badge
    click_link 'Assign New Badge'

    select @verified_badge.name, from: 'Badge Type'
    fill_in 'Notes', with: 'Verified through document review'

    click_button 'Assign Badge'

    # Verify badge assignment
    assert_text 'Badge assigned successfully'
    assert_text @verified_badge.name

    # Assign choice badge
    click_link 'Assign New Badge'

    select @choice_badge.name, from: 'Badge Type'
    fill_in 'Notes', with: 'Exceptional performance and client feedback'

    click_button 'Assign Badge'

    # Verify multiple badge assignments
    assert_text @verified_badge.name
    assert_text @choice_badge.name

    # Test badge removal
    within find('tr', text: @verified_badge.name) do
      click_button 'Remove'
    end

    # Verify badge was removed
    assert_text 'Badge was successfully removed'
    assert_no_text @verified_badge.name
    assert_text @choice_badge.name
  end

  def test_badge_display_on_profiles
    # First assign a badge to the talent user
    BadgeAssignment.create!(
      badge_type: @verified_badge,
      user: @talent_user,
      admin: @admin,
      assigned_at: Time.current,
    )

    BadgeAssignment.create!(
      badge_type: @choice_badge,
      user: @talent_user,
      admin: @admin,
      assigned_at: Time.current,
    )

    # Login as scout to view talent profile
    login_as(@scout_user)

    # Visit talent profile
    visit talent_profile_path(@talent_user)

    # Verify badges are displayed
    assert_selector "[data-controller='badge']", count: 2
    assert_text @verified_badge.name
    assert_text @choice_badge.name

    # Test badge hover effects
    badge_element =
      find("[data-controller='badge']", text: @verified_badge.name)
    badge_element.hover

    # Verify tooltip appears
    assert_text @verified_badge.description

    # Test badge styling and holographic effects
    assert badge_element.has_css?('.badge')
    assert badge_element.has_css?('.badge-icon')
    assert badge_element.has_css?('.badge-name')
  end

  def test_badge_display_in_search_results
    # Assign badges to talent user
    BadgeAssignment.create!(
      badge_type: @verified_badge,
      user: @talent_user,
      admin: @admin,
      assigned_at: Time.current,
    )

    # Login as scout
    login_as(@scout_user)

    # Navigate to talent search
    visit scout_talent_path

    # Verify compact badges appear in search results
    within find('.talent-card', text: @talent_user.name) do
      assert_selector "[data-controller='badge']"
      assert_text @verified_badge.name
      assert_css '.badge-compact'
    end

    # Test compact badge hover
    compact_badge = find('.badge-compact', text: @verified_badge.name)
    compact_badge.hover

    # Verify compact tooltip
    assert_text @verified_badge.description
  end

  def test_badge_interactions_and_analytics
    # Assign badge to talent user
    BadgeAssignment.create!(
      badge_type: @verified_badge,
      user: @talent_user,
      admin: @admin,
      assigned_at: Time.current,
    )

    # Login as scout
    login_as(@scout_user)

    # Visit talent profile
    visit talent_profile_path(@talent_user)

    # Test badge click interaction
    badge_element =
      find("[data-controller*='badge-click']", text: @verified_badge.name)
    badge_element.click

    # Verify badge modal opens
    assert_selector '.badge-modal', visible: true
    assert_text @verified_badge.name
    assert_text @verified_badge.description

    # Close modal
    find('.badge-modal .close-button').click

    # Verify modal closes
    assert_no_selector '.badge-modal', visible: true
  end

  def test_badge_filtering
    # Assign different badges to different users
    BadgeAssignment.create!(
      badge_type: @verified_badge,
      user: @talent_user,
      admin: @admin,
      assigned_at: Time.current,
    )

    # Create another talent user with different badge
    @talent_user_2 =
      User.create!(
        email: '<EMAIL>',
        password: 'password123',
        first_name: 'Jane',
        last_name: 'Smith',
      )
    @talent_user_2.add_role(:talent)

    BadgeAssignment.create!(
      badge_type: @choice_badge,
      user: @talent_user_2,
      admin: @admin,
      assigned_at: Time.current,
    )

    # Login as scout
    login_as(@scout_user)

    # Navigate to talent search
    visit scout_talent_path

    # Test badge filtering
    select @verified_badge.name, from: 'Badge Filter'
    click_button 'Apply Filters'

    # Verify only verified talent appears
    assert_text @talent_user.name
    assert_no_text @talent_user_2.name

    # Clear filters
    click_button 'Clear Filters'

    # Verify both talents appear
    assert_text @talent_user.name
    assert_text @talent_user_2.name
  end

  def test_badge_performance_and_accessibility
    # Assign badge to talent user
    BadgeAssignment.create!(
      badge_type: @verified_badge,
      user: @talent_user,
      admin: @admin,
      assigned_at: Time.current,
    )

    # Login as scout
    login_as(@scout_user)

    # Visit talent profile
    visit talent_profile_path(@talent_user)

    # Test accessibility attributes
    badge_element =
      find("[data-controller='badge']", text: @verified_badge.name)

    assert badge_element.has_css?("[role='img']")
    assert badge_element.has_css?('[aria-label]')

    # Test reduced motion support
    page.execute_script(
      "document.documentElement.style.setProperty('--prefers-reduced-motion', 'reduce')",
    )

    badge_element.hover

    # Verify animations are disabled in reduced motion mode
    assert badge_element.has_css?('.badge')

    # Test high contrast mode
    page.execute_script(
      "document.documentElement.style.setProperty('--prefers-contrast', 'high')",
    )

    # Verify high contrast styling
    assert badge_element.has_css?('.badge')

    # Test keyboard navigation
    badge_element.send_keys(:tab)
    assert badge_element.has_css?(':focus')
  end

  # Helper method to login as a user
  def login_as(user)
    visit sign_in_path
    fill_in 'Email', with: user.email
    fill_in 'Password', with: 'Secret1*3*5*'
    click_button 'Log in'
  end
end
