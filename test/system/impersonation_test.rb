require "application_system_test_case"

class ImpersonationTest < ApplicationSystemTestCase
  def setup
    @admin = users(:admin)
    @scout = users(:scout)
    @talent = users(:talent)
    
    # Ensure users have proper setup
    @scout.update!(onboarding_completed: true)
    @talent.update!(talent_signup_completed: true)
  end

  test "admin can start impersonation through confirmation modal" do
    # Sign in as admin
    sign_in_as(@admin)
    
    # Go to user management
    visit super_admin_users_path
    
    # Find the impersonate button for the scout user
    within("tr", text: @scout.email) do
      click_button "Impersonate"
    end
    
    # Should see confirmation modal
    assert_selector "[data-impersonation-target='modal']", visible: true
    assert_text "Confirm User Impersonation"
    assert_text @scout.name
    assert_text @scout.email
    assert_text "Your session will be logged"
    assert_text "automatically expire after 2 hours"
    
    # Confirm impersonation
    click_button "Start Impersonation"
    
    # Should be redirected to user's landing page
    assert_current_path launchpad_path
    assert_text "Successfully started impersonating"
    
    # Should see impersonation banner
    assert_selector ".bg-amber-100", text: "Impersonating"
    assert_text @scout.name
    assert_text @scout.email
    assert_text @admin.name
    assert_button "Exit Impersonation"
  end

  test "admin can cancel impersonation from confirmation modal" do
    # Sign in as admin
    sign_in_as(@admin)
    
    # Go to user management
    visit super_admin_users_path
    
    # Click impersonate button
    within("tr", text: @scout.email) do
      click_button "Impersonate"
    end
    
    # Should see confirmation modal
    assert_selector "[data-impersonation-target='modal']", visible: true
    
    # Cancel impersonation
    click_button "Cancel"
    
    # Should close modal and stay on user management page
    assert_no_selector "[data-impersonation-target='modal']", visible: true
    assert_current_path super_admin_users_path
    
    # Should not be impersonating
    assert_no_text "Impersonating"
  end

  test "admin can end impersonation through banner" do
    # Start impersonation programmatically
    sign_in_as(@admin)
    visit super_admin_users_path
    
    within("tr", text: @scout.email) do
      click_button "Impersonate"
    end
    
    click_button "Start Impersonation"
    
    # Should see impersonation banner
    assert_selector ".bg-amber-100"
    
    # Click exit impersonation with confirmation
    accept_confirm do
      click_button "Exit Impersonation"
    end
    
    # Should be back in admin panel
    assert_current_path super_admin_users_path
    assert_text "Successfully ended impersonation"
    
    # Should not see impersonation banner
    assert_no_selector ".bg-amber-100"
  end

  test "impersonation banner appears on different pages" do
    # Start impersonation
    start_impersonation(@admin, @scout)
    
    # Check banner appears on different pages
    pages_to_check = [
      launchpad_path,
      scout_jobs_path
    ]
    
    pages_to_check.each do |path|
      visit path
      assert_selector ".bg-amber-100", text: "Impersonating"
      assert_text @scout.name
      assert_button "Exit Impersonation"
    end
  end

  test "modal can be closed with escape key" do
    # Sign in as admin
    sign_in_as(@admin)
    visit super_admin_users_path
    
    # Open modal
    within("tr", text: @scout.email) do
      click_button "Impersonate"
    end
    
    # Should see modal
    assert_selector "[data-impersonation-target='modal']", visible: true
    
    # Press escape key
    find("body").send_keys(:escape)
    
    # Modal should close
    assert_no_selector "[data-impersonation-target='modal']", visible: true
  end

  test "modal can be closed by clicking overlay" do
    # Sign in as admin
    sign_in_as(@admin)
    visit super_admin_users_path
    
    # Open modal
    within("tr", text: @scout.email) do
      click_button "Impersonate"
    end
    
    # Should see modal
    assert_selector "[data-impersonation-target='modal']", visible: true
    
    # Click on overlay (outside modal content)
    find("[data-impersonation-target='modal']").click
    
    # Modal should close
    assert_no_selector "[data-impersonation-target='modal']", visible: true
  end

  test "impersonation button is disabled for super admins" do
    # Sign in as admin
    sign_in_as(@admin)
    visit super_admin_users_path
    
    # Should not see impersonate button for super admin users
    within("tr", text: @admin.email) do
      assert_text "Super Admin"
      assert_no_button "Impersonate"
    end
  end

  test "restricted actions show appropriate error messages" do
    # Start impersonation
    start_impersonation(@admin, @scout)
    
    # Try to access a restricted page
    visit account_profile_path
    
    # Should be redirected with error message
    assert_current_path root_path
    assert_text "not allowed during impersonation for security reasons"
  end

  test "impersonation session timeout redirects to sign in" do
    # This test would require manipulating time or session data
    # For now, we'll test the UI elements are in place
    start_impersonation(@admin, @scout)
    
    # Verify banner shows timeout information in modal
    visit super_admin_users_path
    within("tr", text: @talent.email) do
      click_button "Impersonate"
    end
    
    assert_text "automatically expire after 2 hours"
    click_button "Cancel"
  end

  test "confirmation modal shows security warnings" do
    # Sign in as admin
    sign_in_as(@admin)
    visit super_admin_users_path
    
    # Open confirmation modal
    within("tr", text: @scout.email) do
      click_button "Impersonate"
    end
    
    # Check all security warnings are present
    assert_text "Your session will be logged with IP address and timestamp"
    assert_text "Certain sensitive actions will be restricted during impersonation"
    assert_text "The session will automatically expire after 2 hours"
    assert_text "The user will see an impersonation banner while you're logged in"
    
    click_button "Cancel"
  end

  private

  def sign_in_as(user)
    session = user.sessions.create!
    cookies.signed[:session_token] = session.id
    Current.user = user
  end

  def start_impersonation(admin, target_user)
    sign_in_as(admin)
    visit super_admin_users_path
    
    within("tr", text: target_user.email) do
      click_button "Impersonate"
    end
    
    click_button "Start Impersonation"
  end
end
