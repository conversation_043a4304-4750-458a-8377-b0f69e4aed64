require 'application_system_test_case'

class BadgeFilterTagsTest < ApplicationSystemTestCase
  # Don't load fixtures to avoid foreign key issues
  self.use_transactional_tests = false

  def self.fixtures(*args)
    # Override to prevent fixture loading
  end

  setup do
    # Create a test scout user
    @scout =
      User.create!(
        email: '<EMAIL>',
        password: 'password123123',
        verified: true,
        first_name: '<PERSON><PERSON>',
        last_name: 'Scout',
        time_zone: 'UTC',
        onboarding_completed: true,
        onboarding_step: 'completed',
        signup_intent: 'scout',
        scout_signup_completed: true,
        talent_signup_completed: false,
      )

    # Create an organization for the scout
    @organization =
      Organization.create!(
        name: 'Badge Test Organization',
        slug: 'badge-test-org',
      )

    # Add scout to organization
    OrganizationMembership.create!(
      user: @scout,
      organization: @organization,
      org_role: 'admin',
    )

    @scout.update!(last_logged_in_organization_id: @organization.id)

    # Create talent users with badges for testing
    @talent_with_choice =
      User.create!(
        email: '<EMAIL>',
        password: 'password123123',
        verified: true,
        first_name: 'Choice',
        last_name: 'Talent',
        talent_signup_completed: true,
      )

    @talent_with_expert =
      User.create!(
        email: '<EMAIL>',
        password: 'password123123',
        verified: true,
        first_name: 'Expert',
        last_name: 'Talent',
        talent_signup_completed: true,
      )

    # Create talent profiles
    @profile_choice =
      TalentProfile.create!(
        user: @talent_with_choice,
        headline: 'Choice Writer',
        about: 'Top-rated writer',
        availability_status: 'available',
      )

    @profile_expert =
      TalentProfile.create!(
        user: @talent_with_expert,
        headline: 'Expert Writer',
        about: 'Expert in writing',
        availability_status: 'available',
      )

    # Create badge types
    @choice_badge =
      BadgeType.create!(
        name: 'Ghostwrote Choice',
        description: 'Top-rated ghostwriter',
        icon: 'star',
        background_color: '#FFD700',
        text_color: '#000000',
        priority: 1,
      )

    @expert_badge =
      BadgeType.create!(
        name: 'Expert Writer',
        description: 'Proven expertise',
        icon: 'certificate',
        background_color: '#4F46E5',
        text_color: '#FFFFFF',
        priority: 2,
      )

    # Assign badges
    BadgeAssignment.create!(
      user: @talent_with_choice,
      badge_type: @choice_badge,
      assigned_by: @scout,
      assigned_at: 1.day.ago,
    )

    BadgeAssignment.create!(
      user: @talent_with_expert,
      badge_type: @expert_badge,
      assigned_by: @scout,
      assigned_at: 1.day.ago,
    )
  end

  teardown do
    # Clean up test data
    BadgeAssignment.delete_all
    BadgeType.delete_all
    TalentProfile.delete_all
    OrganizationMembership.delete_all
    Organization.delete_all
    User.delete_all
  end

  test 'displays badge filter tags when badge filters are applied' do
    # Sign in as scout
    visit sign_in_path
    fill_in 'Email', with: @scout.email
    fill_in 'Password', with: 'password123123'
    click_button 'Sign in'

    # Navigate to talent page with badge filter applied via URL
    visit scout_talent_index_path(badge_types: 'Ghostwrote Choice')

    # Verify badge filter tag is displayed
    assert_text 'Ghostwrote Choice'

    # Verify the filter tag has correct data attributes
    assert_selector '[data-filter-type="badge_types"][data-filter-value="Ghostwrote Choice"]'

    # Verify remove button is present
    within(
      '[data-filter-type="badge_types"][data-filter-value="Ghostwrote Choice"]',
    ) { assert_selector 'button' }

    # Verify clear all filters button is present
    assert_text 'Clear all filters'
  end

  test 'displays multiple badge filter tags when multiple badges are selected' do
    # Sign in as scout
    visit sign_in_path
    fill_in 'Email', with: @scout.email
    fill_in 'Password', with: 'password123123'
    click_button 'Sign in'

    # Navigate to talent page with multiple badge filters
    visit scout_talent_index_path(
            badge_types: 'Ghostwrote Choice,Expert Writer',
          )

    # Verify both badge filter tags are displayed
    assert_text 'Ghostwrote Choice'
    assert_text 'Expert Writer'

    # Verify both tags have correct data attributes
    assert_selector '[data-filter-type="badge_types"][data-filter-value="Ghostwrote Choice"]'
    assert_selector '[data-filter-type="badge_types"][data-filter-value="Expert Writer"]'

    # Verify both have remove buttons
    within(
      '[data-filter-type="badge_types"][data-filter-value="Ghostwrote Choice"]',
    ) { assert_selector 'button' }

    within(
      '[data-filter-type="badge_types"][data-filter-value="Expert Writer"]',
    ) { assert_selector 'button' }
  end

  test 'removes individual badge filter when X button is clicked' do
    # Sign in as scout
    visit sign_in_path
    fill_in 'Email', with: @scout.email
    fill_in 'Password', with: 'password123123'
    click_button 'Sign in'

    # Navigate to talent page with multiple badge filters
    visit scout_talent_index_path(
            badge_types: 'Ghostwrote Choice,Expert Writer',
          )

    # Verify both filters are displayed
    assert_text 'Ghostwrote Choice'
    assert_text 'Expert Writer'

    # Click remove button for Ghostwrote Choice filter
    within(
      '[data-filter-type="badge_types"][data-filter-value="Ghostwrote Choice"]',
    ) { click_button }

    # Verify Ghostwrote Choice filter is removed but Expert Writer remains
    assert_no_text 'Ghostwrote Choice'
    assert_text 'Expert Writer'

    # Verify URL is updated correctly
    assert_current_path scout_talent_index_path
    assert page.current_url.include?('badge_types=Expert+Writer')
    refute page.current_url.include?('Ghostwrote+Choice')
  end

  test 'clears badge filters when clear all button is clicked' do
    # Sign in as scout
    visit sign_in_path
    fill_in 'Email', with: @scout.email
    fill_in 'Password', with: 'password123123'
    click_button 'Sign in'

    # Navigate to talent page with badge filters and other filters
    visit scout_talent_index_path(
            badge_types: 'Ghostwrote Choice',
            availability_status: 'available',
          )

    # Verify filters are displayed
    assert_text 'Ghostwrote Choice'
    assert_text 'Available'

    # Click clear all filters
    click_button 'Clear all filters'

    # Verify all filters are removed
    assert_no_text 'Ghostwrote Choice'
    assert_no_text 'Available'

    # Verify URL is clean
    assert_current_path scout_talent_index_path
    refute page.current_url.include?('badge_types')
    refute page.current_url.include?('availability_status')
  end

  test 'preserves search query when removing badge filters' do
    # Sign in as scout
    visit sign_in_path
    fill_in 'Email', with: @scout.email
    fill_in 'Password', with: 'password123123'
    click_button 'Sign in'

    # Navigate to talent page with search and badge filter
    visit scout_talent_index_path(
            query: 'test search',
            badge_types: 'Ghostwrote Choice',
          )

    # Verify filter is displayed
    assert_text 'Ghostwrote Choice'

    # Remove the badge filter
    within(
      '[data-filter-type="badge_types"][data-filter-value="Ghostwrote Choice"]',
    ) { click_button }

    # Verify search query is preserved
    assert page.current_url.include?('query=test+search')
    refute page.current_url.include?('badge_types')
  end

  test 'badge filters work with other filter types' do
    # Sign in as scout
    visit sign_in_path
    fill_in 'Email', with: @scout.email
    fill_in 'Password', with: 'password123123'
    click_button 'Sign in'

    # Navigate to talent page with badge and availability filters
    visit scout_talent_index_path(
            badge_types: 'Expert Writer',
            availability_status: 'available',
          )

    # Verify both filter types are displayed
    assert_text 'Expert Writer'
    assert_text 'Available'

    # Remove only the badge filter
    within(
      '[data-filter-type="badge_types"][data-filter-value="Expert Writer"]',
    ) { click_button }

    # Verify badge filter is removed but availability filter remains
    assert_no_text 'Expert Writer'
    assert_text 'Available'

    # Verify URL reflects the change
    assert page.current_url.include?('availability_status=available')
    refute page.current_url.include?('badge_types')
  end
end
