require "application_system_test_case"

class TalentFilterTagsTest < ApplicationSystemTestCase
  # Don't load fixtures to avoid foreign key issues
  self.use_transactional_tests = false

  def self.fixtures(*args)
    # Override to prevent fixture loading
  end

  setup do
    # Create a test user with scout privileges
    @user = User.create!(
      email: '<EMAIL>',
      password: 'password123123',
      verified: true,
      first_name: 'Test',
      last_name: 'Scout',
      time_zone: 'UTC',
      onboarding_completed: true,
      onboarding_step: 'completed',
      signup_intent: 'scout',
      scout_signup_completed: true,
      talent_signup_completed: false,
    )

    # Create an organization for the scout
    @organization = Organization.create!(name: 'Test Organization', slug: 'test-org')

    # Add scout to organization
    OrganizationMembership.create!(
      user: @user,
      organization: @organization,
      org_role: 'admin',
    )

    @user.update!(last_logged_in_organization_id: @organization.id)
  end

  teardown do
    # Clean up test data
    OrganizationMembership.delete_all
    Organization.delete_all
    User.delete_all
  end

  test "displays active filter tags when filters are applied" do
    # Sign in as scout
    visit sign_in_path
    fill_in 'Email', with: @user.email
    fill_in 'Password', with: 'password123123'
    click_button 'Sign in'

    # Navigate to talent page with filters applied via URL
    visit scout_talent_index_path(
      bookmarked: 'true',
      niches: 'Technology,Marketing',
      ghostwriter_type: 'Expert',
      availability_status: 'available'
    )

    # Verify filter tags are displayed
    assert_text 'Bookmarked Only'
    assert_text 'Technology'
    assert_text 'Marketing'
    assert_text 'Expert'
    assert_text 'Available'

    # Verify category labels are shown
    assert_text 'Bookmarked Talents:'
    assert_text 'Content Topics:'
    assert_text 'Ghostwriter Type:'
    assert_text 'Availability:'

    # Verify clear all filters button is present
    assert_text 'Clear all filters'
  end

  test "removes individual filter when X button is clicked" do
    # Sign in as scout
    visit sign_in_path
    fill_in 'Email', with: @user.email
    fill_in 'Password', with: 'password123123'
    click_button 'Sign in'

    # Navigate to talent page with multiple filters
    visit scout_talent_index_path(
      niches: 'Technology,Marketing',
      ghostwriter_type: 'Expert'
    )

    # Verify both filters are displayed
    assert_text 'Technology'
    assert_text 'Marketing'
    assert_text 'Expert'

    # Click remove button for Technology filter
    within('[data-filter-type="niches"][data-filter-value="Technology"]') do
      click_button
    end

    # Verify Technology filter is removed but others remain
    assert_no_text 'Technology'
    assert_text 'Marketing'
    assert_text 'Expert'

    # Verify URL is updated correctly
    assert_current_path scout_talent_index_path
    assert page.current_url.include?('niches=Marketing')
    assert page.current_url.include?('ghostwriter_type=Expert')
    refute page.current_url.include?('Technology')
  end

  test "clears all filters when clear all button is clicked" do
    # Sign in as scout
    visit sign_in_path
    fill_in 'Email', with: @user.email
    fill_in 'Password', with: 'password123123'
    click_button 'Sign in'

    # Navigate to talent page with multiple filters
    visit scout_talent_index_path(
      bookmarked: 'true',
      niches: 'Technology',
      ghostwriter_type: 'Expert'
    )

    # Verify filters are displayed
    assert_text 'Bookmarked Only'
    assert_text 'Technology'
    assert_text 'Expert'

    # Click clear all filters
    click_button 'Clear all filters'

    # Verify all filters are removed
    assert_no_text 'Bookmarked Only'
    assert_no_text 'Technology'
    assert_no_text 'Expert'

    # Verify URL is clean
    assert_current_path scout_talent_index_path
    refute page.current_url.include?('bookmarked')
    refute page.current_url.include?('niches')
    refute page.current_url.include?('ghostwriter_type')
  end

  test "preserves search query when removing filters" do
    # Sign in as scout
    visit sign_in_path
    fill_in 'Email', with: @user.email
    fill_in 'Password', with: 'password123123'
    click_button 'Sign in'

    # Navigate to talent page with search and filters
    visit scout_talent_index_path(
      query: 'test search',
      niches: 'Technology'
    )

    # Verify filter is displayed
    assert_text 'Technology'

    # Remove the filter
    within('[data-filter-type="niches"][data-filter-value="Technology"]') do
      click_button
    end

    # Verify search query is preserved
    assert page.current_url.include?('query=test+search')
    refute page.current_url.include?('niches')
  end

  test "does not display filter tags section when no filters are active" do
    # Sign in as scout
    visit sign_in_path
    fill_in 'Email', with: @user.email
    fill_in 'Password', with: 'password123123'
    click_button 'Sign in'

    # Navigate to talent page without filters
    visit scout_talent_index_path

    # Verify no filter tags are displayed
    assert_no_text 'Clear all filters'
    assert_no_selector '[data-talent-filter-tags-target="container"]'
  end
end
