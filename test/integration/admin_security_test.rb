require 'test_helper'

class AdminSecurityTest < ActionDispatch::IntegrationTest
  setup do
    # Create roles
    @superadmin_role = Role.find_or_create_by!(name: 'superadmin')
    @support_role = Role.find_or_create_by!(name: 'support')
    @readonly_role = Role.find_or_create_by!(name: 'readonly')
    @scout_role = Role.find_or_create_by!(name: 'scout')

    # Create admin users
    @superadmin = User.create!(
      email: '<EMAIL>',
      password: 'password123123',
      verified: true,
      first_name: 'Super',
      last_name: 'Admin',
      onboarding_completed: true,
      onboarding_step: 'completed'
    )
    UserRole.create!(user: @superadmin, role: @superadmin_role)

    @support_admin = User.create!(
      email: '<EMAIL>',
      password: 'password123123',
      verified: true,
      first_name: 'Support',
      last_name: 'Admin',
      onboarding_completed: true,
      onboarding_step: 'completed'
    )
    UserRole.create!(user: @support_admin, role: @support_role)

    @readonly_admin = User.create!(
      email: '<EMAIL>',
      password: 'password123123',
      verified: true,
      first_name: '<PERSON><PERSON><PERSON><PERSON>',
      last_name: 'Admin',
      onboarding_completed: true,
      onboarding_step: 'completed'
    )
    UserRole.create!(user: @readonly_admin, role: @readonly_role)

    @regular_user = User.create!(
      email: '<EMAIL>',
      password: 'password123123',
      verified: true,
      first_name: 'Regular',
      last_name: 'User',
      onboarding_completed: true,
      onboarding_step: 'completed'
    )
    UserRole.create!(user: @regular_user, role: @scout_role)

    # Create organization
    @organization = Organization.create!(name: 'Test Security Organization')
    [@superadmin, @support_admin, @readonly_admin, @regular_user].each do |user|
      OrganizationMembership.create!(
        user: user,
        organization: @organization,
        org_role: 'member'
      )
      user.update!(last_logged_in_organization_id: @organization.id)
    end
  end

  test 'admin routes require authentication' do
    admin_routes = [
      super_admin_admin_dashboard_path,
      super_admin_admin_users_path,
      super_admin_admin_jobs_path,
      super_admin_admin_organizations_path,
      super_admin_admin_chat_requests_path,
      super_admin_admin_conversations_path,
      super_admin_admin_audit_logs_path
    ]

    admin_routes.each do |route|
      get route
      assert_redirected_to sign_in_path, "Route #{route} should require authentication"
    end
  end

  test 'admin routes require admin role' do
    sign_in_as(@regular_user)

    admin_routes = [
      super_admin_admin_dashboard_path,
      super_admin_admin_users_path,
      super_admin_admin_jobs_path,
      super_admin_admin_organizations_path,
      super_admin_admin_chat_requests_path,
      super_admin_admin_conversations_path,
      super_admin_admin_audit_logs_path
    ]

    admin_routes.each do |route|
      get route
      assert_redirected_to root_path, "Route #{route} should require admin role"
    end
  end

  test 'readonly admin cannot access edit actions' do
    sign_in_as(@readonly_admin)

    # Test edit routes that should be forbidden
    get edit_super_admin_admin_user_path(@regular_user)
    assert_response :forbidden

    get edit_super_admin_admin_organization_path(@organization)
    assert_response :forbidden
  end

  test 'readonly admin cannot perform update actions' do
    sign_in_as(@readonly_admin)

    # Test update actions that should be forbidden
    patch super_admin_admin_user_path(@regular_user), params: {
      user: { first_name: 'Should not update' }
    }
    assert_response :forbidden

    patch super_admin_admin_organization_path(@organization), params: {
      organization: { name: 'Should not update' }
    }
    assert_response :forbidden
  end

  test 'readonly admin cannot perform delete actions' do
    sign_in_as(@readonly_admin)

    # Test delete actions that should be forbidden
    delete super_admin_admin_user_path(@regular_user)
    assert_response :forbidden

    delete super_admin_admin_organization_path(@organization)
    assert_response :forbidden
  end

  test 'support admin cannot access superadmin-only features' do
    sign_in_as(@support_admin)

    # Test audit logs access (superadmin only)
    get super_admin_admin_audit_logs_path
    assert_response :forbidden

    # Test role management (superadmin only)
    get super_admin_admin_roles_path
    assert_response :forbidden
  end

  test 'support admin cannot delete users' do
    sign_in_as(@support_admin)

    delete super_admin_admin_user_path(@regular_user)
    assert_response :forbidden
  end

  test 'support admin cannot delete organizations' do
    sign_in_as(@support_admin)

    delete super_admin_admin_organization_path(@organization)
    assert_response :forbidden
  end

  test 'CSRF protection is enabled for admin actions' do
    sign_in_as(@superadmin)

    # Test that CSRF token is required for state-changing actions
    patch super_admin_admin_user_path(@regular_user), params: {
      user: { first_name: 'Updated Name' }
    }, headers: { 'X-CSRF-Token' => 'invalid_token' }

    # Should either be forbidden or redirect (depending on Rails CSRF handling)
    assert_response :forbidden
  end

  test 'admin actions are logged in audit trail' do
    sign_in_as(@superadmin)

    # Perform an admin action
    assert_difference 'AdminAuditLog.count', 1 do
      patch super_admin_admin_user_path(@regular_user), params: {
        user: { first_name: 'Updated Name' }
      }
    end

    # Verify audit log was created
    audit_log = AdminAuditLog.last
    assert_equal 'update', audit_log.action
    assert_equal 'SuperAdmin::AdminUsersController', audit_log.controller
    assert_equal @regular_user, audit_log.resource
    assert_equal @superadmin, audit_log.admin_user
  end

  test 'sensitive data is not exposed in error messages' do
    sign_in_as(@superadmin)

    # Try to access non-existent resource
    get super_admin_admin_user_path(999999)
    assert_response :not_found

    # Error page should not expose sensitive information
    assert_not_includes response.body, 'password'
    assert_not_includes response.body, 'secret'
    assert_not_includes response.body, 'token'
  end

  test 'admin interface prevents SQL injection' do
    sign_in_as(@superadmin)

    # Test search with SQL injection attempt
    malicious_search = "'; DROP TABLE users; --"
    get super_admin_admin_users_path, params: { search: malicious_search }
    assert_response :success

    # Verify users table still exists by checking user count
    assert User.count > 0
  end

  test 'admin interface prevents XSS attacks' do
    sign_in_as(@superadmin)

    # Create user with potentially malicious content
    malicious_user = User.create!(
      email: '<EMAIL>',
      password: 'password123123',
      verified: true,
      first_name: '<script>alert("xss")</script>',
      last_name: 'User',
      onboarding_completed: true,
      onboarding_step: 'completed'
    )

    get super_admin_admin_user_path(malicious_user)
    assert_response :success

    # Verify script tags are escaped
    assert_not_includes response.body, '<script>alert("xss")</script>'
    assert_includes response.body, '&lt;script&gt;'
  end

  test 'admin interface enforces rate limiting on sensitive actions' do
    sign_in_as(@superadmin)

    # This test would require implementing rate limiting
    # For now, we'll just verify the structure is in place
    assert_respond_to @superadmin, :can_access_admin?
  end

  test 'admin sessions have appropriate security headers' do
    sign_in_as(@superadmin)
    get super_admin_admin_dashboard_path

    # Check for security headers
    assert_not_nil response.headers['X-Frame-Options']
    assert_not_nil response.headers['X-Content-Type-Options']
    assert_not_nil response.headers['X-XSS-Protection']
  end

  test 'admin interface validates file uploads securely' do
    sign_in_as(@superadmin)

    # This test would require implementing file upload functionality
    # For now, we'll verify the basic structure
    assert_respond_to @superadmin, :can?
  end

  test 'admin interface prevents privilege escalation' do
    sign_in_as(@support_admin)

    # Support admin should not be able to grant themselves superadmin role
    patch super_admin_admin_user_path(@support_admin), params: {
      user: { role_ids: [@superadmin_role.id] }
    }

    # Should either be forbidden or ignored
    @support_admin.reload
    assert_not @support_admin.superadmin?
  end

  test 'admin interface prevents mass assignment vulnerabilities' do
    sign_in_as(@superadmin)

    # Try to update protected attributes
    patch super_admin_admin_user_path(@regular_user), params: {
      user: {
        first_name: 'Updated Name',
        id: 999999, # Should be ignored
        created_at: 1.year.ago # Should be ignored
      }
    }

    @regular_user.reload
    assert_equal 'Updated Name', @regular_user.first_name
    assert_not_equal 999999, @regular_user.id
    assert @regular_user.created_at > 1.day.ago
  end

  test 'admin interface logs failed authentication attempts' do
    # Try to access admin area with wrong credentials
    post sign_in_path, params: {
      email: @superadmin.email,
      password: 'wrong_password'
    }

    # This would require implementing failed login logging
    # For now, verify basic authentication works
    assert_redirected_to sign_in_path
  end

  test 'admin interface enforces session timeout' do
    sign_in_as(@superadmin)

    # This would require implementing session timeout
    # For now, verify session exists
    assert session[:user_id].present?
  end

  test 'admin interface prevents concurrent admin sessions' do
    # This test would require implementing concurrent session prevention
    # For now, verify basic session functionality
    sign_in_as(@superadmin)
    assert session[:user_id] == @superadmin.id
  end

  private

  def sign_in_as(user)
    post sign_in_path, params: { email: user.email, password: 'password123123' }
    follow_redirect!
  end
end
