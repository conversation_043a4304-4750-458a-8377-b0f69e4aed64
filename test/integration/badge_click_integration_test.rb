require 'test_helper'

class BadgeClickIntegrationTest < ActionDispatch::IntegrationTest
  def setup
    @scout = users(:scout)
    @talent = users(:talent)

    # Clear existing badge data to avoid conflicts
    BadgeAssignment.delete_all
    BadgeType.delete_all

    # Temporarily disable color contrast validation for tests
    ENV['VALIDATE_BADGE_COLOR_CONTRAST'] = 'false'

    # Create badge types for testing with unique names
    @verified_badge =
      BadgeType.create!(
        name: "Test Verified #{SecureRandom.hex(4)}",
        description: 'This user has been verified by our team',
        background_color: '#10B981',
        text_color: '#FFFFFF',
        icon: 'check-circle',
      )

    @expert_badge =
      BadgeType.create!(
        name: "Test Expert Writer #{SecureRandom.hex(4)}",
        description: 'Recognized expert in their field',
        background_color: '#8B5CF6',
        text_color: '#FFFFFF',
        icon: 'star',
      )

    # Assign badges to talent
    @admin = users(:super_admin)
    @verified_assignment =
      BadgeAssignment.create!(
        user: @talent,
        badge_type: @verified_badge,
        admin: @admin,
        assigned_at: Time.current,
      )
    @expert_assignment =
      BadgeAssignment.create!(
        user: @talent,
        badge_type: @expert_badge,
        admin: @admin,
        assigned_at: Time.current,
      )
  end

  def teardown
    # Restore environment variable
    ENV['VALIDATE_BADGE_COLOR_CONTRAST'] = 'true'
  end

  test 'badge components include required data attributes for modal integration' do
    sign_in_as(@scout)

    # Visit a page that renders badges (using talent discovery as example)
    get scout_talent_path
    assert_response :success

    # Check that badge modal is included in the page
    assert_select '#badge-modal[data-controller="badge-modal"]'
    assert_select '#badge-modal[role="dialog"]'
    assert_select '#badge-modal[aria-modal="true"]'
  end

  test 'badge components have correct controller and action attributes' do
    # Test the badge partial directly by rendering it
    badge_html = render_badge_partial(@verified_badge, @verified_assignment)

    # Parse the HTML to check attributes
    doc = Nokogiri::HTML::DocumentFragment.parse(badge_html)
    badge_element = doc.at_css('[data-controller]')

    assert badge_element, 'Badge should have data-controller attribute'
    assert_includes badge_element['data-controller'],
                    'badge-click',
                    'Badge should include badge-click controller'
    assert_includes badge_element['data-action'],
                    'click->badge-click#click',
                    'Badge should have click action'

    # Check required data attributes for badge modal
    assert badge_element['data-badge-click-badge-id-value'],
           'Badge should have badge ID data attribute'
    assert badge_element['data-badge-click-badge-name-value'],
           'Badge should have badge name data attribute'
    assert badge_element['data-badge-click-badge-description-value'],
           'Badge should have badge description data attribute'
  end

  test 'compact badge components have correct controller and action attributes' do
    # Test the compact badge partial
    compact_badge_html =
      render_compact_badge_partial(@verified_badge, @verified_assignment)

    # Parse the HTML to check attributes
    doc = Nokogiri::HTML::DocumentFragment.parse(compact_badge_html)
    badge_element = doc.at_css('[data-controller]')

    assert badge_element, 'Compact badge should have data-controller attribute'
    assert_includes badge_element['data-controller'],
                    'badge-click',
                    'Compact badge should include badge-click controller'
    assert_includes badge_element['data-action'],
                    'click->badge-click#click',
                    'Compact badge should have click action'

    # Check required data attributes for badge modal
    assert badge_element['data-badge-click-badge-id-value'],
           'Compact badge should have badge ID data attribute'
    assert badge_element['data-badge-click-badge-name-value'],
           'Compact badge should have badge name data attribute'
  end

  test 'badge modal is included in all layout files' do
    # Test scout layout
    sign_in_as(@scout)
    get scout_talent_path
    assert_response :success
    assert_select '#badge-modal',
                  'Badge modal should be present in scout layout'

    # Test talent layout (if talent has access to pages with badges)
    sign_in_as(@talent)
    get root_path # or another appropriate talent-accessible path
    assert_response :success
    # Note: This test assumes talent can access pages with badge modal
    # Adjust the path based on your application's routing
  end

  test 'badge data attributes contain correct information' do
    badge_html = render_badge_partial(@verified_badge, @verified_assignment)
    doc = Nokogiri::HTML::DocumentFragment.parse(badge_html)
    badge_element = doc.at_css('[data-controller]')

    # Verify badge data matches the badge type
    assert_equal @verified_badge.id.to_s,
                 badge_element['data-badge-click-badge-id-value']
    assert_equal @verified_badge.name,
                 badge_element['data-badge-click-badge-name-value']
    assert_equal @verified_badge.description,
                 badge_element['data-badge-click-badge-description-value']

    assert_equal @verified_badge.background_color,
                 badge_element['data-badge-click-badge-background-color-value']
    assert_equal @verified_badge.text_color,
                 badge_element['data-badge-click-badge-text-color-value']
  end

  test 'badge components have proper accessibility attributes' do
    badge_html = render_badge_partial(@verified_badge, @verified_assignment)
    doc = Nokogiri::HTML::DocumentFragment.parse(badge_html)
    badge_element = doc.at_css('[data-controller]')

    # Check accessibility attributes
    assert_equal 'img', badge_element['role']
    assert badge_element['aria-label'].present?, 'Badge should have aria-label'
    assert_includes badge_element['aria-label'],
                    @verified_badge.name,
                    'Badge aria-label should include badge name'

    # Check that icons have aria-hidden
    icon_element = doc.at_css('[data-phosphor]')
    if icon_element
      assert_equal 'true',
                   icon_element['aria-hidden'],
                   "Badge icon should have aria-hidden='true'"
    end
  end

  test 'badge components have proper cursor styling for interaction' do
    badge_html = render_badge_partial(@verified_badge, @verified_assignment)
    doc = Nokogiri::HTML::DocumentFragment.parse(badge_html)
    badge_element = doc.at_css('[data-controller]')

    # Check that badge has pointer cursor
    assert_includes badge_element['class'],
                    'cursor-pointer',
                    'Badge should have cursor-pointer class'

    # Check for hover effects
    assert_includes badge_element['class'],
                    'hover:shadow-md',
                    'Badge should have hover effects'
  end

  test 'badge modal has all required targets and actions' do
    sign_in_as(@scout)
    get scout_talent_path
    assert_response :success

    # Check modal structure
    assert_select '#badge-modal[data-badge-modal-target="container"]'
    assert_select '#badge-modal [data-badge-modal-target="backdrop"]'
    assert_select '#badge-modal [data-badge-modal-target="content"]'
    assert_select '#badge-modal [data-badge-modal-target="closeButton"]'
    assert_select '#badge-modal [data-badge-modal-target="title"]'
    assert_select '#badge-modal [data-badge-modal-target="description"]'
    assert_select '#badge-modal [data-badge-modal-target="badgeDisplay"]'

    # Check modal actions
    assert_select '#badge-modal [data-action*="badge-modal#close"]'
    assert_select '#badge-modal [data-action*="badge-modal#stopPropagation"]'
  end

  test 'JavaScript controllers are properly registered' do
    sign_in_as(@scout)
    get scout_talent_path
    assert_response :success

    # Check that the page includes the necessary JavaScript
    # This is a basic check - in a real app you might want to test the actual JS loading
    assert_select 'script', minimum: 1

    # The actual controller registration happens in app/javascript/controllers/index.js
    # which is tested by the presence of data-controller attributes in the HTML
    assert_select '[data-controller*="badge-modal"]'
  end

  private

  def render_badge_partial(badge_type, badge_assignment)
    # Create a minimal view context to render the partial
    view_context = ActionView::Base.new(ActionView::LookupContext.new([]))
    view_context.extend(ApplicationHelper)

    # Mock the necessary helper methods and variables
    def view_context.badge_type
      @badge_type
    end

    def view_context.badge_assignment
      @badge_assignment
    end

    view_context.instance_variable_set(:@badge_type, badge_type)
    view_context.instance_variable_set(:@badge_assignment, badge_assignment)

    # Render the partial with default options
    view_context.render(
      partial: 'shared/badge',
      locals: {
        badge_type: badge_type,
        badge_assignment: badge_assignment,
        context: 'profile_section',
        holographic_intensity: 0.6,
        rotation_factor: 10,
        glow_intensity: 0.8,
      },
    )
  end

  def render_compact_badge_partial(badge_type, badge_assignment)
    view_context = ActionView::Base.new(ActionView::LookupContext.new([]))
    view_context.extend(ApplicationHelper)

    view_context.instance_variable_set(:@badge_type, badge_type)
    view_context.instance_variable_set(:@badge_assignment, badge_assignment)

    view_context.render(
      partial: 'shared/badge_compact',
      locals: {
        badge_type: badge_type,
        badge_assignment: badge_assignment,
        context: 'card',
        icon_only: false,
      },
    )
  end
end
