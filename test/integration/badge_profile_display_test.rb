require 'test_helper'

class BadgeProfileDisplayTest < ActionDispatch::IntegrationTest
  def setup
    @talent_user = users(:talent)
    @scout_user = users(:scout)
    @admin_user = users(:admin)
    @organization = organizations(:one)

    # Create organization membership for scout user
    OrganizationMembership.create!(
      user: @scout_user,
      organization: @organization,
      org_role: 'owner',
    )

    # Create a talent profile for testing
    @talent_profile =
      TalentProfile.create!(
        user: @talent_user,
        headline: 'Expert Ghostwriter',
        about: 'Experienced writer with 5+ years in content creation',
        looking_for: 'Long-term writing partnerships',
        availability_status: 'available',
      )

    # Badge assignments already exist in fixtures for talent user
    # verified_talent_badge, expert_badge_assignment, expiring_soon_badge
  end

  test 'talent profile show page displays badges' do
    sign_in_as @talent_user
    get talent_profile_path

    assert_response :success

    # Talent user has verified, expert, and premium badges from fixtures
    assert_select '[data-controller="badge"]', minimum: 1
    assert_match 'Verified', response.body
  end

  test 'scout viewing talent profile sees badges' do
    sign_in_as @scout_user
    get scout_talent_path(@talent_profile)

    assert_response :success
    assert_select '[data-controller="badge"]', minimum: 1
    assert_match 'Verified', response.body
  end

  # Note: Skipping search results test due to Elasticsearch dependency
  # test 'talent cards in search results display badges' do
  #   sign_in_as @scout_user
  #   get scout_talent_index_path
  #   assert_response :success
  #   assert_select '[data-controller="badge"]', minimum: 1
  # end

  test 'user without badges shows no badges message' do
    # Create a user without badges
    user_without_badges =
      User.create!(
        email: '<EMAIL>',
        password: 'password123456',
        first_name: 'No',
        last_name: 'Badges',
        verified: true,
      )

    profile_without_badges =
      TalentProfile.create!(
        user: user_without_badges,
        headline: 'Test headline',
        about: 'Test about',
      )

    sign_in_as @scout_user
    get scout_talent_path(profile_without_badges)

    assert_response :success
    assert_match 'No badges assigned', response.body
  end

  test 'badge display respects priority ordering' do
    sign_in_as @scout_user
    get scout_talent_path(@talent_profile)

    assert_response :success

    # Check that badges appear in priority order
    # Talent user has verified (priority 0) and other badges from fixtures
    # Verified should appear first due to priority 0 (lower number = higher priority)
    assert_match 'Verified', response.body
  end

  test 'legacy achievement badges fallback works' do
    # Remove all badge assignments
    @talent_user.badge_assignments.destroy_all

    # Add legacy achievement badges
    @talent_profile.update!(
      achievement_badges: ['Expert Writer', 'Fast Turnaround'],
    )

    sign_in_as @scout_user
    get scout_talent_path(@talent_profile)

    assert_response :success
    assert_match 'Legacy Achievement Badges', response.body
    assert_match 'Expert Writer', response.body
    assert_match 'Fast Turnaround', response.body
  end

  test 'shared user_badges component renders correctly' do
    sign_in_as @scout_user
    get scout_talent_path(@talent_profile)

    assert_response :success

    # Check for holographic effect data attributes
    assert_select '[data-badge-holographic-intensity-value]'
    assert_select '[data-badge-rotation-factor-value]'
    assert_select '[data-badge-glow-intensity-value]'
    assert_select '[data-badge-prismatic-effect-value="true"]'
  end

  teardown do
    BadgeType.destroy_all
    BadgeAssignment.destroy_all
  end
end
