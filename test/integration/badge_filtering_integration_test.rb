require 'test_helper'

class BadgeFilteringIntegrationTest < ActionDispatch::IntegrationTest
  def setup
    # Create scout user and organization
    @scout =
      User.create!(
        email: '<EMAIL>',
        password: 'password123456',
        first_name: 'Scout',
        last_name: 'Integration',
        verified: true,
        scout_signup_completed: true,
      )

    @organization =
      Organization.create!(
        name: 'Integration Test Org',
        slug: 'integration-test',
      )
    OrganizationMembership.create!(
      user: @scout,
      organization: @organization,
      org_role: 'admin',
    )
    @scout.update!(last_logged_in_organization_id: @organization.id)

    # Create talent users with different badge combinations
    @talent_multi_badges =
      User.create!(
        email: '<EMAIL>',
        password: 'password123456',
        first_name: '<PERSON>',
        last_name: '<PERSON><PERSON>',
        verified: true,
        talent_signup_completed: true,
      )

    @talent_single_badge =
      User.create!(
        email: '<EMAIL>',
        password: 'password123456',
        first_name: 'Single',
        last_name: '<PERSON><PERSON>',
        verified: true,
        talent_signup_completed: true,
      )

    @talent_no_badges =
      User.create!(
        email: '<EMAIL>',
        password: 'password123456',
        first_name: 'No',
        last_name: '<PERSON><PERSON>',
        verified: true,
        talent_signup_completed: true,
      )

    # Create talent profiles
    @profile_multi =
      TalentProfile.create!(
        user: @talent_multi_badges,
        headline: 'Multi-Badge Expert',
        about: 'Writer with multiple achievements',
        availability_status: 'available',
      )

    @profile_single =
      TalentProfile.create!(
        user: @talent_single_badge,
        headline: 'Single Badge Writer',
        about: 'Specialized writer',
        availability_status: 'available',
      )

    @profile_none =
      TalentProfile.create!(
        user: @talent_no_badges,
        headline: 'New Writer',
        about: 'Starting journey',
        availability_status: 'available',
      )

    # Create badge types
    @choice_badge =
      BadgeType.create!(
        name: 'Ghostwrote Choice',
        description: 'Top-rated ghostwriter',
        icon: 'star',
        background_color: '#FFD700',
        text_color: '#000000',
        priority: 1,
      )

    @expert_badge =
      BadgeType.create!(
        name: 'Expert Writer',
        description: 'Proven expertise',
        icon: 'certificate',
        background_color: '#4F46E5',
        text_color: '#FFFFFF',
        priority: 2,
      )

    @premium_badge =
      BadgeType.create!(
        name: 'Premium Writer',
        description: 'Premium service provider',
        icon: 'crown',
        background_color: '#9333EA',
        text_color: '#FFFFFF',
        priority: 3,
      )

    # Assign badges
    # Multi-badge user gets Choice and Expert
    BadgeAssignment.create!(
      user: @talent_multi_badges,
      badge_type: @choice_badge,
      assigned_by: @scout,
      assigned_at: 2.days.ago,
    )

    BadgeAssignment.create!(
      user: @talent_multi_badges,
      badge_type: @expert_badge,
      assigned_by: @scout,
      assigned_at: 1.day.ago,
    )

    # Single-badge user gets only Premium
    BadgeAssignment.create!(
      user: @talent_single_badge,
      badge_type: @premium_badge,
      assigned_by: @scout,
      assigned_at: 1.day.ago,
    )

    # No badges for the third user
  end

  def teardown
    BadgeAssignment.destroy_all
    BadgeType.destroy_all
    TalentProfile.destroy_all
    OrganizationMembership.destroy_all
    Organization.destroy_all
    User.destroy_all
  end

  test 'complete badge filtering workflow' do
    # Sign in as scout
    post sign_in_url,
         params: {
           email: @scout.email,
           password: 'password123456',
         }

    # Visit talent index page
    get scout_talent_index_path
    assert_response :success

    # Verify all talent profiles are shown initially
    assert_includes response.body, 'Multi Badges'
    assert_includes response.body, 'Single Badge'
    assert_includes response.body, 'No Badges'

    # Apply Ghostwrote Choice filter via URL
    get scout_talent_index_path, params: { badge_types: 'Ghostwrote Choice' }
    assert_response :success

    # Verify only multi-badge user is shown
    assert_includes response.body, 'Multi Badges'
    assert_not_includes response.body, 'Single Badge'
    assert_not_includes response.body, 'No Badges'

    # Verify active filter tag is displayed
    assert_includes response.body, 'Ghostwrote Choice'
    assert_select '[data-filter-type="badge_types"][data-filter-value="Ghostwrote Choice"]'

    # Apply multiple badge filter
    get scout_talent_index_path,
        params: {
          badge_types: 'Ghostwrote Choice,Premium Writer',
        }
    assert_response :success

    # Verify both multi-badge and single-badge users are shown
    assert_includes response.body, 'Multi Badges'
    assert_includes response.body, 'Single Badge'
    assert_not_includes response.body, 'No Badges'

    # Verify both filter tags are displayed
    assert_includes response.body, 'Ghostwrote Choice'
    assert_includes response.body, 'Premium Writer'
  end

  test 'badge filtering with search query' do
    # Sign in as scout
    post sign_in_url,
         params: {
           email: @scout.email,
           password: 'password123456',
         }

    # Apply badge filter with search query
    get scout_talent_index_path,
        params: {
          badge_types: 'Expert Writer',
          query: 'Multi',
        }
    assert_response :success

    # Should show multi-badge user (has Expert Writer badge and matches "Multi" query)
    assert_includes response.body, 'Multi Badges'
    assert_not_includes response.body, 'Single Badge'
    assert_not_includes response.body, 'No Badges'

    # Verify both search query and badge filter are preserved
    assert_includes response.body, 'Expert Writer'
  end

  test 'badge filtering with other filters' do
    # Sign in as scout
    post sign_in_url,
         params: {
           email: @scout.email,
           password: 'password123456',
         }

    # Apply badge filter with availability filter
    get scout_talent_index_path,
        params: {
          badge_types: 'Premium Writer',
          availability_status: 'available',
        }
    assert_response :success

    # Should show single-badge user (has Premium Writer badge and is available)
    assert_not_includes response.body, 'Multi Badges'
    assert_includes response.body, 'Single Badge'
    assert_not_includes response.body, 'No Badges'

    # Verify both filters are shown as active
    assert_includes response.body, 'Premium Writer'
    assert_includes response.body, 'Available'
  end

  test 'badge filter removal via URL manipulation' do
    # Sign in as scout
    post sign_in_url,
         params: {
           email: @scout.email,
           password: 'password123456',
         }

    # Start with badge filter applied
    get scout_talent_index_path, params: { badge_types: 'Ghostwrote Choice' }
    assert_response :success
    assert_includes response.body, 'Multi Badges'
    assert_not_includes response.body, 'Single Badge'

    # Remove badge filter by visiting without the parameter
    get scout_talent_index_path
    assert_response :success

    # Should show all profiles again
    assert_includes response.body, 'Multi Badges'
    assert_includes response.body, 'Single Badge'
    assert_includes response.body, 'No Badges'

    # Should not show any badge filter tags
    assert_select '[data-filter-type="badge_types"]', count: 0
  end

  test 'badge filtering with expired badges' do
    # Create an expired badge assignment
    expired_assignment =
      BadgeAssignment.create!(
        user: @talent_no_badges,
        badge_type: @choice_badge,
        assigned_by: @scout,
        assigned_at: 10.days.ago,
        expires_at: 1.day.ago, # Expired yesterday
      )

    # Sign in as scout
    post sign_in_url,
         params: {
           email: @scout.email,
           password: 'password123456',
         }

    # Apply Ghostwrote Choice filter
    get scout_talent_index_path, params: { badge_types: 'Ghostwrote Choice' }
    assert_response :success

    # Should only show multi-badge user (active badge), not the user with expired badge
    assert_includes response.body, 'Multi Badges'
    assert_not_includes response.body, 'Single Badge'
    assert_not_includes response.body, 'No Badges'
  end

  test 'badge filtering displays correct badge components in results' do
    # Sign in as scout
    post sign_in_url,
         params: {
           email: @scout.email,
           password: 'password123456',
         }

    # Apply filter to show multi-badge user
    get scout_talent_index_path, params: { badge_types: 'Ghostwrote Choice' }
    assert_response :success

    # Verify badge components are displayed in search results
    assert_select '[data-controller="badge"]', minimum: 1
    assert_includes response.body, 'Ghostwrote Choice'
    assert_includes response.body, 'Expert Writer' # Multi-badge user has both badges
  end
end
