require 'test_helper'

class ImpersonationButtonTest < ActionDispatch::IntegrationTest
  setup do
    @admin = users(:super_admin)
    @scout = users(:scout)
    @talent = users(:talent)

    # Ensure admin has superadmin role
    superadmin_role = Role.find_or_create_by!(name: 'superadmin')
    @admin.roles << superadmin_role unless @admin.superadmin?
  end

  test 'impersonation button triggers modal with correct data' do
    sign_in_as(@admin)
    get super_admin_users_path

    assert_response :success

    # Check that the page includes the impersonation controller
    assert_select '[data-controller="impersonation"]'

    # Check that impersonate buttons exist with correct data attributes
    assert_select 'button[data-action="click->impersonation#showConfirmation"]' do |buttons|
      # Find the button for the scout user
      scout_button =
        buttons.find { |btn| btn['data-user-id'] == @scout.id.to_s }
      assert scout_button, 'Should have impersonate button for scout'
      assert_equal @scout.name, scout_button['data-user-name']
      assert_equal @scout.email, scout_button['data-user-email']
    end

    # Check that the modal is included
    assert_select '[data-impersonation-target="modal"]'
    assert_select '[data-impersonation-target="form"]'
    assert_select '[data-impersonation-target="userName"]'
    assert_select '[data-impersonation-target="userEmail"]'
  end

  test 'impersonation form submits to correct endpoint' do
    sign_in_as(@admin)

    # Simulate the form submission that would happen after clicking confirm
    post super_admin_masquerades_path,
         params: {
           user_id: @scout.id,
         },
         headers: {
           'HTTP_USER_AGENT' => 'Test Browser',
         }

    # Should redirect to the user's landing page
    assert_redirected_to launchpad_path

    # Should create an impersonation log
    log = ImpersonationLog.last
    assert_equal @admin, log.admin
    assert_equal @scout, log.user
    assert_not_nil log.started_at
    assert_nil log.ended_at
  end

  test 'modal includes all required elements' do
    sign_in_as(@admin)
    get super_admin_users_path

    assert_response :success

    # Check modal structure
    assert_select '[data-impersonation-target="modal"]' do
      assert_select '[data-impersonation-target="form"]'
      assert_select '[data-impersonation-target="userName"]'
      assert_select '[data-impersonation-target="userEmail"]'
      assert_select 'button[data-action="click->impersonation#confirm"]',
                    text: 'Start Impersonation'
      assert_select 'button[data-action="click->impersonation#cancel"]',
                    text: 'Cancel'
    end
  end

  private

  def sign_in_as(user)
    post(sign_in_url, params: { email: user.email, password: 'Secret1*3*5*' })
    user
  end
end
