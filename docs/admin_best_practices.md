# Admin Interface Best Practices Guide

## Security Best Practices

### Access Control

#### Role Assignment Principles
1. **Principle of Least Privilege**: Assign minimum necessary permissions
2. **Regular Review**: Audit admin roles quarterly
3. **Temporary Access**: Use time-limited admin access when possible
4. **Documentation**: Document all role assignments and changes

```ruby
# Good: Specific role assignment with justification
user.update!(role: 'support')  # Customer support team member
AdminAuditLog.log_action(
  user: Current.user,
  action: 'role_assignment',
  resource: user,
  change_data: { 
    role: ['user', 'support'],
    justification: 'Customer support team member - ticket #12345'
  }
)

# Bad: Blanket superadmin assignment
user.update!(role: 'superadmin')  # Too broad, no justification
```

#### Session Management
```ruby
# Implement session timeout for admin users
class SuperAdmin::BaseController < ApplicationController
  before_action :check_admin_session_timeout
  
  private
  
  def check_admin_session_timeout
    if session[:admin_last_activity] && 
       session[:admin_last_activity] < 2.hours.ago
      reset_session
      redirect_to sign_in_path, alert: 'Admin session expired'
    end
    
    session[:admin_last_activity] = Time.current
  end
end
```

### Data Protection

#### Sensitive Data Handling
1. **Mask Sensitive Information**: Don't display full credit card numbers, SSNs
2. **Audit Access**: Log all access to sensitive user data
3. **Export Controls**: Limit bulk data exports to authorized personnel
4. **Data Retention**: Follow company data retention policies

```ruby
# Good: Masked display of sensitive data
def masked_email
  email.gsub(/(.{2}).*(@.*)/, '\1***\2')
end

# Good: Audit sensitive data access
def show
  @user = User.find(params[:id])
  
  AdminAuditLog.log_action(
    user: Current.user,
    action: 'view_sensitive_data',
    resource: @user,
    ip_address: request.remote_ip
  )
end
```

## Operational Best Practices

### Data Management

#### Bulk Operations Safety
```ruby
# Always implement confirmation for bulk operations
def bulk_update
  return unless params[:confirm] == 'yes'
  
  users = User.where(id: params[:user_ids])
  
  # Preview what will be affected
  if params[:preview]
    render json: {
      affected_count: users.count,
      sample_users: users.limit(5).pluck(:name, :email)
    }
    return
  end
  
  # Perform bulk operation with transaction
  ActiveRecord::Base.transaction do
    users.each do |user|
      old_values = user.attributes.dup
      user.update!(bulk_params)
      
      AdminAuditLog.log_action(
        user: Current.user,
        action: 'bulk_update',
        resource: user,
        change_data: user.previous_changes
      )
    end
  end
end
```

#### Search & Filtering Best Practices
```ruby
# Efficient search implementation
class AdminUsersController < SuperAdmin::BaseController
  def index
    @users = build_user_query
    @users = @users.page(params[:page]).per(25)
    
    # Cache search results for pagination
    cache_key = "admin_users_search_#{search_cache_key}"
    @total_count = Rails.cache.fetch(cache_key, expires_in: 5.minutes) do
      @users.count
    end
  end
  
  private
  
  def build_user_query
    users = User.includes(:organization)
    
    # Apply filters systematically
    users = apply_text_search(users)
    users = apply_role_filter(users)
    users = apply_date_filter(users)
    users = apply_status_filter(users)
    
    users.order(order_column => order_direction)
  end
  
  def apply_text_search(users)
    return users unless params[:search].present?
    
    search_term = "%#{params[:search].strip}%"
    users.where(
      "users.name ILIKE :search OR users.email ILIKE :search OR organizations.name ILIKE :search",
      search: search_term
    ).joins(:organization)
  end
end
```

### Performance Optimization

#### Database Query Optimization
```ruby
# Good: Efficient queries with proper includes
def index
  @jobs = Job.includes(:organization, :user)
             .where(build_filter_conditions)
             .page(params[:page])
             .per(25)
end

# Bad: N+1 queries
def index
  @jobs = Job.page(params[:page]).per(25)
  # This will cause N+1 queries when accessing job.organization.name
end

# Good: Use select for large datasets
def export_users
  User.select(:id, :name, :email, :created_at)
      .where(build_filter_conditions)
      .find_each(batch_size: 1000) do |user|
        # Process user
      end
end
```

#### Caching Strategy
```ruby
# Cache expensive dashboard calculations
def dashboard_stats
  Rails.cache.fetch("admin_dashboard_#{Current.user.id}", expires_in: 10.minutes) do
    calculate_dashboard_stats
  end
end

# Cache search results for pagination
def cached_search_results
  cache_key = "admin_search_#{search_params.hash}_#{params[:page]}"
  Rails.cache.fetch(cache_key, expires_in: 5.minutes) do
    perform_search
  end
end
```

## User Experience Best Practices

### Interface Design

#### Consistent Navigation
```erb
<!-- Consistent breadcrumb pattern -->
<nav class="mb-6">
  <ol class="flex items-center space-x-2 text-sm text-stone-600">
    <li><%= link_to "Admin", super_admin_root_path, class: "hover:text-stone-900" %></li>
    <li class="flex items-center">
      <svg class="w-4 h-4 mx-2" fill="currentColor" viewBox="0 0 20 20">
        <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
      </svg>
      <%= link_to "Users", super_admin_admin_users_path, class: "hover:text-stone-900" %>
    </li>
    <li class="flex items-center">
      <svg class="w-4 h-4 mx-2" fill="currentColor" viewBox="0 0 20 20">
        <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
      </svg>
      <span class="text-stone-900"><%= @user.name %></span>
    </li>
  </ol>
</nav>
```

#### Form Design Best Practices
```erb
<!-- Consistent form styling and validation -->
<%= form_with model: [:super_admin, @user], local: true, class: "space-y-6" do |form| %>
  <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
    <div>
      <%= form.label :name, class: "block text-sm font-medium text-stone-700" %>
      <%= form.text_field :name, 
          class: "mt-1 block w-full rounded-md border-stone-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm #{'border-red-300' if @user.errors[:name].any?}" %>
      <% if @user.errors[:name].any? %>
        <p class="mt-1 text-sm text-red-600"><%= @user.errors[:name].first %></p>
      <% end %>
    </div>
    
    <div>
      <%= form.label :email, class: "block text-sm font-medium text-stone-700" %>
      <%= form.email_field :email, 
          class: "mt-1 block w-full rounded-md border-stone-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm #{'border-red-300' if @user.errors[:email].any?}" %>
      <% if @user.errors[:email].any? %>
        <p class="mt-1 text-sm text-red-600"><%= @user.errors[:email].first %></p>
      <% end %>
    </div>
  </div>
  
  <div class="flex justify-end space-x-3">
    <%= link_to "Cancel", super_admin_admin_users_path, 
        class: "px-4 py-2 text-sm font-medium text-stone-700 bg-white border border-stone-300 rounded-md hover:bg-stone-50" %>
    <%= form.submit "Save User", 
        class: "px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700" %>
  </div>
<% end %>
```

### Error Handling

#### User-Friendly Error Messages
```ruby
class SuperAdmin::AdminUsersController < SuperAdmin::BaseController
  def update
    @user = User.find(params[:id])
    
    if @user.update(user_params)
      AdminAuditLog.log_action(
        user: Current.user,
        action: 'update',
        resource: @user,
        change_data: @user.previous_changes
      )
      
      redirect_to super_admin_admin_user_path(@user), 
                  notice: 'User updated successfully.'
    else
      # Provide specific error context
      flash.now[:alert] = "Unable to update user: #{@user.errors.full_messages.join(', ')}"
      render :edit
    end
  rescue ActiveRecord::RecordNotFound
    redirect_to super_admin_admin_users_path, 
                alert: 'User not found.'
  rescue StandardError => e
    Rails.logger.error "Admin user update error: #{e.message}"
    redirect_to super_admin_admin_user_path(@user), 
                alert: 'An unexpected error occurred. Please try again.'
  end
end
```

## Audit & Compliance Best Practices

### Comprehensive Audit Logging
```ruby
# Log all significant admin actions
class SuperAdmin::BaseController < ApplicationController
  after_action :log_admin_action, except: [:index, :show]
  
  private
  
  def log_admin_action
    return unless performed?
    
    AdminAuditLog.log_action(
      user: Current.user,
      action: action_name,
      resource: instance_variable_get("@#{controller_name.singularize}"),
      change_data: extract_changes,
      ip_address: request.remote_ip,
      user_agent: request.user_agent
    )
  end
  
  def extract_changes
    resource = instance_variable_get("@#{controller_name.singularize}")
    return nil unless resource&.respond_to?(:previous_changes)
    
    resource.previous_changes.presence
  end
end
```

---

**Last Updated**: 2025-06-29  
**Version**: 1.0  
**Review Schedule**: Quarterly
