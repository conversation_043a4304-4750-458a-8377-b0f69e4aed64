# Admin Interface Design Patterns

This document outlines the standardized design patterns for all admin interface pages to ensure visual consistency across the super admin area.

## Overview

The admin interface follows a consistent three-section layout pattern with clear visual hierarchy and separation. All admin pages should implement these patterns to maintain design consistency and improve user experience.

## Core Design Principles

### 1. Stone Color Palette

- **Primary Background**: `stone-50`, `stone-100`
- **Text Colors**: `stone-900` (primary), `stone-600` (secondary), `stone-500` (muted)
- **Borders**: `border-stone-200`, `border-stone-300`
- **Interactive Elements**: `stone-600` (buttons), `stone-500` (focus states)

### 2. Visual Hierarchy

- Clear separation between functional sections
- Consistent spacing patterns using Tailwind classes
- Subtle border dividers for section separation
- Proper typography hierarchy

### 3. Responsive Design

- Mobile-first approach
- Flexible layouts that adapt to screen sizes
- Consistent spacing across breakpoints

## Page Layout Structure

### Three-Section Layout Pattern

All admin pages should follow this structure:

```erb
<div class="max-w-7xl mx-auto">
  <!-- 1. <PERSON> Header Section -->
  <div class="mb-8">
    <h1 class="text-3xl font-bold text-stone-900">[Page Title]</h1>
    <p class="mt-2 text-stone-600">[Page Description]</p>
  </div>

  <!-- 2. Search and Filters Section -->
  <%= render 'shared/admin/search_and_filters', ... %>

  <!-- 3. Table Section -->
  <div class="mt-8 pt-6 border-t border-stone-200">
    <!-- Results Summary and Export -->
    <div class="mb-4 flex justify-between items-center">
      <!-- Summary and actions -->
    </div>

    <!-- Data Table -->
    <%= render 'shared/admin/table', ... %>

    <!-- Pagination -->
    <%= render 'shared/admin/pagination', ... %>
  </div>
</div>
```

## Section-Specific Patterns

### 1. Page Header Section

**Purpose**: Page identification and context
**Spacing**: `mb-8` bottom margin

```erb
<div class="mb-8">
  <h1 class="text-3xl font-bold text-stone-900">[Title]</h1>
  <p class="mt-2 text-stone-600">[Description]</p>
</div>
```

**Key Elements**:

- Page title using `text-3xl font-bold text-stone-900`
- Optional description using `mt-2 text-stone-600`
- Consistent bottom margin for separation

### 2. Search and Filters Section

**Purpose**: User input controls for data filtering
**Component**: Uses `shared/admin/search_and_filters` partial

**Layout Structure**:

- **Top Row**: Search field (flex-1) + Filter/Clear buttons
- **Bottom Row**: Filter dropdowns in responsive grid

```erb
<!-- Search and Action Buttons Row -->
<div class="flex items-end gap-4">
  <!-- Search Field (takes most space) -->
  <div class="flex-1">
    <%= form.label :search, "Search", class: "block text-sm font-medium text-stone-700" %>
    <%= form.text_field :search, ... %>
  </div>

  <!-- Filter and Clear Buttons -->
  <div class="flex items-center space-x-4">
    <%= form.submit "Filter", class: "..." %>
    <%= link_to "Clear", ..., class: "text-stone-600 hover:text-stone-900" %>
  </div>
</div>

<!-- Filter Dropdowns Row -->
<div class="grid grid-cols-1 gap-4 sm:grid-cols-[dynamic]">
  <!-- Filter dropdowns -->
</div>
```

**Key Features**:

- Search field takes most horizontal space (`flex-1`)
- Filter/Clear buttons aligned on same row
- **Clear button only shows when filters are applied**
- Dropdowns in separate row with responsive grid
- Stone color palette for consistency
- Focus states using `focus:border-stone-500 focus:ring-stone-500`

### 3. Table Section

**Purpose**: Data display and pagination
**Visual Separation**: `mt-8 pt-6 border-t border-stone-200`

```erb
<div class="mt-8 pt-6 border-t border-stone-200">
  <!-- Results Summary and Export -->
  <div class="mb-4 flex justify-between items-center">
    <p class="text-sm text-stone-600">
      Showing [from] to [to] of [total] [items]
    </p>
    <div class="flex space-x-2">
      <!-- Export buttons -->
    </div>
  </div>

  <!-- Data Table -->
  <%= render 'shared/admin/table', ... %>

  <!-- Pagination -->
  <%= render 'shared/admin/pagination', ... %>
</div>
```

**Key Elements**:

- Clear visual separation with border and padding
- Results summary on the left
- Export actions on the right
- Consistent table styling via shared partial
- Pagination at bottom

## Component Guidelines

### Buttons

**Primary Action (Filter)**:

```erb
class: "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-stone-600 hover:bg-stone-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-stone-500"
```

**Secondary Action (Clear)**:

```erb
class: "text-stone-600 hover:text-stone-900"
```

**Export Button**:

```erb
class: "inline-flex items-center px-3 py-2 border border-stone-300 shadow-sm text-sm leading-4 font-medium rounded-md text-stone-700 bg-white hover:bg-stone-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-stone-500"
```

### Form Controls

**Text Inputs**:

```erb
class: "mt-1 block w-full rounded-md border-stone-300 shadow-sm focus:border-stone-500 focus:ring-stone-500 sm:text-sm"
```

**Select Dropdowns**:

```erb
class: "mt-1 block w-full rounded-md border-stone-300 shadow-sm focus:border-stone-500 focus:ring-stone-500 sm:text-sm"
```

**Labels**:

```erb
class: "block text-sm font-medium text-stone-700"
```

### Spacing Patterns

**Section Separation**:

- Between header and filters: Natural flow (no extra spacing)
- Between filters and table: `mt-8 pt-6 border-t border-stone-200`

**Internal Spacing**:

- Form elements: `gap-4` for horizontal, `space-y-4` for vertical
- Button groups: `space-x-4` or `space-x-2`
- Grid layouts: `gap-4`

## Implementation Checklist

When creating or updating admin pages, ensure:

- [ ] Three-section layout structure implemented
- [ ] Stone color palette used consistently
- [ ] Search field takes most horizontal space in filter row
- [ ] Filter/Clear buttons aligned with search field
- [ ] Dropdowns in separate row below search
- [ ] Visual separation between filters and table (`mt-8 pt-6 border-t border-stone-200`)
- [ ] Results summary and export actions properly positioned
- [ ] Shared table component used for data display
- [ ] Pagination included at bottom of table section
- [ ] Responsive design maintained across breakpoints
- [ ] Focus states properly implemented for accessibility
- [ ] Consistent button and form control styling

## Files Updated

The following files implement these patterns:

### Shared Components

- `app/views/shared/admin/_search_and_filters.html.erb` - Filter component
- `app/views/shared/admin/_table.html.erb` - Table component

### Admin Pages

- `app/views/super_admin/users/index.html.erb` - User management
- `app/views/super_admin/admin_users/index.html.erb` - Admin users (uses advanced search)
- `app/views/super_admin/admin_organizations/index.html.erb` - Organizations
- `app/views/super_admin/admin_conversations/index.html.erb` - Conversations
- `app/views/super_admin/admin_chat_requests/index.html.erb` - Chat requests

## Maintenance

When adding new admin pages:

1. Follow the three-section layout pattern
2. Use the shared components where possible
3. Maintain stone color palette consistency
4. Implement proper visual separation
5. Test responsive behavior
6. Update this documentation if new patterns emerge

This ensures all admin interfaces maintain visual consistency and provide a cohesive user experience across the super admin area.
