# Admin Interface Troubleshooting Guide

## Quick Diagnosis Checklist

When encountering admin interface issues, follow this systematic approach:

1. **Check User Permissions**: Verify admin role and access level
2. **Review Recent Changes**: Check audit logs for recent administrative actions
3. **Validate Data Integrity**: Ensure database consistency and relationships
4. **Check System Resources**: Monitor server performance and connectivity
5. **Review Error Logs**: Examine application logs for specific error messages

## Common Issues & Solutions

### Authentication & Access Issues

#### Problem: "Access Denied" or "Permission Denied"
**Symptoms:**
- Cannot access `/super_admin` routes
- Redirected to login page repeatedly
- 403 Forbidden errors

**Solutions:**
```ruby
# Check user admin status
user = User.find_by(email: "<EMAIL>")
user.admin?  # Should return true
user.role    # Should be 'superadmin', 'support', or 'readonly'

# Verify Current.user is set properly
Current.user&.admin?  # Should return true in admin context
```

**Resolution Steps:**
1. Verify user has admin role assigned
2. Check authentication session is valid
3. Ensure `Current.user` is properly set
4. Review role assignment in database

#### Problem: Session Expires Frequently
**Symptoms:**
- Frequent logouts during admin work
- Session timeout warnings
- Lost work due to session expiration

**Solutions:**
1. **Check Session Configuration:**
   ```ruby
   # In config/application.rb or session store config
   config.session_store :cookie_store, expire_after: 4.hours
   ```

2. **Monitor Session Activity:**
   ```ruby
   # Check active sessions
   Session.where(user: current_user).active
   ```

3. **Extend Session for Admin Users:**
   - Consider longer session timeouts for admin users
   - Implement session refresh for active admin work

### Data Management Issues

#### Problem: Search Not Returning Results
**Symptoms:**
- Search queries return empty results
- Filters not working properly
- Expected data not appearing

**Diagnostic Steps:**
```ruby
# Test direct database queries
User.where("email ILIKE ?", "%search_term%").count
Job.where(status: 'published').count

# Check search indexes (if using Elasticsearch/Searchkick)
User.reindex  # Rebuild search index if needed
```

**Solutions:**
1. **Database Query Issues:**
   - Check for case sensitivity in searches
   - Verify column names match search parameters
   - Test queries directly in Rails console

2. **Search Index Problems:**
   - Rebuild search indexes if using external search
   - Check search service connectivity
   - Verify search configuration

#### Problem: Data Export Failures
**Symptoms:**
- CSV exports timeout or fail
- Incomplete export files
- Memory errors during large exports

**Solutions:**
```ruby
# For large datasets, implement streaming exports
def export_users_csv
  CSV.generate do |csv|
    csv << User.column_names
    User.find_each(batch_size: 1000) do |user|
      csv << user.attributes.values
    end
  end
end

# Monitor memory usage during exports
# Consider background job processing for large exports
```

**Resolution Steps:**
1. Implement pagination for large datasets
2. Use background jobs for time-consuming exports
3. Add progress indicators for user feedback
4. Set appropriate timeout limits

### Performance Issues

#### Problem: Slow Admin Page Loading
**Symptoms:**
- Pages take >5 seconds to load
- Database query timeouts
- High server resource usage

**Diagnostic Tools:**
```ruby
# Check slow queries in Rails console
ActiveRecord::Base.logger = Logger.new(STDOUT)

# Monitor N+1 queries
# Use bullet gem in development to identify issues

# Check database indexes
# Ensure foreign keys and search columns are indexed
```

**Optimization Strategies:**
1. **Database Optimization:**
   ```sql
   -- Add indexes for commonly searched columns
   CREATE INDEX idx_users_email ON users(email);
   CREATE INDEX idx_jobs_status ON jobs(status);
   CREATE INDEX idx_audit_logs_created_at ON admin_audit_logs(created_at);
   ```

2. **Query Optimization:**
   ```ruby
   # Use includes to prevent N+1 queries
   User.includes(:organization, :roles).limit(50)
   
   # Implement pagination
   User.page(params[:page]).per(25)
   ```

3. **Caching Implementation:**
   ```ruby
   # Cache expensive calculations
   Rails.cache.fetch("admin_dashboard_stats", expires_in: 5.minutes) do
     {
       total_users: User.count,
       active_jobs: Job.published.count,
       # ... other stats
     }
   end
   ```

### UI/UX Issues

#### Problem: Missing UI Elements
**Symptoms:**
- Pagination controls not appearing
- Filter dropdowns empty
- Empty state messages not showing

**Diagnostic Steps:**
```erb
<!-- Check for required CSS classes -->
<div class="pagination">...</div>
<div class="empty-state">...</div>
<select name="category">...</select>

<!-- Verify data is being passed to views -->
<%= debug @users if Rails.env.development? %>
```

**Solutions:**
1. **CSS/Styling Issues:**
   - Ensure required CSS classes are present
   - Check for conflicting styles
   - Verify responsive design breakpoints

2. **Data Binding Issues:**
   - Confirm controller is passing data to views
   - Check for nil values in view logic
   - Verify partial rendering

#### Problem: Form Submissions Failing
**Symptoms:**
- Forms submit but changes not saved
- Validation errors not displaying
- CSRF token errors

**Solutions:**
```erb
<!-- Ensure CSRF tokens are included -->
<%= form_with model: @user, local: true do |form| %>
  <%= form.hidden_field :authenticity_token, value: form_authenticity_token %>
  <!-- form fields -->
<% end %>

<!-- Check strong parameters -->
def user_params
  params.require(:user).permit(:email, :name, :role, :verified)
end
```

### Database Issues

#### Problem: Foreign Key Constraint Violations
**Symptoms:**
- Cannot delete records due to dependencies
- Association errors in admin interface
- Data integrity warnings

**Diagnostic Queries:**
```sql
-- Check for orphaned records
SELECT * FROM jobs WHERE organization_id NOT IN (SELECT id FROM organizations);

-- Find records with missing associations
SELECT u.* FROM users u 
LEFT JOIN organizations o ON u.organization_id = o.id 
WHERE u.organization_id IS NOT NULL AND o.id IS NULL;
```

**Solutions:**
1. **Clean Up Orphaned Data:**
   ```ruby
   # Remove orphaned records
   Job.where.not(organization_id: Organization.pluck(:id)).destroy_all
   
   # Or update to valid associations
   Job.where(organization_id: nil).update_all(organization_id: default_org.id)
   ```

2. **Implement Proper Cascading:**
   ```ruby
   # In models, ensure proper dependency handling
   class Organization < ApplicationRecord
     has_many :jobs, dependent: :destroy
     has_many :users, dependent: :nullify
   end
   ```

### Audit Log Issues

#### Problem: Audit Logs Not Recording
**Symptoms:**
- Admin actions not appearing in audit logs
- Missing change tracking
- Incomplete audit trail

**Diagnostic Steps:**
```ruby
# Test audit logging manually
AdminAuditLog.log_action(
  user: Current.user,
  action: 'test',
  resource: User.first,
  change_data: { test: 'value' }
)

# Check if Current.user is set
Current.user&.id  # Should return admin user ID

# Verify audit log model is working
AdminAuditLog.count  # Should increase after admin actions
```

**Solutions:**
1. **Fix Current.user Context:**
   ```ruby
   # Ensure Current.user is set in admin controllers
   class SuperAdmin::BaseController < ApplicationController
     before_action :set_current_user
     
     private
     
     def set_current_user
       Current.user = current_user
     end
   end
   ```

2. **Add Missing Audit Calls:**
   ```ruby
   # Add to admin controller actions
   def update
     if @user.update(user_params)
       AdminAuditLog.log_action(
         user: Current.user,
         action: 'update',
         resource: @user,
         change_data: @user.previous_changes
       )
       redirect_to admin_user_path(@user)
     end
   end
   ```

## Emergency Procedures

### System Recovery

#### Database Corruption
1. **Immediate Actions:**
   - Stop application servers
   - Assess extent of corruption
   - Restore from latest backup if necessary

2. **Data Recovery:**
   ```bash
   # PostgreSQL recovery
   pg_dump ghostwrote_production > backup_$(date +%Y%m%d_%H%M%S).sql
   
   # Check database integrity
   psql -d ghostwrote_production -c "SELECT pg_size_pretty(pg_database_size('ghostwrote_production'));"
   ```

#### Mass Data Deletion
1. **Stop Further Damage:**
   - Immediately revoke admin access for affected user
   - Check audit logs for extent of deletion

2. **Recovery Process:**
   ```ruby
   # Check what was deleted from audit logs
   deleted_actions = AdminAuditLog.where(action: 'destroy')
                                  .where('created_at > ?', 1.hour.ago)
   
   # Restore from backup if available
   # Or recreate critical data from audit trail
   ```

### Escalation Procedures

#### When to Escalate
- Data corruption or loss
- Security breaches or unauthorized access
- System-wide performance degradation
- Critical functionality failures

#### Escalation Contacts
1. **Technical Issues**: Development team lead
2. **Security Issues**: Security team + management
3. **Data Issues**: Database administrator + legal team
4. **User Impact**: Customer support + product team

## Prevention Strategies

### Regular Maintenance

#### Daily Tasks
- Monitor system performance metrics
- Review error logs for new issues
- Check audit logs for unusual activity
- Verify backup completion

#### Weekly Tasks
- Review user feedback and support tickets
- Analyze performance trends
- Update documentation as needed
- Test critical admin functions

#### Monthly Tasks
- Full system health check
- Security audit and review
- Performance optimization review
- Documentation updates

### Monitoring Setup

```ruby
# Set up monitoring for critical metrics
class AdminHealthCheck
  def self.check_system_health
    {
      database_connectivity: check_database,
      admin_functionality: check_admin_access,
      audit_logging: check_audit_logs,
      performance: check_response_times
    }
  end
  
  private
  
  def self.check_database
    User.connection.active?
  rescue
    false
  end
  
  def self.check_admin_access
    # Test admin route accessibility
    true  # Implement actual check
  end
end
```

---

**Emergency Contact**: <EMAIL>  
**Last Updated**: 2025-06-29  
**Version**: 1.0
