# Playwright MCP Browser Troubleshooting Framework

## Overview
This framework provides comprehensive procedures for diagnosing, resolving, and preventing Playwright MCP browser issues to ensure reliable automated testing functionality.

## 1. Diagnostic Steps

### 1.1 Check Browser Process Status
```bash
# Check for running Chrome/Playwright processes
ps aux | grep -E "(chrome|playwright|chromium)" | grep -v grep

# Look for processes with MCP profile paths
ps aux | grep "mcp-chrome-profile" | grep -v grep

# Check MCP server status
ps aux | grep "mcp-server-playwright" | grep -v grep
```

### 1.2 Identify Profile Locks
```bash
# Check for browser profile directory
ls -la "/Users/<USER>/Library/Caches/ms-playwright/"

# Check for lock files in profile
find "/Users/<USER>/Library/Caches/ms-playwright/" -name "*lock*" -o -name "SingletonLock" 2>/dev/null
```

### 1.3 Verify MCP Server Connection
```bash
# Check if MCP server is responding
curl -s http://localhost:3000/health 2>/dev/null || echo "MCP server not responding"

# Check for MCP server processes
pgrep -f "mcp-server-playwright"
```

## 2. Resolution Procedures

### 2.1 Kill Conflicting Browser Processes
```bash
# Step 1: Find main Chrome process with MCP profile
CHROME_PID=$(ps aux | grep "mcp-chrome-profile" | grep -v grep | awk '{print $2}' | head -1)

# Step 2: Kill main Chrome process (will terminate child processes)
if [ ! -z "$CHROME_PID" ]; then
    echo "Killing Chrome process: $CHROME_PID"
    kill -9 $CHROME_PID
    sleep 2
fi

# Step 3: Kill any remaining Chrome processes
pkill -f "chrome.*mcp-chrome-profile" 2>/dev/null || true

# Step 4: Verify processes are terminated
ps aux | grep "mcp-chrome-profile" | grep -v grep || echo "All Chrome processes terminated"
```

### 2.2 Clear Browser Profile Cache
```bash
# Remove entire MCP browser profile directory
rm -rf "/Users/<USER>/Library/Caches/ms-playwright/mcp-chrome-profile"

# Verify removal
ls -la "/Users/<USER>/Library/Caches/ms-playwright/" | grep mcp-chrome-profile || echo "Profile cache cleared"
```

### 2.3 Restart MCP Server (if needed)
```bash
# Kill existing MCP server
pkill -f "mcp-server-playwright"

# Wait for cleanup
sleep 3

# Restart MCP server (adjust command as needed for your setup)
# npm exec @playwright/mcp@latest &
```

## 3. Verification Protocol

### 3.1 Test Basic Browser Navigation
```javascript
// Test navigation to a simple page
await page.goto('http://localhost:5010/up');
```

### 3.2 Test Page Snapshots
```javascript
// Capture page snapshot
const snapshot = await page.snapshot();
```

### 3.3 Test Screenshots
```javascript
// Take screenshot
await page.screenshot({ path: 'test-screenshot.png' });
```

### 3.4 Test JavaScript Execution
```javascript
// Test JavaScript evaluation
const result = await page.evaluate(() => {
    return document.readyState;
});
```

### 3.5 Test Form Interactions
```javascript
// Test clicking and typing
await page.click('button');
await page.type('input[type="text"]', 'test');
```

## 4. Prevention Measures

### 4.1 Clean Shutdown Procedures
- Always close browser instances properly
- Use `browser.close()` in test cleanup
- Implement proper error handling in test scripts

### 4.2 Profile Management
- Use unique profile directories for different test suites
- Regularly clean up old profile directories
- Monitor profile directory size

### 4.3 Process Monitoring
```bash
# Add to development scripts
alias check-mcp="ps aux | grep -E '(chrome|playwright).*mcp' | grep -v grep"
alias clean-mcp="pkill -f 'chrome.*mcp-chrome-profile'; rm -rf ~/Library/Caches/ms-playwright/mcp-chrome-profile"
```

### 4.4 Environment Setup
- Ensure sufficient disk space for browser profiles
- Set appropriate timeout values for browser operations
- Use headless mode when possible to reduce resource usage

## 5. Common Error Patterns

### 5.1 "Browser is already in use" Error
**Symptoms:**
```
Error: Browser is already in use for /Users/<USER>/mcp-chrome-profile, use --isolated to run multiple instances
```

**Resolution:**
1. Follow Section 2.1 to kill browser processes
2. Follow Section 2.2 to clear profile cache
3. Retry browser operation

### 5.2 "No current snapshot available" Error
**Symptoms:**
```
Error: No current snapshot available. Capture a snapshot or navigate to a new location first.
```

**Resolution:**
1. Navigate to a page first: `await page.goto(url)`
2. Then capture snapshot: `await page.snapshot()`

### 5.3 Browser Launch Timeout
**Symptoms:**
- Browser fails to start within timeout period
- Connection refused errors

**Resolution:**
1. Check system resources (CPU, memory)
2. Increase browser launch timeout
3. Clear browser cache and restart

### 5.4 Profile Lock Issues
**Symptoms:**
- Browser starts but becomes unresponsive
- Multiple browser instances conflict

**Resolution:**
1. Kill all browser processes
2. Remove profile directory completely
3. Restart with fresh profile

## 6. Quick Recovery Checklist

### Emergency Reset (Use when all else fails)
```bash
#!/bin/bash
# Emergency MCP reset script

echo "🔄 Emergency MCP Reset Starting..."

# 1. Kill all browser processes
echo "1️⃣ Killing browser processes..."
pkill -f "chrome.*mcp-chrome-profile" 2>/dev/null || true
pkill -f "chromium.*mcp" 2>/dev/null || true

# 2. Clear all MCP profiles
echo "2️⃣ Clearing browser profiles..."
rm -rf "/Users/<USER>/Library/Caches/ms-playwright/"

# 3. Kill MCP server
echo "3️⃣ Restarting MCP server..."
pkill -f "mcp-server-playwright" 2>/dev/null || true

# 4. Wait for cleanup
echo "4️⃣ Waiting for cleanup..."
sleep 5

# 5. Verify clean state
echo "5️⃣ Verifying clean state..."
ps aux | grep -E "(chrome|playwright).*mcp" | grep -v grep || echo "✅ Clean state achieved"

echo "🎉 Emergency reset complete!"
```

## 7. Monitoring and Maintenance

### 7.1 Regular Health Checks
```bash
# Daily health check script
#!/bin/bash
echo "🔍 MCP Health Check $(date)"

# Check for zombie processes
ZOMBIE_COUNT=$(ps aux | grep -E "(chrome|playwright).*mcp" | grep -v grep | wc -l)
echo "Active MCP processes: $ZOMBIE_COUNT"

# Check profile directory size
PROFILE_SIZE=$(du -sh ~/Library/Caches/ms-playwright/ 2>/dev/null | cut -f1)
echo "Profile cache size: $PROFILE_SIZE"

# Check available disk space
DISK_SPACE=$(df -h . | tail -1 | awk '{print $4}')
echo "Available disk space: $DISK_SPACE"
```

### 7.2 Automated Cleanup
```bash
# Weekly cleanup script
#!/bin/bash
# Clean up old browser profiles and temporary files
find ~/Library/Caches/ms-playwright/ -type d -mtime +7 -exec rm -rf {} \; 2>/dev/null || true
```

## 8. Integration with Development Workflow

### 8.1 Pre-Test Setup
```bash
# Add to test setup scripts
function setup_mcp_testing() {
    echo "🚀 Setting up MCP testing environment..."
    
    # Verify clean state
    if ps aux | grep -E "(chrome|playwright).*mcp" | grep -v grep > /dev/null; then
        echo "⚠️  Existing MCP processes detected, cleaning up..."
        pkill -f "chrome.*mcp-chrome-profile" 2>/dev/null || true
        sleep 2
    fi
    
    # Ensure fresh profile
    rm -rf "/Users/<USER>/Library/Caches/ms-playwright/mcp-chrome-profile"
    
    echo "✅ MCP environment ready"
}
```

### 8.2 Post-Test Cleanup
```bash
function cleanup_mcp_testing() {
    echo "🧹 Cleaning up MCP testing environment..."
    
    # Kill browser processes
    pkill -f "chrome.*mcp-chrome-profile" 2>/dev/null || true
    
    # Optional: Clear profile (comment out to preserve for debugging)
    # rm -rf "/Users/<USER>/Library/Caches/ms-playwright/mcp-chrome-profile"
    
    echo "✅ MCP cleanup complete"
}
```

---

## Usage Examples

### Quick Fix for "Browser already in use"
```bash
# One-liner fix
pkill -f "chrome.*mcp-chrome-profile"; rm -rf ~/Library/Caches/ms-playwright/mcp-chrome-profile
```

### Comprehensive Reset
```bash
# Full reset with verification
./emergency-mcp-reset.sh && echo "Ready for testing"
```

### Health Check Before Testing
```bash
# Verify MCP is ready
ps aux | grep "mcp-server-playwright" | grep -v grep && echo "MCP server running" || echo "Start MCP server"
```
