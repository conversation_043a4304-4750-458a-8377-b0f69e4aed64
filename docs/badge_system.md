# Ghostwriter Badges System Documentation

## Overview

The Ghostwriter Badges system is a comprehensive feature that allows super administrators to create, assign, and manage customizable badges for ghostwriters on the Ghostwrote platform. These badges provide visual recognition and social proof, appearing on talent profiles and in search results to help clients identify exceptional or verified talent.

## System Architecture

### Database Schema

The badge system consists of four main database tables:

#### badge_types

- **Purpose**: Stores badge type definitions and appearance settings
- **Key Fields**:
  - `name`: Unique badge name (required)
  - `description`: Badge description for tooltips (required)
  - `background_color`: Hex color code for badge background (required)
  - `text_color`: Hex color code for badge text (required)
  - `icon`: Phosphor icon name (required)
  - `priority`: Display order (lower numbers first)
  - `active`: Whether badge can be assigned

#### badge_assignments

- **Purpose**: Links badges to users with assignment metadata
- **Key Fields**:
  - `badge_type_id`: Reference to badge type
  - `user_id`: Reference to user receiving badge
  - `admin_id`: Reference to admin who assigned badge
  - `assigned_at`: Assignment timestamp
  - `expires_at`: Optional expiration date
  - `notes`: Optional assignment notes

#### badge_views (Analytics)

- **Purpose**: Tracks badge display events for analytics
- **Key Fields**:
  - `viewed_user_id`: User whose badges were viewed
  - `viewer_user_id`: User who viewed the badges
  - `badge_types_displayed`: Array of badge type IDs shown

#### badge_clicks (Analytics)

- **Purpose**: Tracks badge interaction events
- **Key Fields**:
  - `badge_type_id`: Badge that was clicked
  - `user_id`: User whose badge was clicked
  - `clicked_by_user_id`: User who clicked the badge

### Model Relationships

```ruby
# BadgeType Model
class BadgeType < ApplicationRecord
  has_many :badge_assignments, dependent: :restrict_with_error
  has_many :users, through: :badge_assignments

  # Scopes
  scope :active, -> { where(active: true) }
  scope :by_priority, -> { order(priority: :asc) }
end

# BadgeAssignment Model
class BadgeAssignment < ApplicationRecord
  belongs_to :badge_type
  belongs_to :user
  belongs_to :admin, class_name: 'User'

  # Scopes
  scope :active,
        -> { where('expires_at IS NULL OR expires_at > ?', Time.current) }
  scope :by_priority,
        -> { joins(:badge_type).order('badge_types.priority ASC') }
end

# User Model Extensions
class User < ApplicationRecord
  has_many :badge_assignments, dependent: :destroy
  has_many :badges, through: :badge_assignments, source: :badge_type
  has_many :assigned_badges,
           class_name: 'BadgeAssignment',
           foreign_key: 'admin_id'

  def active_badges
    badge_assignments.active.by_priority.includes(:badge_type).map(&:badge_type)
  end
end
```

## Features

### 1. Badge Type Management

- **Create**: Super admins can create new badge types with custom appearance
- **Edit**: Modify existing badge properties (name, colors, icon, priority)
- **Delete**: Remove badge types (only if not assigned to users)
- **Preview**: Live preview shows how badges will appear to users

### 2. Badge Assignment

- **Assign**: Super admins can assign badges to any user
- **Revoke**: Remove badge assignments from users
- **Expiration**: Optional expiration dates for temporary badges
- **Notes**: Add context or reasoning for badge assignments
- **Audit Trail**: All assignments/revocations are logged

### 3. Badge Display

- **Profile Display**: Full-size badges with interactive effects on talent profiles
- **Search Results**: Compact badges in search result listings
- **Visual Effects**: Parallax and tilt effects on hover
- **Responsive Design**: Optimized for all screen sizes

### 4. Analytics & Reporting

- **View Tracking**: Monitor badge display events
- **Click Tracking**: Track user interactions with badges
- **Performance Reports**: Badge effectiveness metrics
- **Distribution Reports**: Badge assignment statistics

## Visual Design

### Badge Appearance

- **Glassy Effect**: Modern backdrop-filter styling with transparency
- **Interactive Effects**: Parallax and 3D tilt animations on hover
- **Customizable Colors**: Admin-defined background and text colors
- **Icon Integration**: Phosphor icons for visual identity
- **Accessibility**: WCAG-compliant color contrast ratios

### Display Modes

1. **Full Size**: Used on talent profiles with complete visual effects
2. **Compact**: Used in search results with simplified styling
3. **Tooltip**: Description appears on hover for additional context

## Performance Considerations

### Optimization Strategies

- **Efficient Queries**: Proper indexing and eager loading
- **CSS Performance**: Hardware-accelerated transforms
- **Lazy Loading**: Effects only activate on user interaction
- **Caching**: Badge data cached at appropriate levels

### Monitoring

- **Render Times**: Track page performance with badges
- **Database Performance**: Monitor query execution times
- **User Experience**: Ensure smooth animations across devices

## Security

### Access Control

- **Super Admin Only**: Badge management restricted to super admin role
- **Audit Logging**: All badge operations logged for security review
- **Input Validation**: Strict validation of colors, icons, and text

### Data Protection

- **SQL Injection Prevention**: Parameterized queries throughout
- **XSS Protection**: Proper escaping of user-generated content
- **CSRF Protection**: Standard Rails CSRF tokens on all forms

## Developer Integration Guide

### Adding Badges to Views

#### Profile Display

```erb
<!-- In talent profile view -->
<% if @user.active_badges.any? %>
  <div class="badges-container mb-4">
    <% @user.active_badges.each do |badge| %>
      <%= render 'shared/badge', badge: badge, size: 'full' %>
    <% end %>
  </div>
<% end %>
```

#### Search Results Display

```erb
<!-- In search result partial -->
<% if talent.active_badges.any? %>
  <div class="badges-compact">
    <% talent.active_badges.limit(3).each do |badge| %>
      <%= render 'shared/badge_compact', badge: badge %>
    <% end %>
  </div>
<% end %>
```

### Badge Component Usage

#### Full Badge Component

```erb
<!-- app/views/shared/_badge.html.erb -->
<%= render 'shared/badge',
    badge: badge_type,
    size: 'full',           # 'full' or 'compact'
    interactive: true,      # Enable hover effects
    tooltip: true          # Show description on hover
%>
```

#### Compact Badge Component

```erb
<!-- app/views/shared/_badge_compact.html.erb -->
<%= render 'shared/badge_compact',
    badge: badge_type,
    show_tooltip: true
%>
```

### JavaScript Integration

#### Stimulus Controller

```javascript
// Badge controller is automatically loaded
// Add data-controller="badge" to enable effects
<div data-controller="badge" class="badge-container">
  <!-- Badge content -->
</div>
```

#### Custom Badge Events

```javascript
// Listen for badge interactions
document.addEventListener("badge:clicked", (event) => {
  const badgeType = event.detail.badgeType;
  const userId = event.detail.userId;
  // Handle badge click analytics
});
```

### Analytics Integration

#### Tracking Badge Views

```ruby
# In controller where badges are displayed
def show
  @user = User.find(params[:id])

  # Track badge view if user has badges
  if @user.active_badges.any?
    BadgeView.create!(
      viewed_user: @user,
      viewer_user: current_user,
      badge_types_displayed: @user.active_badges.pluck(:id),
    )
  end
end
```

#### Tracking Badge Clicks

```javascript
// Automatically tracked by badge controller
// Or manually track:
fetch("/api/badge_clicks", {
  method: "POST",
  headers: { "Content-Type": "application/json" },
  body: JSON.stringify({
    badge_type_id: badgeTypeId,
    user_id: userId,
  }),
});
```

### Performance Optimization

#### Database Queries

```ruby
# Efficient badge loading
users_with_badges =
  User
    .includes(badge_assignments: :badge_type)
    .where(badge_assignments: { id: badge_assignment_ids })

# Preload badges for multiple users
User.includes(:active_badges).where(id: user_ids)
```

#### Caching Strategies

```ruby
# Cache user badges
def active_badges
  Rails
    .cache
    .fetch("user_#{id}_active_badges", expires_in: 1.hour) do
      badge_assignments
        .active
        .by_priority
        .includes(:badge_type)
        .map(&:badge_type)
    end
end
```

### Testing

#### Model Tests

```ruby
# test/models/badge_type_test.rb
test 'validates color format' do
  badge = BadgeType.new(background_color: 'invalid')
  assert_not badge.valid?
  assert_includes badge.errors[:background_color], 'is invalid'
end
```

#### System Tests

```ruby
# test/system/badges_test.rb
test 'displays badges on profile' do
  visit user_path(@user_with_badges)
  assert_selector '.badge', count: @user_with_badges.active_badges.count
end
```

### API Endpoints

#### Badge Analytics API

```ruby
# POST /api/badge_views
# POST /api/badge_clicks
# GET /api/badge_analytics (admin only)
```

### Deployment Considerations

#### Database Migrations

```bash
# Run migrations in order
rails db:migrate
```

#### Asset Compilation

```bash
# Ensure badge styles are compiled
rails assets:precompile
```

#### Environment Variables

```bash
# Optional: Disable color contrast validation in test
VALIDATE_BADGE_COLOR_CONTRAST=false
```
