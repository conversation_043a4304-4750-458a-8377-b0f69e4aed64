# User Impersonation Feature

## Overview

The user impersonation feature allows super administrators to temporarily log in as any user in the system for support and debugging purposes. This feature includes comprehensive security measures, audit logging, and user interface components.

## Architecture

### Core Components

1. **ImpersonationLog Model** - Tracks all impersonation sessions with audit trail
2. **Current Model Extensions** - Manages impersonation state and session detection
3. **SuperAdmin::ImpersonationsController** - <PERSON>les starting/ending impersonation
4. **Impersonation Banner** - Visual indicator when impersonating
5. **Security Restrictions** - Prevents sensitive actions during impersonation

### Security Features

- **Super Admin Only**: Only users with `super_admin: true` can impersonate
- **Audit Logging**: All impersonation sessions are logged with timestamps, IP addresses, and user agents
- **Session Timeout**: Impersonation sessions expire after 24 hours
- **Restricted Actions**: Certain sensitive actions are blocked during impersonation
- **No Nested Impersonation**: Cannot start impersonation while already impersonating
- **Self-Impersonation Prevention**: Admins cannot impersonate themselves

## Usage

### Starting Impersonation

1. Navigate to Super Admin → Users
2. Find the user you want to impersonate
3. Click "Impersonate" button
4. Confirm the action in the modal dialog
5. You'll be redirected to the user's appropriate landing page

### During Impersonation

- A yellow banner appears at the top indicating impersonation status
- Shows who you're impersonating and who the admin is
- Certain actions are restricted for security
- Session automatically expires after 24 hours

### Ending Impersonation

1. Click "Exit Impersonation" button in the banner
2. Confirm the action
3. You'll be returned to the Super Admin users page

## Technical Implementation

### Database Schema

```sql
-- ImpersonationLog table
CREATE TABLE impersonation_logs (
  id BIGINT PRIMARY KEY,
  admin_id BIGINT NOT NULL,
  user_id BIGINT NOT NULL,
  started_at TIMESTAMP NOT NULL,
  ended_at TIMESTAMP,
  ip_address INET NOT NULL,
  user_agent TEXT NOT NULL,
  created_at TIMESTAMP NOT NULL,
  updated_at TIMESTAMP NOT NULL
);
```

### Key Methods

- `Current.impersonating?` - Check if currently impersonating
- `Current.impersonation_expired?` - Check if session has expired
- `Current.impersonator` - Get the admin who started impersonation
- `Current.impersonation_log` - Get the current impersonation log

### Routes

```ruby
namespace :super_admin do
  resources :masquerades, only: [:create, :destroy]
end
```

## Testing

The feature includes comprehensive test coverage:

- **Model Tests**: 13 tests for ImpersonationLog model
- **Current Tests**: 10 tests for Current model extensions  
- **Controller Tests**: 12 tests for ImpersonationsController
- **Integration Tests**: 7 tests for complete impersonation flows

Total: 42 tests with 162 assertions, all passing.

## Configuration

### Timeout Settings

The impersonation timeout is set to 24 hours and can be modified in `app/models/current.rb`:

```ruby
def impersonation_expired?
  return false unless impersonating?
  log = impersonation_log
  return false unless log
  log.started_at < 24.hours.ago  # Modify this value to change timeout
end
```

### Restricted Actions

Actions that are blocked during impersonation can be configured in the application controller's `check_impersonation_restrictions` method.

## Monitoring

All impersonation activities are logged in the `impersonation_logs` table with:
- Admin who performed the impersonation
- User who was impersonated  
- Start and end timestamps
- IP address and user agent
- Duration of the session

## Troubleshooting

### Common Issues

1. **Banner not showing**: Check that `Current.impersonating?` returns true
2. **Session timeout not working**: Verify the impersonation log `started_at` timestamp
3. **Cannot start impersonation**: Ensure user has `super_admin: true`
4. **Restricted actions**: Some actions are intentionally blocked during impersonation

### Debug Information

In development, you can check impersonation state:

```ruby
# In rails console or view
Current.impersonating?
Current.impersonator
Current.impersonation_log
Current.impersonation_expired?
```
