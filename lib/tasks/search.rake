namespace :search do
  desc "Reindex all searchable models"
  task reindex_all: :environment do
    puts "Starting reindex of all searchable models..."
    
    # List of models that use searchkick
    searchable_models = [
      TalentProfile,
      Job,
      JobApplication,
      Conversation
    ]
    
    searchable_models.each do |model|
      puts "Reindexing #{model.name}..."
      begin
        model.reindex
        puts "✓ #{model.name} reindexed successfully"
      rescue => e
        puts "✗ Error reindexing #{model.name}: #{e.message}"
      end
    end
    
    puts "Reindexing complete!"
  end
  
  desc "Reindex TalentProfile model only"
  task reindex_talent_profiles: :environment do
    puts "Reindexing TalentProfile..."
    begin
      TalentProfile.reindex
      puts "✓ TalentProfile reindexed successfully"
    rescue => e
      puts "✗ Error reindexing TalentProfile: #{e.message}"
    end
  end
  
  desc "Check search index status for all models"
  task status: :environment do
    puts "Search Index Status Report"
    puts "=" * 50
    
    searchable_models = [
      TalentProfile,
      Job,
      JobApplication,
      Conversation
    ]
    
    searchable_models.each do |model|
      puts "\n#{model.name}:"
      puts "  Database count: #{model.count}"
      
      begin
        search_result = model.search("*", limit: 0)
        puts "  Search index count: #{search_result.total_count}"
        
        if model.count == search_result.total_count
          puts "  Status: ✓ In sync"
        else
          puts "  Status: ✗ Out of sync (difference: #{model.count - search_result.total_count})"
        end
      rescue => e
        puts "  Status: ✗ Error accessing search index: #{e.message}"
      end
    end
    
    puts "\n" + "=" * 50
  end
end
