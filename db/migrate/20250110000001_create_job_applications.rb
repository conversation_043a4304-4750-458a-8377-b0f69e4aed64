class CreateJobApplications < ActiveRecord::Migration[7.0]
  def change
    create_table :job_applications do |t|
      t.references :job, null: false, foreign_key: true
      t.references :user, null: false, foreign_key: true
      t.integer :status, default: 0
      t.boolean :invited, default: false
      t.datetime :invited_at
      t.datetime :applied_at
      t.datetime :accepted_at
      t.datetime :rejected_at
      t.text :application_letter

      t.timestamps
    end

    add_index :job_applications, [ :job_id, :user_id ], unique: true
  end
end
