class CreateTalentProfiles < ActiveRecord::Migration[7.1]
  def change
    create_table :talent_profiles do |t|
      t.references :user, null: false, foreign_key: true
      t.text :bio
      t.text :looking_for
      t.string :skills, array: true, default: []
      t.text :about
      t.string :vsl_link
      t.integer :availability_status, default: 0
      t.decimal :price_range_min, precision: 10, scale: 2
      t.decimal :price_range_max, precision: 10, scale: 2
      t.integer :pricing_model, default: 0
      t.string :portfolio_link
      t.string :linkedin_url
      t.string :x_url
      t.string :website_url
      t.string :achievement_badges, array: true, default: []
      t.string :platform_choice
      t.string :location
      t.string :instagram_url
      t.string :threads_url
      t.string :niches, array: true, default: []
      t.string :outcomes, array: true, default: []
      t.string :ghostwriter_type, array: true, default: []
      t.string :social_media_specialty, array: true, default: []

      t.timestamps
    end
  end
end
