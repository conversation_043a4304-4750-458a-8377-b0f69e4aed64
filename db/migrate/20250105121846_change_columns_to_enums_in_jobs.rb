class ChangeColumnsToEnumsInJobs < ActiveRecord::Migration[8.0]
  def change
    change_column :jobs, :job_type, :integer, using: 'job_type::integer'
    change_column :jobs, :notification_preference, :integer, using: 'notification_preference::integer'
    change_column :jobs, :outcome, :integer, using: 'outcome::integer'
    change_column :jobs, :payment_frequency, :integer, using: 'payment_frequency::integer'
    change_column :jobs, :platform, :integer, using: 'platform::integer'
  end
end
