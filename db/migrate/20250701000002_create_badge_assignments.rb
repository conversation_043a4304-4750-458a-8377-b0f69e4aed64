class CreateBadgeAssignments < ActiveRecord::Migration[8.0]
  def change
    create_table :badge_assignments do |t|
      # Foreign key relationships
      t.references :badge_type, null: false, foreign_key: true
      t.references :user, null: false, foreign_key: true
      t.references :admin, null: false, foreign_key: { to_table: :users }

      # Assignment metadata
      t.datetime :assigned_at, null: false
      t.datetime :expires_at, null: true
      t.text :notes

      t.timestamps
    end

    # Ensure a user can only have one of each badge type
    # This prevents duplicate badge assignments
    add_index :badge_assignments, %i[badge_type_id user_id], unique: true

    # Performance indexes for common queries
    add_index :badge_assignments, :assigned_at
    add_index :badge_assignments, :expires_at

    # Index for finding all badges assigned by a specific admin
    add_index :badge_assignments,
              :admin_id,
              name: 'index_badge_assignments_on_assigning_admin'

    # Composite index for active badge queries (expires_at IS NULL OR expires_at > NOW())
    # This will help with the active scope in the model
    add_index :badge_assignments, %i[user_id expires_at]

    # Index for badge type queries (finding all users with a specific badge)
    add_index :badge_assignments, %i[badge_type_id assigned_at]
  end
end
