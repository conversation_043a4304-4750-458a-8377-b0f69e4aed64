class CreateSavedSearches < ActiveRecord::Migration[8.0]
  def change
    create_table :saved_searches do |t|
      t.references :user, null: false, foreign_key: true
      t.string :name, null: false, limit: 100
      t.string :resource_type, null: false, limit: 50
      t.text :search_params, null: false

      t.timestamps
    end

    add_index :saved_searches, %i[user_id resource_type]
    add_index :saved_searches, %i[user_id name resource_type], unique: true
    add_index :saved_searches, :resource_type
  end
end
