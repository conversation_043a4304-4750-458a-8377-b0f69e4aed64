class CreateCsvExports < ActiveRecord::Migration[8.0]
  def change
    create_table :csv_exports do |t|
      t.string :export_id, null: false
      t.references :admin_user, null: false, foreign_key: { to_table: :users }
      t.string :controller_name, null: false
      t.text :filters
      t.integer :status, default: 0, null: false
      t.text :error_message
      t.integer :record_count
      t.datetime :started_at
      t.datetime :completed_at

      t.timestamps
    end
    add_index :csv_exports, :export_id, unique: true
  end
end
