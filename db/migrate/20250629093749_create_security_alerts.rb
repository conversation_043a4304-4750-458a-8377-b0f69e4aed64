class CreateSecurityAlerts < ActiveRecord::Migration[8.0]
  def change
    create_table :security_alerts do |t|
      t.references :user, null: false, foreign_key: true
      t.string :alert_type, null: false
      t.string :severity, null: false
      t.text :description, null: false
      t.json :metadata
      t.datetime :resolved_at
      t.references :resolved_by, null: true, foreign_key: { to_table: :users }

      t.timestamps
    end

    add_index :security_alerts, :created_at
    add_index :security_alerts, :alert_type
    add_index :security_alerts, :severity
    add_index :security_alerts, %i[user_id created_at]
    add_index :security_alerts, :resolved_at
  end
end
