class AddCounterCaches < ActiveRecord::Migration[8.0]
  def change
    # Add counter cache for job applications count on jobs
    add_column :jobs, :job_applications_count, :integer, default: 0, null: false

    # Add counter cache for messages count on conversations
    add_column :conversations,
               :messages_count,
               :integer,
               default: 0,
               null: false

    # Add counter cache for jobs count on organizations
    add_column :organizations, :jobs_count, :integer, default: 0, null: false

    # Add counter cache for conversations count on users (for participants)
    add_column :users, :conversations_count, :integer, default: 0, null: false

    # Add counter cache for chat requests sent/received
    add_column :users,
               :sent_chat_requests_count,
               :integer,
               default: 0,
               null: false
    add_column :users,
               :received_chat_requests_count,
               :integer,
               default: 0,
               null: false

    # Reset counter caches for existing records
    reversible do |dir|
      dir.up do
        # Reset job applications count
        execute <<-SQL if defined?(JobApplication)
            UPDATE jobs
            SET job_applications_count = (
              SELECT COUNT(*)
              FROM job_applications
              WHERE job_applications.job_id = jobs.id
            )
          SQL

        # Reset messages count
        execute <<-SQL
          UPDATE conversations
          SET messages_count = (
            SELECT COUNT(*)
            FROM messages
            WHERE messages.conversation_id = conversations.id
          )
        SQL

        # Reset jobs count
        execute <<-SQL
          UPDATE organizations
          SET jobs_count = (
            SELECT COUNT(*)
            FROM jobs
            WHERE jobs.organization_id = organizations.id
          )
        SQL

        # Reset chat requests count
        execute <<-SQL
          UPDATE users
          SET sent_chat_requests_count = (
            SELECT COUNT(*)
            FROM chat_requests
            WHERE chat_requests.scout_id = users.id
          )
        SQL

        execute <<-SQL
          UPDATE users
          SET received_chat_requests_count = (
            SELECT COUNT(*)
            FROM chat_requests
            WHERE chat_requests.talent_id = users.id
          )
        SQL

        # Reset conversations count (count participants)
        execute <<-SQL
          UPDATE users
          SET conversations_count = (
            SELECT COUNT(*)
            FROM conversation_participants
            WHERE conversation_participants.user_id = users.id
          )
        SQL
      end
    end
  end
end
