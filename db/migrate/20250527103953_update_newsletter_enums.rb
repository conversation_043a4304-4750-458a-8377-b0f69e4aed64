class UpdateNewsletterEnums < ActiveRecord::Migration[8.0]
  def up
    # Update existing newsletter_frequency values to match new enum
    execute "UPDATE jobs SET newsletter_frequency = 'monthly' WHERE newsletter_frequency = 'monthly_newsletter'"
    
    # Update existing newsletter_length values to match new enum
    execute "UPDATE jobs SET newsletter_length = 'under_300_words' WHERE newsletter_length = 'short'"
    execute "UPDATE jobs SET newsletter_length = 'words_300_600' WHERE newsletter_length = 'medium'"
    execute "UPDATE jobs SET newsletter_length = 'words_600_1000_plus' WHERE newsletter_length = 'long'"
  end

  def down
    # Revert newsletter_frequency values
    execute "UPDATE jobs SET newsletter_frequency = 'monthly_newsletter' WHERE newsletter_frequency = 'monthly'"
    
    # Revert newsletter_length values
    execute "UPDATE jobs SET newsletter_length = 'short' WHERE newsletter_length = 'under_300_words'"
    execute "UPDATE jobs SET newsletter_length = 'medium' WHERE newsletter_length = 'words_300_600'"
    execute "UPDATE jobs SET newsletter_length = 'long' WHERE newsletter_length = 'words_600_1000_plus'"
  end
end
