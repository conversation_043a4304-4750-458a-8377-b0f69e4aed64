class CreateAdminAuditLogs < ActiveRecord::Migration[8.0]
  def change
    create_table :admin_audit_logs do |t|
      t.references :admin_user, null: false, foreign_key: { to_table: :users }
      t.string :action, null: false
      t.string :controller, null: false
      t.references :resource, polymorphic: true, null: true
      t.json :changes
      t.string :ip_address
      t.string :user_agent

      t.timestamps
    end

    add_index :admin_audit_logs, :created_at
    add_index :admin_audit_logs, :action
    add_index :admin_audit_logs, %i[resource_type resource_id]
  end
end
