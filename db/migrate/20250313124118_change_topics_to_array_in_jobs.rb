class ChangeTopicsToArrayInJobs < ActiveRecord::Migration[8.0]
  def up
    # First, create a temporary column to store the array
    add_column :jobs, :topics_array, :string, array: true, default: []

    # Copy data from the text column to the array column
    # This will split the comma-separated values into an array
    execute <<-SQL
      UPDATE jobs
      SET topics_array = string_to_array(topics, ',')
      WHERE topics IS NOT NULL
    SQL

    # Remove the old column
    remove_column :jobs, :topics

    # Rename the new column to the original name
    rename_column :jobs, :topics_array, :topics
  end

  def down
    # First, create a temporary text column
    add_column :jobs, :topics_text, :text

    # Copy data from the array column to the text column
    # This will join the array elements with commas
    execute <<-SQL
      UPDATE jobs
      SET topics_text = array_to_string(topics, ',')
      WHERE topics IS NOT NULL
    SQL

    # Remove the array column
    remove_column :jobs, :topics

    # Rename the text column to the original name
    rename_column :jobs, :topics_text, :topics
  end
end
