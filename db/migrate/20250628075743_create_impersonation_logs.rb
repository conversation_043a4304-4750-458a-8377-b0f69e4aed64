class CreateImpersonationLogs < ActiveRecord::Migration[8.0]
  def change
    create_table :impersonation_logs do |t|
      t.references :admin, null: false, foreign_key: { to_table: :users }
      t.references :user, null: false, foreign_key: true
      t.datetime :started_at, null: false
      t.datetime :ended_at
      t.string :ip_address
      t.string :user_agent

      t.timestamps
    end
  end
end
