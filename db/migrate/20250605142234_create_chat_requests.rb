class CreateChatRequests < ActiveRecord::Migration[8.0]
  def change
    create_table :chat_requests do |t|
      t.references :scout, null: false, foreign_key: { to_table: :users }
      t.references :talent, null: false, foreign_key: { to_table: :users }
      t.integer :status, default: 0, null: false
      t.datetime :requested_at
      t.datetime :accepted_at
      t.datetime :declined_at

      t.timestamps
    end

    # Add unique index to prevent duplicate pending requests
    add_index :chat_requests,
              %i[scout_id talent_id status],
              unique: true,
              where: 'status = 0',
              name: 'index_chat_requests_unique_pending'
  end
end
