class AddBadgePerformanceIndexes < ActiveRecord::Migration[8.0]
  def change
    # Additional performance indexes for complex badge queries

    # Composite index for finding active badges by priority for a specific user
    # This supports the common query: user.badge_assignments.active.by_priority
    add_index :badge_assignments,
              %i[user_id assigned_at expires_at],
              name: 'index_badge_assignments_on_user_active_priority'

    # Index for admin audit queries - finding all badge assignments by admin in date range
    add_index :badge_assignments,
              %i[admin_id assigned_at],
              name: 'index_badge_assignments_on_admin_and_date'

    # Index for badge type analytics - finding assignment counts and trends
    add_index :badge_assignments,
              %i[badge_type_id assigned_at expires_at],
              name: 'index_badge_assignments_on_type_date_expiry'

    # Partial index for active assignments only (PostgreSQL specific optimization)
    # This will be much smaller and faster for active badge queries
    # Note: We use a simple condition that doesn't rely on NOW() to avoid immutability issues
    execute <<-SQL if connection.adapter_name.downcase.include?('postgresql')
        CREATE INDEX index_badge_assignments_active_only
        ON badge_assignments (user_id, badge_type_id, assigned_at)
        WHERE expires_at IS NULL;
      SQL

    # Index for badge type management - finding which badges are currently in use
    # This supports the restriction on deleting badge types that are assigned
    # We'll handle expiration logic in the application layer for better performance
    add_index :badge_assignments,
              :badge_type_id,
              where: 'expires_at IS NULL',
              name: 'index_badge_assignments_active_by_type'
  end

  def down
    # Remove the custom PostgreSQL index if it exists
    if connection.adapter_name.downcase.include?('postgresql')
      execute 'DROP INDEX IF EXISTS index_badge_assignments_active_only;'
    end

    # Rails will automatically remove the other indexes when rolling back
  end
end
