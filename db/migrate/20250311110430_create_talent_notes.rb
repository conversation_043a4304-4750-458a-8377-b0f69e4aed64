class CreateTalentNotes < ActiveRecord::Migration[8.0]
  def change
    create_table :talent_notes do |t|
      t.references :talent_profile, null: false, foreign_key: true
      t.references :user, null: false, foreign_key: true
      t.references :organization, null: false, foreign_key: true
      t.references :last_modified_by, foreign_key: { to_table: :users }
      t.integer :category, default: 0
      t.boolean :pinned, default: false

      t.timestamps
    end
  end
end
