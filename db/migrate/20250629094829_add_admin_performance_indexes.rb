class AddAdminPerformanceIndexes < ActiveRecord::Migration[8.0]
  def change
    # Jobs table indexes for admin filtering and sorting
    add_index :jobs, :status
    add_index :jobs, :job_category
    add_index :jobs, :platform
    add_index :jobs, :budget_range
    add_index :jobs, :is_premium
    add_index :jobs, :published_at
    add_index :jobs, :expires_at
    add_index :jobs, :topics, using: :gin # GIN index for array searches

    # Composite indexes for common job filter combinations
    add_index :jobs, %i[status created_at]
    add_index :jobs, %i[organization_id status]
    add_index :jobs, %i[is_premium status]

    # Users table indexes for admin filtering and sorting
    add_index :users, :verified
    add_index :users, :onboarding_completed
    add_index :users, :scout_signup_completed
    add_index :users, :talent_signup_completed
    add_index :users, :first_name
    add_index :users, :last_name

    # Composite indexes for user searches and filtering
    add_index :users, %i[first_name last_name]
    add_index :users, %i[verified created_at]

    # Messages table indexes for admin interface
    add_index :messages, :created_at
    add_index :messages, :read_at
    add_index :messages, %i[conversation_id created_at]

    # Conversations table indexes
    add_index :conversations, :created_at
    add_index :conversations, :updated_at
    add_index :conversations, %i[job_id created_at]

    # Job Applications indexes
    add_index :job_applications, :status if table_exists?(:job_applications)
    add_index :job_applications, :created_at if table_exists?(:job_applications)
    if table_exists?(:job_applications)
      add_index :job_applications, %i[job_id status]
    end
    if table_exists?(:job_applications)
      add_index :job_applications, %i[user_id status]
    end

    # Chat Requests indexes
    add_index :chat_requests, :status
    add_index :chat_requests, :created_at
    add_index :chat_requests, %i[scout_id status]
    add_index :chat_requests, %i[talent_id status]

    # Talent Profiles indexes (if table exists)
    if table_exists?(:talent_profiles)
      add_index :talent_profiles, :skills, using: :gin # GIN index for array searches
      if column_exists?(:talent_profiles, :is_premium)
        add_index :talent_profiles, :is_premium
      end
      if column_exists?(:talent_profiles, :availability)
        add_index :talent_profiles, :availability
      end
      if column_exists?(:talent_profiles, :location)
        add_index :talent_profiles, :location
      end
      if column_exists?(:talent_profiles, :is_premium)
        add_index :talent_profiles, %i[user_id is_premium]
      end
    end

    # Organizations indexes for admin interface
    add_index :organizations, :created_at
    add_index :organizations, :name if column_exists?(:organizations, :name)

    # Roles and UserRoles indexes for admin role management
    if table_exists?(:user_roles)
      unless index_exists?(:user_roles, %i[user_id role_id])
        add_index :user_roles, %i[user_id role_id], unique: true
      end
      unless index_exists?(:user_roles, :created_at)
        add_index :user_roles, :created_at
      end
    end

    if table_exists?(:roles)
      add_index :roles, :name, unique: true unless index_exists?(:roles, :name)
      add_index :roles, :created_at unless index_exists?(:roles, :created_at)
    end

    # Admin Audit Logs additional indexes (some may already exist)
    unless index_exists?(:admin_audit_logs, :controller)
      add_index :admin_audit_logs, :controller
    end
    unless index_exists?(:admin_audit_logs, %i[admin_user_id created_at])
      add_index :admin_audit_logs, %i[admin_user_id created_at]
    end
    unless index_exists?(:admin_audit_logs, %i[resource_type created_at])
      add_index :admin_audit_logs, %i[resource_type created_at]
    end
  end
end
