class CreateBadgeAnalyticsTables < ActiveRecord::Migration[8.0]
  def change
    # Create badge_views table for tracking profile views with badges
    create_table :badge_views do |t|
      t.references :viewed_user, null: false, foreign_key: { to_table: :users }
      t.references :viewer_user, null: true, foreign_key: { to_table: :users }
      t.references :session, null: true, foreign_key: true
      t.json :badge_types_displayed, null: false
      t.string :ip_address
      t.string :user_agent
      t.string :referrer
      t.string :request_path
      t.json :metadata

      t.timestamps
    end

    # Create badge_clicks table for tracking badge click interactions
    create_table :badge_clicks do |t|
      t.references :badge_type, null: false, foreign_key: true
      t.references :badge_owner, null: false, foreign_key: { to_table: :users }
      t.references :clicker_user, null: true, foreign_key: { to_table: :users }
      t.references :session, null: true, foreign_key: true
      t.string :click_context, null: false
      t.string :ip_address
      t.string :user_agent
      t.string :referrer
      t.string :request_path
      t.json :metadata

      t.timestamps
    end

    # Add indexes for badge_views table (skip single column indexes that are auto-created by foreign keys)
    add_index :badge_views, :created_at
    add_index :badge_views, :ip_address
    add_index :badge_views, %i[viewed_user_id created_at]
    add_index :badge_views, %i[viewer_user_id created_at]
    add_index :badge_views, %i[created_at viewed_user_id]

    # Add indexes for badge_clicks table (skip single column indexes that are auto-created by foreign keys)
    add_index :badge_clicks, :created_at
    add_index :badge_clicks, :click_context
    add_index :badge_clicks, :ip_address
    add_index :badge_clicks, %i[badge_type_id created_at]
    add_index :badge_clicks, %i[badge_owner_id created_at]
    add_index :badge_clicks, %i[clicker_user_id created_at]
    add_index :badge_clicks, %i[click_context created_at]
    add_index :badge_clicks, %i[created_at badge_type_id]
  end
end
