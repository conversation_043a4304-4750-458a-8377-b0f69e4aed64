class CreateSessionActivities < ActiveRecord::Migration[8.0]
  def change
    create_table :session_activities do |t|
      t.references :session, null: false, foreign_key: true
      t.string :activity_type, null: false
      t.string :controller
      t.string :action
      t.string :ip_address
      t.string :user_agent
      t.string :request_path
      t.json :metadata

      t.timestamps
    end

    add_index :session_activities, :created_at
    add_index :session_activities, :activity_type
    add_index :session_activities, %i[session_id created_at]
    add_index :session_activities, :ip_address
  end
end
