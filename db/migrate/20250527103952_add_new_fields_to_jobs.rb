class AddNewFieldsToJobs < ActiveRecord::Migration[8.0]
  def change
    add_column :jobs, :newsletter_frequency, :string
    add_column :jobs, :newsletter_length, :string
    add_column :jobs, :lead_magnet_type, :string
    add_column :jobs, :work_duration, :string
    add_column :jobs, :target_audience_description, :text
    add_column :jobs, :emulated_brands_description, :text
    add_column :jobs, :involvement_level, :string
    add_column :jobs, :social_media_goal_type, :string
    add_column :jobs, :social_media_understands_risk_acknowledged, :boolean
  end
end
