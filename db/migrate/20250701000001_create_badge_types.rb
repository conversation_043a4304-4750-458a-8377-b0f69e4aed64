class CreateBadgeTypes < ActiveRecord::Migration[8.0]
  def change
    create_table :badge_types do |t|
      # Core badge properties
      t.string :name, null: false
      t.text :description, null: false
      
      # Visual customization
      t.string :background_color, null: false, default: '#ffffff'
      t.string :text_color, null: false, default: '#000000'
      t.string :icon, null: false
      
      # Display and management
      t.integer :priority, default: 0
      t.boolean :active, default: true
      
      t.timestamps
    end
    
    # Add unique constraint on name to prevent duplicate badge types
    add_index :badge_types, :name, unique: true
    
    # Add index for active badges (most common query)
    add_index :badge_types, :active
    
    # Add index for priority ordering
    add_index :badge_types, :priority
    
    # Composite index for active badges ordered by priority (common query pattern)
    add_index :badge_types, [:active, :priority]
  end
end
