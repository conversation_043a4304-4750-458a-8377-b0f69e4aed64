name: "ghostwrote"

services:
  rails-app:
    build:
      context: ..
      dockerfile: .devcontainer/Dockerfile

    volumes:
    - ../..:/workspaces:cached

    # Overrides default command so things don't shut down after the process ends.
    command: sleep infinity

    # Uncomment the next line to use a non-root user for all processes.
    # user: vscode

    # Use "forwardPorts" in **devcontainer.json** to forward an app port locally.
    # (Adding the "ports" property to this file will not forward from a Codespace.)
    depends_on:
    - selenium
    - postgres

  selenium:
    image: selenium/standalone-chromium
    restart: unless-stopped

  postgres:
    image: postgres:16.1
    restart: unless-stopped
    networks:
    - default
    volumes:
    - postgres-data:/var/lib/postgresql/data
    environment:
        POSTGRES_USER: postgres
        POSTGRES_PASSWORD: postgres

volumes:
  postgres-data:
