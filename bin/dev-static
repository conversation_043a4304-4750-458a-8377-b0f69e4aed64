#!/usr/bin/env ruby
# frozen_string_literal: true

def installed?(process)
  IO.popen "#{process} -v"
rescue Errno::ENOENT
  false
end

def run(process)
  system "#{process} start -f Procfile.dev-static"
rescue Errno::ENOENT
  warn <<~MSG
    ERROR:
    Please ensure `Procfile.dev-static` exists in your project!
  MSG
  exit!
end

if installed? "overmind"
  run "overmind"
elsif installed? "foreman"
  run "foreman"
else
  warn <<~MSG
    NOTICE:
    For this script to run, you need either 'overmind' or 'foreman' installed on your machine. Please try this script after installing one of them.
  MSG
  exit!
end
