#!/usr/bin/env node

/**
 * Playwright MCP Demo Script
 *
 * This script demonstrates basic web browsing capabilities using Playwright MCP.
 * It shows how to navigate to websites, take screenshots, and interact with pages.
 */

const { spawn } = require("child_process");
const path = require("path");

class PlaywrightDemo {
  constructor() {
    this.mcpProcess = null;
  }

  async startMCPServer() {
    console.log("🚀 Starting Playwright MCP server...");

    const configPath = path.join(__dirname, "playwright-mcp-config.json");

    this.mcpProcess = spawn(
      "node",
      [
        "node_modules/@playwright/mcp/cli.js",
        "--config",
        configPath,
        "--port",
        "8931",
      ],
      {
        stdio: "pipe",
      }
    );

    this.mcpProcess.stdout.on("data", (data) => {
      console.log(`📊 MCP: ${data.toString().trim()}`);
    });

    this.mcpProcess.stderr.on("data", (data) => {
      console.error(`❌ MCP Error: ${data.toString().trim()}`);
    });

    this.mcpProcess.on("error", (error) => {
      console.error("❌ Failed to start MCP server:", error);
    });

    // Wait a bit for the server to start
    await new Promise((resolve) => setTimeout(resolve, 3000));
    console.log("✅ MCP server should be running on http://localhost:8931");
  }

  async runDemo() {
    console.log("🎭 Playwright MCP Demo");
    console.log("======================");
    console.log("");

    await this.startMCPServer();

    console.log("📝 Demo Instructions:");
    console.log("");
    console.log("1. The Playwright MCP server is now running");
    console.log(
      "2. You can connect to it via MCP clients or use the SSE endpoint"
    );
    console.log("3. Server endpoint: http://localhost:8931/sse");
    console.log("");
    console.log("🔧 Available Tools:");
    console.log("  • browser_navigate - Navigate to URLs");
    console.log("  • browser_snapshot - Get page accessibility snapshot");
    console.log("  • browser_click - Click elements");
    console.log("  • browser_type - Type text");
    console.log("  • browser_take_screenshot - Take screenshots");
    console.log("  • browser_tab_new - Open new tabs");
    console.log("  • And many more...");
    console.log("");
    console.log("🌐 Example Usage:");
    console.log(
      '  1. Navigate to a website: browser_navigate("https://example.com")'
    );
    console.log("  2. Take a snapshot: browser_snapshot()");
    console.log("  3. Take a screenshot: browser_take_screenshot()");
    console.log("  4. Click an element: browser_click(element, ref)");
    console.log("");
    console.log(
      "💡 Tip: Use the accessibility snapshot to find element references"
    );
    console.log(
      "    for interactions instead of relying on visual coordinates."
    );
    console.log("");
    console.log("Press Ctrl+C to stop the demo and server.");

    // Keep the process running
    process.on("SIGINT", () => {
      this.stop();
    });

    // Keep alive
    setInterval(() => {
      // Just keep the process alive
    }, 1000);
  }

  stop() {
    console.log("\n🛑 Stopping Playwright MCP demo...");
    if (this.mcpProcess) {
      this.mcpProcess.kill("SIGTERM");
    }
    process.exit(0);
  }
}

// Run the demo if this script is executed directly
if (require.main === module) {
  const demo = new PlaywrightDemo();
  demo.runDemo().catch(console.error);
}

module.exports = PlaywrightDemo;
