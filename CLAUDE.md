# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

```bash
# Start development server with all services
bin/dev

# Individual development services
rails s -b 0.0.0.0 -p 5010          # Rails server
bin/shakapacker-dev-server           # Webpack dev server with HMR
bin/shakapacker --watch              # Webpack watcher (server bundle only)
yarn build:css --watch               # Tailwind CSS watcher

# CSS build
yarn build:css

# Testing
rails test                           # Unit/integration tests
rails test:system                    # System tests with Capybara
rails test test/models/user_test.rb  # Run single test file

# Playwright automation
yarn playwright:demo                 # Demo automation
yarn playwright:test                 # Test automation
yarn playwright:mcp                  # MCP integration

# Code quality
bundle exec rubocop                  # Ruby linting (Rails Omakase style)
bundle exec brakeman                 # Security analysis
bundle exec erb_lint                 # ERB template linting
```

## Architecture Overview

**Ghostwrote** is a Ruby on Rails 8.0.2 marketplace connecting businesses (scouts) with ghostwriters (talent). The application uses a modern Rails stack with Hotwire for SPA-like behavior and React components for enhanced interactivity.

### User Types & Core Models
- **Scouts**: Businesses posting jobs (`scout/` namespace)
- **Talent**: Ghostwriters applying for jobs (`talent/` namespace)
- **Superadmin**: Administrative users with elevated permissions

Key models: `User`, `Organization`, `Job`, `TalentProfile`, `JobApplication`, `Conversation`, `Message`, `ChatRequest`

### Tech Stack
- **Backend**: Rails 8.0.2, PostgreSQL, Elasticsearch (via Searchkick)
- **Frontend**: Hotwire (Turbo + Stimulus), React on Rails, Tailwind CSS
- **Caching/Jobs**: Solid Cache, Solid Queue, Solid Cable
- **Authentication**: Authentication Zero with custom roles
- **Payments**: Pay gem with Stripe integration
- **Asset Pipeline**: Shakapacker (Webpack), Propshaft
- **Deployment**: Kamal with Docker

### Form Wizard System

The application features a sophisticated form wizard architecture for job posting:
- **Centralized configuration**: `app/javascript/form_wizard/form_config.js`
- **Step management**: `app/javascript/form_wizard/step_manager.js` 
- **Unified validation**: `app/javascript/form_wizard/validator.js`
- **Category-specific steps**: Different workflows for social_media, newsletter, lead_magnet jobs
- **Progress tracking**: Visual progress indicators and state persistence

See `FORM_WIZARD_REFACTOR.md` for detailed architecture documentation.

### Directory Structure

```
app/
├── controllers/
│   ├── scout/          # Business user controllers
│   ├── talent/         # Ghostwriter controllers
│   └── identity/       # Authentication (Authentication Zero)
├── models/
│   ├── concerns/       # Shared model concerns
│   └── pay/           # Payment-related models (Pay gem)
├── views/
│   ├── scout/         # Business interface views
│   ├── talent/        # Ghostwriter interface views
│   └── shared/        # Shared components
├── javascript/
│   ├── controllers/   # Stimulus controllers
│   ├── components/    # React components
│   └── form_wizard/   # Form wizard system
```

### Key Patterns
- **Namespace separation**: Clean separation between scout and talent functionality
- **Enum-heavy models**: Extensive use of Rails enums for job categories, statuses, etc.
- **Stimulus-first interactivity**: Modern JavaScript with Stimulus controllers
- **Tailwind-first styling**: Utility-first CSS approach with custom plugins
- **Multi-tenant architecture**: Organization-based tenancy
- **Background job processing**: Solid Queue for async operations
- **Real-time features**: Solid Cable for WebSocket connections

### Testing Strategy
- **Unit tests**: Models and controllers in `test/models/`, `test/controllers/`
- **Integration tests**: Complex workflows
- **System tests**: End-to-end testing with Capybara and Selenium
- **Playwright automation**: Browser automation for complex user flows
- **Test fixtures**: Consistent test data setup

### Development Notes
- The application is actively being developed with frequent changes to job posting functionality
- Payment integration is handled via the Pay gem with Stripe
- Search functionality uses Elasticsearch through Searchkick
- Real-time messaging system between scouts and talent
- Multi-step job posting wizard with category-specific workflows
- File uploads handled via Active Storage with AWS S3 support