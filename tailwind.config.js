const defaultTheme = require("tailwindcss/defaultTheme");

module.exports = {
  content: [
    "./public/*.html",
    "./app/helpers/**/*.rb",
    "./app/javascript/**/*.js",
    "./app/javascript/**/*.jsx",
    "./app/views/**/*.{erb,haml,html,slim}",
    "./app/assets/stylesheets/**/*.css",
  ],
  safelist: [
    "admin-badge",
    "admin-badge-primary",
    "admin-badge-success",
    "admin-badge-warning",
    "admin-badge-danger",
    "admin-badge-info",
    "admin-badge-premium",
    // Badge component classes
    "badge",
    "badge-compact",
    "badge-icon",
    "badge-name",
    "badge-tooltip",
    "badge-tooltip-compact",
    "badge-sm",
    "badge-lg",
    "badge-verified",
    "badge-choice",
    "badge-premium",
    "badge-expert",
    "badge-featured",
    "badge-new",
    "badge-loading",
  ],
  theme: {
    extend: {
      fontFamily: {
        sans: ["Inter var", ...defaultTheme.fontFamily.sans],
      },
    },
  },
  plugins: [
    require("@tailwindcss/forms"),
    require("@tailwindcss/typography"),
    require("@tailwindcss/container-queries"),
  ],
};
