version: '3.8'

volumes:
  socket_ghost:
    driver: local
  pgdata_ghost:
    driver: local
  minio_data_ghost:
    driver: local
  esdata:
    driver: local


services:
  db:
    image: postgres:17
    container_name: postgres_ghost
    ports:
      - 5432:5432
    volumes:
      - socket_ghost:/var/run/postgresql/
      - pgdata_ghost:/var/lib/postgresql/data
    environment:
      POSTGRES_HOST: "localhost"
      POSTGRES_HOST_AUTH_METHOD: "trust"
      POSTGRES_PASSWORD: "password"
      POSTGRES_USER: "postgres"
  
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.17.0
    container_name: elasticsearch
    environment:
      - discovery.type=single-node
      - ES_JAVA_OPTS=-Xms512m -Xmx512m
      - xpack.security.enabled=false
    ports:
      - "9200:9200"
    volumes:
      - esdata:/usr/share/elasticsearch/data

  aws:
    image: minio/minio:latest
    container_name: aws_minio_ghost
    ports:
      - 9000:9000
      - 9001:9001
    command: server /data --console-address ":9001"
    environment:
      MINIO_ACCESS_KEY: minio
      MINIO_SECRET_KEY: minio123
      MINIO_REGION_NAME: myregion
      MINIO_API_CORS_ALLOW_ORIGIN: '*'
    volumes:
      - minio_data_ghost:/data

  mailpit:
    image: axllent/mailpit
    container_name: mailpit_ghost
    ports:
      - "1025:1025" # SMTP port
      - "8025:8025" # Web interface
