Directory structure:
└── lazaronixon-authentication-zero/
├── README.md
├── authentication-zero.gemspec
├── CHANGELOG.md
├── CODE_OF_CONDUCT.md
├── Gemfile
├── LICENSE.txt
├── Rakefile
├── .rubocop.yml
├── lib/
│ ├── authentication-zero.rb
│ ├── authentication_zero.rb
│ ├── authentication_zero/
│ │ └── version.rb
│ └── generators/
│ └── authentication/
│ ├── authentication_generator.rb
│ ├── USAGE
│ └── templates/
│ ├── config/
│ │ └── initializers/
│ │ ├── omniauth.rb
│ │ └── webauthn.rb
│ ├── controllers/
│ │ ├── api/
│ │ │ ├── application_controller.rb.tt
│ │ │ ├── passwords_controller.rb.tt
│ │ │ ├── registrations_controller.rb.tt
│ │ │ ├── sessions_controller.rb.tt
│ │ │ ├── authentications/
│ │ │ │ └── events_controller.rb.tt
│ │ │ └── identity/
│ │ │ ├── email_verifications_controller.rb.tt
│ │ │ ├── emails_controller.rb.tt
│ │ │ └── password_resets_controller.rb.tt
│ │ └── html/
│ │ ├── application_controller.rb.tt
│ │ ├── home_controller.rb.tt
│ │ ├── invitations_controller.rb.tt
│ │ ├── masquerades_controller.rb.tt
│ │ ├── passwords_controller.rb.tt
│ │ ├── registrations_controller.rb.tt
│ │ ├── sessions_controller.rb.tt
│ │ ├── authentications/
│ │ │ └── events_controller.rb.tt
│ │ ├── identity/
│ │ │ ├── email_verifications_controller.rb.tt
│ │ │ ├── emails_controller.rb.tt
│ │ │ └── password_resets_controller.rb.tt
│ │ ├── sessions/
│ │ │ ├── omniauth_controller.rb.tt
│ │ │ ├── passwordlesses_controller.rb.tt
│ │ │ └── sudos_controller.rb.tt
│ │ └── two_factor_authentication/
│ │ ├── challenge/
│ │ │ ├── recovery_codes_controller.rb.tt
│ │ │ ├── security_keys_controller.rb.tt
│ │ │ └── totps_controller.rb.tt
│ │ └── profile/
│ │ ├── recovery_codes_controller.rb.tt
│ │ ├── security_keys_controller.rb.tt
│ │ └── totps_controller.rb.tt
│ ├── erb/
│ │ ├── authentications/
│ │ │ └── events/
│ │ │ └── index.html.erb.tt
│ │ ├── home/
│ │ │ └── index.html.erb.tt
│ │ ├── identity/
│ │ │ ├── emails/
│ │ │ │ └── edit.html.erb.tt
│ │ │ └── password_resets/
│ │ │ ├── edit.html.erb.tt
│ │ │ └── new.html.erb.tt
│ │ ├── invitations/
│ │ │ └── new.html.erb.tt
│ │ ├── passwords/
│ │ │ └── edit.html.erb.tt
│ │ ├── registrations/
│ │ │ └── new.html.erb.tt
│ │ ├── sessions/
│ │ │ ├── index.html.erb.tt
│ │ │ ├── new.html.erb.tt
│ │ │ ├── passwordlesses/
│ │ │ │ └── new.html.erb.tt
│ │ │ └── sudos/
│ │ │ └── new.html.erb.tt
│ │ ├── two_factor_authentication/
│ │ │ ├── challenge/
│ │ │ │ ├── recovery_codes/
│ │ │ │ │ └── new.html.erb.tt
│ │ │ │ ├── security_keys/
│ │ │ │ │ └── new.html.erb.tt
│ │ │ │ └── totps/
│ │ │ │ └── new.html.erb.tt
│ │ │ └── profile/
│ │ │ ├── recovery_codes/
│ │ │ │ ├── \_recovery_code.html.erb.tt
│ │ │ │ └── index.html.erb.tt
│ │ │ ├── security_keys/
│ │ │ │ ├── \_form_confirm.html.erb.tt
│ │ │ │ ├── \_form_edit.html.erb.tt
│ │ │ │ ├── \_security_key.html.erb.tt
│ │ │ │ ├── edit.html.erb.tt
│ │ │ │ ├── index.html.erb.tt
│ │ │ │ └── new.html.erb.tt
│ │ │ └── totps/
│ │ │ └── new.html.erb.tt
│ │ └── user_mailer/
│ │ ├── email_verification.html.erb.tt
│ │ ├── invitation_instructions.html.erb.tt
│ │ ├── password_reset.html.erb.tt
│ │ └── passwordless.html.erb.tt
│ ├── javascript/
│ │ └── controllers/
│ │ └── web_authn_controller.js
│ ├── lib/
│ │ └── account_middleware.rb
│ ├── mailers/
│ │ └── user_mailer.rb.tt
│ ├── migrations/
│ │ ├── create_accounts_migration.rb.tt
│ │ ├── create_events_migration.rb.tt
│ │ ├── create_recovery_codes_migration.rb.tt
│ │ ├── create_security_keys_migration.rb.tt
│ │ ├── create_sessions_migration.rb.tt
│ │ ├── create_sign_in_tokens_migration.rb.tt
│ │ └── create_users_migration.rb.tt
│ ├── models/
│ │ ├── account.rb.tt
│ │ ├── current.rb.tt
│ │ ├── event.rb.tt
│ │ ├── recovery_code.rb.tt
│ │ ├── security_key.rb.tt
│ │ ├── session.rb.tt
│ │ ├── sign_in_token.rb.tt
│ │ ├── user.rb.tt
│ │ └── concerns/
│ │ └── account_scoped.rb
│ └── test_unit/
│ ├── test_helper.rb.tt
│ ├── users.yml
│ ├── controllers/
│ │ ├── api/
│ │ │ ├── passwords_controller_test.rb.tt
│ │ │ ├── registrations_controller_test.rb.tt
│ │ │ ├── sessions_controller_test.rb.tt
│ │ │ └── identity/
│ │ │ ├── email_verifications_controller_test.rb.tt
│ │ │ ├── emails_controller_test.rb.tt
│ │ │ └── password_resets_controller_test.rb.tt
│ │ └── html/
│ │ ├── passwords_controller_test.rb.tt
│ │ ├── registrations_controller_test.rb.tt
│ │ ├── sessions_controller_test.rb.tt
│ │ └── identity/
│ │ ├── email_verifications_controller_test.rb.tt
│ │ ├── emails_controller_test.rb.tt
│ │ └── password_resets_controller_test.rb.tt
│ └── mailers/
│ └── user_mailer_test.rb.tt
└── .github/
├── FUNDING.yml
└── workflows/
└── CI.yml

Files Content:

================================================
FILE: README.md
================================================

# Authentication Zero

The purpose of authentication zero is to generate a pre-built authentication system into a rails application (web or api-only) that follows both security and rails best practices. By generating code into the user's application instead of using a library, the user has complete freedom to modify the authentication system so it works best with their app.

## Installation

```
$ bundle add authentication-zero
```

If you are using Rails < 7.2, you must use version 3.

```
$ bundle add authentication-zero --version "~> 3"
```

If you are using Rails < 7.1, you must use version 2.

```
$ bundle add authentication-zero --version "~> 2"
```

## Usage

```
$ rails generate authentication
```

## Developer responsibilities

Since Authentication Zero generates this code into your application instead of building these modules into the gem itself, you now have complete freedom to modify the authentication system, so it works best with your use case. The one caveat with using a generated authentication system is it will not be updated after it's been generated. Therefore, as improvements are made to the output of `rails generate authentication`, it becomes your responsibility to determine if these changes need to be ported into your application. Security-related and other important improvements will be explicitly and clearly marked in the `CHANGELOG.md` file and upgrade notes.

## Features

### Essential

- Sign up
- Email and password validations
- Checks if a password has been found in any data breach (--pwned)
- Authentication by cookie
- Authentication by token (--api)
- Two factor authentication + recovery codes (--two-factor)
- Two factor authentication using a hardware security key (--webauthn)
- Verify email using a link with token
- Ask password before sensitive data changes, aka: sudo (--sudoable)
- Reset the user password and send reset instructions
- Reset the user password only from verified emails
- Lock mechanism to prevent email bombing (--lockable)
- Send e-mail confirmation when your email has been changed
- Manage multiple sessions & devices
- Activity log (--trackable)
- Log out

### More

- Social login with omni auth (--omniauthable)
- Passwordless authentication (--passwordless)
- Send invitations (--invitable)
- "Sign-in as" button (--masqueradable)
- Multi-tentant application (--tenantable)

## Generated code

- [has_secure_password](https://api.rubyonrails.org/classes/ActiveModel/SecurePassword/ClassMethods.html#method-i-has_secure_password): Adds methods to set and authenticate against a bcrypt password.
- [authenticate_by](https://edgeapi.rubyonrails.org/classes/ActiveRecord/SecurePassword/ClassMethods.html#method-i-authenticate_by): Given a set of attributes, finds a record using the non-password attributes, and then authenticates that record using the password attributes.
- [generates_token_for](https://edgeapi.rubyonrails.org/classes/ActiveRecord/TokenFor/ClassMethods.html#method-i-generates_token_for): Defines the behavior of tokens generated for a specific purpose.
- [signed cookies](https://api.rubyonrails.org/classes/ActionDispatch/Cookies.html): Returns a jar that'll automatically generate a signed representation of cookie value and verify it when reading from the cookie again.
- [httponly cookies](https://api.rubyonrails.org/classes/ActionDispatch/Cookies.html): A cookie with the httponly attribute is inaccessible to the JavaScript, this precaution helps mitigate cross-site scripting (XSS) attacks.
- [signed_id](https://api.rubyonrails.org/classes/ActiveRecord/SignedId.html): Returns a signed id that is tamper proof, so it's safe to send in an email or otherwise share with the outside world.
- [current attributes](https://api.rubyonrails.org/classes/ActiveSupport/CurrentAttributes.html): Abstract super class that provides a thread-isolated attributes singleton, which resets automatically before and after each request.
- [action mailer](https://api.rubyonrails.org/classes/ActionMailer/Base.html): Action Mailer allows you to send email from your application using a mailer model and views.
- [log filtering](https://guides.rubyonrails.org/action_controller_overview.html#log-filtering): Parameters 'token' and 'password' are marked [FILTERED] in the log.
- [functional tests](https://guides.rubyonrails.org/testing.html#functional-tests-for-your-controllers): In Rails, testing the various actions of a controller is a form of writing functional tests.
- [system testing](https://guides.rubyonrails.org/testing.html#system-testing): System tests allow you to test user interactions with your application, running tests in either a real or a headless browser.

### Sudoable

Use `before_action :require_sudo` in controllers with sensitive information, it will ask for your password on the first access or after 30 minutes.

### Tenantable

Some artifacts are generated in the application, which makes it possible to implement row-level multitenancy applications. The `Current.account` is set using the current user account.

You should follow some steps to make it work:

- Add `account_id` to each scoped table. ex: `rails g migration add_account_to_projects account:references`.
- Add `include AccountScoped` to scoped models. It set up the account relationship and default scope using the current account.

Set `Current.account` through the URL. `http://myapp.com/:account_id`. (optional)

- Add `require_relative "../lib/account_middleware"` to `config/application.rb`.
- Add `config.middleware.use AccountMiddleware` to your application class.
- More customization is required...

## Development

To release a new version, update the version number in `version.rb`, and then run `bundle exec rake release`, which will create a git tag for the version, push git commits and tags, and push the `.gem` file to [rubygems.org](https://rubygems.org).

## Contributing

Bug reports and pull requests are welcome on GitHub at https://github.com/lazaronixon/authentication-zero. This project is intended to be a safe, welcoming space for collaboration, and contributors are expected to adhere to the [code of conduct](https://github.com/lazaronixon/authentication-zero/blob/main/CODE_OF_CONDUCT.md).

## License

The gem is available as open source under the terms of the [MIT License](https://opensource.org/licenses/MIT).

## Code of Conduct

Everyone interacting in the AuthenticationZero project's codebases, issue trackers, chat rooms and mailing lists is expected to follow the [code of conduct](https://github.com/lazaronixon/authentication-zero/blob/main/CODE_OF_CONDUCT.md).

================================================
FILE: authentication-zero.gemspec
================================================
require_relative 'lib/authentication_zero/version'

Gem::Specification.new do |spec|
spec.name = "authentication-zero"
spec.version = AuthenticationZero::VERSION
spec.authors = ["Nixon"]
spec.email = ["<EMAIL>"]

spec.summary = "An authentication system generator for Rails applications"
spec.homepage = "https://github.com/lazaronixon/authentication-zero"
spec.license = "MIT"

spec.metadata["homepage_uri"] = spec.homepage
spec.metadata["source_code_uri"] = "https://github.com/lazaronixon/authentication-zero"
spec.metadata["changelog_uri"] = "https://github.com/lazaronixon/authentication-zero/blob/main/CHANGELOG.md"

# Specify which files should be added to the gem when it is released.

# The `git ls-files -z` loads the files in the RubyGem that have been added into git.

spec.files = Dir.chdir(File.expand_path('..', **FILE**)) do
`git ls-files -z`.split("\x0").reject { |f| f.match(%r{^(test|spec|features)/}) }
end
end

================================================
FILE: CHANGELOG.md
================================================

## New version

## Authentication Zero 4.0.3

- We don't need to add `config.action_mailer.default_url_options` anymore
- Make gem add bcrypt more resilient

## Authentication Zero 4.0.2

- Remove dependency on redis / kredis for sudoable
- Fix --webauthn option. (add @github/webauthn-json)
- Update application_controller to rails 8
- Remove --ratelimit option

## Authentication Zero 4.0.1

- Remove rate limit from api generator

## Authentication Zero 4.0.0

- Remove system tests
- Use native rate_limit for lockable
- Copy web_authn_controller.js instead of depend on stimulus-web-authn

## Authentication Zero 3.0.2

- Fix bug where token is not expired/invalid

## Authentication Zero 3.0.0

- Use the new normalizes API
- Use the new password_challenge API
- Use the new authenticate_by API
- Use the new generates_token_for API

## Authentication Zero 2.16.35

- Adjust relationship so that account has many users

## Authentication Zero 2.16.34

- Adjust relationship so that account has one user

## Authentication Zero 2.16.33

- Add account to user by default when tenantable

## Authentication Zero 2.16.32

- Refactor account middleware for account scoping

## Authentication Zero 2.16.31

- Remove raising exception when Current.account is nil in AccountScoped

## Authentication Zero 2.16.30

- Add multi-tenant artifacts that you can use. (--tenantable)

## Authentication Zero 2.16.29

- Replaced session with session_record, it has a conflict on rails 7.1 (bug-fix)

## Authentication Zero 2.16.25

- Add new option to refresh otp secret

## Authentication Zero 2.16.24

- Remove otp secret from client

## Authentication Zero 2.16.21

- Add two factor authentication using a hardware security key (--webauthn)
- Move two factor authentication to new namespaces

## Authentication Zero 2.16.18

- Use session to store the token for the 2fa challenge

## Authentication Zero 2.16.16

- Add recovery codes to two factor auth
- Removed code-verifiable strategy
- Respond password reset edit api with no_content

## Authentication Zero 2.16.15

- Add sign-in as button functionallity (--masqueradable)

## Authentication Zero 2.16.14

- Remove password requirements
- Rubocop compliant
- Brakeman compliant

## Authentication Zero 2.16.13

- Enable resend invitation
- Refactor first_or_initialize -> find_or_initialize_by

## Authentication Zero 2.16.12

- Bring back --sudoable, just for html and you should set before_action yourself
- Bring back --ratelimit
- Removed signed in email notification

## Authentication Zero 2.16.11

- Added sending invitation
- Remove password challenge for 2FA
- Remove lock from sign in

## Authentication Zero 2.16.8

- Verify email using identity/email_verification?sid=xxx instead of
  identity/email_verification/edit?sid=xxx

## Authentication Zero 2.16.6

- Remove passwordless from api template
- Remove sudoable, I want to make things simple for new users,
  and it will became even simpler with the new rails 7.1 "password challenge api"

## Authentication Zero 2.16.5

- Revoke all password reset tokens (security enhancement)
- Sign in without password (new feature)

## Authentication Zero 2.16.4 (February 11, 2023)

- Increase attemps for lockable sign-in

## Authentication Zero 2.16.3 (December 30, 2022)

- Require lock for sign in when lockable

## Authentication Zero 2.16.2 (December 21, 2022)

- Remove api documentation and reference for api docs from README
- Remove bundle install instruction
- Dont require sudo for omniauth users
- Add gems instead of uncomment gemfile lines
- Fix home view

## Authentication Zero 2.16.1 (December 20, 2022)

- Safe navigation for email normalization
- Fix omniauth not verifying user

## Authentication Zero 2.16.0 (May 2, 2022)

- Generate home controller
- Add default_url_options to environments

## Authentication Zero 2.13.0 (May 2, 2022)

- Migrate tokens to a table structure
- Refactor lockable to a controller method

## Authentication Zero 2.12.0 (March 28, 2022)

- Remove model option from generator

## Authentication Zero 2.11.0 (March 27, 2022)

- Remove sudo from default generator
- Remove sudo_at from database
- Implement sudoable using redis

## Authentication Zero 2.10.0 (March 2, 2022)

- Implement two-factor

## Authentication Zero 2.9.0 (March 2, 2022)

- Implement trackable

## Authentication Zero 2.8.0 (March 2, 2022)

- Organize controllers in identity and sessions namespaces

## Authentication Zero 2.7.0 (March 2, 2022)

- Implemented omniauth

## Authentication Zero 2.6.0 (March 1, 2022)

- Implemented ratelimit

## Authentication Zero 2.5.0 (February 28, 2022)

- Implemented pwned

## Authentication Zero 2.4.0 (February 28, 2022)

- Implemented lockable

## Authentication Zero 2.3.0 (February 26, 2022)

- Implemented sudo
- Destroy sessions after change password
- On system tests, assert_current_path in sign_in

================================================
FILE: CODE_OF_CONDUCT.md
================================================

# Contributor Covenant Code of Conduct

## Our Pledge

In the interest of fostering an open and welcoming environment, we as
contributors and maintainers pledge to making participation in our project and
our community a harassment-free experience for everyone, regardless of age, body
size, disability, ethnicity, gender identity and expression, level of experience,
nationality, personal appearance, race, religion, or sexual identity and
orientation.

## Our Standards

Examples of behavior that contributes to creating a positive environment
include:

- Using welcoming and inclusive language
- Being respectful of differing viewpoints and experiences
- Gracefully accepting constructive criticism
- Focusing on what is best for the community
- Showing empathy towards other community members

Examples of unacceptable behavior by participants include:

- The use of sexualized language or imagery and unwelcome sexual attention or
  advances
- Trolling, insulting/derogatory comments, and personal or political attacks
- Public or private harassment
- Publishing others' private information, such as a physical or electronic
  address, without explicit permission
- Other conduct which could reasonably be considered inappropriate in a
  professional setting

## Our Responsibilities

Project maintainers are responsible for clarifying the standards of acceptable
behavior and are expected to take appropriate and fair corrective action in
response to any instances of unacceptable behavior.

Project maintainers have the right and responsibility to remove, edit, or
reject comments, commits, code, wiki edits, issues, and other contributions
that are not aligned to this Code of Conduct, or to ban temporarily or
permanently any contributor for other behaviors that they deem inappropriate,
threatening, offensive, or harmful.

## Scope

This Code of Conduct applies both within project spaces and in public spaces
when an individual is representing the project or its community. Examples of
representing a project or community include using an official project e-mail
address, posting via an official social media account, or acting as an appointed
representative at an online or offline event. Representation of a project may be
further defined and clarified by project maintainers.

## Enforcement

Instances of abusive, harassing, or otherwise unacceptable behavior may be
reported by contacting the project <NAME_EMAIL>. All
complaints will be reviewed and investigated and will result in a response that
is deemed necessary and appropriate to the circumstances. The project team is
obligated to maintain confidentiality with regard to the reporter of an incident.
Further details of specific enforcement policies may be posted separately.

Project maintainers who do not follow or enforce the Code of Conduct in good
faith may face temporary or permanent repercussions as determined by other
members of the project's leadership.

## Attribution

This Code of Conduct is adapted from the [Contributor Covenant][homepage], version 1.4,
available at [https://contributor-covenant.org/version/1/4][version]

[homepage]: https://contributor-covenant.org
[version]: https://contributor-covenant.org/version/1/4/

================================================
FILE: Gemfile
================================================
source "https://rubygems.org"

# Specify your gem's dependencies in authentication_zero.gemspec

gemspec

gem "rake", "~> 12.0"

================================================
FILE: LICENSE.txt
================================================
The MIT License (MIT)

Copyright (c) 2022 Nixon

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in
all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
THE SOFTWARE.

================================================
FILE: Rakefile
================================================
require "bundler/gem_tasks"
task :default => :spec

================================================
FILE: .rubocop.yml
================================================
inherit_from: https://raw.githubusercontent.com/rails/rails/v7.0.4/.rubocop.yml

Style/HashSyntax:
Exclude: - Gemfile

Layout/EmptyLinesAroundBlockBody:
Exclude: - Gemfile

Layout/TrailingWhitespace:
Exclude: - config/initializers/filter_parameter_logging.rb

Style/FrozenStringLiteralComment:
Enabled: false

================================================
FILE: lib/authentication-zero.rb
================================================
require "authentication_zero"

================================================
FILE: lib/authentication_zero.rb
================================================
require "authentication_zero/version"

module AuthenticationZero
end

================================================
FILE: lib/authentication_zero/version.rb
================================================
module AuthenticationZero
VERSION = "4.0.4"
end

================================================
FILE: lib/generators/authentication/authentication_generator.rb
================================================
require "rails/generators/active_record"

class AuthenticationGenerator < Rails::Generators::Base
include ActiveRecord::Generators::Migration

class_option :api, type: :boolean, desc: "Generates API authentication"
class_option :pwned, type: :boolean, desc: "Add pwned password validation"
class_option :sudoable, type: :boolean, desc: "Add password request before sensitive data changes"
class_option :lockable, type: :boolean, desc: "Add password reset locking"
class_option :passwordless, type: :boolean, desc: "Add passwordless sign in"
class_option :omniauthable, type: :boolean, desc: "Add social login support"
class_option :trackable, type: :boolean, desc: "Add activity log support"
class_option :two_factor, type: :boolean, desc: "Add two factor authentication"
class_option :webauthn, type: :boolean, desc: "Add two factor authentication using a hardware security key"
class_option :invitable, type: :boolean, desc: "Add sending invitations"
class_option :masqueradable, type: :boolean, desc: "Add sign-in as button functionallity"
class_option :tenantable, type: :boolean, desc: "Add artifacts to implement a row-level tenant app"

source_root File.expand_path("templates", **dir**)

def add_gems
if bcrypt_present?
uncomment_lines "Gemfile", /gem "bcrypt"/
else
gem "bcrypt", "~> 3.1.7", comment: "Use Active Model has_secure_password [https://guides.rubyonrails.org/active_model_basics.html#securepassword]"
end

    if options.pwned?
      gem "pwned", comment: "Use Pwned to check if a password has been found in any of the huge data breaches [https://github.com/philnash/pwned]"
    end

    if omniauthable?
      gem "omniauth", comment: "Use OmniAuth to support multi-provider authentication [https://github.com/omniauth/omniauth]"
      gem "omniauth-rails_csrf_protection", comment: "Provides a mitigation against CVE-2015-9284 [https://github.com/cookpad/omniauth-rails_csrf_protection]"
    end

    if two_factor?
      gem "rotp", comment: "Use rotp for generating and validating one time passwords [https://github.com/mdp/rotp]"
      gem "rqrcode", comment: "Use rqrcode for creating and rendering QR codes into various formats [https://github.com/whomwah/rqrcode]"
    end

    if webauthn?
      gem "webauthn", comment: "Use webauthn for making rails become a conformant web authn relying party [https://github.com/cedarcode/webauthn-ruby]"
    end

end

def create_configuration_files
copy_file "config/initializers/omniauth.rb" if omniauthable?
copy_file "config/initializers/webauthn.rb" if webauthn?
end

def create_lib_files
copy_file "lib/account_middleware.rb" if options.tenantable?
end

def create_migrations
migration_template "migrations/create_accounts_migration.rb", "#{db_migrate_path}/create_accounts_migration.rb" if options.tenantable?
migration_template "migrations/create_users_migration.rb", "#{db_migrate_path}/create_users.rb"
migration_template "migrations/create_sessions_migration.rb", "#{db_migrate_path}/create_sessions.rb"
migration_template "migrations/create_events_migration.rb", "#{db_migrate_path}/create_events.rb" if options.trackable?
migration_template "migrations/create_recovery_codes_migration.rb", "#{db_migrate_path}/create_recovery_codes.rb" if two_factor?
migration_template "migrations/create_security_keys_migration.rb", "#{db_migrate_path}/create_security_keys.rb" if webauthn?
migration_template "migrations/create_sign_in_tokens_migration.rb", "#{db_migrate_path}/create_sign_in_tokens_migration.rb" if passwordless?
end

def create_models
copy_file "models/concerns/account_scoped.rb", "app/models/concerns/account_scoped.rb" if options.tenantable?

    template "models/account.rb", "app/models/account.rb" if options.tenantable?
    template "models/current.rb", "app/models/current.rb"
    template "models/event.rb", "app/models/event.rb" if options.trackable?
    template "models/recovery_code.rb", "app/models/recovery_code.rb" if two_factor?
    template "models/security_key.rb", "app/models/security_key.rb" if webauthn?
    template "models/session.rb", "app/models/session.rb"
    template "models/sign_in_token.rb", "app/models/sign_in_token.rb" if passwordless?
    template "models/user.rb", "app/models/user.rb"

end

def create_fixture_file
copy_file "test_unit/users.yml", "test/fixtures/users.yml"
end

def create_controllers
template "controllers/#{format}/authentications/events_controller.rb", "app/controllers/authentications/events_controller.rb" if options.trackable?

    directory "controllers/#{format}/identity", "app/controllers/identity"

    template "controllers/#{format}/sessions/omniauth_controller.rb", "app/controllers/sessions/omniauth_controller.rb" if omniauthable?
    template "controllers/#{format}/sessions/passwordlesses_controller.rb", "app/controllers/sessions/passwordlesses_controller.rb" if passwordless?
    template "controllers/#{format}/sessions/sudos_controller.rb", "app/controllers/sessions/sudos_controller.rb" if sudoable?

    if two_factor?
      template "controllers/html/two_factor_authentication/challenge/recovery_codes_controller.rb", "app/controllers/two_factor_authentication/challenge/recovery_codes_controller.rb"
      template "controllers/html/two_factor_authentication/challenge/security_keys_controller.rb", "app/controllers/two_factor_authentication/challenge/security_keys_controller.rb" if webauthn?
      template "controllers/html/two_factor_authentication/challenge/totps_controller.rb", "app/controllers/two_factor_authentication/challenge/totps_controller.rb"

      template "controllers/html/two_factor_authentication/profile/recovery_codes_controller.rb", "app/controllers/two_factor_authentication/profile/recovery_codes_controller.rb"
      template "controllers/html/two_factor_authentication/profile/security_keys_controller.rb", "app/controllers/two_factor_authentication/profile/security_keys_controller.rb" if webauthn?
      template "controllers/html/two_factor_authentication/profile/totps_controller.rb", "app/controllers/two_factor_authentication/profile/totps_controller.rb"
    end

    template "controllers/#{format}/application_controller.rb", "app/controllers/application_controller.rb", force: true
    template "controllers/#{format}/home_controller.rb", "app/controllers/home_controller.rb" unless options.api?
    template "controllers/#{format}/invitations_controller.rb", "app/controllers/invitations_controller.rb" if invitable?
    template "controllers/#{format}/masquerades_controller.rb", "app/controllers/masquerades_controller.rb" if masqueradable?
    template "controllers/#{format}/passwords_controller.rb", "app/controllers/passwords_controller.rb"
    template "controllers/#{format}/registrations_controller.rb", "app/controllers/registrations_controller.rb"
    template "controllers/#{format}/sessions_controller.rb", "app/controllers/sessions_controller.rb"

end

def install_javascript
return unless webauthn?
copy_file "javascript/controllers/web_authn_controller.js", "app/javascript/controllers/web_authn_controller.js"
run "bin/importmap pin @rails/request.js @github/webauthn-json" if importmaps?
run "yarn add @rails/request.js @github/webauthn-json" if node?
end

def create_views
if options.api?
template "erb/user_mailer/email_verification.html.erb", "app/views/user_mailer/email_verification.html.erb"
template "erb/user_mailer/password_reset.html.erb", "app/views/user_mailer/password_reset.html.erb"
else
directory "erb/authentications/events", "app/views/authentications/events" if options.trackable?
directory "erb/home", "app/views/home"
directory "erb/identity", "app/views/identity"
directory "erb/invitations", "app/views/invitations" if invitable?
directory "erb/passwords", "app/views/passwords"
directory "erb/registrations", "app/views/registrations"

      directory "erb/sessions/passwordlesses", "app/views/sessions/passwordlesses" if passwordless?
      directory "erb/sessions/sudos", "app/views/sessions/sudos" if sudoable?
      template  "erb/sessions/index.html.erb", "app/views/sessions/index.html.erb"
      template  "erb/sessions/new.html.erb", "app/views/sessions/new.html.erb"

      if two_factor?
        directory "erb/two_factor_authentication/challenge/recovery_codes", "app/views/two_factor_authentication/challenge/recovery_codes"
        directory "erb/two_factor_authentication/challenge/security_keys", "app/views/two_factor_authentication/challenge/security_keys" if webauthn?
        directory "erb/two_factor_authentication/challenge/totps", "app/views/two_factor_authentication/challenge/totps"

        directory "erb/two_factor_authentication/profile/recovery_codes", "app/views/two_factor_authentication/profile/recovery_codes"
        directory "erb/two_factor_authentication/profile/security_keys", "app/views/two_factor_authentication/profile/security_keys" if webauthn?
        directory "erb/two_factor_authentication/profile/totps", "app/views/two_factor_authentication/profile/totps"
      end

      template "erb/user_mailer/email_verification.html.erb", "app/views/user_mailer/email_verification.html.erb"
      template "erb/user_mailer/invitation_instructions.html.erb", "app/views/user_mailer/invitation_instructions.html.erb" if invitable?
      template "erb/user_mailer/password_reset.html.erb", "app/views/user_mailer/password_reset.html.erb"
      template "erb/user_mailer/passwordless.html.erb", "app/views/user_mailer/passwordless.html.erb" if passwordless?
    end

end

def create_mailers
directory "mailers", "app/mailers"
end

def add_routes
route 'root "home#index"' unless options.api?

    if sudoable?
      route "resource :sudo, only: [:new, :create]", namespace: :sessions
    end

    if invitable?
      route "resource :invitation, only: [:new, :create]"
    end

    if passwordless?
      route "resource :passwordless, only: [:new, :edit, :create]", namespace: :sessions
    end

    if masqueradable?
      route 'post "users/:user_id/masquerade", to: "masquerades#create", as: :user_masquerade'
    end

    if omniauthable?
      route 'post "/auth/:provider/callback", to: "sessions/omniauth#create"'
      route 'get  "/auth/:provider/callback", to: "sessions/omniauth#create"'
      route 'get  "/auth/failure",            to: "sessions/omniauth#failure"'
    end

    if two_factor?
      route "resources :recovery_codes, only: [:index, :create]", namespace: [:two_factor_authentication, :profile]
      route "resource  :totp,           only: [:new, :create, :update]", namespace: [:two_factor_authentication, :profile]
      route "resources :security_keys", namespace: [:two_factor_authentication, :profile] if webauthn?

      route "resource :recovery_codes, only: [:new, :create]", namespace: [:two_factor_authentication, :challenge]
      route "resource :totp,           only: [:new, :create]", namespace: [:two_factor_authentication, :challenge]
      route "resource :security_keys,  only: [:new, :create]", namespace: [:two_factor_authentication, :challenge] if webauthn?
    end

    if options.trackable?
      route "resources :events, only: :index", namespace: :authentications
    end

    route "resource :password_reset,     only: [:new, :edit, :create, :update]", namespace: :identity
    route "resource :email_verification, only: [:show, :create]", namespace: :identity
    route "resource :email,              only: [:edit, :update]", namespace: :identity

    route "resource  :password, only: [:edit, :update]"
    route "resources :sessions, only: [:index, :show, :destroy]"

    route 'post "sign_up", to: "registrations#create"'
    route 'get  "sign_up", to: "registrations#new"' unless options.api?

    route 'post "sign_in", to: "sessions#create"'
    route 'get  "sign_in", to: "sessions#new"' unless options.api?

end

def create_test_files
directory "test_unit/controllers/#{format}", "test/controllers"
directory "test_unit/mailers/", "test/mailers"
template "test_unit/test_helper.rb", "test/test_helper.rb", force: true
end

private
def format
options.api? ? "api" : "html"
end

    def omniauthable?
      options.omniauthable? && !options.api?
    end

    def passwordless?
      options.passwordless? && !options.api?
    end

    def two_factor?
      options.two_factor? && !options.api?
    end

    def webauthn?
      options.webauthn? && two_factor?
    end

    def invitable?
      options.invitable? && !options.api?
    end

    def masqueradable?
      options.masqueradable? && !options.api?
    end

    def sudoable?
      options.sudoable? && !options.api?
    end

    def bcrypt_present?
      File.read("Gemfile").include?('gem "bcrypt"')
    end

    def importmaps?
      Rails.root.join("config/importmap.rb").exist?
    end

    def node?
      Rails.root.join("package.json").exist?
    end

end

================================================
FILE: lib/generators/authentication/USAGE
================================================
Description:
The purpose of authentication zero is to generate a pre-built
authentication system into a rails application that
follows both security and rails best practices.

    By generating code into the user's application
    instead of using a library, the user has complete freedom
    to modify the authentication system so it works best with their app.

Example:
bin/rails generate authentication

================================================
FILE: lib/generators/authentication/templates/config/initializers/omniauth.rb
================================================
Rails.application.config.middleware.use OmniAuth::Builder do
provider :developer unless Rails.env.production? # You should replace it with your provider
end

================================================
FILE: lib/generators/authentication/templates/config/initializers/webauthn.rb
================================================
WebAuthn.configure do |config|
config.origin = "http://localhost:3000"
config.rp_name = "Example Inc."
end

================================================
FILE: lib/generators/authentication/templates/controllers/api/application_controller.rb.tt
================================================
class ApplicationController < ActionController::API
include ActionController::HttpAuthentication::Token::ControllerMethods

before_action :set_current_request_details
before_action :authenticate

private
def authenticate
if session*record = authenticate_with_http_token { |token, *| Session.find_signed(token) }
Current.session = session_record
else
request_http_token_authentication
end
end

    def set_current_request_details
      Current.user_agent = request.user_agent
      Current.ip_address = request.ip
    end

end

================================================
FILE: lib/generators/authentication/templates/controllers/api/passwords_controller.rb.tt
================================================
class PasswordsController < ApplicationController
before_action :set_user

def update
if @user.update(user_params)
render json: @user
else
render json: @user.errors, status: :unprocessable_entity
end
end

private
def set_user
@user = Current.user
end

    def user_params
      params.permit(:password, :password_confirmation, :password_challenge).with_defaults(password_challenge: "")
    end

end

================================================
FILE: lib/generators/authentication/templates/controllers/api/registrations_controller.rb.tt
================================================
class RegistrationsController < ApplicationController
skip_before_action :authenticate

def create
@user = User.new(user_params)

    if @user.save
      send_email_verification
      render json: @user, status: :created
    else
      render json: @user.errors, status: :unprocessable_entity
    end

end

private
def user_params
params.permit(:email, :password, :password_confirmation)
end

    def send_email_verification
      UserMailer.with(user: @user).email_verification.deliver_later
    end

end

================================================
FILE: lib/generators/authentication/templates/controllers/api/sessions_controller.rb.tt
================================================
class SessionsController < ApplicationController
skip_before_action :authenticate, only: :create

before_action :set_session, only: %i[ show destroy ]

def index
render json: Current.user.sessions.order(created_at: :desc)
end

def show
render json: @session
end

def create
if user = User.authenticate_by(email: params[:email], password: params[:password])
@session = user.sessions.create!
response.set_header "X-Session-Token", @session.signed_id

      render json: @session, status: :created
    else
      render json: { error: "That email or password is incorrect" }, status: :unauthorized
    end

end

def destroy
@session.destroy
end

private
def set_session
@session = Current.user.sessions.find(params[:id])
end
end

================================================
FILE: lib/generators/authentication/templates/controllers/api/authentications/events_controller.rb.tt
================================================
class Authentications::EventsController < ApplicationController
def index
render json: Current.user.events.order(created_at: :desc)
end
end

================================================
FILE: lib/generators/authentication/templates/controllers/api/identity/email_verifications_controller.rb.tt
================================================
class Identity::EmailVerificationsController < ApplicationController
skip_before_action :authenticate, only: :show

before_action :set_user, only: :show

def show
@user.update!(verified: true); head(:no_content)
end

def create
UserMailer.with(user: Current.user).email_verification.deliver_later
end

private
def set_user
@user = User.find_by_token_for!(:email_verification, params[:sid])
rescue StandardError
render json: { error: "That email verification link is invalid" }, status: :bad_request
end
end

================================================
FILE: lib/generators/authentication/templates/controllers/api/identity/emails_controller.rb.tt
================================================
class Identity::EmailsController < ApplicationController
before_action :set_user

def update
if @user.update(user_params)
render_show
else
render json: @user.errors, status: :unprocessable_entity
end
end

private
def set_user
@user = Current.user
end

    def user_params
      params.permit(:email, :password_challenge).with_defaults(password_challenge: "")
    end

    def render_show
      if @user.email_previously_changed?
        resend_email_verification; render(json: @user)
      else
        render json: @user
      end
    end

    def resend_email_verification
      UserMailer.with(user: @user).email_verification.deliver_later
    end

end

================================================
FILE: lib/generators/authentication/templates/controllers/api/identity/password_resets_controller.rb.tt
================================================
class Identity::PasswordResetsController < ApplicationController
skip_before_action :authenticate

before_action :set_user, only: :update

def edit
head :no_content
end

def create
if @user = User.find_by(email: params[:email], verified: true)
UserMailer.with(user: @user).password_reset.deliver_later
else
render json: { error: "You can't reset your password until you verify your email" }, status: :bad_request
end
end

def update
if @user.update(user_params)
render json: @user
else
render json: @user.errors, status: :unprocessable_entity
end
end

private
def set_user
@user = User.find_by_token_for!(:password_reset, params[:sid])
rescue StandardError
render json: { error: "That password reset link is invalid" }, status: :bad_request
end

    def user_params
      params.permit(:password, :password_confirmation)
    end

end

================================================
FILE: lib/generators/authentication/templates/controllers/html/application_controller.rb.tt
================================================
class ApplicationController < ActionController::Base

# Only allow modern browsers supporting webp images, web push, badges, import maps, CSS nesting, and CSS :has.

allow_browser versions: :modern

before_action :set_current_request_details
before_action :authenticate

private
def authenticate
if session_record = Session.find_by_id(cookies.signed[:session_token])
Current.session = session_record
else
redirect_to sign_in_path
end
end

    def set_current_request_details
      Current.user_agent = request.user_agent
      Current.ip_address = request.ip
    end
    <%- if sudoable? %>
    def require_sudo
      unless Current.session.sudo?
        redirect_to new_sessions_sudo_path(proceed_to_url: request.original_url)
      end
    end
    <%- end -%>

end

================================================
FILE: lib/generators/authentication/templates/controllers/html/home_controller.rb.tt
================================================
class HomeController < ApplicationController
def index
end
end

================================================
FILE: lib/generators/authentication/templates/controllers/html/invitations_controller.rb.tt
================================================
class InvitationsController < ApplicationController
def new
@user = User.new
end

def create
@user = User.create_with(user_params).find_or_initialize_by(email: params[:email])

    if @user.save
      send_invitation_instructions
      redirect_to new_invitation_path, notice: "An invitation email has been sent to #{@user.email}"
    else
      render :new, status: :unprocessable_entity
    end

end

private
def user_params
params.permit(:email).merge(password: SecureRandom.base58, verified: true)
end

    def send_invitation_instructions
      UserMailer.with(user: @user).invitation_instructions.deliver_later
    end

end

================================================
FILE: lib/generators/authentication/templates/controllers/html/masquerades_controller.rb.tt
================================================
class MasqueradesController < ApplicationController
before_action :authorize
before_action :set_user

def create
session_record = @user.sessions.create!
cookies.signed.permanent[:session_token] = { value: session_record.id, httponly: true }

    redirect_to root_path, notice: "Signed in successfully"

end

private
def set_user
@user = User.find(params[:user_id])
end

    def authorize
      redirect_to(root_path, alert: "You must be in development") unless Rails.env.development?
    end

end

================================================
FILE: lib/generators/authentication/templates/controllers/html/passwords_controller.rb.tt
================================================
class PasswordsController < ApplicationController
before_action :set_user

def edit
end

def update
if @user.update(user_params)
redirect_to root_path, notice: "Your password has been changed"
else
render :edit, status: :unprocessable_entity
end
end

private
def set_user
@user = Current.user
end

    def user_params
      params.permit(:password, :password_confirmation, :password_challenge).with_defaults(password_challenge: "")
    end

end

================================================
FILE: lib/generators/authentication/templates/controllers/html/registrations_controller.rb.tt
================================================
class RegistrationsController < ApplicationController
skip_before_action :authenticate

def new
@user = User.new
end

def create
@user = User.new(user_params)

    if @user.save
      session_record = @user.sessions.create!
      cookies.signed.permanent[:session_token] = { value: session_record.id, httponly: true }

      send_email_verification
      redirect_to root_path, notice: "Welcome! You have signed up successfully"
    else
      render :new, status: :unprocessable_entity
    end

end

private
def user_params
params.permit(:email, :password, :password_confirmation)
end

    def send_email_verification
      UserMailer.with(user: @user).email_verification.deliver_later
    end

end

================================================
FILE: lib/generators/authentication/templates/controllers/html/sessions_controller.rb.tt
================================================
class SessionsController < ApplicationController
skip_before_action :authenticate, only: %i[ new create ]

before_action :set_session, only: :destroy

def index
@sessions = Current.user.sessions.order(created_at: :desc)
end

def new
end

def create
if user = User.authenticate_by(email: params[:email], password: params[:password])
<%- if two_factor? -%>
if user.otp_required_for_sign_in?
session[:challenge_token] = user.signed_id(purpose: :authentication_challenge, expires_in: 20.minutes)
redirect_to new_two_factor_authentication_challenge_totp_path
else
@session = user.sessions.create!
cookies.signed.permanent[:session_token] = { value: @session.id, httponly: true }

        redirect_to root_path, notice: "Signed in successfully"
      end
      <%- else -%>
      @session = user.sessions.create!
      cookies.signed.permanent[:session_token] = { value: @session.id, httponly: true }

      redirect_to root_path, notice: "Signed in successfully"
      <%- end -%>
    else
      redirect_to sign_in_path(email_hint: params[:email]), alert: "That email or password is incorrect"
    end

end

def destroy
@session.destroy; redirect_to(sessions_path, notice: "That session has been logged out")
end

private
def set_session
@session = Current.user.sessions.find(params[:id])
end
end

================================================
FILE: lib/generators/authentication/templates/controllers/html/authentications/events_controller.rb.tt
================================================
class Authentications::EventsController < ApplicationController
def index
@events = Current.user.events.order(created_at: :desc)
end
end

================================================
FILE: lib/generators/authentication/templates/controllers/html/identity/email_verifications_controller.rb.tt
================================================
class Identity::EmailVerificationsController < ApplicationController
skip_before_action :authenticate, only: :show

before_action :set_user, only: :show

def show
@user.update! verified: true
redirect_to root_path, notice: "Thank you for verifying your email address"
end

def create
send_email_verification
redirect_to root_path, notice: "We sent a verification email to your email address"
end

private
def set_user
@user = User.find_by_token_for!(:email_verification, params[:sid])
rescue StandardError
redirect_to edit_identity_email_path, alert: "That email verification link is invalid"
end

    def send_email_verification
      UserMailer.with(user: Current.user).email_verification.deliver_later
    end

end

================================================
FILE: lib/generators/authentication/templates/controllers/html/identity/emails_controller.rb.tt
================================================
class Identity::EmailsController < ApplicationController
before_action :set_user

def edit
end

def update
if @user.update(user_params)
redirect_to_root
else
render :edit, status: :unprocessable_entity
end
end

private
def set_user
@user = Current.user
end

    def user_params
      params.permit(:email, :password_challenge).with_defaults(password_challenge: "")
    end

    def redirect_to_root
      if @user.email_previously_changed?
        resend_email_verification
        redirect_to root_path, notice: "Your email has been changed"
      else
        redirect_to root_path
      end
    end

    def resend_email_verification
      UserMailer.with(user: @user).email_verification.deliver_later
    end

end

================================================
FILE: lib/generators/authentication/templates/controllers/html/identity/password_resets_controller.rb.tt
================================================
class Identity::PasswordResetsController < ApplicationController
skip_before_action :authenticate

<%- if options.lockable? -%>
rate_limit to: 10, within: 1.hour, only: :create, with: -> { redirect_to root_path, alert: "Try again later" }
<%- end -%>
before_action :set_user, only: %i[ edit update ]

def new
end

def edit
end

def create
if @user = User.find_by(email: params[:email], verified: true)
send_password_reset_email
redirect_to sign_in_path, notice: "Check your email for reset instructions"
else
redirect_to new_identity_password_reset_path, alert: "You can't reset your password until you verify your email"
end
end

def update
if @user.update(user_params)
redirect_to sign_in_path, notice: "Your password was reset successfully. Please sign in"
else
render :edit, status: :unprocessable_entity
end
end

private
def set_user
@user = User.find_by_token_for!(:password_reset, params[:sid])
rescue StandardError
redirect_to new_identity_password_reset_path, alert: "That password reset link is invalid"
end

    def user_params
      params.permit(:password, :password_confirmation)
    end

    def send_password_reset_email
      UserMailer.with(user: @user).password_reset.deliver_later
    end

end

================================================
FILE: lib/generators/authentication/templates/controllers/html/sessions/omniauth_controller.rb.tt
================================================
class Sessions::OmniauthController < ApplicationController
skip_before_action :verify_authenticity_token
skip_before_action :authenticate

def create
@user = User.create_with(user_params).find_or_initialize_by(omniauth_params)

    if @user.save
      session_record = @user.sessions.create!
      cookies.signed.permanent[:session_token] = { value: session_record.id, httponly: true }

      redirect_to root_path, notice: "Signed in successfully"
    else
      redirect_to sign_in_path, alert: "Authentication failed"
    end

end

def failure
redirect_to sign_in_path, alert: params[:message]
end

private
def user_params
{ email: omniauth.info.email, password: SecureRandom.base58, verified: true }
end

    def omniauth_params
      { provider: omniauth.provider, uid: omniauth.uid }
    end

    def omniauth
      request.env["omniauth.auth"]
    end

end

================================================
FILE: lib/generators/authentication/templates/controllers/html/sessions/passwordlesses_controller.rb.tt
================================================
class Sessions::PasswordlessesController < ApplicationController
skip_before_action :authenticate

<%- if options.lockable? -%>
rate_limit to: 10, within: 1.hour, only: :create, with: -> { redirect_to root_path, alert: "Try again later" }
<%- end -%>
before_action :set_user, only: :edit

def new
end

def edit
session_record = @user.sessions.create!
cookies.signed.permanent[:session_token] = { value: session_record.id, httponly: true }

    revoke_sign_in_tokens
    redirect_to(root_path, notice: "Signed in successfully")

end

def create
if @user = User.find_by(email: params[:email], verified: true)
send_passwordless_email
redirect_to sign_in_path, notice: "Check your email for sign in instructions"
else
redirect_to new_sessions_passwordless_path, alert: "You can't sign in until you verify your email"
end
end

private
def set_user
token = SignInToken.find_signed!(params[:sid]); @user = token.user
rescue StandardError
redirect_to new_sessions_passwordless_path, alert: "That sign in link is invalid"
end

    def send_passwordless_email
      UserMailer.with(user: @user).passwordless.deliver_later
    end

    def revoke_sign_in_tokens
      @user.sign_in_tokens.delete_all
    end

end

================================================
FILE: lib/generators/authentication/templates/controllers/html/sessions/sudos_controller.rb.tt
================================================
class Sessions::SudosController < ApplicationController
def new
end

def create
session_record = Current.session

    if session_record.user.authenticate(params[:password])
      session_record.touch(:sudo_at); redirect_to(params[:proceed_to_url])
    else
      redirect_to new_sessions_sudo_path(proceed_to_url: params[:proceed_to_url]), alert: "The password you entered is incorrect"
    end

end
end

================================================
FILE: lib/generators/authentication/templates/controllers/html/two_factor_authentication/challenge/recovery_codes_controller.rb.tt
================================================
class TwoFactorAuthentication::Challenge::RecoveryCodesController < ApplicationController
skip_before_action :authenticate

before_action :set_user

def new
end

def create
if recover_code = @user.recovery_codes.find_by(code: params[:code], used: false)
recover_code.update!(used: true); sign_in_and_redirect_to_root
else
redirect_to new_two_factor_authentication_challenge_recovery_codes_path, alert: "That code didn't work. Please try again"
end
end

private
def set_user
@user = User.find_signed!(session[:challenge_token], purpose: :authentication_challenge)
rescue StandardError
redirect_to sign_in_path, alert: "That's taking too long. Please re-enter your password and try again"
end

    def sign_in_and_redirect_to_root
      session_record = @user.sessions.create!
      cookies.signed.permanent[:session_token] = { value: session_record.id, httponly: true }

      redirect_to root_path, notice: "Signed in successfully"
    end

end

================================================
FILE: lib/generators/authentication/templates/controllers/html/two_factor_authentication/challenge/security_keys_controller.rb.tt
================================================
class TwoFactorAuthentication::Challenge::SecurityKeysController < ApplicationController
skip_before_action :authenticate

before_action :set_user

def new
respond_to do |format|
format.html
format.json { render json: options_for_get }
end
end

def create
if @user.security_keys.exists?(external_id: credential.id)
sign_in_and_redirect_to_root
else
render json: { error: "Verification failed: #{e.message}" }, status: :unprocessable_entity
end
end

private
def set_user
@user = User.find_signed!(session[:challenge_token], purpose: :authentication_challenge)
rescue StandardError
redirect_to sign_in_path, alert: "That's taking too long. Please re-enter your password and try again"
end

    def sign_in_and_redirect_to_root
      session_record = @user.sessions.create!
      cookies.signed.permanent[:session_token] = { value: session_record.id, httponly: true }

      render json: { status: "ok", location: root_url }, status: :created
    end

    def options_for_get
      WebAuthn::Credential.options_for_get(allow: external_ids)
    end

    def external_ids
      @user.security_keys.pluck(:external_id)
    end

    def credential
      @credential ||= WebAuthn::Credential.from_get(params.require(:credential))
    end

end

================================================
FILE: lib/generators/authentication/templates/controllers/html/two_factor_authentication/challenge/totps_controller.rb.tt
================================================
class TwoFactorAuthentication::Challenge::TotpsController < ApplicationController
skip_before_action :authenticate

before_action :set_user

def new
end

def create
@totp = ROTP::TOTP.new(@user.otp_secret, issuer: "YourAppName")

    if @totp.verify(params[:code], drift_behind: 15)
      sign_in_and_redirect_to_root
    else
      redirect_to new_two_factor_authentication_challenge_totp_path, alert: "That code didn't work. Please try again"
    end

end

private
def set_user
@user = User.find_signed!(session[:challenge_token], purpose: :authentication_challenge)
rescue StandardError
redirect_to sign_in_path, alert: "That's taking too long. Please re-enter your password and try again"
end

    def sign_in_and_redirect_to_root
      session_record = @user.sessions.create!
      cookies.signed.permanent[:session_token] = { value: session_record.id, httponly: true }

      redirect_to root_path, notice: "Signed in successfully"
    end

end

================================================
FILE: lib/generators/authentication/templates/controllers/html/two_factor_authentication/profile/recovery_codes_controller.rb.tt
================================================
class TwoFactorAuthentication::Profile::RecoveryCodesController < ApplicationController
before_action :set_user

def index
if Current.user.recovery_codes.exists?
@recovery_codes = @user.recovery_codes
else
@recovery_codes = @user.recovery_codes.create!(new_recovery_codes)
end
end

def create
@user.recovery_codes.delete_all
@user.recovery_codes.create!(new_recovery_codes)

    redirect_to two_factor_authentication_profile_recovery_codes_path, notice: "Your new recovery codes have been generated"

end

private
def set_user
@user = Current.user
end

    def new_recovery_codes
      10.times.map { { code: new_recovery_code } }
    end

    def new_recovery_code
      SecureRandom.alphanumeric(10).downcase
    end

end

================================================
FILE: lib/generators/authentication/templates/controllers/html/two_factor_authentication/profile/security_keys_controller.rb.tt
================================================
class TwoFactorAuthentication::Profile::SecurityKeysController < ApplicationController
before_action :set_user
before_action :set_security_key, only: %i[ edit update destroy ]

def index
@security_keys = @user.security_keys
end

def new
respond_to do |format|
format.html
format.json { render json: options_for_create }
end
end

def edit
end

def create
@security_key = @user.security_keys.create!(credential_params)
render json: { status: "ok", location: edit_two_factor_authentication_profile_security_key_url(@security_key, confirmation: true) }, status: :created
end

def update
@security_key.update! name: params[:name]
redirect_to two_factor_authentication_profile_security_keys_path, notice: "Your changes have been saved"
end

def destroy
@security_key.destroy
redirect_to two_factor_authentication_profile_security_keys_path, notice: "#{@security_key.name} has been removed"
end

private
def set_user
@user = Current.user
end

    def set_security_key
      @security_key = @user.security_keys.find(params[:id])
    end

    def options_for_create
      WebAuthn::Credential.options_for_create(user: user_info, exclude: external_ids)
    end

    def user_info
      { id: @user.webauthn_id, name: @user.email }
    end

    def external_ids
      @user.security_keys.pluck(:external_id)
    end

    def credential_params
      { external_id: credential.id, name: "security key" }
    end

    def credential
      @credential ||= WebAuthn::Credential.from_create(params.require(:credential))
    end

end

================================================
FILE: lib/generators/authentication/templates/controllers/html/two_factor_authentication/profile/totps_controller.rb.tt
================================================
class TwoFactorAuthentication::Profile::TotpsController < ApplicationController
before_action :set_user
before_action :set_totp, only: %i[ new create ]

def new
@qr_code = RQRCode::QRCode.new(provisioning_uri)
end

def create
if @totp.verify(params[:code], drift_behind: 15)
@user.update! otp_required_for_sign_in: true
redirect_to two_factor_authentication_profile_recovery_codes_path
else
redirect_to new_two_factor_authentication_profile_totp_path, alert: "That code didn't work. Please try again"
end
end

def update
@user.update! otp_secret: ROTP::Base32.random
redirect_to new_two_factor_authentication_profile_totp_path
end

private
def set_user
@user = Current.user
end

    def set_totp
      @totp = ROTP::TOTP.new(@user.otp_secret, issuer: "YourAppName")
    end

    def provisioning_uri
      @totp.provisioning_uri @user.email
    end

end

================================================
FILE: lib/generators/authentication/templates/erb/authentications/events/index.html.erb.tt
================================================

<h1>Activity Log</h1>

<div id="sessions">
  <%% @events.each do |event| %>
    <div id="<%%= dom_id event %>">
      <p>
        <strong>User Agent:</strong>
        <%%= event.user_agent %>
      </p>

      <p>
        <strong>Action:</strong>
        <%%= event.action %>
      </p>

      <p>
        <strong>Ip Address:</strong>
        <%%= event.ip_address %>
      </p>

      <p>
        <strong>Created at:</strong>
        <%%= event.created_at %>
      </p>
    </div>

<%% end %>

</div>

<br>

<div>
  <%%= link_to "Back", root_path %>
</div>

================================================
FILE: lib/generators/authentication/templates/erb/home/<USER>
================================================

<p style="color: green"><%%= notice %></p>

<p>Signed as <%%= Current.user.email %></p>

<h2>Login and verification</h2>

<div>
  <%%= link_to "Change password", edit_password_path %>
</div>

<div>
  <%%= link_to "Change email address", edit_identity_email_path %>
</div>
<%- if two_factor? %>
<div>
  <%%= link_to "Two-Factor Authentication", new_two_factor_authentication_profile_totp_path %>
</div>

<%% if Current.user.otp_required_for_sign_in? %>

  <div><%%= link_to "Recovery Codes", two_factor_authentication_profile_recovery_codes_path %></div>
  <%- if webauthn? -%>
  <div><%%= link_to "Security keys", two_factor_authentication_profile_security_keys_path %></div>
  <%- end -%>
<%% end %>
<%- end -%>
<%- if invitable? %>
<div>
  <%%= link_to "Send invitation", new_invitation_path %>
</div>
<%- end -%>
<%- if masqueradable? %>
<%%= button_to "Signin as last user", user_masquerade_path(User.last) %>
<%- end -%>

<h2>Access history</h2>

<div>
  <%%= link_to "Devices & Sessions", sessions_path %>
</div>
<%- if options.trackable? %>
<div>
  <%%= link_to "Activity Log", authentications_events_path %>
</div>
<%- end -%>

<br>

<%%= button_to "Log out", Current.session, method: :delete %>

================================================
FILE: lib/generators/authentication/templates/erb/identity/emails/edit.html.erb.tt
================================================

<p style="color: red"><%%= alert %></p>

<%% if Current.user.verified? %>

  <h1>Change your email</h1>
<%% else %>
  <h1>Verify your email</h1>
  <p>We sent a verification email to the address below. Check that email and follow those instructions to confirm it's your email address.</p>
  <p><%%= button_to "Re-send verification email", identity_email_verification_path %></p>
<%% end %>

<%%= form_with(url: identity_email_path, method: :patch) do |form| %>
<%% if @user.errors.any? %>
<div style="color: red">
<h2><%%= pluralize(@user.errors.count, "error") %> prohibited this user from being saved:</h2>

      <ul>
        <%% @user.errors.each do |error| %>
          <li><%%= error.full_message %></li>
        <%% end %>
      </ul>
    </div>

<%% end %>

  <div>
    <%%= form.label :email, "New email", style: "display: block" %>
    <%%= form.email_field :email, required: true, autofocus: true %>
  </div>

  <div>
    <%%= form.label :password_challenge, style: "display: block" %>
    <%%= form.password_field :password_challenge, required: true, autocomplete: "current-password" %>
  </div>

  <div>
    <%%= form.submit "Save changes" %>
  </div>
<%% end %>

<br>

<div>
  <%%= link_to "Back", root_path %>
</div>

================================================
FILE: lib/generators/authentication/templates/erb/identity/password_resets/edit.html.erb.tt
================================================

<h1>Reset your password</h1>

<%%= form_with(url: identity_password_reset_path, method: :patch) do |form| %>
<%% if @user.errors.any? %>
<div style="color: red">
<h2><%%= pluralize(@user.errors.count, "error") %> prohibited this user from being saved:</h2>

      <ul>
        <%% @user.errors.each do |error| %>
          <li><%%= error.full_message %></li>
        <%% end %>
      </ul>
    </div>

<%% end %>

<%%= form.hidden_field :sid, value: params[:sid] %>

  <div>
    <%%= form.label :password, "New password", style: "display: block" %>
    <%%= form.password_field :password, required: true, autofocus: true, autocomplete: "new-password" %>
    <div>12 characters minimum.</div>
  </div>

  <div>
    <%%= form.label :password_confirmation, "Confirm new password", style: "display: block" %>
    <%%= form.password_field :password_confirmation, required: true, autocomplete: "new-password" %>
  </div>

  <div>
    <%%= form.submit "Save changes" %>
  </div>
<%% end %>

================================================
FILE: lib/generators/authentication/templates/erb/identity/password_resets/new.html.erb.tt
================================================

<p style="color: red"><%%= alert %></p>

<h1>Forgot your password?</h1>

<%%= form_with(url: identity_password_reset_path) do |form| %>

  <div>
    <%%= form.label :email, style: "display: block" %>
    <%%= form.email_field :email, required: true, autofocus: true %>
  </div>

  <div>
    <%%= form.submit "Send password reset email" %>
  </div>
<%% end %>

================================================
FILE: lib/generators/authentication/templates/erb/invitations/new.html.erb.tt
================================================

<p style="color: green"><%%= notice %></p>

<h1>Send invitation</h1>

<%%= form_with(url: invitation_path) do |form| %>
<%% if @user.errors.any? %>
<div style="color: red">
<h2><%%= pluralize(@user.errors.count, "error") %> prohibited this user from being saved:</h2>

      <ul>
        <%% @user.errors.each do |error| %>
          <li><%%= error.full_message %></li>
        <%% end %>
      </ul>
    </div>

<%% end %>

  <div>
    <%%= form.label :email, style: "display: block" %>
    <%%= form.email_field :email, required: true, autofocus: true %>
  </div>

  <div>
    <%%= form.submit "Send an invitation" %>
  </div>
<%% end %>

<br>

<div>
  <%%= link_to "Back", root_path %>
</div>

================================================
FILE: lib/generators/authentication/templates/erb/passwords/edit.html.erb.tt
================================================

<p style="color: red"><%%= alert %></p>

<h1>Change your password</h1>

<%%= form_with(url: password_path, method: :patch) do |form| %>
<%% if @user.errors.any? %>
<div style="color: red">
<h2><%%= pluralize(@user.errors.count, "error") %> prohibited this user from being saved:</h2>

      <ul>
        <%% @user.errors.each do |error| %>
          <li><%%= error.full_message %></li>
        <%% end %>
      </ul>
    </div>

<%% end %>

  <div>
    <%%= form.label :password_challenge, style: "display: block" %>
    <%%= form.password_field :password_challenge, required: true, autofocus: true, autocomplete: "current-password" %>
  </div>

  <div>
    <%%= form.label :password, "New password", style: "display: block" %>
    <%%= form.password_field :password, required: true, autocomplete: "new-password" %>
    <div>12 characters minimum.</div>
  </div>

  <div>
    <%%= form.label :password_confirmation, "Confirm new password", style: "display: block" %>
    <%%= form.password_field :password_confirmation, required: true, autocomplete: "new-password" %>
  </div>

  <div>
    <%%= form.submit "Save changes" %>
  </div>
<%% end %>

<br>

<div>
  <%%= link_to "Back", root_path %>
</div>

================================================
FILE: lib/generators/authentication/templates/erb/registrations/new.html.erb.tt
================================================

<h1>Sign up</h1>

<%%= form_with(url: sign_up_path) do |form| %>
<%% if @user.errors.any? %>
<div style="color: red">
<h2><%%= pluralize(@user.errors.count, "error") %> prohibited this user from being saved:</h2>

      <ul>
        <%% @user.errors.each do |error| %>
          <li><%%= error.full_message %></li>
        <%% end %>
      </ul>
    </div>

<%% end %>

  <div>
    <%%= form.label :email, style: "display: block" %>
    <%%= form.email_field :email, value: @user.email, required: true, autofocus: true, autocomplete: "email" %>
  </div>

  <div>
    <%%= form.label :password, style: "display: block" %>
    <%%= form.password_field :password, required: true, autocomplete: "new-password" %>
    <div>12 characters minimum.</div>
  </div>

  <div>
    <%%= form.label :password_confirmation, style: "display: block" %>
    <%%= form.password_field :password_confirmation, required: true, autocomplete: "new-password" %>
  </div>

  <div>
    <%%= form.submit "Sign up" %>
  </div>
<%% end %>

================================================
FILE: lib/generators/authentication/templates/erb/sessions/index.html.erb.tt
================================================

<p style="color: green"><%%= notice %></p>

<h1>Devices & Sessions</h1>

<div id="sessions">
  <%% @sessions.each do |session| %>
    <div id="<%%= dom_id session %>">
      <p>
        <strong>User Agent:</strong>
        <%%= session.user_agent %>
      </p>

      <p>
        <strong>Ip Address:</strong>
        <%%= session.ip_address %>
      </p>

      <p>
        <strong>Created at:</strong>
        <%%= session.created_at %>
      </p>

    </div>
    <p>
      <%%= button_to "Log out", session, method: :delete %>
    </p>

<%% end %>

</div>

<br>

<div>
  <%%= link_to "Back", root_path %>
</div>

================================================
FILE: lib/generators/authentication/templates/erb/sessions/new.html.erb.tt
================================================

<p style="color: green"><%%= notice %></p>
<p style="color: red"><%%= alert %></p>

<h1>Sign in</h1>

<%%= form_with(url: sign_in_path) do |form| %>

  <div>
    <%%= form.label :email, style: "display: block" %>
    <%%= form.email_field :email, value: params[:email_hint], required: true, autofocus: true, autocomplete: "email" %>
  </div>

  <div>
    <%%= form.label :password, style: "display: block" %>
    <%%= form.password_field :password, required: true, autocomplete: "current-password" %>
  </div>

  <div>
    <%%= form.submit "Sign in" %>
  </div>
<%% end %>

<br>

<%- if passwordless? %>

<div>
  <%%= link_to "Sign in without password", new_sessions_passwordless_path %>
</div>
<%- end -%>
<%- if omniauthable? %>
<div>
  <%%= button_to "Sign in with OmniAuth", "/auth/developer", "data-turbo" => false %>
</div>
<%- end -%>

<br>

<div>
  <%%= link_to "Sign up", sign_up_path %> |
  <%%= link_to "Forgot your password?", new_identity_password_reset_path %>
</div>

================================================
FILE: lib/generators/authentication/templates/erb/sessions/passwordlesses/new.html.erb.tt
================================================

<p style="color: red"><%%= alert %></p>

<h1>Sign in without password</h1>

<%%= form_with(url: sessions_passwordless_path) do |form| %>

  <div>
    <%%= form.label :email, style: "display: block" %>
    <%%= form.email_field :email, required: true, autofocus: true %>
  </div>

  <div>
    <%%= form.submit "Sign in" %>
  </div>
<%% end %>

================================================
FILE: lib/generators/authentication/templates/erb/sessions/sudos/new.html.erb.tt
================================================

<p style="color: red"><%%= alert %></p>

<h1>Enter your password to continue</h1>

<%%= form_with(url: sessions_sudo_path) do |form| %>

<%%= form.hidden_field :proceed_to_url, value: params[:proceed_to_url] %>

  <div>
    <%%= form.password_field :password, required: true, autofocus: true, autocomplete: "current-password" %>
  </div>

  <div>
    <%%= form.submit "Continue" %>
  </div>
<%% end %>

<br>

<p>
  <strong>Why are you asking me to do this?</strong><br>
  To better protect your account, we'll occasionally ask you to confirm your password before performing sensitive actions.
</p>

<p>
  <strong>Forgot your password?</strong><br>
  We'll help you <%%= link_to "reset it", new_identity_password_reset_path %> so you can continue.
</p>

================================================
FILE: lib/generators/authentication/templates/erb/two_factor_authentication/challenge/recovery_codes/new.html.erb.tt
================================================

<p style="color: red"><%%= alert %></p>

<%%= form_with(url: two_factor_authentication_challenge_recovery_codes_path) do |form| %>

  <div>
    <%%= form.label :code do %>
      <h1>OK, enter one of your recovery codes below:</h1>
    <%% end %>
    <%%= form.text_field :code, autofocus: true, required: true, autocomplete: :off %>
  </div>

  <div>
    <%%= form.submit "Continue" %>
  </div>
<%% end %>

<div>
  <p>To access your account, enter one of the recovery codes (e.g., xxxxxxxxxx) you saved when you set up your two-factor authentication device.</p>
</div>

================================================
FILE: lib/generators/authentication/templates/erb/two_factor_authentication/challenge/security_keys/new.html.erb.tt
================================================

<div data-controller="web-authn"
     data-web-authn-loading-class="web-authn-loading"
     data-web-authn-fallback-url-value="<%%= new_two_factor_authentication_challenge_totp_path %>"
     data-web-authn-challenge-url-value="<%%= new_two_factor_authentication_challenge_security_keys_url %>"
     data-web-authn-verification-url-value="<%%= two_factor_authentication_challenge_security_keys_url %>">

  <p data-web-authn-target="error" style="color: red" hidden></p>

  <h1>Verify with your security key.</h1>
  <%%= button_tag "Use security key", type: :button, data: { web_authn_target: "button", action: "web-authn#getCredential" } %>
  <p>Have your security key ready. If it's the USB kind insert it now and then, if it has one, press the activation button when asked.</p>
</div>

================================================
FILE: lib/generators/authentication/templates/erb/two_factor_authentication/challenge/totps/new.html.erb.tt
================================================

<p style="color: red"><%%= alert %></p>

<%%= form_with(url: two_factor_authentication_challenge_totp_path) do |form| %>

  <div>
    <%%= form.label :code do %>
      <h1>Next, open the 2FA authenticator app on your phone and type the six digit code below:</h1>
    <%% end %>
    <%%= form.text_field :code, autofocus: true, required: true, autocomplete: :off %>
  </div>

  <div>
    <%%= form.submit "Verify" %>
  </div>
<%% end %>

<div>
  <p><strong>Don't have your phone?</strong></p>
  <div><%%= link_to "Use a recovery code to access your account.", new_two_factor_authentication_challenge_recovery_codes_path %></div>
  <%- if webauthn? %>
  <%% if @user.security_keys.exists? %>
    <div><%%= link_to "Use a security key to access your account.", new_two_factor_authentication_challenge_security_keys_path %></div>
  <%% end %>
  <%- end -%>
</div>

================================================
FILE: lib/generators/authentication/templates/erb/two_factor_authentication/profile/recovery_codes/\_recovery_code.html.erb.tt
================================================
<%% if recovery_code.used? %>

  <li><del><%%= recovery_code.code %></del></li>
<%% else %>
  <li><%%= recovery_code.code %></li>
<%% end %>

================================================
FILE: lib/generators/authentication/templates/erb/two_factor_authentication/profile/recovery_codes/index.html.erb.tt
================================================

<p style="color: green"><%%= notice %></p>

<h1>Two-factor recovery codes</h1>
<p>Recovery codes provide a way to log in if you lose your phone (or don't have it with you). Save these and keep them somewhere safe.</p>

<ul><%%= render @recovery_codes %></ul>

<div><%%= link_to "OK, I'm done", root_path %></div>

<hr>

<h2>Need new recovery codes?</h2>

<p>If you think your codes have fallen into the wrong hands, you can get a new set. Be sure to save the new ones because the old codes will stop working.</p>

<%%= button_to "Generate new recovery codes", two_factor_authentication_profile_recovery_codes_path %>

================================================
FILE: lib/generators/authentication/templates/erb/two_factor_authentication/profile/security_keys/\_form_confirm.html.erb.tt
================================================
<%%= form_with(url: two_factor_authentication_profile_security_key_path, method: :patch) do |form| %>

  <p>One more step. Please give this security key a nickname to help you remember it.</p>

  <div>
    <%%= form.label :name, style: "display: block" %>
    <%%= form.text_field :name, autofocus: true, required: true %>
    <div>e.g., Macbook Touch ID</div>
  </div>

  <div>
    <%%= form.submit "Save" %>
  </div>
<%% end %>

================================================
FILE: lib/generators/authentication/templates/erb/two_factor_authentication/profile/security_keys/\_form_edit.html.erb.tt
================================================
<%%= form_with(url: two_factor_authentication_profile_security_key_path, method: :patch) do |form| %>

  <div>
    <%%= form.label :name, style: "display: block" %>
    <%%= form.text_field :name, value: @security_key.name, autofocus: true, required: true %>
    <div>e.g., Macbook Touch ID</div>
  </div>

  <div>
    <%%= form.submit "Save changes" %>
  </div>
<%% end %>

<br>

<div>
  <%%= button_to "Remove this security key...", two_factor_authentication_profile_security_key_path(@security_key), method: :delete %>
</div>

================================================
FILE: lib/generators/authentication/templates/erb/two_factor_authentication/profile/security_keys/\_security_key.html.erb.tt
================================================

<li><%%= link_to security_key.name, edit_two_factor_authentication_profile_security_key_path(security_key) %></li>

================================================
FILE: lib/generators/authentication/templates/erb/two_factor_authentication/profile/security_keys/edit.html.erb.tt
================================================

<h1>Edit security key</h1>

<%%= render params[:confirmation] ? "form_confirm" : "form_edit" %>

================================================
FILE: lib/generators/authentication/templates/erb/two_factor_authentication/profile/security_keys/index.html.erb.tt
================================================

<p style="color: green"><%%= notice %></p>

<h1>Security keys</h1>
<p>A security key is a hardware device used to verify your identity. For example, a built-in fingerprint reader, a plug-in USB key, or a login system like Windows Hello.</p>

<ul><%%= render @security_keys %></ul>

<br>

<div>
  <%%= link_to "Add security key", new_two_factor_authentication_profile_security_key_path %>
</div>

<br>

<div>
  <%%= link_to "Back", root_path %>
</div>

================================================
FILE: lib/generators/authentication/templates/erb/two_factor_authentication/profile/security_keys/new.html.erb.tt
================================================

<div data-controller="web-authn"
     data-web-authn-loading-class="web-authn-loading"
     data-web-authn-challenge-url-value="<%%= new_two_factor_authentication_profile_security_key_url %>"
     data-web-authn-verification-url-value="<%%= two_factor_authentication_profile_security_keys_url %>">

  <p data-web-authn-target="error" style="color: red" hidden></p>

  <h1>Add a security key</h1>
  <p>Have your security key ready. If it's the USB kind insert it now and then, if it has one, press the activation button when asked.</p>

<%%= button_tag "I'm ready, let's go", type: :button, data: { web_authn_target: "button", action: "web-authn#createCredential" } %>

</div>

================================================
FILE: lib/generators/authentication/templates/erb/two_factor_authentication/profile/totps/new.html.erb.tt
================================================

<p style="color: red"><%%= alert %></p>

<%% if Current.user.otp_required_for_sign_in? %>

  <h1>Want to replace your existing 2FA setup?</h1>

  <p>Your account is already protected with two-factor authentication. You can replace that setup if you want to switch to a new phone or authenticator app.</p>

  <p><strong>Do you want to continue? Your existing 2FA setup will no longer work.</strong></p>

<%%= button_to "Yes, replace my 2FA setup", two_factor_authentication_profile_totp_path, method: :patch %>

  <hr>
<%% end %>

<h1>Upgrade your security with 2FA</h1>

<h2>Step 1: Get an Authenticator App</h2>
<p>First, you'll need a 2FA authenticator app on your phone. <strong>If you already have one, skip to step 2.</strong></p>
<p><strong>If you don't have one, or you aren't sure, we recommend Microsoft Authenticator</strong>. You can download it free on the Apple App Store for iPhone, or Google Play Store for Android. Please grab your phone, search the store, and install it now.</p>

<h2>Step 2: Scan + Enter the Code</h2>
<p>Next, open the authenticator app, tap "Scan QR code" or "+", and, when it asks, point your phone's camera at this QR code picture below.</p>

<figure>
  <%%= image_tag @qr_code.as_png(resize_exactly_to: 200).to_data_url%>
  <figcaption>Point your camera here</figcaption>
</figure>

<%%= form_with(url: two_factor_authentication_profile_totp_path) do |form| %>

  <div>
    <%%= form.label :code, "After scanning with your camera, the app will generate a six-digit code. Enter it here:", style: "display: block" %>
    <%%= form.text_field :code, required: true, autofocus: true, autocomplete: :off %>
  </div>

  <div>
    <%%= form.submit "Verify and activate" %>
  </div>
<%% end %>

<br>

<div>
  <%%= link_to "Back", root_path %>
</div>

================================================
FILE: lib/generators/authentication/templates/erb/user_mailer/email_verification.html.erb.tt
================================================

<p>Hey there,</p>

<p>This is to confirm that <%%= @user.email %> is the email you want to use on your account. If you ever lose your password, that's where we'll email a reset link.</p>

<p><strong>You must hit the link below to confirm that you received this email.</strong></p>

<p><%%= link_to "Yes, use this email for my account", identity_email_verification_url(sid: @signed_id) %></p>

<hr>

<p>Have questions or need help? Just reply to this email and our support team will help you sort it out.</p>

================================================
FILE: lib/generators/authentication/templates/erb/user_mailer/invitation_instructions.html.erb.tt
================================================

<p>Hey there,</p>

<p>Someone has invited you to the application, you can accept it through the link below.</p>

<p><%%= link_to "Accept invitation", edit_identity_password_reset_url(sid: @signed_id) %></p>

<p>If you don't want to accept the invitation, please ignore this email. Your account won't be created until you access the link above and set your password.</p>

<hr>

<p>Have questions or need help? Just reply to this email and our support team will help you sort it out.</p>

================================================
FILE: lib/generators/authentication/templates/erb/user_mailer/password_reset.html.erb.tt
================================================

<p>Hey there,</p>

<p>Can't remember your password for <strong><%%= @user.email %></strong>? That's OK, it happens. Just hit the link below to set a new one.</p>

<p><%%= link_to "Reset my password", edit_identity_password_reset_url(sid: @signed_id) %></p>

<p>If you did not request a password reset you can safely ignore this email, it expires in 20 minutes. Only someone with access to this email account can reset your password.</p>

<hr>

<p>Have questions or need help? Just reply to this email and our support team will help you sort it out.</p>

================================================
FILE: lib/generators/authentication/templates/erb/user_mailer/passwordless.html.erb.tt
================================================

<p>Hey there,</p>

<p>You requested a magic sign-in link. Here you go:</p>

<p><%%= link_to "Sign in without password", edit_sessions_passwordless_url(sid: @signed_id) %></p>

<hr>

<p>Have questions or need help? Just reply to this email and our support team will help you sort it out.</p>

================================================
FILE: lib/generators/authentication/templates/javascript/controllers/web_authn_controller.js
================================================
import { Controller } from "@hotwired/stimulus"
import { create, get, supported } from "@github/webauthn-json"
import { FetchRequest } from "@rails/request.js"

export default class WebAuthnController extends Controller {
static targets = [ "error", "button", "supportText" ]
static classes = [ "loading" ]
static values = {
challengeUrl: String,
verificationUrl: String,
fallbackUrl: String,
retryText: { type: String, default: "Retry" },
notAllowedText: { type: String, default: "That didn't work. Either it was cancelled or took too long. Please try again." },
invalidStateText: { type: String, default: "We couldn't add that security key. Please confirm you haven't already registered it, then try again." }
}

connect() {
if (!supported()) {
this.handleUnsupportedBrowser()
}
}

getCredential() {
this.hideError()
this.disableForm()
this.requestChallengeAndVerify(get)
}

createCredential() {
this.hideError()
this.disableForm()
this.requestChallengeAndVerify(create)
}

// Private

handleUnsupportedBrowser() {
this.buttonTarget.parentNode.removeChild(this.buttonTarget)

    if (this.fallbackUrlValue) {
      window.location.replace(this.fallbackUrlValue)
    } else {
      this.supportTextTargets.forEach(target => target.hidden = !target.hidden)
    }

}

async requestChallengeAndVerify(fn) {
try {
const challengeResponse = await this.requestPublicKeyChallenge()
const credentialResponse = await fn({ publicKey: challengeResponse })
this.onCompletion(await this.verify(credentialResponse))
} catch (error) {
this.onError(error)
}
}

async requestPublicKeyChallenge() {
return await this.request("get", this.challengeUrlValue)
}

async verify(credentialResponse) {
return await this.request("post", this.verificationUrlValue, {
body: JSON.stringify({ credential: credentialResponse }),
contentType: "application/json",
responseKind: "json"
})
}

onCompletion(response) {
window.location.replace(response.location)
}

onError(error) {
if (error.code === 0 && error.name === "NotAllowedError") {
this.errorTarget.textContent = this.notAllowedTextValue
} else if (error.code === 11 && error.name === "InvalidStateError") {
this.errorTarget.textContent = this.invalidStateTextValue
} else {
this.errorTarget.textContent = error.message
}
this.showError()
}

hideError() {
if (this.hasErrorTarget) this.errorTarget.hidden = true
}

showError() {
if (this.hasErrorTarget) {
this.errorTarget.hidden = false
this.buttonTarget.textContent = this.retryTextValue
this.enableForm()
}
}

enableForm() {
this.element.classList.remove(this.loadingClass)
this.buttonTarget.disabled = false
}

disableForm() {
this.element.classList.add(this.loadingClass)
this.buttonTarget.disabled = true
}

async request(method, url, options) {
const request = new FetchRequest(method, url, { responseKind: "json", ...options })
const response = await request.perform()
return response.json
}
}

================================================
FILE: lib/generators/authentication/templates/lib/account_middleware.rb
================================================
class AccountMiddleware
def initialize(app)
@app = app
end

def call(env)
request = ActionDispatch::Request.new(env)

    if m = /\A(\/(\d{1,}))/.match(request.path_info)
      script_name, account_id, path_info = [m[1], m[2], m.post_match]
      request.script_name = script_name
      request.path_info   = path_info.presence || "/"
      set_current_account(account_id)
      @app.call(request.env)
    else
      @app.call(request.env)
    end

end

private
def set_current_account(account_id)
Current.account = Account.find(account_id)
end
end

================================================
FILE: lib/generators/authentication/templates/mailers/user_mailer.rb.tt
================================================
class UserMailer < ApplicationMailer
def password_reset
@user = params[:user]
@signed_id = @user.generate_token_for(:password_reset)

    mail to: @user.email, subject: "Reset your password"

end

def email_verification
@user = params[:user]
@signed_id = @user.generate_token_for(:email_verification)

    mail to: @user.email, subject: "Verify your email"

end
<%- if passwordless? %>
def passwordless
@user = params[:user]
@signed_id = @user.sign_in_tokens.create.signed_id(expires_in: 1.day)

    mail to: @user.email, subject: "Your sign in link"

end
<%- end -%>
<%- if invitable? %>
def invitation_instructions
@user = params[:user]
@signed_id = @user.generate_token_for(:password_reset)

    mail to: @user.email, subject: "Invitation instructions"

end
<%- end -%>
end

================================================
FILE: lib/generators/authentication/templates/migrations/create_accounts_migration.rb.tt
================================================
class <%= migration_class_name %> < ActiveRecord::Migration[<%= ActiveRecord::Migration.current_version %>]
def change
create_table :accounts
end
end

================================================
FILE: lib/generators/authentication/templates/migrations/create_events_migration.rb.tt
================================================
class <%= migration_class_name %> < ActiveRecord::Migration[<%= ActiveRecord::Migration.current_version %>]
def change
create_table :events do |t|
t.references :user, null: false, foreign_key: true
t.string :action, null: false
t.string :user_agent
t.string :ip_address

      t.timestamps
    end

end
end

================================================
FILE: lib/generators/authentication/templates/migrations/create_recovery_codes_migration.rb.tt
================================================
class <%= migration_class_name %> < ActiveRecord::Migration[<%= ActiveRecord::Migration.current_version %>]
def change
create_table :recovery_codes do |t|
t.references :user, null: false, foreign_key: true
t.string :code, null: false
t.boolean :used, null: false, default: false

      t.timestamps
    end

end
end

================================================
FILE: lib/generators/authentication/templates/migrations/create_security_keys_migration.rb.tt
================================================
class <%= migration_class_name %> < ActiveRecord::Migration[<%= ActiveRecord::Migration.current_version %>]
def change
create_table :security_keys do |t|
t.references :user, null: false, foreign_key: true
t.string :name, null: false
t.string :external_id, null: false, index: { unique: true }

      t.timestamps
    end

end
end

================================================
FILE: lib/generators/authentication/templates/migrations/create_sessions_migration.rb.tt
================================================
class <%= migration_class_name %> < ActiveRecord::Migration[<%= ActiveRecord::Migration.current_version %>]
def change
create_table :sessions do |t|
t.references :user, null: false, foreign_key: true
t.string :user_agent
t.string :ip_address
<%- if sudoable? %>
t.datetime :sudo_at, null: false
<%- end -%>

      t.timestamps
    end

end
end

================================================
FILE: lib/generators/authentication/templates/migrations/create_sign_in_tokens_migration.rb.tt
================================================
class <%= migration_class_name %> < ActiveRecord::Migration[<%= ActiveRecord::Migration.current_version %>]
def change
create_table :sign_in_tokens do |t|
t.references :user, null: false, foreign_key: true
end
end
end

================================================
FILE: lib/generators/authentication/templates/migrations/create_users_migration.rb.tt
================================================
class <%= migration_class_name %> < ActiveRecord::Migration[<%= ActiveRecord::Migration.current_version %>]
def change
create_table :users do |t|
t.string :email, null: false, index: { unique: true }
t.string :password_digest, null: false

      t.boolean :verified, null: false, default: false
      <%- if two_factor? %>
      t.boolean :otp_required_for_sign_in, null: false, default: false
      t.string  :otp_secret, null: false
      <%- end -%>
      <%- if webauthn? %>
      t.string :webauthn_id, null: false
      <%- end -%>
      <%- if omniauthable? %>
      t.string :provider
      t.string :uid
      <%- end -%>
      <%- if options.tenantable? %>
      t.references :account, null: false, foreign_key: true
      <%- end -%>

      t.timestamps
    end

end
end

================================================
FILE: lib/generators/authentication/templates/models/account.rb.tt
================================================
class Account < ApplicationRecord
has_many :users, dependent: :destroy
end

================================================
FILE: lib/generators/authentication/templates/models/current.rb.tt
================================================
class Current < ActiveSupport::CurrentAttributes
attribute :session
attribute :user_agent, :ip_address
<%- if options.tenantable? %>
attribute :account
<%- end -%>

delegate :user, to: :session, allow_nil: true
<%- if options.tenantable? %>
def session=(session)
super; self.account = session.user.account
end
<%- end -%>
end

================================================
FILE: lib/generators/authentication/templates/models/event.rb.tt
================================================
class Event < ApplicationRecord
belongs_to :user

before_create do
self.user_agent = Current.user_agent
self.ip_address = Current.ip_address
end
end

================================================
FILE: lib/generators/authentication/templates/models/recovery_code.rb.tt
================================================
class RecoveryCode < ApplicationRecord
belongs_to :user
end

================================================
FILE: lib/generators/authentication/templates/models/security_key.rb.tt
================================================
class SecurityKey < ApplicationRecord
belongs_to :user
end

================================================
FILE: lib/generators/authentication/templates/models/session.rb.tt
================================================
class Session < ApplicationRecord
belongs_to :user

before_create do
self.user_agent = Current.user_agent
self.ip_address = Current.ip_address
<%- if sudoable? %>
self.sudo_at = Time.current
<%- end -%>
end
<%- if options.trackable? %>
after_create { user.events.create! action: "signed_in" }
after_destroy { user.events.create! action: "signed_out" }
<%- end -%>
<%- if sudoable? %>
def sudo?
sudo_at > 30.minutes.ago
end
<%- end -%>
end

================================================
FILE: lib/generators/authentication/templates/models/sign_in_token.rb.tt
================================================
class SignInToken < ApplicationRecord
belongs_to :user
end

================================================
FILE: lib/generators/authentication/templates/models/user.rb.tt
================================================
class User < ApplicationRecord
has_secure_password

generates_token_for :email_verification, expires_in: 2.days do
email
end

generates_token_for :password_reset, expires_in: 20.minutes do
password_salt.last(10)
end

<%- if options.tenantable? %>
belongs_to :account
<%- end -%>

has_many :sessions, dependent: :destroy
<%- if two_factor? -%>
has_many :recovery_codes, dependent: :destroy
<%- end -%>
<%- if webauthn? -%>
has_many :security_keys, dependent: :destroy
<%- end -%>
<%- if passwordless? -%>
has_many :sign_in_tokens, dependent: :destroy
<%- end -%>
<%- if options.trackable? -%>
has_many :events, dependent: :destroy
<%- end -%>

validates :email, presence: true, uniqueness: true, format: { with: URI::MailTo::EMAIL_REGEXP }
validates :password, allow_nil: true, length: { minimum: 12 }
<%- if options.pwned? -%>
validates :password, not_pwned: { message: "might easily be guessed" }
<%- end -%>

normalizes :email, with: -> { \_1.strip.downcase }

before_validation if: :email_changed?, on: :update do
self.verified = false
end
<%- if two_factor? %>
before_validation on: :create do
self.otp_secret = ROTP::Base32.random
end
<%- end -%>
<%- if webauthn? %>
before_validation on: :create do
self.webauthn_id = WebAuthn.generate_user_id
end
<%- end -%>
<%- if options.tenantable? %>
before_validation on: :create do
self.account = Account.new
end
<%- end -%>

after_update if: :password_digest_previously_changed? do
sessions.where.not(id: Current.session).delete_all
end
<%- if options.trackable? %>
after_update if: :email_previously_changed? do
events.create! action: "email_verification_requested"
end

after_update if: :password_digest_previously_changed? do
events.create! action: "password_changed"
end

after_update if: [:verified_previously_changed?, :verified?] do
events.create! action: "email_verified"
end
<%- end -%>
end

================================================
FILE: lib/generators/authentication/templates/models/concerns/account_scoped.rb
================================================
module AccountScoped
extend ActiveSupport::Concern

included do
belongs_to :account  
 default_scope { where account: Current.account }
end
end

================================================
FILE: lib/generators/authentication/templates/test_unit/test_helper.rb.tt
================================================
ENV["RAILS_ENV"] ||= "test"
require_relative "../config/environment"
require "rails/test_help"

class ActiveSupport::TestCase

# Run tests in parallel with specified workers

parallelize(workers: :number_of_processors)

# Setup all fixtures in test/fixtures/\*.yml for all tests in alphabetical order.

fixtures :all

# Add more helper methods to be used by all tests here...

<%- if options.api? -%>
def sign_in_as(user)
post(sign_in_url, params: { email: user.email, password: "Secret1*3*5*" }); [user, response.headers["X-Session-Token"]]
end
<%- else -%>
def sign_in_as(user)
post(sign_in_url, params: { email: user.email, password: "Secret1*3*5*" }); user
end
<%- end -%>
end

================================================
FILE: lib/generators/authentication/templates/test_unit/users.yml
================================================

# Read about fixtures at https://api.rubyonrails.org/classes/ActiveRecord/FixtureSet.html

lazaro_nixon:
email: <EMAIL>
password_digest: <%= BCrypt::Password.create("Secret1*3*5\*") %>
verified: true

================================================
FILE: lib/generators/authentication/templates/test_unit/controllers/api/passwords_controller_test.rb.tt
================================================
require "test_helper"

class PasswordsControllerTest < ActionDispatch::IntegrationTest
setup do
@user, @token = sign_in_as(users(:lazaro_nixon))
end

def default_headers
{ "Authorization" => "Bearer #{@token}" }
end

test "should update password" do
patch password_url, params: { password_challenge: "Secret1*3*5*", password: "Secret6*4*2*", password_confirmation: "Secret6*4*2\*" }, headers: default_headers
assert_response :success
end

test "should not update password with wrong password challenge" do
patch password_url, params: { password_challenge: "SecretWrong1*3", password: "Secret6*4*2*", password_confirmation: "Secret6*4*2\*" }, headers: default_headers

    assert_response :unprocessable_entity
    assert_equal ["is invalid"], response.parsed_body["password_challenge"]

end
end

================================================
FILE: lib/generators/authentication/templates/test_unit/controllers/api/registrations_controller_test.rb.tt
================================================
require "test_helper"

class RegistrationsControllerTest < ActionDispatch::IntegrationTest
test "should sign up" do
assert_difference("User.count") do
post sign_up_url, params: { email: "<EMAIL>", password: "Secret1*3*5*", password_confirmation: "Secret1*3*5*" }
end

    assert_response :created

end
end

================================================
FILE: lib/generators/authentication/templates/test_unit/controllers/api/sessions_controller_test.rb.tt
================================================
require "test_helper"

class SessionsControllerTest < ActionDispatch::IntegrationTest
setup do
@user, @token = sign_in_as(users(:lazaro_nixon))
end

def default_headers
{ "Authorization" => "Bearer #{@token}" }
end

test "should get index" do
get sessions_url, headers: default_headers
assert_response :success
end

test "should show session" do
get session_url(@user.sessions.last), headers: default_headers
assert_response :success
end

test "should sign in" do
post sign_in_url, params: { email: @user.email, password: "Secret1*3*5\*" }
assert_response :created
end

test "should not sign in with wrong credentials" do
post sign_in_url, params: { email: @user.email, password: "SecretWrong1\*3" }
assert_response :unauthorized
end

test "should sign out" do
delete session_url(@user.sessions.last), headers: default_headers
assert_response :no_content
end
end

================================================
FILE: lib/generators/authentication/templates/test_unit/controllers/api/identity/email_verifications_controller_test.rb.tt
================================================
require "test_helper"

class Identity::EmailVerificationsControllerTest < ActionDispatch::IntegrationTest
setup do
@user, @token = sign_in_as(users(:lazaro_nixon))
@user.update! verified: false
end

def default_headers
{ "Authorization" => "Bearer #{@token}" }
end

test "should send a verification email" do
assert_enqueued_email_with UserMailer, :email_verification, params: { user: @user } do
post identity_email_verification_url, headers: default_headers
end

    assert_response :no_content

end

test "should verify email" do
sid = @user.generate_token_for(:email_verification)

    get identity_email_verification_url, params: { sid: sid }, headers: default_headers
    assert_response :no_content

end

test "should not verify email with expired token" do
sid = @user.generate_token_for(:email_verification)

    travel 3.days

    get identity_email_verification_url, params: { sid: sid }, headers: default_headers

    assert_response :bad_request
    assert_equal "That email verification link is invalid", response.parsed_body["error"]

end
end

================================================
FILE: lib/generators/authentication/templates/test_unit/controllers/api/identity/emails_controller_test.rb.tt
================================================
require "test_helper"

class Identity::EmailsControllerTest < ActionDispatch::IntegrationTest
setup do
@user, @token = sign_in_as(users(:lazaro_nixon))
end

def default_headers
{ "Authorization" => "Bearer #{@token}" }
end

test "should update email" do
patch identity_email_url, params: { email: "<EMAIL>", password_challenge: "Secret1*3*5\*" }, headers: default_headers
assert_response :success
end

test "should not update email with wrong password challenge" do
patch identity_email_url, params: { email: "<EMAIL>", password_challenge: "SecretWrong1\*3" }, headers: default_headers

    assert_response :unprocessable_entity
    assert_equal ["is invalid"], response.parsed_body["password_challenge"]

end
end

================================================
FILE: lib/generators/authentication/templates/test_unit/controllers/api/identity/password_resets_controller_test.rb.tt
================================================
require "test_helper"

class Identity::PasswordResetsControllerTest < ActionDispatch::IntegrationTest
setup do
@user = users(:lazaro_nixon)
end

test "should get edit" do
sid = @user.generate_token_for(:password_reset)

    get edit_identity_password_reset_url(sid: sid)
    assert_response :no_content

end

test "should send a password reset email" do
assert_enqueued_email_with UserMailer, :password_reset, params: { user: @user } do
post identity_password_reset_url, params: { email: @user.email }
end

    assert_response :no_content

end

test "should not send a password reset email to a nonexistent email" do
assert_no_enqueued_emails do
post identity_password_reset_url, params: { email: "<EMAIL>" }
end

    assert_response :bad_request
    assert_equal "You can't reset your password until you verify your email", response.parsed_body["error"]

end

test "should not send a password reset email to a unverified email" do
@user.update! verified: false

    assert_no_enqueued_emails do
      post identity_password_reset_url, params: { email: @user.email }
    end

    assert_response :bad_request
    assert_equal "You can't reset your password until you verify your email", response.parsed_body["error"]

end

test "should update password" do
sid = @user.generate_token_for(:password_reset)

    patch identity_password_reset_url, params: { sid: sid, password: "Secret6*4*2*", password_confirmation: "Secret6*4*2*" }
    assert_response :success

end

test "should not update password with expired token" do
sid = @user.generate_token_for(:password_reset)

    travel 30.minutes

    patch identity_password_reset_url, params: { sid: sid, password: "Secret6*4*2*", password_confirmation: "Secret6*4*2*" }

    assert_response :bad_request
    assert_equal "That password reset link is invalid", response.parsed_body["error"]

end
end

================================================
FILE: lib/generators/authentication/templates/test_unit/controllers/html/passwords_controller_test.rb.tt
================================================
require "test_helper"

class PasswordsControllerTest < ActionDispatch::IntegrationTest
setup do
@user = sign_in_as(users(:lazaro_nixon))
end

test "should get edit" do
get edit_password_url
assert_response :success
end

test "should update password" do
patch password_url, params: { password_challenge: "Secret1*3*5*", password: "Secret6*4*2*", password_confirmation: "Secret6*4*2\*" }
assert_redirected_to root_url
end

test "should not update password with wrong password challenge" do
patch password_url, params: { password_challenge: "SecretWrong1*3", password: "Secret6*4*2*", password_confirmation: "Secret6*4*2\*" }

    assert_response :unprocessable_entity
    assert_select "li", /Password challenge is invalid/

end
end

================================================
FILE: lib/generators/authentication/templates/test_unit/controllers/html/registrations_controller_test.rb.tt
================================================
require "test_helper"

class RegistrationsControllerTest < ActionDispatch::IntegrationTest
test "should get new" do
get sign_up_url
assert_response :success
end

test "should sign up" do
assert_difference("User.count") do
post sign_up_url, params: { email: "<EMAIL>", password: "Secret1*3*5*", password_confirmation: "Secret1*3*5*" }
end

    assert_redirected_to root_url

end
end

================================================
FILE: lib/generators/authentication/templates/test_unit/controllers/html/sessions_controller_test.rb.tt
================================================
require "test_helper"

class SessionsControllerTest < ActionDispatch::IntegrationTest
setup do
@user = users(:lazaro_nixon)
end

test "should get index" do
sign_in_as @user

    get sessions_url
    assert_response :success

end

test "should get new" do
get sign_in_url
assert_response :success
end

test "should sign in" do
post sign_in_url, params: { email: @user.email, password: "Secret1*3*5\*" }
assert_redirected_to root_url

    get root_url
    assert_response :success

end

test "should not sign in with wrong credentials" do
post sign_in_url, params: { email: @user.email, password: "SecretWrong1\*3" }
assert_redirected_to sign_in_url(email_hint: @user.email)
assert_equal "That email or password is incorrect", flash[:alert]

    get root_url
    assert_redirected_to sign_in_url

end

test "should sign out" do
sign_in_as @user

    delete session_url(@user.sessions.last)
    assert_redirected_to sessions_url

    follow_redirect!
    assert_redirected_to sign_in_url

end
end

================================================
FILE: lib/generators/authentication/templates/test_unit/controllers/html/identity/email_verifications_controller_test.rb.tt
================================================
require "test_helper"

class Identity::EmailVerificationsControllerTest < ActionDispatch::IntegrationTest
setup do
@user = sign_in_as(users(:lazaro_nixon))
@user.update! verified: false
end

test "should send a verification email" do
assert_enqueued_email_with UserMailer, :email_verification, params: { user: @user } do
post identity_email_verification_url
end

    assert_redirected_to root_url

end

test "should verify email" do
sid = @user.generate_token_for(:email_verification)

    get identity_email_verification_url(sid: sid, email: @user.email)
    assert_redirected_to root_url

end

test "should not verify email with expired token" do
sid = @user.generate_token_for(:email_verification)

    travel 3.days

    get identity_email_verification_url(sid: sid, email: @user.email)

    assert_redirected_to edit_identity_email_url
    assert_equal "That email verification link is invalid", flash[:alert]

end
end

================================================
FILE: lib/generators/authentication/templates/test_unit/controllers/html/identity/emails_controller_test.rb.tt
================================================
require "test_helper"

class Identity::EmailsControllerTest < ActionDispatch::IntegrationTest
setup do
@user = sign_in_as(users(:lazaro_nixon))
end

test "should get edit" do
get edit_identity_email_url
assert_response :success
end

test "should update email" do
patch identity_email_url, params: { email: "<EMAIL>", password_challenge: "Secret1*3*5\*" }
assert_redirected_to root_url
end

test "should not update email with wrong password challenge" do
patch identity_email_url, params: { email: "<EMAIL>", password_challenge: "SecretWrong1\*3" }

    assert_response :unprocessable_entity
    assert_select "li", /Password challenge is invalid/

end
end

================================================
FILE: lib/generators/authentication/templates/test_unit/controllers/html/identity/password_resets_controller_test.rb.tt
================================================
require "test_helper"

class Identity::PasswordResetsControllerTest < ActionDispatch::IntegrationTest
setup do
@user = users(:lazaro_nixon)
end

test "should get new" do
get new_identity_password_reset_url
assert_response :success
end

test "should get edit" do
sid = @user.generate_token_for(:password_reset)

    get edit_identity_password_reset_url(sid: sid)
    assert_response :success

end

test "should send a password reset email" do
assert_enqueued_email_with UserMailer, :password_reset, params: { user: @user } do
post identity_password_reset_url, params: { email: @user.email }
end

    assert_redirected_to sign_in_url

end

test "should not send a password reset email to a nonexistent email" do
assert_no_enqueued_emails do
post identity_password_reset_url, params: { email: "<EMAIL>" }
end

    assert_redirected_to new_identity_password_reset_url
    assert_equal "You can't reset your password until you verify your email", flash[:alert]

end

test "should not send a password reset email to a unverified email" do
@user.update! verified: false

    assert_no_enqueued_emails do
      post identity_password_reset_url, params: { email: @user.email }
    end

    assert_redirected_to new_identity_password_reset_url
    assert_equal "You can't reset your password until you verify your email", flash[:alert]

end

test "should update password" do
sid = @user.generate_token_for(:password_reset)

    patch identity_password_reset_url, params: { sid: sid, password: "Secret6*4*2*", password_confirmation: "Secret6*4*2*" }
    assert_redirected_to sign_in_url

end

test "should not update password with expired token" do
sid = @user.generate_token_for(:password_reset)

    travel 30.minutes

    patch identity_password_reset_url, params: { sid: sid, password: "Secret6*4*2*", password_confirmation: "Secret6*4*2*" }

    assert_redirected_to new_identity_password_reset_url
    assert_equal "That password reset link is invalid", flash[:alert]

end
end

================================================
FILE: lib/generators/authentication/templates/test_unit/mailers/user_mailer_test.rb.tt
================================================
require "test_helper"

class UserMailerTest < ActionMailer::TestCase
setup do
@user = users(:lazaro_nixon)
end

test "password_reset" do
mail = UserMailer.with(user: @user).password_reset
assert_equal "Reset your password", mail.subject
assert_equal [@user.email], mail.to
end

test "email_verification" do
mail = UserMailer.with(user: @user).email_verification
assert_equal "Verify your email", mail.subject
assert_equal [@user.email], mail.to
end
end

================================================
FILE: .github/FUNDING.yml
================================================
custom: ["https://www.paypal.com/cgi-bin/webscr?cmd=_donations&business=8F3EJLDJVVPDL&currency_code=BRL&source=url", "twitter.com/lazaronixon"]
ko_fi: lazaronixon

================================================
FILE: .github/workflows/CI.yml
================================================

# This workflow uses actions that are not certified by GitHub. They are

# provided by a third-party and are governed by separate terms of service,

# privacy policy, and support documentation.

#

# This workflow will install a prebuilt Ruby version, install dependencies, and

# run tests and linters.

name: "Generate sample app and run tests"
on: [push]

jobs:
test_html:
name: 🧪 Run HTML Tests
runs-on: ubuntu-latest
steps: - name: Checkout code
uses: actions/checkout@v3

      - name: Install Ruby and gems
        uses: ruby/setup-ruby@v1
        with:
          ruby-version: "3.2"
          bundler-cache: true

      - name: Install the latest Rails gem
        run: gem install rails -v "7.2.1"

      - name: Install Rubocop
        run: gem install rubocop rubocop-performance rubocop-minitest rubocop-packaging rubocop-minitest rubocop-rails

      - name: Install Brakeman
        run: gem install brakeman

      - name: Create fresh Rails app and run generator
        run: |
          rails new test-app --skip-action-mailbox --skip-action-text --skip-active-storage
          cp .rubocop.yml test-app/.rubocop.yml
          cd test-app
          bundle add authentication-zero --path ..
          bin/rails generate authentication
          bundle install
          bin/rails db:migrate

      - name: Rubocop
        run: cd test-app && rubocop

      - name: Brakeman
        run: cd test-app && brakeman

      - name: Tests
        run: |
          cd test-app
          bin/rails test

test_api:
name: 🧪 Run API Tests
runs-on: ubuntu-latest
steps: - name: Checkout code
uses: actions/checkout@v3

      - name: Install Ruby and gems
        uses: ruby/setup-ruby@v1
        with:
          ruby-version: "3.2"
          bundler-cache: true

      - name: Install the latest Rails gem
        run: gem install rails -v "7.2.1"

      - name: Install Rubocop
        run: gem install rubocop rubocop-performance rubocop-minitest rubocop-packaging rubocop-minitest rubocop-rails

      - name: Install Brakeman
        run: gem install brakeman

      - name: Create fresh Rails app and run generator
        run: |
          rails new test-app --skip-action-mailbox --skip-action-text --skip-active-storage
          cp .rubocop.yml test-app/.rubocop.yml
          cd test-app
          bundle add authentication-zero --path ..
          bin/rails generate authentication --api
          bundle install
          bin/rails db:migrate

      - name: Rubocop
        run: cd test-app && rubocop

      - name: Brakeman
        run: cd test-app && brakeman

      - name: Tests
        run: cd test-app && bin/rails test
