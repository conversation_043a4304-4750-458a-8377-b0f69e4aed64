#!/usr/bin/env node

/**
 * Login Automation Script using Playwright MCP
 *
 * This script helps automate login to your app running on localhost:5010
 */

const http = require("http");
const readline = require("readline");

class PlaywrightMCPClient {
  constructor(baseUrl = "http://localhost:8931") {
    this.baseUrl = baseUrl;
  }

  async makeRequest(tool, args = {}) {
    return new Promise((resolve, reject) => {
      const postData = JSON.stringify({
        jsonrpc: "2.0",
        id: Date.now(),
        method: "tools/call",
        params: {
          name: tool,
          arguments: args,
        },
      });

      const options = {
        hostname: "localhost",
        port: 8931,
        path: "/mcp",
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json, text/event-stream",
          "Content-Length": Buffer.byteLength(postData),
        },
      };

      const req = http.request(options, (res) => {
        let data = "";
        res.on("data", (chunk) => {
          data += chunk;
        });
        res.on("end", () => {
          try {
            const response = JSON.parse(data);
            resolve(response);
          } catch (error) {
            console.error("Parse error:", error);
            console.error("Raw response:", data);
            reject(error);
          }
        });
      });

      req.on("error", (error) => {
        reject(error);
      });

      req.write(postData);
      req.end();
    });
  }

  async navigate(url) {
    console.log(`🌐 Navigating to: ${url}`);
    const result = await this.makeRequest("browser_navigate", { url });
    return result;
  }

  async takeScreenshot(filename) {
    console.log(`📸 Taking screenshot: ${filename || "screenshot.png"}`);
    const result = await this.makeRequest("browser_take_screenshot", {
      filename,
    });
    return result;
  }

  async getSnapshot() {
    console.log(`📋 Getting page snapshot...`);
    const result = await this.makeRequest("browser_snapshot");
    return result;
  }

  async click(element, ref) {
    console.log(`👆 Clicking: ${element}`);
    const result = await this.makeRequest("browser_click", { element, ref });
    return result;
  }

  async type(element, ref, text, submit = false) {
    console.log(`⌨️  Typing into: ${element}`);
    const result = await this.makeRequest("browser_type", {
      element,
      ref,
      text,
      submit,
    });
    return result;
  }

  async waitFor(time = null, text = null) {
    if (time) {
      console.log(`⏳ Waiting ${time} seconds...`);
      const result = await this.makeRequest("browser_wait_for", { time });
      return result;
    }
    if (text) {
      console.log(`⏳ Waiting for text: "${text}"`);
      const result = await this.makeRequest("browser_wait_for", { text });
      return result;
    }
  }
}

async function promptUser(question) {
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout,
  });

  return new Promise((resolve) => {
    rl.question(question, (answer) => {
      rl.close();
      resolve(answer);
    });
  });
}

async function loginToApp() {
  console.log("🎭 Playwright MCP Login Automation");
  console.log("==================================");
  console.log("");

  const client = new PlaywrightMCPClient();

  try {
    // Step 1: Navigate to the app
    console.log("📍 Step 1: Navigating to your app");
    const navResult = await client.navigate("http://localhost:5010");

    if (navResult.error) {
      console.error("❌ Navigation failed:", navResult.error);
      return;
    }

    console.log("✅ Successfully navigated to localhost:5010");
    console.log("");

    // Wait for page to load
    await client.waitFor(2);

    // Step 2: Take a screenshot to see the current state
    console.log("📍 Step 2: Taking screenshot of current page");
    await client.takeScreenshot("app-initial.png");
    console.log("✅ Screenshot saved as app-initial.png");
    console.log("");

    // Step 3: Get page snapshot to understand the structure
    console.log("📍 Step 3: Analyzing page structure");
    const snapshotResult = await client.getSnapshot();

    if (snapshotResult.error) {
      console.error("❌ Failed to get page snapshot:", snapshotResult.error);
      return;
    }

    const snapshot = snapshotResult.result;
    console.log("✅ Page snapshot obtained");

    // Look for login-related elements
    const hasLoginForm =
      snapshot.includes("email") ||
      snapshot.includes("username") ||
      snapshot.includes("password") ||
      snapshot.includes("login") ||
      snapshot.includes("sign in");

    if (hasLoginForm) {
      console.log("🔍 Found login form elements on the page");
      console.log("");

      // Display relevant parts of the snapshot
      console.log("📄 Page structure (relevant parts):");
      const lines = snapshot.split("\n");
      const relevantLines = lines.filter(
        (line) =>
          line.toLowerCase().includes("email") ||
          line.toLowerCase().includes("username") ||
          line.toLowerCase().includes("password") ||
          line.toLowerCase().includes("login") ||
          line.toLowerCase().includes("sign in") ||
          line.toLowerCase().includes("button") ||
          line.toLowerCase().includes("input")
      );

      relevantLines.slice(0, 20).forEach((line) => {
        console.log("  ", line.trim());
      });

      if (relevantLines.length > 20) {
        console.log(`  ... and ${relevantLines.length - 20} more lines`);
      }

      console.log("");
      console.log("💡 I can see login elements on the page!");
      console.log("");

      // Ask user for credentials
      const email = await promptUser("📧 Enter your email/username: ");
      const password = await promptUser("🔒 Enter your password: ");

      console.log("");
      console.log("🤖 Attempting to fill in login form...");

      // Try to find and fill email/username field
      const emailFields = relevantLines.filter(
        (line) =>
          (line.toLowerCase().includes("email") ||
            line.toLowerCase().includes("username")) &&
          line.includes("input")
      );

      if (emailFields.length > 0) {
        console.log("📧 Found email/username field, filling it...");
        // Extract reference from the line (this is a simplified approach)
        const emailRef = emailFields[0].match(/\[(\d+)\]/)?.[1];
        if (emailRef) {
          await client.type("Email/Username field", emailRef, email);
        }
      }

      // Try to find and fill password field
      const passwordFields = relevantLines.filter(
        (line) =>
          line.toLowerCase().includes("password") && line.includes("input")
      );

      if (passwordFields.length > 0) {
        console.log("🔒 Found password field, filling it...");
        const passwordRef = passwordFields[0].match(/\[(\d+)\]/)?.[1];
        if (passwordRef) {
          await client.type("Password field", passwordRef, password);
        }
      }

      // Try to find and click login button
      const loginButtons = relevantLines.filter(
        (line) =>
          (line.toLowerCase().includes("login") ||
            line.toLowerCase().includes("sign in")) &&
          (line.includes("button") || line.includes("submit"))
      );

      if (loginButtons.length > 0) {
        console.log("🔘 Found login button, clicking it...");
        const buttonRef = loginButtons[0].match(/\[(\d+)\]/)?.[1];
        if (buttonRef) {
          await client.click("Login button", buttonRef);
        }
      }

      // Wait for login to process
      await client.waitFor(3);

      // Take final screenshot
      console.log("📸 Taking final screenshot...");
      await client.takeScreenshot("app-after-login.png");

      console.log("");
      console.log("🎉 Login automation completed!");
      console.log("📸 Screenshots saved:");
      console.log("  • app-initial.png (before login)");
      console.log("  • app-after-login.png (after login)");
    } else {
      console.log("🤔 No obvious login form found on the page");
      console.log("");
      console.log("📄 Here's what I can see on the page:");
      console.log(
        snapshot.substring(0, 1000) + (snapshot.length > 1000 ? "..." : "")
      );
      console.log("");
      console.log("💡 The page might:");
      console.log("  • Already be logged in");
      console.log("  • Have a different login flow");
      console.log("  • Require navigation to a login page first");
    }
  } catch (error) {
    console.error("❌ Login automation failed:", error.message);
    console.log("");
    console.log("🔧 Make sure:");
    console.log("  • Your app is running on localhost:5010");
    console.log(
      "  • The Playwright MCP server is running (yarn playwright:mcp)"
    );
  }
}

// Run the login automation if this script is executed directly
if (require.main === module) {
  loginToApp().catch(console.error);
}

module.exports = { PlaywrightMCPClient, loginToApp };
